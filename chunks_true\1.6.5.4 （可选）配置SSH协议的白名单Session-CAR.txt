

通过配置 SSH 协议的白名单 Session-CAR ，可以区分会话限速。避免发生 SSH Server 报
文发生流量冲击时， SSH Server 会话间报文互相抢占带宽的情况。

##### 背景信息


当 SSH Server 报文发生流量攻击时， SSH Server 会话间报文会发生互相抢占带宽的情
况。 SSH 协议的白名单 Session-CAR 用于对白名单报文通道进行隔离，实现区分会话限
速，避免 SSH Server 会话间报文互相抢占带宽资源。当默认的白名单 Session-CAR 的带
宽参数不满足业务要求时，可以对带宽参数进行调整，灵活适应业务要求。

##### 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **whitelist session-car ssh-server** { **cir** cir-value | **cbs** cbs-value | **pir** pirvalue | **pbs** pbs-value } [*] ，配置 SSH 协议的白名单 Session-CAR 的带宽参数。


步骤 **3** （可选）执行命令 **whitelist session-car ssh-server disable** ，去使能 SSH 协议的白名
单 Session-CAR 功能。


说明


一般情况下不建议关闭该功能。


步骤 **4** 执行命令 **commit** ，提交配置。


**----**
结束
