

当用户通过 Console 口登录设备实现本地维护时，可以根据使用需求或对设备安全的考
虑，配置相应的 Console 用户界面属性。

##### 背景信息


Console 用户界面的属性在设备上都有缺省值，用户一般不需要另外配置。但是用户可
以根据使用需求以及对设备安全的考虑，配置相关属性，比如用户界面的终端属性以
及用户验证方式等。


如需配置 Console 用户界面，请参见 配置 **Console** 用户界面 。


说明


改变 Console 用户界面属性后会立即生效，所以通过 Console 口登录设备后配置 Console 用户界面
属性可能在配置过程中发生连接中断，建议通过其他登录方式配置 Console 用户界面属性。若用
户需要通过 Console 口再次登录设备，需要改变 PC 机上运行的终端仿真程序的相应配置，使之与
设备上配置的 Console 用户界面属性保持一致。
