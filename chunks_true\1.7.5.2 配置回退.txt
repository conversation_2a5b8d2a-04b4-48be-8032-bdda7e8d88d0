

配置回退能够快速、方便地将配置一次性批量回退到用户指定的历史配置状态，本节
介绍系统回退到指定配置状态的操作步骤。

##### 背景信息


用户提交配置后，如果发现配置错误、配置产生故障导致此配置对网络产生了超出预
期的结果时，可以使用此功能将系统的配置批量回退至历史状态。

##### 操作步骤


步骤 **1** （可选）选择相应的配置生效模式，并进行配置编辑和提交。


说明


两种生效模式的特点如下：


          - 立即生效模式：用户执行命令并回车后，系统就会检测当前配置和历史配置是否产生差
异。如果有差异，系统就会提交用户的配置操作。立即生效模式不会生成配置回退点。


          - 两阶段生效模式：用户执行一系列配置命令后，必须输入 **commit** [ **description**
description ] 命令，系统才会检查当前配置和历史配置的差异，并且生成配置回退点，这
样就可以实现对某个业务一系列命令配置的整体生效。而且，如果用户希望能够快速找到
要回退的配置点，可以使用参数 **description** description 为此次配置添加简单的描述，便于
以后跟踪查看。推荐使用此模式进行配置编辑和提交。


       - 执行命令 **system-view** **immediately** ，进入立即生效模式的系统视图，然后执行
所需的配置命令进行配置编辑，配置会立即生效。


       - 执行命令 **system-view** ，进入两阶段生效模式的系统视图。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 194


HUAWEI NetEngine40E
配置指南 1 基础配置


a. （可选）执行命令 **undo configuration checkpoint auto-save disable** ，使
能自动生成配置回退点功能。


b. 进行配置编辑。


c. 执行命令 **commit** [ **description** description | **label** label ] 提交配置使得配
置生效。


步骤 **2** 查看配置回退点的列表以及配置差异。

1. 执行命令 **display configuration commit list** [ **verbose** ] [ number-of-commits
| **label** ] 查看所有配置回退点列表，确认配置回退点是否生成、以及各回退点的相
关信息。


如果希望查看最近一个或者多个配置回退点的相关信息，可以使用 number-ofcommits 参数指定个数。

2. 执行命令 **display configuration commit changes** [ **at** commit-id | **since**
commit-id | **last** number-of-commits ] 查看配置变更。由此可以分析如果执行回
退，可能变化的配置有哪些，从而确定是否执行配置回退，以及如果配置回退会
对系统产生造成哪些影响。


如果希望查看所有配置回退点的配置变更，在命令中不指定任何参数。


如果希望查看某个配置回退点的配置变更，可以使用 **at** commit-id 参数指定配置
回退点。


如果希望查看从某个配置回退点之前到当前状态的配置变更，可以使用 **since**
commit-id 参数指定配置回退点。


如果希望查看最近一次或者多次配置变更，可以使用 **last** number-of-commits 参
数指定次数。


步骤 **3** 根据用户所需的历史配置状态进行配置回退。


       - 如果某个配置回退点对应的历史配置能够满足用户需要，则执行如下步骤：


a. 执行命令 **return** 退回到用户视图，避免回退前用户还有数据未提交。

b. 执行命令 **rollback configuration** { **to** **commit-id** commit-id | **last**
number-of-commits | **to** **label** label | **to** **file** file-name } 选择配置回退点或
者回退的配置次数进行配置回退，将系统回退至所需的历史配置状态。配置
的回退包括：创建的配置会被删除、删除的配置会被重新创建、修改的配置
会被改回原值。


如果希望系统回退到某个配置回退点生成时的历史配置状态，可以使用 **to**
**commit-id** commit-id 参数指定配置回退点。


如果希望系统回退掉最近一个或者多个配置回退点之前的历史配置状态，可
以使用 **last** number-of-commits 参数指定个数。


说明


配置回退结束后，可以执行命令 **display configuration rollback result** 查看最近一
次的回退结果。


       - 如果当前配置有错误，或者与当前配置相比，配置回退点对应的配置更能满足用
户需要，则用户可执行如下步骤，加载对应配置回退点对应的配置，然后在编辑
成需要的内容：


a. 执行命令 **return** 退回到用户视图，避免回退前用户还有数据未提交。


b. 执行命令 **system-view** ，进入系统视图。

c. 执行命令 **load configuration rollback changes** { **at commit-id** atcommit-id | **to commit-id** commit-id | **last** number-of-commits | **to label**
user-label } ，加载指定配置回退点或者用户标签所在的系统配置。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 195


HUAWEI NetEngine40E
配置指南 1 基础配置


说明


配置回退结束后，可以执行命令 **display configuration rollback changes load-**
**result** 查看最近一次的回退结果。


**----**
结束

##### 检查配置结果

       - 执行命令 **display configuration commit list** [ **verbose** ] [ number-of-commits
| **label** ] ，查看配置回退点列表。

       - 执行命令 **display configuration commit changes** [ commit-id | **since** commitid | **last** number-of-commits ] ，查看配置变更。

       - 执行命令 **display configuration rollback result** ，查看最近一次配置回退的失败
信息和配置命令在回退过程中的提示信息。

       - 执行命令 **display configuration rollback changes load-result** ，查看加载指定
配置回退点或者用户标签所在配置时的具体失败命令和原因。
