#!/usr/bin/env python3
"""
排查失败标题的详细原因
"""

import fitz
import re
from main3 import PDFToMarkdownConverter

def debug_failed_titles():
    """调试失败的标题匹配"""
    
    with PDFToMarkdownConverter("2.pdf", {"debug_mode": True}) as converter:
        # 生成raw markdown
        raw_markdown = converter.generate_raw_markdown()
        
        # 提取TOC标题
        toc_titles = converter.extract_toc_titles()
        
        print("=== 排查失败标题 ===")
        
        failed_titles = []
        success_count = 0
        
        # 测试所有标题的匹配
        for i, title in enumerate(toc_titles):
            start_pos, end_pos = converter.find_title_in_markdown(title)
            if start_pos == -1:
                failed_titles.append((i, title))
                print(f"❌ 失败 {i+1}: '{title.text}' (级别: {title.level})")
            else:
                success_count += 1
        
        print(f"\n=== 统计结果 ===")
        print(f"成功: {success_count}/{len(toc_titles)}")
        print(f"失败: {len(failed_titles)}/{len(toc_titles)}")
        
        # 详细分析失败的标题
        print(f"\n=== 详细分析失败标题 ===")
        
        lines = raw_markdown.split('\n')
        
        for i, (idx, title) in enumerate(failed_titles):
            print(f"\n--- 失败标题 {i+1}: {title.text} ---")
            
            # 分析标题特征
            title_words = title.text.split()
            print(f"标题词汇: {title_words}")
            
            # 在raw markdown中搜索相关内容
            print("搜索相关行:")
            
            # 搜索包含标题关键词的行
            relevant_lines = []
            for line_num, line in enumerate(lines):
                line_clean = line.strip()
                if not line_clean:
                    continue
                
                # 检查是否包含标题的主要词汇
                matches = 0
                for word in title_words:
                    if len(word) > 2 and word in line_clean:
                        matches += 1
                
                if matches >= max(1, len(title_words) * 0.3):  # 至少30%匹配
                    relevant_lines.append((line_num, line_clean, matches))
            
            # 按匹配度排序
            relevant_lines.sort(key=lambda x: x[2], reverse=True)
            
            print(f"找到 {len(relevant_lines)} 个相关行:")
            for line_num, line_content, match_count in relevant_lines[:5]:
                print(f"  行{line_num} (匹配{match_count}词): {line_content[:80]}...")
            
            # 尝试手动匹配策略
            print("手动匹配测试:")
            
            # 策略1: 精确匹配
            title_normalized = re.sub(r'\s+', ' ', title.text.strip())
            for line_num, line in enumerate(lines):
                line_normalized = re.sub(r'\s+', ' ', line.strip())
                if title_normalized == line_normalized:
                    print(f"  精确匹配成功: 行{line_num}")
                    break
            else:
                print("  精确匹配失败")
            
            # 策略2: 去除点号匹配
            for line_num, line in enumerate(lines):
                line_clean = re.sub(r'\.+\s*\d*$', '', line.strip())
                line_normalized = re.sub(r'\s+', ' ', line_clean)
                if title_normalized == line_normalized:
                    print(f"  去点号匹配成功: 行{line_num}")
                    break
            else:
                print("  去点号匹配失败")
            
            # 策略3: 智能空格处理
            def normalize_smart(text):
                text = text.strip()
                text = re.sub(r'\s+', ' ', text)
                # 移除英文和中文之间的空格
                text = re.sub(r'([a-zA-Z])\s+([^\sa-zA-Z0-9])', r'\1\2', text)
                text = re.sub(r'([a-zA-Z])\s+([一-龯])', r'\1\2', text)
                return text
            
            title_smart = normalize_smart(title.text)
            for line_num, line in enumerate(lines):
                line_smart = normalize_smart(line)
                if title_smart == line_smart:
                    print(f"  智能匹配成功: 行{line_num}")
                    break
                
                # 也尝试去点号版本
                line_no_dots = re.sub(r'\.+\s*\d*$', '', line.strip())
                line_smart_no_dots = normalize_smart(line_no_dots)
                if title_smart == line_smart_no_dots:
                    print(f"  智能去点号匹配成功: 行{line_num}")
                    break
            else:
                print("  智能匹配失败")
            
            # 策略4: 部分匹配
            for line_num, line in enumerate(lines):
                if title.text in line or any(word in line for word in title_words if len(word) > 3):
                    print(f"  部分匹配: 行{line_num}: {line.strip()[:60]}...")
                    break
            else:
                print("  部分匹配失败")

if __name__ == "__main__":
    debug_failed_titles()
