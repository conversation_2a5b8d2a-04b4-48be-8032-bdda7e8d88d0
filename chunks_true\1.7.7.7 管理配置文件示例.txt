

本示例中，通过演示说明了保存配置文件、配置系统下次启动时使用的配置文件等基
本操作。

##### 组网需求


如 图 **1-58** 所示，用户登录设备。


图 **1-58** 管理配置文件组网图

##### 配置注意事项


无

##### 配置思路


采用如下的思路进行配置：


1. 修改配置。


2. 保存配置文件。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 229


HUAWEI NetEngine40E
配置指南 1 基础配置


3. 配置系统下次启动时使用的配置文件。


4. 系统升级后，比较运行配置和下次启动配置文件是否一致，以确认升级后配置是
否丢失。

##### 操作步骤


步骤 **1** 修改配置。


例如，使能 SFTP 服务。


<HUAWEI> **system-view**

[ ~ HUAWEI] **sftp server enable**

[*HUAWEI] **commit**

[ ~ HUAWEI] **quit**


步骤 **2** 保存配置到 vrpcfg.cfg 文件。


<HUAWEI> **save vrpcfg.cfg**
Warning: Are you sure to save the configuration to vrpcfg.cfg? [Y/N]: **y**


步骤 **3** 配置系统下次启动时使用的配置文件。

<HUAWEI> **startup saved-configuration vrpcfg.cfg**


步骤 **4** 系统升级后，比较运行配置和下次启动配置文件是否一致，以确认升级后配置是否丢
失。

<HUAWEI> **compare configuration**
The current configuration is the same as the next startup configuration file.


**----**
结束

##### 配置文件


#

sysname HUAWEI
#
sftp server enable
