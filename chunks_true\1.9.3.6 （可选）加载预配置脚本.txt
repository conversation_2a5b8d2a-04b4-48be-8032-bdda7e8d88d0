

当设备空配置启动时，在进入 ZTP 流程之前，如果需要对设备进行预配置命令下发，则
需要设置预配置脚本。

##### 操作步骤


步骤 **1** 根据文件类型和格式要求编辑预配置脚本，文件格式请参见预配置脚本。


步骤 **2** 上传预配置脚本至主控板的存储介质中。


说明


本设备支持 FTP ， TFTP 以及 SFTP 上传文件，请参考 通过 **FTP** 访问其他设备的文件 ， 通过 **TFTP** 访
问其他设备的文件 以及 **1.8.7** 通过 **SFTP** 访问其他设备的文件 操作。请根据实际情况选择文件上传
方式上传到设备。


步骤 **3** 执行命令 **set ztp pre-configuration** file-name ，加载预配置脚本。


若用户希望设备空配置启动时不执行 ZTP 预配置流程，可以执行命令 **reset ztp pre-**
**configuration** ，清空预配置脚本。


步骤 **4** 执行命令 **display ztp status** ，可以查看当前设备预配置脚本的配置状态。


说明


在设备软件大包从低版本升级到当前版本时，若设置的启动配置文件为 vrpcfg.zip ，加载的预配
置脚本会被执行（若不想执行预配置脚本，可以通过执行命令 **reset ztp pre-configuration** 清空
预配置脚本）；当设置其它配置文件时，预配置脚本不会被执行。


**----**
结束
