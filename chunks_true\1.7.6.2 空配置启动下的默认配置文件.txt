

当设备升级到当前版本并空配置启动时，需要工程师进行现场配置，设备的运维管理
很不方便。如果希望设备空配置启动后实现即插即用，可以对设备预置一个默认配置
文件，具体请参考 *********** 配置下次启动时加载的配置文件 ，默认配置文件中携带满足
即插即用的一些配置，设备下次空配置启动后就会使用预置的默认配置文件进行配置
恢复。 default-custom.defcfg 文件是设备自带的默认配置文件，只用于设备首次上
线，用户可以根据需要自行修改，具体内容如下：


!Router function begin
#

aaa
local-user root password irreversible-cipher $1c$]f(3Q<j7uS$!0!)8@e`\+lj]vQx\2l&y-$M(|\n_ERFU_BF$!6X$
local-user root service-type ssh
local-user root user-group manage-ug
local-user root expire 2000-01-01
user-password password-force-change disable
#
snmp-agent protocol source all-interface
#

stelnet server enable


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 201


HUAWEI NetEngine40E
配置指南 1 基础配置


snetconf server enable

ssh user root
ssh user root authentication-type password
ssh user root service-type stelnet snetconf
ssh server-source all-interface
ssh ipv6 server-source all-interface
ssh server key-exchange dh_group_exchange_sha256 dh_group_exchange_sha1 dh_group14_sha1
ecdh_sha2_nistp256 ecdh_sha2_nistp384 ecdh_sha2_nistp521
#
ssh server publickey ecc rsa rsa_sha2_256 rsa_sha2_512
#
ssh client publickey ecc rsa rsa_sha2_256 rsa_sha2_512
#
ssh client key-exchange dh_group_exchange_sha256 dh_group_exchange_sha1 dh_group14_sha1
ecdh_sha2_nistp256 ecdh_sha2_nistp384 ecdh_sha2_nistp521
#

return

!Router function end


!Transport function begin
#
undo dcn security-mode enable
#
undo dtls policy qx_dtls_client
#
snmp-agent protocol source all-interface
#

aaa
local-user root password irreversible-cipher $1c$]f(3Q<j7uS$!0!)8@e`\+lj]vQx\2l&y-$M(|\n_ERFU_BF$!6X$
local-user root service-type ssh mml
local-user root user-group manage-ug
local-user root expire 2000-01-01
user-password password-force-change disable
#

stelnet server enable

snetconf server enable

ssh user root
ssh user root authentication-type password
ssh user root service-type stelnet snetconf
#

ssh server-source all-interface
ssh ipv6 server-source all-interface
ssh server key-exchange dh_group_exchange_sha256 dh_group_exchange_sha1 dh_group14_sha1
ecdh_sha2_nistp256 ecdh_sha2_nistp384 ecdh_sha2_nistp521
#
ssh server publickey ecc rsa rsa_sha2_256 rsa_sha2_512
#
ssh client publickey ecc rsa rsa_sha2_256 rsa_sha2_512
#
ssh client key-exchange dh_group_exchange_sha256 dh_group_exchange_sha1 dh_group14_sha1
ecdh_sha2_nistp256 ecdh_sha2_nistp384 ecdh_sha2_nistp521
#

return
!Transport function end


说明


文件中的密码支持设置为用户自定义的明文类型，为了提高安全性，设置的密码建议包含多种类
型字符，包括大写字母、小写字母、数字及特殊字符。
