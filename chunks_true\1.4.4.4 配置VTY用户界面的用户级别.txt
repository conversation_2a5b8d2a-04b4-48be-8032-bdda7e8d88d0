

为了限制不同用户对设备的访问权限，可配置 VTY 用户界面的用户级别，对用户进行分
级管理，从而增加设备管理的安全性。

##### 背景信息


用户的级别与命令级别对应，不同级别的用户登录后，只能使用等于或低于自己级别
的命令。

##### 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **user-interface** **vty** first-ui-number [ last-ui-number ] ，进入 VTY 用户界面
视图。


步骤 **3** 执行命令 **user privilege** **level** level ，设置用户级别。


说明


如果用户界面下配置的命令级别访问权限与用户名本身对应的操作权限冲突，以用户名本身对应
的级别为准。


例如：用户 001 对应 3 级命令，但是在 VTY 0 用户界面上为用户 001 配置的命令级别是 2 。当用户
001 通过 VTY 0 登录系统时，能访问 3 级或 3 级以下的命令。


步骤 **4** 执行命令 **commit** ，提交配置。


**----**
结束
