

二次认证功能可以防止用户误操作造成的业务中断。

##### 背景信息


设备上有些命令，如果用户误操作会关联删除相关特性的配置，导致业务中断，造成
用户网络中断。为了防止误操作，用户可以启用二次认证功能。


当二次认证功能启用后，执行这些命令时，需要先输入登录密码进行二次认证后该命
令才能生效，命令范围包含： **reboot** 、 **reset saved-configuration** 、 **rollback**
**configuration** 、 **undo mpls** 、 **undo mpls te** 、 **undo mpls rsvp** 、 **undo mpls ldp** 、
**undo mpls l2vpn** 、 **undo multicast ipv6 routing-enable** 、 **undo multicast**
**routing-enable** 、 **undo pim** 、 **undo igmp** 、 **undo bgp** 、 **undo stp enable** 。


说明


为防止误操作导致某些业务不可用，建议使能二次认证功能。

##### 任务示例


1. 执行命令 **system-view** ，进入系统视图。

2. 执行命令 **configuration re-authentication enable** ，启用二次认证功能。

3. 执行命令 **commit** ，提交配置。
