

合理的配置 Telnet 服务器参数可提高系统的安全性。目前， Telnet 服务器参数包括侦听
端口号和指定源接口。

##### 背景信息


       - 侦听端口号


如果使用标准的侦听端口号，可能会有攻击者不断访问此端口，导致带宽和服务
器性能的下降，造成其他正常用户无法访问。所以可以重新配置 Telnet 服务器的侦
听端口号，攻击者不知道 Telnet 侦听端口号的更改，有效防止了攻击者对 Telnet 服
务标准端口的访问。


       - 源接口


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 67


HUAWEI NetEngine40E
配置指南 1 基础配置


系统非空配置启动时， Telnet 服务器端接收来自所有接口登录连接请求，系统安全
性比较低。为了提高系统安全性，可指定 Telnet 服务器端的源接口，增加登录受限
功能，仅授权客户可以登录服务器。


系统空配置启动时，不指定任何源接口，用户无法通过 Telnet 方式接入。


成功指定 Telnet 服务器端的源接口后，系统只允许 Telnet 用户通过指定的源接口登
录服务器，通过其他接口登录的 Telnet 用户都将被拒绝。但对于已登录到服务器的
Telnet 用户不会产生影响，只限制后续登录的 Telnet 用户。

##### 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 配置 Telnet 服务器的参数


       - （可选）执行命令 **telnet server port** port-number ，设置 Telnet 服务器的侦听端
口号。


如果配置了新的侦听端口号， Telnet 服务器端先断开当前已经建立的所有 Telnet 连
接，然后使用新的端口号开始侦听。


       - 配置 Telnet 服务器的源接口或者源地址。


– 执行命令 **telnet server-source** **-i** { interface-type interface-number |
interface-name } ，指定 Telnet 服务器的源接口。


说明


如果指定的 Telnet 服务器源接口为逻辑接口，必须已经成功创建逻辑接口，否则会导
致该步骤无法执行成功。


– 执行命令 **telnet server-source** **all-interface** ，指定 Telnet 服务器的源接口为
设备上所有接口。


Telnet 用户可通过设备上配置了 IP 地址的物理接口、已创建并配置 IP 地址的逻
辑接口登录。


说明


配置 **telnet server-source** **all-interface** 命令后，将不会指定 Telnet 服务器的源 IPv4
接口，用户可从所有有效接口登录，增加系统安全风险，建议用户取消配置该命令。


– 执行命令 **telnet ipv6 server-source** **-a** ipv6-address [ **-vpn-instance** vpninstance-name ] ，指定 Telnet 服务器的源 IPv6 地址。


成功指定 Telnet 服务器的源 IPv6 地址后，系统只允许 Telnet 用户通过指定的源
地址登录服务器，通过其他 IPv6 地址登录的 Telnet 用户都将被拒绝。


– 执行命令 **telnet ipv6 server-source** **all-interface** ，指定 Telnet 服务器的源
IPv6 地址为设备上所有已存在的接口上配置的 IPv6 地址。


Telnet 用户可通过设备上配置了 IPv6 地址的物理接口、已创建并配置 IPv6 地址
的逻辑接口登录。


说明


配置 **telnet ipv6 server-source** **all-interface** 命令后，将不会指定 Telnet 服务器的源
IPv6 地址，用户可从所有有效 IPv6 地址登录，增加系统安全风险，建议用户取消配置
该命令。


– 执行命令 **telnet server-source** **physic-isolate** **-i** { interface-type interfacenumber | interface-name } **-a** ip-address ，指定 Telnet 服务器的源接口，并
设置 Telnet 服务器的接口隔离属性。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 68


HUAWEI NetEngine40E
配置指南 1 基础配置


– 执行命令 **telnet ipv6 server-source** **physic-isolate** **-i** { interface-type
interface-number | interface-name } **-a** ipv6-address ，指定 Telnet 服务器的
源 IPv6 接口，并设置 Telnet 服务器的接口隔离属性。


说明


成功设置接口隔离属性后，只能通过配置的物理口连接设备，即报文只能从配置的物
理口上送，通过其他接口上送的报文会被丢弃。


步骤 **3** 执行命令 **commit** ，提交配置。


**----**
结束
