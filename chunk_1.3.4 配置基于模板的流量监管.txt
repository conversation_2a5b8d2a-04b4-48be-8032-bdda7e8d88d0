头标题: 1.3.4 配置基于模板的流量监管
尾标题: 1.3.5 配置基于复杂流分类的流量监管
内容: HUAWEI NetEngine40E配置指南 目 录1.12.3.1 （可选）配置 VPN 的 QoS 模板 .............................................................................................................................3541.12.3.1.1 （可选）配置流队列 WRED 对象 ....................................................................................................................... 3551.12.3.1.2 配置流队列的调度参数 .......................................................................................................................................... 3551.12.3.1.3 （可选）配置流队列到类队列的映射 ................................................................................................................ 3571.12.3.1.4 （可选）配置业务模板 .......................................................................................................................................... 3571.12.3.1.5 （可选）配置用户组队列的流量整形 ................................................................................................................ 3571.12.3.1.6 定义 VPN 的 QoS 模板并配置调度参数 ............................................................................................................ 3581.12.3.1.7 检查配置结果 ........................................................................................................................................................... 3591.12.4 配置 VPWS QoS 流量统计 ...........................................................................................................................................3591.12.5 配置 VPLS QoS 基本功能 .............................................................................................................................................3601.12.5.1 （可选）配置 VPN 的 QoS 模板 .............................................................................................................................3611.12.5.1.1 （可选）配置流队列 WRED 对象 ....................................................................................................................... 3611.12.5.1.2 配置流队列的调度参数 .......................................................................................................................................... 3621.12.5.1.3 （可选）配置流队列到类队列的映射 ................................................................................................................ 3631.12.5.1.4 （可选）配置业务模板 .......................................................................................................................................... 3631.12.5.1.5 （可选）配置用户组队列的流量整形 ................................................................................................................ 3641.12.5.1.6 定义 VPN 的 QoS 模板并配置调度参数 ............................................................................................................ 3641.12.5.1.7 检查配置结果 ........................................................................................................................................................... 3651.12.6 配置 VPLS QoS 流量统计 .............................................................................................................................................3651.12.7 配置举例 ........................................................................................................................................................................... 3671.12.7.1 配置 VPWS QoS 的基本功能示例 .......................................................................................................................... 3671.12.7.2 配置 VPWS QoS 的流量统计功能示例 ..................................................................................................................3761.13 确定性 IP 网络配置 ........................................................................................................................................................... 3861.13.1 确定性 IP 网络简介 ........................................................................................................................................................ 3861.13.2 Deterministstic IP( 确定性 IP 网络 ) 配置注意事项 ................................................................................................ 3861.13.3 配置确定性 IP 网络 ........................................................................................................................................................ 3881.13.3.1 配置时钟同步 ...............................................................................................................................................................3881.13.3.2 使能确定性 IP 网络 .....................................................................................................................................................3891.13.3.3 配置确定性 IP 网络报文复杂流分类的流量策略 .................................................................................................3901.13.3.4 检查配置结果 ...............................................................................................................................................................3911.13.4 维护确定性 IP 网络 ........................................................................................................................................................ 3921.13.5 配置举例 ........................................................................................................................................................................... 3921.13.5.1 配置 EVPN VPWS over SRv6 TE Policy 场景下的确定性 IP 网络示例 .........................................................392文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 viiiHUAWEI NetEngine40E配置指南 1 QoS# **1**## **QoS**1.1 前 言1.2 QoS 简介介绍了 QoS 的基本概念和解决方案， DiffServ 模型和相关技术。1.3 流量监管和流量整形配置介绍流量监管和流量整形的基本概念和配置指导。1.4 拥塞管理和拥塞避免配置通过调整网络的流量来解除网络拥塞并介绍当网络发生拥塞时的几种不同丢包策略。1.5 基于类的 QoS 配置本章介绍了复杂流分类中流量策略和简单流分类中优先级映射的配置和举例。1.6 策略路由配置1.7 QPPB 配置介绍了 QPPB 的基本原理、配置过程和配置举例。1.8 HQoS 配置传统的 QoS 基于端口进行流量调度，无法区分用户和业务。 HQoS 可以针对每个用户的业务流进行队列调度。1.9 MPLS DiffServ 模式配置介绍了 MPLS DiffServ 模式的基本原理、配置过程和配置举例。1.10 用户接入 QoS 配置1.11 组播 QoS 配置组播 QoS 可以保证用户流畅的观看电视节目，做到精细化区分和管理用户带宽。1.12 VPN QoS 配置介绍了 VPN QoS （ Virtual Private Network Quality of Service ）技术的原理、应用和配置。1.13 确定性 IP 网络配置文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 1HUAWEI NetEngine40E配置指南 1 QoS### 1.1 前 言##### 概述本文档介绍了 QoS 的基本概念、在不同应用场景中的配置过程和配置举例。##### License 依赖License 的详细信息，请查阅 License 使用指南。       - 企业网用户：– NE40E License 使用指南： **License** [使用指南](https://support.huawei.com/enterprise/zh/doc/EDOC1100194059)– NE40E-X8AK License 使用指南： **License** [使用指南](https://support.huawei.com/enterprise/zh/doc/EDOC1100194062)##### 产品版本与本文档相对应的产品版本如下所示。##### 读者对象|产品名称|产品版本||---|---||HUAWEI NetEngine40E|V800R023C10SPC500||iMaster NCE-IP|V100R023C10SPC100|本文档主要适用于以下工程师：       - 数据配置工程师       - 调测工程师       - 网络监控工程师       - 系统维护工程师##### 安全声明       - 受限公开声明产品资料中主要介绍了您在使用华为设备时，在网络部署及维护时，需要使用的命令。对用于生产、装备、返厂检测维修的接口、命令，不在资料中说明。对于部分仅用于工程实施、定位故障的高级命令以及升级兼容命令，如使用不当，将可能导致设备异常或者业务中断，建议较高权限的工程师使用。如您需要，请向华为公司申请。       - 加密算法声明使用加密算法时， DES/3DES/RSA （ 3072 位以下 )/MD5 （数字签名场景和口令加密） /SHA1 （数字签名场景）加密算法安全性低，存在安全风险，在协议支持的加密算法选择范围内，建议使用更安全的加密算法，例如 AES/RSA （ 3072 位及以上） /SHA2/HMAC-SHA2 。出于安全性考虑，不建议使用不安全协议 Telnet 、 FTP 、 TFTP ；不建议使用特性BGP 、 LDP 、 PCEP 、 MSDP 、 DCN 、 TCP-AO 、 MSTP 、 VRRP 、 E-trunk 、 AAA 、文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 2HUAWEI NetEngine40E配置指南 1 QoSIPSEC 、 BFD 、 QX 、端口扩展、 SSH 、 SNMP 、 IS-IS 、 RIP 、 SSL 、 NTP 、 OSPF 、Keychain 中的弱安全算法。如果确实需要使用，请执行 undo crypto weakalgorithm disable 命令使能弱安全算法功能。详细步骤请参见《配置指南》。出于安全性考虑，不建议使用该特性中的弱安全算法，若当前系统已关闭弱安全算法功能，配置弱安全算法会提示 Error 信息。如果确实需要使用弱安全算法，请先执行 **undo crypto weak-algorithm disable** 命令使能弱安全算法功能。       - 密码配置声明– 当密码加密方式为 cipher 时，输入以 %^%#......%^%# 为起始和结束符的合法密文（本设备可以解密的密文）时，在设备上查看配置文件时显示的是和配置相同的密文，请不要采用该方式直接配置密码。–为保证设备安全，请定期修改密码。       - MAC 地址、公网 IP 地址使用的声明– 出于特性介绍及配置示例的需要，产品资料中会使用真实设备的 MAC 地址、公网的 IP 地址，如无特殊说明，出现的真实设备的 MAC 地址、公网的 IP 地址均为示意，不指代任何实际意义。– 因开源及第三方软件中自带公网地址（包括公网 IP 地址、公网 URL 地址 / 域名、邮箱地址），本产品没有使用这些公网地址，这遵循业界实践，也符合开源软件使用规范。–出于功能特性实现的需要，设备会使用如下公网地址表 **1-1** 公网地址列表|公网地址|说明||---|---||http://www.huawei.com|华为官方网站地址||<EMAIL>|华为企业用户服务邮箱|       - 个人数据声明–您购买的产品、服务或特性在业务运营或故障定位的过程中将可能获取或使用用户的某些个人数据，因此您有义务根据所适用国家的法律制定必要的用户隐私政策并采取足够的措施以确保用户的个人数据受到充分的保护。–废弃、回收或者再利用设备时，请注意根据需要备份或清除设备中的数据，避免数据泄露的安全风险。如需支持，请联系售后技术支持人员。       - 预置证书使用声明在生产阶段预置于华为设备的华为证书是华为设备必备的出厂身份凭证，对其使用声明如下：–华为预置证书仅用于部署阶段为设备接入客户网络建立初始安全通道以及设备对接，华为不对预置证书的安全性做承诺与保证。–对于将华为预置证书作为业务证书使用而导致的安全风险和安全事件，由客户自行处置并承担后果。– 华为预置证书有效期自 2041 年起开始过期，可以通过 **display pki cert_list****domain default** 命令查看实际的有效期。–预置证书过期后，使用预置证书的业务会中断。– 华为建议客户通过部署 PKI 系统对现网设备、软件签发证书并做好证书的生命周期管理（为保证安全性推荐使用短有效期的证书）。– 华为产品中用于产品入网初始化配置和连接时使用的华为 PKI 根证书支持禁用（当验证华为新网元入网时，可配置重启该证书）。建议客户完成产品入网文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 3HUAWEI NetEngine40E配置指南 1 QoS配置并为产品配置客户 CA 签发的证书后，将该根证书禁用。对于客户未禁用华为 PKI 根证书而带来的安全风险和安全事件，由客户自行处置并承担后果。       - 产品生命周期政策华为公司对产品生命周期的规定以“产品生命周期终止政策”为准，该政策的详细内容请参见如下网址： **[https://support.huawei.com/ecolumnsweb/zh/](https://support.huawei.com/ecolumnsweb/zh/warranty-policy)****[warranty-policy](https://support.huawei.com/ecolumnsweb/zh/warranty-policy)** 。       - 漏洞处理流程华为公司对产品漏洞管理的规定以“漏洞处理流程”为准，该流程的详细内容请参见如下网址： **[https://www.huawei.com/cn/psirt/vul-response-process](https://www.huawei.com/cn/psirt/vul-response-process)**如企业客户须获取漏洞信息，请参见如下网址： **[https://](https://securitybulletin.huawei.com/enterprise/cn/security-advisory)****[securitybulletin.huawei.com/enterprise/cn/security-advisory](https://securitybulletin.huawei.com/enterprise/cn/security-advisory)**       - 华为企业业务最终用户许可协议 (EULA)本最终用户许可协议是最终用户（个人、公司或其他任何实体）与华为公司就华为软件的使用所缔结的协议。最终用户对华为软件的使用受本协议约束，该协议的详细内容请参见如下网址： **[https://e.huawei.com/cn/about/eula](https://e.huawei.com/cn/about/eula)** 。       - 产品资料生命周期策略华为公司针对随产品版本发布的售后客户资料（产品资料），发布了“产品资料生命周期策略”，该策略的详细内容请参见如下网址： **[https://](https://support.huawei.com/enterprise/zh/bulletins-website/ENEWS2000017760)****[support.huawei.com/enterprise/zh/bulletins-website/](https://support.huawei.com/enterprise/zh/bulletins-website/ENEWS2000017760)****[ENEWS2000017760](https://support.huawei.com/enterprise/zh/bulletins-website/ENEWS2000017760)** 。       - 华为预置证书权责说明华为公司对随设备出厂的预置数字证书，发布了“华为设备预置数字证书权责说明”，该说明的详细内容请参见如下网址： **[https://support.huawei.com/](https://support.huawei.com/enterprise/zh/bulletins-service/ENEWS2000015766)****[enterprise/zh/bulletins-service/ENEWS2000015766](https://support.huawei.com/enterprise/zh/bulletins-service/ENEWS2000015766)** 。       - 设备升级、打补丁的声明对设备进行升级或打补丁操作时，请使用软件数字签名（ OpenPGP ）验证工具验证软件。为避免软件被篡改或替换，防止给用户带来安全风险，建议用户进行此项操作。       - 特性声明– NetStream 功能，出于对网络流量的统计管理，可能涉及对最终用户的通信内容分析，建议您在所适用法律法规允许的目的和范围内方可启用相应的功能。在采集、存储用户通信内容的过程中，您应采取足够的措施以确保用户的通信内容受到严格保护。–镜像功能，可能基于运维目的需要对某些最终用户的通信内容进行分析，建议您在所适用法律法规允许的目的和范围内方可启用相应的功能。在采集、存储用户通信内容的过程中，您应采取足够的措施以确保用户的通信内容受到严格保护。–报文头获取功能，出于检测通信传输中的故障和错误的目的，可能涉及采集或存储个人用户某些通信内容。本公司无法单方采集或存储用户通信内容。建议您只有在所适用法律法规允许的目的和范围内方可启用相应的功能。在采集、存储用户通信内容的过程中，您应采取足够的措施以确保用户的通信内容受到严格保护。       - 可靠性设计声明对于网络规划和站点设计，必须严格遵守可靠性设计原则，具备设备级和方案级保护。设备级保护包括双网双平面，双机、跨板双链路的规划原则，避免出现单点，单链路故障。方案级指 FRR 、 VRRP 等快速收敛保护机制。在应用方案级保护文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 4HUAWEI NetEngine40E配置指南 1 QoS时，应避免保护方案的主备路径经过相同链路或者传输，以免方案级保护不生效。##### 特别声明       - 本文档中包含了 NE40E 支持的所有产品内容，如果需要了解在本国销售的设备或单板等硬件相关信息，请查阅硬件描述章节。       - 本手册仅作为使用指导，其内容依据实验室设备信息编写。手册提供的内容具有一般性的指导意义，并不确保涵盖所有使用场景。因版本升级、设备型号不同、板卡限制不同、配置文件不同等原因，可能造成手册中提供的内容与用户使用的设备界面不一致。请以用户设备界面的信息为准，本手册不再针对前述情况造成的差异一一说明。       - 本手册中提供的最大值是设备在实验室特定场景（例如被测试设备上只有某种类型的单板，或者只配置了某一种协议）达到的最大值。在现实网络中，由于设备硬件配置不同、承载的业务不同等原因可能会使设备测试出的最大值与手册中提供的数据不一致。       - 本手册中出现的接口编号仅作示例，并不代表设备上实际具有此编号的接口，实际使用中请以设备上存在的接口编号为准。       - 本手册中的硬件照片仅供参考，具体请以发货的硬件实物为准。       - 本手册中体现设备支持的相关硬件板卡，存在特定客户定制的需求，实际支持以售前销售界面为准。       - 出于特性介绍及配置示例的需要，产品资料中会使用公网 IP 地址，如无特殊说明，资料里出现的公网 IP 地址均为示意，不指代任何实际意义。       - 本手册中配置指南出现的“ XX 配置注意事项”，请结合产品的实际特性支持情况来使用。       - 本手册中的日志参考和告警参考，记录的是对应产品上注册的日志和告警信息。实际应用中可触发的日志和告警，取决于当前产品所支持的业务功能。       - 本文档中描述的所有设备尺寸数据均为设计尺寸，不包含尺寸公差。在部件制造过程中，由于加工或测量等因素的影响，实际尺寸存在一定的偏差。##### 符号约定在本文中可能出现下列标志，它们所代表的含义如下。|符号|说明||---|---|||表示如不避免则将会导致死亡或严重伤害的具有高等<br>级风险的危害。|||表示如不避免则可能导致死亡或严重伤害的具有中等<br>级风险的危害。|||表示如不避免则可能导致轻微或中度伤害的具有低等<br>级风险的危害。|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 5HUAWEI NetEngine40E配置指南 1 QoS##### 命令格式约定 修订记录|符号|说明||---|---|||用于传递设备或环境安全警示信息。如不避免则可能<br>会导致设备损坏、数据丢失、设备性能降低或其它不<br>可预知的结果。<br>“须知”不涉及人身伤害。|||对正文中重点信息的补充说明。<br>“说明”不是安全警示信息，不涉及人身、设备及环<br>境伤害信息。||格式|意义||---|---||粗体|命令行关键字（命令中保持不变、必须照输的部分）采用<br>加粗字体表示。||斜体|命令行参数（命令中必须由实际值进行替代的部分）采用<br>斜体表示。||[ ]|表示用“[ ]”括起来的部分在命令配置时是可选的。||{ x | y | ... }|表示从两个或多个选项中选取一个。||[ x | y | ... ]|表示从两个或多个选项中选取一个或者不选。||{ x | y | ... }*|表示从两个或多个选项中选取多个，最少选取一个，最多<br>选取所有选项。||[ x | y | ... ]*|表示从两个或多个选项中选取多个或者不选。||&<1-n>|表示符号&前面的参数可以重复1～n次。||#|由“#”开始的行表示为注释行。|修改记录累积了每次文档变更的说明。最新版本的文档包含以前所有文档版本的更新内容。|产品版本|文档版本|发布日期||---|---|---||V800R023C10SPC500|02|2024-06-30||V800R023C10SPC500|01|2024-03-30|### 1.2 QoS 简介介绍了 QoS 的基本概念和解决方案， DiffServ 模型和相关技术。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 6HUAWEI NetEngine40E配置指南 1 QoS##### 什么是 QoS服务质量 QoS （ Quality of Service ）是针对各种业务的不同需求，为其提供端到端的服务质量保证。 QoS 不会增加网络带宽，它是有效利用现有网络资源的工具，允许不同的流量不平等的竞争网络资源，语音、视频和重要的数据应用在网络设备中可以优先得到服务。随着网络技术的飞速发展，互联网中的业务越来越多样化。除了传统的 WWW 、 EMail 、 FTP 应用外，用户还尝试在 Internet 上拓展新业务，比如 IP 电话、电子商务、多媒体游戏、远程教学、远程医疗、可视电话、电视会议、视频点播、在线电影等。企业用户也希望通过 VPN 技术，将分布在各地的分支机构连接起来，开展一些事务性应用，比如访问公司的数据库或通过 Telnet 管理远程设备。图 **1-1** 互联网业务网络的普及，业务的多样化，使互联网流量激增，产生网络拥塞，转发时延增加，严重时还会产生丢包，导致业务质量下降甚至不可用。所以，要在 IP 网络上开展这些实时性业务，就必须解决网络拥塞问题。解决网络拥塞的最好的办法是增加网络的带宽。但从运营、维护的成本考虑，这是不现实的，最有效的解决方案就是应用一个“有保证”的策略对网络拥塞进行管理。QoS 技术就是在这种背景下发展起来的。 QoS 技术在当今的互联网中应用越来越多，其作用越来越重要。如果没有 QoS 技术，业务的服务质量就无法保证。##### 四大 QoS 组件       - 流分类和标记 （ Classification and Marking ）：要实现差分服务，需要首先将数据包分为不同的类别或者设置为不同的优先级。将数据包分为不同的类别，称为流分类，流分类并不修改原来的数据包。将数据包设置为不同的优先级称为标记，而标记会修改原来的数据包。说明这里的标记是“外部标记”，一般是在报文离开设备的时候在报文中进行设置，修改报文QoS 优先级字段，目的是为了将 QoS 信息传递给下一台设备；本文后面还有“内部标记”，用于设备内部处理报文，不修改报文。一般是在报文进入设备的时候，就通过流分类，给报文打上内部标记，这样，在报文从设备发出之前，都可以根据内部标记进行 QoS处理。       - 流量监管和整形 （ Policing and Shaping ）：是指将业务流量限制在特定的带宽，当业务流量超过额定带宽时，超过的流量将被丢弃或缓存。其中，将超过的流量丢弃的技术称为流量监管，将超过的流量缓存的技术称为流量整形。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 7HUAWEI NetEngine40E配置指南 1 QoS       - 拥塞管理 （ Congestion management ）：在网络发生拥塞时，将报文放入队列中缓存，并采取某种调度算法安排报文的转发次序。       - 拥塞避免 （ Congestion avoidance ）：监督网络资源的使用情况，当发现拥塞有加剧的趋势时采取主动丢弃报文的策略，通过调整流量来解除网络的过载。其中，分类和标记是实现差分服务的前提和基础；流量监管、流量整形、拥塞管理和拥塞避免从不同方面对网络流量及其分配的资源实施控制，是提供差分服务的具体体现。四个 QoS 组件在网络设备上有着一定的处理顺序，一般情况下按下图所示的顺序处理。图 **1-2** QoS 技术处理流程如 图 **1-3** 所示，四个 QoS 组件按照 DiffServ 模型和业务开展的需要在网络的不同位置实施。原则上在业务接入端口入方向实施流分类 / 标记、流量监管；业务接入端口出方向实施流量整形（如果业务接入端口接入多个不同等级的业务，则业务接入端口出方向还要实施队列调度和丢包策略）；网络侧端口出方向实施拥塞管理和拥塞避免。图 **1-3** QoS 组件部署位置示例文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 8HUAWEI NetEngine40E配置指南 1 QoS### 1.3 流量监管和流量整形配置介绍流量监管和流量整形的基本概念和配置指导。#### 1.3.1 流量监管和流量整形简介流量监管和流量整形是 QoS 保证服务质量的关键要素，为网络的稳定提供了基本的 QoS功能。##### 流量监管简介流量监管 TP （ Traffic Policing ）是指对进入设备的流量进行监控，确保其没有滥用网络资源。通过监控进入网络的某一流量的规格，限制它在一个允许的范围之内，若某个连接的报文流量过大，就丢弃报文，或重新设置该报文的优先级（比如限制 HTTP 报文不能占用超过 50% 的网络带宽），以保护网络资源和运营商的利益不受损害。运营商之间都签有服务水平协议（ SLA ），其中包含每种业务流的承诺速率 CIR（ Committed Information Rate ）、峰值速率 PIR （ Peak Information Rate ）、承诺突发尺寸 CBS （ Committed Burst Size ）、峰值突发尺寸 PBS （ Peak Burst Size ）等流量参数，对超出 SLA 约定的流量报文可指定给予 pass （通过）、 drop （直接丢弃）或markdown （降级）等处理，此处降级是指降低服务等级（ Service Class ），或者是提高丢弃等级（ Color ），即报文在网络拥塞时将被优先丢弃，从而保证在 SLA 约定范围之内的报文享受到 SLA 预定的服务。流量监管采用承诺访问速率 CAR （ Committed Access Rate ）来对流量进行控制。 CAR使用令牌桶算法进行流量速率的评估，依据评估结果，实施预先设定好的监管动作。对应于 SLA 预定的处理动作，流量监管动作包括：       - 转发（ pass ）：对测量结果不超过 CIR 的报文通常处理为继续正常转发。       - 丢弃（ discard ）：对测量结果超过 PIR 的报文通常进行丢弃。       - 重标记（ remark ）：对处于承诺速率（ CIR ）与峰值速率（ PIR ）之间的流量通常执行 Remark 动作，此时的报文不丢弃，而是通过 Remark 降低优先级进行尽力而为转发。##### 流量整形简介流量整形通常是为了使报文速率与下游设备相匹配。当从高速链路向低速链路传输数据，或发生突发流量时，带宽会在低速链路出口处出现瓶颈，导致数据丢失严重。这种情况下，需要在进入高速链路的设备出口处进行流量整形，如图 1 。图 **1-4** 从高速链路向低速链路传输数据文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 9HUAWEI NetEngine40E配置指南 1 QoS通过在上游设备的接口出方向配置流量整形，将上游不规整的流量进行削峰填谷，输出一条比较平整的流量（如图 2 ），从而解决下游设备的瞬时拥塞问题。图 **1-5** 使用流量整形的效果#### 1.3.2 流量监管和流量整形配置注意事项##### 特性限制表 **1-2** 本特性的使用限制|特性限制|系列|涉及产品||---|---|---||在主接口的下行方向，如下命令行互斥：<br>1、接口下的qos car命令<br>2、配置了car命令的qos-profle<br>3、statistics enable/statistic mode forward|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||接口上行方向配置的qos car或含car配置的qos-profle<br>对终结标签节点的报文不生效，终结标签节点包含GRE<br>隧道终结节点、MPLS出PE隧道终结节点以及SRv6的<br>endpoint和egress节点。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 10HUAWEI NetEngine40E配置指南 1 QoS|特性限制|系列|涉及产品||---|---|---||多点到多点IP硬管道业务在上行NP做硬管道CAR，所以<br>对于AC口存在跨NP模块（trunk口成员跨NP或者多个<br>物理AC口跨NP同时接入）的情况，硬管道带宽翻倍。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A||IPv6 GRE的tunnel接口下的端口car功能，不支持色敏<br>模式的car。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||CAR限速带宽受限于芯片能力，限速带宽的生效值上限<br>是设备的最大带宽。配置的参数超过最大带宽时按最大<br>带宽400G bps生效。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A||CAR限速带宽受限于芯片能力，限速带宽的生效值上限<br>是单板的最大带宽，当配置的参数超过最大带宽时按最<br>大带宽生效。<br>LPUF-51-E/LPUI-51-E/LPUI-51-S/LPUF-120/<br>LPUF-120-B/LPUF-120-E/LPUI-102-E/LPUI-120/<br>LPUI-120-B/LPUI-120-L/LPUI-52-E/LPUI-120-E/<br>LPUF-240/LPUF-240-B/LPUF-240-E/LPUI-240/<br>LPUI-240-B/LPUI-240-L/LPUF-480/LPUF-480-B/<br>LPUI-480/LPUI-480-B/LPUI-480-L/LPUF-480-E/<br>LPUI-480-CM/LPUF-400-E单板的最大带宽为100G<br>bps。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 11HUAWEI NetEngine40E配置指南 1 QoS|特性限制|系列|涉及产品||---|---|---||CAR限速带宽受限于芯片能力，限速带宽的生效值上限<br>是单板的最大带宽，当配置的参数超过最大带宽时按最<br>大带宽生效。<br>LPUF-53A/LPUF-243A/LPUI-242A/LPUF-483A/<br>LPUI-52C/LPUI-402C/LPUF-400-E/LPUI-2TA/<br>LPUF-1T2A/LPUI-1T2A-CM/LPUI-483A-CM/<br>LPUI-243A-CM/LPUF-243-K/LPUF-53-K/VSUI-400-K/<br>LPUI-2T-K/LPUF-53D/LPUF-243D/LPUF-483D/<br>VSUI-400A单板的最大带宽为400G bps。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A||CAR限速带宽受限于芯片能力，限速带宽的生效值上限<br>是单板的最大带宽，当配置的参数超过最大带宽时按最<br>大带宽生效。<br>LPUI-2T/LPUI-2T-B/LPUI-2T-CM/LPUF-481/<br>LPUF-402-E/LPUF-1T-E/LPUI-1T-CM单板的最大带宽<br>为1024G bps。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A||CAR限速带宽受限于芯片能力，限速带宽的生效值上限<br>是单板的最大带宽，当配置的参数超过最大带宽时按最<br>大带宽生效。<br>LPUI-200/LPUI-200-L/LPUF-200/LPUF-200-B/<br>LPUI-1T/LPUI-1T-B/LPUI-1T-L/LPUI-241/LPUI-241-B/<br>LPUI-241-CM/LPUF-241/LPUF-241-E/LPUI-480-CM/<br>LPUF-52/LPUI-245A-CM/LPUI-245-CM/LPUF-200A-<br>E/LPUF-200-E/LPUF-240/LPUF-240A-E/LPUF-240-E/<br>LPUF-245A/LPUF-245A-E/LPUI-240/LPUI-480/<br>LPUF-480/LPUF-485A/LPUF-55A/LPUF-400-E/<br>LPUF-480-E/LPUF-485A-E/LPUF-245/LPUF-245-E/<br>LPUF-485/LPUF-485-E/LPUF-485-CM/LPUI-240-CM/<br>LPUF-241-E/LPUI-480-CM单板的最大带宽为100G<br>bps。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK|#### 1.3.3 配置基于接口的流量监管基于接口的流量监管通过在指定接口配置 CAR 来实现对接口流量的统一监管。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 请根据流量监管应用的接口类型选择下面的配置步骤。       - 在三层接口上应用a. 执行命令 **interface** interface-type interface-number ，进入三层接口视图。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 12HUAWEI NetEngine40E配置指南 1 QoS       - 在二层接口上应用a. 执行命令 **interface** interface-type interface-number ，进入三层接口视图。b. 执行命令 **portswitch** ，切换到二层端口视图。将二层接口加入 VLAN ，请根据实际情况进行如下配置。#### ▪ 如果把二层端口加入到指定的 VLAN 中，执行命令 port default vlanvlan-id 。说明执行该命令之前请确认指定的 VLAN 已经创建。#### ▪ 如果配置当前端口允许通过的多个连续 VLAN ID ，执行命令 port trunk**allow-pass** **vlan** { { vlan-id1 [ **to** vlan-id2 ] } & <1-10> | **all** } 。步骤 **3** 执行命令 **qos car** { **cir-percentage** cir-percentage-value [ **pir-percentage** pirpercentage-value ] } [ **cbs** cbs-value [ **pbs** pbs-value ] ] [ **green** { **discard** | pass[ **service-class** class **color** color ] } | **yellow** { **discard** | **pass** [ **service-class** class**color** color ] } | **red** { **discard** | **pass** [ **service-class** class **color** color ] } ] *{ **inbound** | **outbound** } [ **color-aware** ] 或执行命令 **qos car** { **cir** cir-value [ **pir** pirvalue ] } [ **cbs** cbs-value [ **pbs** pbs-value ] ] [ **adjust** adjust-value ] [ **green**{ **discard** | **pass** [ **service-class** class **color** color ] } | **yellow** { **discard** | **pass**[ **service-class** class **color** color ] } | **red** { **discard** | **pass** [ **service-class** class **color**color ] } ] * { **inbound** | **outbound** } [ **vlan** { vlan-id1 [ **to** vlan-id2 ] &<1-10> } ][ **identifier** { **none vid ce-vid vid-ce-vid** } ] [ **color-aware** ] ，在接口上配置 CAR 。说明          - [ **vlan** { vlan-id1 [ **to** vlan-id2 ] & <1-10> } ] 参数只对二层接口有效，用来配置对 VLAN 报文进行流量监管。该命令在三层接口上配置时，不能配置 VLAN ID ；该命令在二层端口上配置时必须配置 VLAN ID 。          - 当在一个接口下同时配置接口 CAR 和复杂流分类 CAR 动作时，接口 CAR 统计数据中不包括做流分类 CAR 动作的报文数和字节数。          - 当复杂流分类的 CAR 与接口 CAR 同时配置时，会做两级 CAR ，先做复杂流分类的 CAR ，然后做接口 CAR ；当广播抑制和接口 CAR 同时配置时，接口 CAR 的统计数据仅针对已知单播报文，广播抑制的统计数据针对广播报文；当上送 CPU CAR 和接口 CAR 同时配置时，以上送CPU CAR 的统计数据为准。          - Trunk 接口的成员接口上不能配置接口 CAR 。          - **cir** 和 **pir** 的单位是 kbit/s ， **cbs** 和 **pbs** 的单位是 byte 。          - 如果网络流量较简单，可以配置单令牌桶监管，请选择参数 **cir** 和 **cbs** 。          - 如果网络流量较复杂，需要配置双令牌桶监管，请选择参数 **cir** 、 **pir** 、 **cbs** 和 **pbs** 。步骤 **4** （可选）执行命令 **qos qos-car member-link-scheduler distribute** ，配置在端口 CAR动作应用到 Trunk 口时， Trunk 成员口流量按照权重分配。步骤 **5** 执行命令 **commit** ，提交配置。**----**结束##### 检查配置结果完成配置后，可以按以下指导来检查配置结果。       - 使用 **display interface** [ interface-type [ interface-number ] ] 命令查看接口的流量信息。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 13HUAWEI NetEngine40E配置指南 1 QoS       - 使用 **display car statistics interface** interface-type interface-number{ **inbound** | **outbound** } 命令查看三层接口指定方向的 CAR 统计数据。       - 使用 **display car statistics interface** interface-type interface-number **vlan**vlan-id { **inbound** | **outbound** } 命令查看二层接口指定方向的 CAR 统计数据。#### 1.3.4 配置基于模板的流量监管配置基于 QoS 模板的流量监管支持在 QoS 模板上配置 CAR 策略，并通过在接口上应用QoS 模板来实现对接口流量的监管。##### 操作步骤       - 在 QoS 模板中配置流量监管。a. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **qos-profile** profile-name ，定义 QoS 模板并进入 QoS 模板视图。c. （可选）执行命令 **description** description-info ，配置 QoS 模板的描述信息。d. 执行命令 **car** { **cir** cir-value [ **pir** <pir-value> ] | **cir cir-percentage** cirpercentage-value [ **pir pir-percentage** pir-percentage-value ] } [ **cbs** cbsvalue [ **pbs** pbs-value ] ] [ **green** { **discard** | **pass** [ **service-class** class**color** color ] } | **yellow** { **discard** | **pass** [ **service-class** class **color**color ] } | **red** { **discard** | **pass** [ **service-class** class **color** color ] } ] *[ **inbound** | **outbound** ] [ **color-aware** ] ，配置 CAR 来保证用户流量。e. （可选）执行命令 **broadcast-suppression** **cir** cir-value [ **cbs** cbs-value ][ **inbound** | **outbound** ] ，配置 QoS 模板下的广播报文抑制速率。f. （可选）执行命令 **multicast-suppression** **cir** cir-value [ **cbs** cbs-value ][ **inbound** | **outbound** ] ，配置 QoS 模板下的组播报文抑制速率。g. （可选）执行命令 **unknown-unicast-suppression** **cir** cir-value [ **cbs** cbsvalue ] [ **inbound** | **outbound** ] ，配置 QoS 模板下的未知单播报文的抑制速率。h. （可选）执行命令 **bu-suppression** **cir** cir-value [ **cbs** cbs-value ]**inbound** ，配置 QoS 模板下广播和未知单播报文的整体抑制速率。i. （可选）执行命令 **bum-suppression** **cir** cir-value [ **cbs** cbs-value ]**inbound** ，配置 QoS 模板下广播、未知单播和组播报文的整体抑制速率。说明接口上 **qos-profile** 命令与接口上配置的同方向的 **CAR** 和流量抑制互斥，不能同时配置。       - 应用 QoS 模板。a. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **interface** interface-type interface-number ，进入接口视图。c. 请根据接口类型选择不同的命令行应用 QoS 模板。#### ▪ 执行命令 qos-profile qos-profile-name { inbound | outbound }[ **identifier** **none** ] [ **group** group-name ] ，在 IP-Trunk 接口、 Dot1Q子接口、端口扩展接口上应用 QoS 模板。#### ▪ 执行命令 qos-profile qos-profile-name { inbound | outbound } vlanvlan-id-begin [ **to** vlan-id-end ] [ **identifier** { **vlan-id** | **none** } ]文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 14HUAWEI NetEngine40E配置指南 1 QoS[ **group** group-name ] ，在二层接口或者 Dot1Q 终结子接口上应用 QoS模板。#### ▪ 执行命令 qos-profile qos-profile-name { inbound | outbound } pe**vid** pe-vlan-id **ce-vid** { ce-vlan-id-begin [ **to** ce-vlan-id-end ] }[ **identifier** { **pe-vid** | **ce-vid** | **pe-ce-vid** | **none** } ] [ **group** groupname ] ，在 QinQ 终结子接口上应用 QoS 模板。#### ▪ 执行命令 qos-profile qos-profile-name { inbound | outbound }[ **identifier** { **none** | **vid** | **ce-vid** | **vid-ce-vid** } ] [ **group** groupname ] ，在 EVC 二层子接口上应用 QoS 模板。#### ▪ 执行命令 qos-profile qos-profile-name { inbound | outbound } vnivni-id **source** sourceip **peer** peerip ，在 NVE 接口上应用 QoS 模板。d. 执行命令 **commit** ，提交配置。e. 执行命令 **quit** ，退回到系统视图。**----**结束##### 检查配置结果完成配置后，可以按以下指导来检查配置结果。       - 使用 **display qos-profile application** { qos-profile-name } 命令，查看 QoS 模板的应用信息。       - 使用 **display qos-profile configuration** [ qos-profile-name ] 命令，查看 QoS 模板的配置。       - 使用 **monitor qos-profile statistics** **interface** { interface-name | interface-typeinterface-number } [ **vlan** vlan-id | **pe-vid** pe-vid **ce-vid** ce-vid | **vid** vid-id | **ce-****vid** ce-vid | **vid** vid-id **ce-vid** ce-vid ] { **inbound** | **outbound** } [ **interval**seconds [ **repeat** repeat ] ] 命令，监控 QoS 模板的统计信息。       - 使用 **display traffic buffer usage slot** slot-id 命令，查看当前 buffer 的使用情况。VS 模式下，该命令仅在 Admin VS 支持。#### 1.3.5 配置基于复杂流分类的流量监管配置基于复杂流分类的流量监管。##### 应用环境网络中有大量的用户发送数据，这些不断突发的数据会使网络拥塞，严重影响网络的正常运行和服务质量。如果需要对进入网络的满足特定条件的某一类或几类报文进行流量控制，在网络空闲和拥塞时都能够保证一定带宽（丢失部分的数据不会对整体数据产生太大的影响），可以通过将复杂流分类与流量控制行为结合，配置基于复杂流分类的流量监管策略。然后将该策略应用于网络入接口，使某一类或几类报文流量限制在合理的范围之内，让有限的网络资源更好地发挥效用。说明复杂流分类是指根据五元组（源地址、源端口号、协议号码、目的地址、目的端口号）等报文信息对报文进行分类，通常应用于网络的边缘位置。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 15HUAWEI NetEngine40E配置指南 1 QoS##### 前置任务在配置基于复杂流分类的流量监管之前，需要完成以下任务：       - 配置相关接口的物理参数       - 配置相关接口的链路层属性，保证接口的正常工作       - 配置相关接口的 IP 地址       - 使能路由协议，实现互通##### ******* 定义流分类要对网络中的流量进行基于类的 QoS 配置，就需要先定义流分类，流量的分类可以按照 ACL 规则、报文优先级、 MAC 地址、协议地址等进行定义。##### 操作步骤       - 定义基于三层 / 四层信息的流分类a. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **traffic classifier** classifier-name [ **operator** { **and** | **or** } ] ，定义流分类并进入流分类视图。如果在一个流分类中配置了多个匹配规则，可以通过指定参数 operator 来设置这些规则之间的关系，其中：#### ▪ and ：指定类下的规则之间是逻辑“与”的关系，即数据包必须匹配全部规则才属于该类。#### ▪ or ：指定类下的规则之间是逻辑“或”的关系，即数据包只要匹配其中任何一个规则就属于该类。c. 请根据实际情况对流分类的匹配规则进行定义。说明如果为 IPv6 报文，在步骤 3 中选择匹配规则时，请指定关键字 **ipv6** 。其中定义基于源、目的 IP 地址的匹配规则只适用于 IPv6 报文， IPv4 报文不支持。ACL 匹配规则可以根据用户的不同需求定义不同的访问控制列表，包括协议类型、源地址、目的地址或则报文中的优先级字段等。 **if-match acl** 命令根据 **rule** 命令定义的ACL 规则过滤报文，然后进行对应的流行为。#### ▪ 如果定义 ACL 匹配规则，执行命令 if-match [ ipv6 ] acl { acl-number |**name** acl-name } [ **precedence** precedence-value ] 。#### ▪ 如果定义 DSCP 匹配规则，执行命令 if-match [ ipv6 ] dscp dscpvalue 。#### ▪ 如果定义 IPv4 TCP Flag 匹配规则，执行命令 if-match tcp syn-flag{ tcpflag-value [ **mask** tcpflag-mask ] | **bit-match** { **established** | **fin**| **syn** | **rst** | **psh** | **ack** | **urg** | **ece** | **cwr** | **ns** } } 。#### ▪ 如果定义 IPv6 TCP Flag 匹配规则，执行命令 if-match ipv6 tcp syn-flag{ tcpflag-value-ipv6 [ **mask** tcpflag-mask-ipv6 ] | **bit-match**{ **established** | **fin** | **syn** | **rst** | **psh** | **ack** | **urg** } } 。#### ▪ 如果定义 IP 报文优先级的匹配规则，执行命令 if-match [ ipv6 ] ip**precedence** ip-precedence 。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 16
最终截取: #### 1.3.4 配置基于模板的流量监管


配置基于 QoS 模板的流量监管支持在 QoS 模板上配置 CAR 策略，并通过在接口上应用
QoS 模板来实现对接口流量的监管。

##### 操作步骤


       - 在 QoS 模板中配置流量监管。


a. 执行命令 **system-view** ，进入系统视图。

b. 执行命令 **qos-profile** profile-name ，定义 QoS 模板并进入 QoS 模板视图。

c. （可选）执行命令 **description** description-info ，配置 QoS 模板的描述信
息。


d. 执行命令 **car** { **cir** cir-value [ **pir** <pir-value> ] | **cir cir-percentage** cirpercentage-value [ **pir pir-percentage** pir-percentage-value ] } [ **cbs** cbsvalue [ **pbs** pbs-value ] ] [ **green** { **discard** | **pass** [ **service-class** class
**color** color ] } | **yellow** { **discard** | **pass** [ **service-class** class **color**
color ] } | **red** { **discard** | **pass** [ **service-class** class **color** color ] } ] *

[ **inbound** | **outbound** ] [ **color-aware** ] ，配置 CAR 来保证用户流量。


e. （可选）执行命令 **broadcast-suppression** **cir** cir-value [ **cbs** cbs-value ]

[ **inbound** | **outbound** ] ，配置 QoS 模板下的广播报文抑制速率。


f. （可选）执行命令 **multicast-suppression** **cir** cir-value [ **cbs** cbs-value ]

[ **inbound** | **outbound** ] ，配置 QoS 模板下的组播报文抑制速率。


g. （可选）执行命令 **unknown-unicast-suppression** **cir** cir-value [ **cbs** cbsvalue ] [ **inbound** | **outbound** ] ，配置 QoS 模板下的未知单播报文的抑制速
率。


h. （可选）执行命令 **bu-suppression** **cir** cir-value [ **cbs** cbs-value ]
**inbound** ，配置 QoS 模板下广播和未知单播报文的整体抑制速率。


i. （可选）执行命令 **bum-suppression** **cir** cir-value [ **cbs** cbs-value ]
**inbound** ，配置 QoS 模板下广播、未知单播和组播报文的整体抑制速率。


说明


接口上 **qos-profile** 命令与接口上配置的同方向的 **CAR** 和流量抑制互斥，不能同时配
置。


       - 应用 QoS 模板。


a. 执行命令 **system-view** ，进入系统视图。


b. 执行命令 **interface** interface-type interface-number ，进入接口视图。


c. 请根据接口类型选择不同的命令行应用 QoS 模板。

#### ▪ 执行命令 qos-profile qos-profile-name { inbound | outbound }

[ **identifier** **none** ] [ **group** group-name ] ，在 IP-Trunk 接口、 Dot1Q
子接口、端口扩展接口上应用 QoS 模板。

#### ▪ 执行命令 qos-profile qos-profile-name { inbound | outbound } vlan

vlan-id-begin [ **to** vlan-id-end ] [ **identifier** { **vlan-id** | **none** } ]


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 14



HUAWEI NetEngine40E
配置指南 1 QoS


[ **group** group-name ] ，在二层接口或者 Dot1Q 终结子接口上应用 QoS
模板。

#### ▪ 执行命令 qos-profile qos-profile-name { inbound | outbound } pe
**vid** pe-vlan-id **ce-vid** { ce-vlan-id-begin [ **to** ce-vlan-id-end ] }

[ **identifier** { **pe-vid** | **ce-vid** | **pe-ce-vid** | **none** } ] [ **group** groupname ] ，在 QinQ 终结子接口上应用 QoS 模板。

#### ▪ 执行命令 qos-profile qos-profile-name { inbound | outbound }

[ **identifier** { **none** | **vid** | **ce-vid** | **vid-ce-vid** } ] [ **group** groupname ] ，在 EVC 二层子接口上应用 QoS 模板。

#### ▪ 执行命令 qos-profile qos-profile-name { inbound | outbound } vni

vni-id **source** sourceip **peer** peerip ，在 NVE 接口上应用 QoS 模板。


d. 执行命令 **commit** ，提交配置。


e. 执行命令 **quit** ，退回到系统视图。


**----**
结束

##### 检查配置结果


完成配置后，可以按以下指导来检查配置结果。

       - 使用 **display qos-profile application** { qos-profile-name } 命令，查看 QoS 模板
的应用信息。

       - 使用 **display qos-profile configuration** [ qos-profile-name ] 命令，查看 QoS 模
板的配置。

       - 使用 **monitor qos-profile statistics** **interface** { interface-name | interface-type
interface-number } [ **vlan** vlan-id | **pe-vid** pe-vid **ce-vid** ce-vid | **vid** vid-id | **ce-**
**vid** ce-vid | **vid** vid-id **ce-vid** ce-vid ] { **inbound** | **outbound** } [ **interval**
seconds [ **repeat** repeat ] ] 命令，监控 QoS 模板的统计信息。

       - 使用 **display traffic buffer usage slot** slot-id 命令，查看当前 buffer 的使用情
况。


VS 模式下，该命令仅在 Admin VS 支持。

