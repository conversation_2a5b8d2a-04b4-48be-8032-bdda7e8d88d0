import pymupdf4llm
import pymupdf
import json
import logging
from dataclasses import dataclass
from typing import List, Dict, Any, Tuple, Optional
import re


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('pdf_converter.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def print_dict(str):
    print(json.dumps(str,ensure_ascii=False,indent=2))


# data = pymupdf4llm.to_markdown("2.pdf", page_chunks=True,pages=[25])

# page_1_text = data[0]
# print_dict(page_1_text)



@dataclass
class TitleInfo:
    """标题信息"""
    level: int
    text: str
    page_num: int
    original_text: str  # TOC中的原始文本
    
    
def clean_title_text(title: str) -> str:
    """清理标题文本"""
    # 移除多余的空格和特殊字符
    title = re.sub(r'\s+', ' ', title.strip())
        
    # 移除可能的页码信息
    title = re.sub(r'\s*\.\.\.\s*\d+\s*$', '', title)
    title = re.sub(r'\s*\d+\s*$', '', title)
        
    return title.strip()
    
def extract_toc_titles(doc) -> List[TitleInfo]:
        """提取TOC标题信息"""
        logger.info("提取TOC标题信息...")
        
        toc = doc.get_toc()
        if not toc:
            logger.error("PDF中没有找到目录信息")
            return []
        
        titles = []
        for item in toc:
            level, title, page_num = item
            
            # 清理标题文本
            # clean_title = self.clean_title_text(title)
            title_info = TitleInfo(
                level=level,
                text=title,
                page_num=page_num,
                original_text=title
            )
            titles.append(title_info)
        
        logger.info(f"提取到 {len(titles)} 个标题")
        
        return titles

def extract_text_between_substrings(main_string_list, start_substring: str, end_substring: str) -> str:
     
        def _normalize_for_hash_match(text):
            text =   re.sub(r'[*#\s]+', '', text)
            return text
        
        # 提取所有带#的行
        hash_lines = []
        for i, line in enumerate(main_string_list):
            if '#' in line:
                hash_lines.append((i, _normalize_for_hash_match(line)))
            if '**' in line:
                hash_lines.append((i, _normalize_for_hash_match(line)))

        start_title= _normalize_for_hash_match(start_substring)
        end_title= _normalize_for_hash_match(end_substring)
        
        #精准匹配
        start_index=0
        end_index=0
        for line in hash_lines:
            if start_title == line[1]:
                start_index=line[0]
            if end_title == line[1]:
                end_index=line[0]
        #模糊匹配
        if start_index==0:
            for line in hash_lines:
                if start_title in line[1]:
                    start_index=line[0]
                    break
        if end_index==0:
            for line in hash_lines:
                if end_title in line[1]:
                    end_index=line[0]
                    break
        #最后一个标题  
        if(start_index==end_index):
            end_index=len(main_string_list)-1
            
        result_content=[]
        for line in main_string_list[start_index+1:end_index]:
            result_content.append(line)
        if len(result_content)>0:
            return ("\n".join(result_content)).replace('#','//#')
        else:
            print(start_title)
            print(end_title)
            print(hash_lines)
            print(main_string_list)
            return ""


def extract_content(file_url):
    
    doc= pymupdf.open(file_url)
    titles_metadata_list=extract_toc_titles(doc)
    result_content_list=[]
    for index,title_metadata in enumerate(titles_metadata_list):
        # print(f'正在处理{title_metadata}')
        start_page=title_metadata.page_num
        if(index==len(titles_metadata_list)-1):
            end_page=doc.page_count
        else:
            end_page=titles_metadata_list[index+1].page_num
        # print(f'页码范围为{start_page} - {end_page}')
        raw_content_in_range_chunk = pymupdf4llm.to_markdown(file_url, page_chunks=True,pages=[num for num in range(start_page-1, end_page)])
        raw_content_list =[raw_content_in_range['text'] for raw_content_in_range in raw_content_in_range_chunk]
        raw_content=''.join(raw_content_list)
        raw_content_list = raw_content.split('\n')
        

        content = extract_text_between_substrings(raw_content_list, title_metadata.original_text,titles_metadata_list[min(index+1,len(titles_metadata_list)-1)].original_text)
        # print(content)
        if content=='':
            print('当前标题找不到内容')
            #写入raw_content到chunks目录下的文件，命名为title.text.txt
            with open(f'chunks/{title_metadata.text.replace('/','_')}.txt', 'w', encoding='utf-8') as f:
                f.write(f"标题: {title_metadata.original_text}\n")
                f.write(f"下一任标题：{titles_metadata_list[min(index+1,len(titles_metadata_list)-1)].original_text}")
                f.write(raw_content)
        else: 
            result_content_list.append(int(title_metadata.level)*"#" + " " + title_metadata.original_text)
            result_content_list.append(content)
    return '\n'.join(result_content_list)


from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
logger = logging.getLogger(__name__)

PDF_DIR = Path(r'./raw_pdf')   # 放 PDF 的目录
OUT_DIR = PDF_DIR / 'md'                                 # 生成 markdown 的子目录
OUT_DIR.mkdir(exist_ok=True)

for pdf_file in PDF_DIR.glob('*.pdf'):
    markdown = extract_content(str(pdf_file))            # 你的函数
    md_path = OUT_DIR / f'{pdf_file.stem}.md'
    md_path.write_text(markdown, encoding='utf-8')
    logger.info(f'Markdown 文件已保存到: {md_path}')