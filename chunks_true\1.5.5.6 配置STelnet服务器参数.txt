

用户可调整合理的服务器参数，保证服务器的可靠性。服务器参数包括服务器密钥对
更新时间、 SSH 认证超时时间、 SSH 验证重试次数、兼容低版本 SSH 协议，以及 SSH 服
务器侦听端口号等。

##### 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 配置 SSH 服务器参数，根据需要，可执行如 表 **1-19** 中的一个或多个操作。


表 **1-19** 服务器参数








|服务器参数|命令|说明|
|---|---|---|
|（可选）配置<br>密钥对更新时<br>间|执行命令**ssh server rekey-**<br>**interval** interval|配置服务器密钥对更新时间，使<br>得当SSH服务器的更新周期到达<br>时，自动更新服务器密钥对，从<br>而可以保证安全性。|
|（可选）配置<br>SSH认证超时<br>时间|执行命令**ssh server timeout**<br>seconds|当设置的SSH认证超时时间到达<br>后，如果用户还未登录成功，则<br>终止当前连接，确保了安全性。|
|（可选）配置<br>SSH验证重试<br>次数|执行命令**ssh server**<br>**authentication-retries** times|配置SSH验证重试次数用来设置<br>SSH用户请求连接的认证重试次<br>数，防止非法用户登录。|
|（可选）使能<br>SSH服务器上<br>的客户端IP地<br>址锁定功能|执行命令**undo ssh server ip-**<br>**block disable**|如果SSH服务器上的客户端IP地<br>址锁定功能处于使能状态，则被<br>锁定的客户端IP地址不能被认证<br>通过，同时会在**display ssh**<br>**server ip-block list**命令回显中<br>显示被锁定的客户端IP地址。<br>如果SSH服务器上的客户端IP地<br>址锁定功能去使能，则**display**<br>**ssh server ip-block list**命令回<br>显中会把先前锁定的客户端IP地<br>址记录删除，新的认证失败的客<br>户端IP地址也不会被记录显示。|
|（可选）使能<br>兼容低版本<br>SSH协议|执行命令**ssh server**<br>**compatible-ssh1x enable**<br>如果需要允许SSH1.5的客户端登<br>录，则执行**ssh server**<br>**compatible-ssh1x enable**，使<br>能兼容低版本功能。<br>说明<br>如果SSH协议使能兼容低版本功<br>能，系统会提示存在安全风险。|SSH协议有SSH1.X（SSH2.0之前<br>的版本）和SSH2.0版本。<br>SSH2.0协议相比SSH1.X协议来<br>说，在结构上做了扩展，可以支<br>持更多的认证方法和密钥交换方<br>法，同时提高了服务能力（如<br>SFTP）。|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 94


HUAWEI NetEngine40E
配置指南 1 基础配置








|服务器参数|命令|说明|
|---|---|---|
|（可选）配置<br>SSH服务器侦<br>听端口号|执行命令**ssh server port** port-<br>number<br>如果配置了新的侦听端口号，<br>SSH服务器端先断开当前已经建<br>立的所有STelnet和SFTP连接，<br>然后使用新的端口号开始侦听。|如果使用标准的侦听端口号，可<br>能会有攻击者不断访问此端口，<br>导致带宽和服务器性能的下降，<br>造成其他正常用户无法访问。所<br>以可以重新配置SSH服务器的侦<br>听端口号，攻击者不知道SSH侦<br>听端口号的更改，有效防止了攻<br>击者对SSH服务标准端口的访<br>问，确保了安全性。|
|（可选）配置<br>SSH服务器访<br>问控制列表|执行命令**ssh** [**ipv6** ]**server acl**<br>{ acl-number | acl-name }|该配置用来设置SSH服务器允许<br>指定客户端访问，有效防止非法<br>用户登录SSH服务器，确保了安<br>全性。|
|（可选）使能<br>SSH服务器上<br>的keepalive特<br>性|执行命令**ssh server keepalive**<br>**enable**|使能keepalive特性后，服务器<br>收到客户端发来的保活报文后，<br>会回应一个保活响应报文。用来<br>检测对端是否可达，以便尽早发<br>现网络故障。|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 95


HUAWEI NetEngine40E
配置指南 1 基础配置








|服务器参数|命令|说明|
|---|---|---|
|指定SSH服务<br>器的源接口或<br>者源地址|●执行命令**ssh server-source**<br>**-i** { interface-type interface-<br>number | interface-name }<br>●执行命令**ssh server-source**<br>**-a** ip-address [**-vpn-**<br>**instance** vpnName ]<br>●执行命令**ssh server-source**<br>**all-interface**<br>●执行命令**ssh ipv6 server-**<br>**source** **-a** ipv6-address [**-**<br>**vpn-instance** vpn-instance-<br>name ]<br>●执行命令**ssh ipv6 server-**<br>**source** **all-interface**<br>●执行命令**ssh server-source**<br>**physic-isolate** **-i**<br>{ interface-type interface-<br>number | interface-name }**-**<br>**a** ip-address<br>●执行命令**ssh ipv6 server-**<br>**source** **physic-isolate** **-i**<br>{ interface-type interface-<br>number | interface-name }**-**<br>**a** ipv6-address|●成功指定SSH服务器的源接口<br>后，系统只允许SSH用户通过<br>指定的源接口登录服务器，<br>通过其他接口登录的SSH用户<br>都将被拒绝。<br>●成功指定SSH服务器的源接口<br>为设备上所有接口。SSH用户<br>可通过设备上配置了IPv4地<br>址的物理接口、已创建并配<br>置IPv4地址的逻辑接口登<br>录。<br>说明<br>配置**ssh server-source** **all-**<br>**interface**命令后，将不会指定<br>SSH服务器的源接口，用户可从<br>所有有效接口登录，增加系统<br>安全风险，建议用户取消配置<br>该命令。<br>●成功指定SSH服务器的源IPv6<br>地址后，系统只允许SSH用户<br>通过指定的源地址登录服务<br>器，通过其他IPv6地址登录<br>的SSH用户都将被拒绝。<br>●成功指定SSH服务器的源接口<br>为设备上所有IPv6接口。SSH<br>用户可通过设备上配置了<br>IPv6地址的物理接口、已创<br>建并配置IPv6地址的逻辑接<br>口登录。<br>说明<br>配置**ssh ipv6 server-source**<br>**all-interface**命令后，将不会指<br>定SSH服务器的源IPv6接口，用<br>户可从所有有效IPv6接口登录，<br>增加系统安全风险，建议用户<br>取消配置该命令。<br>●成功指定SSH服务器的源接<br>口，并设置SSH服务器的接口<br>隔离属性。<br>●成功指定SSH服务器的源IPv6<br>接口，并设置SSH服务器的接<br>口隔离属性。<br>说明<br>成功设置接口隔离属性后，只<br>能通过配置的物理口连接设<br>备，即报文只能从配置的物理<br>口上送，通过其他接口上送的<br>报文会被丢弃。|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 96


HUAWEI NetEngine40E
配置指南 1 基础配置








|服务器参数|命令|说明|
|---|---|---|
|（可选）配置<br>SSH服务器认<br>证方式的伪列<br>表模式|执行命令**ssh server**<br>**authentication-method**<br>**bogus-list disable**|去使能SSH服务器认证方式的伪<br>列表模式，可减少某些客户端使<br>用password认证方式登录服务<br>器时的认证时长。|



步骤 **3** 执行命令 **commit** ，提交配置。


**----**
结束
