

在本示例中，通过配置 Console 用户界面的物理属性、终端属性、用户级别、验证方式
和验证密码，实现通过 Console 口使用 Password 方式登录设备。

##### 组网需求


在初始化空配置设备或本地维护设备时，用户需要通过 Console 用户界面登录并进行配
置。设备管理员可以根据使用需求或对设备安全性的考虑，可配置 Console 用户界面的
相关属性。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 49


HUAWEI NetEngine40E
配置指南 1 基础配置

##### 配置注意事项 配置思路


采用如下的思路配置 Console 用户界面：


1. 配置 Console 用户界面的物理属性。


2. 配置 Console 用户界面的终端属性。


3. 配置 Console 用户界面的用户级别。


4. 配置 Console 用户界面的验证方式和验证密码。


说明


以上数据除用户名、密码外设备均有缺省值，一般不需要单独配置。

##### 数据准备


为完成此配置举例，需准备如下的数据：


       - Console 用户界面的传输速率为 4800bit/s 。


       - Console 用户界面的流控方式为 None 。


       - Console 用户界面的校验位为 even 。


       - Console 用户界面的停止位为 2 。


       - Console 用户界面的数据位为 6 。


       - Console 用户界面断开连接的时间为 30 。


       - Console 用户界面的终端屏幕每屏显示的行数为 30 。


       - Console 用户界面的历史命令缓冲区大小为 20 。


       - Console 用户界面的用户级别为 15 。


       - Console 用户界面的用户验证方式为 aaa 。

##### 操作步骤


步骤 **1** 配置 Console 用户界面的物理属性


<HUAWEI> **system-view**

[ ~ HUAWEI] **user-interface console 0**

[ ~ HUAWEI-ui-console0] **speed 4800**

[*HUAWEI-ui-console0] **flow-control none**

[*HUAWEI-ui-console0] **parity even**

[*HUAWEI-ui-console0] **stopbits 2**

[*HUAWEI-ui-console0] **databits 6**

[*HUAWEI-ui-console0] **commit**


步骤 **2** 配置 Console 用户界面的终端属性


[ ~ HUAWEI-ui-console0] **shell**

[*HUAWEI-ui-console0] **idle-timeout 30**

[*HUAWEI-ui-console0] **screen-length 30**

[*HUAWEI-ui-console0] **history-command max-size 20**

[*HUAWEI-ui-console0] **commit**


步骤 **3** 配置 Console 用户界面的用户级别


[ ~ HUAWEI-ui-console0] **user privilege level 3**

[*HUAWEI-ui-console0] **commit**


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 50


HUAWEI NetEngine40E
配置指南 1 基础配置


步骤 **4** 配置 Console 用户界面的用户验证方式为 AAA 验证


[ ~ HUAWEI-ui-console0] **authentication-mode aaa**

[ ~ HUAWEI-ui-console0] **quit**

[*HUAWEI] **aaa**

[*HUAWEI-aaa] **local-user admin1234 password irreversible-cipher YsHsjx_202206**

[*HUAWEI-aaa] **local-user admin1234 level 3**

[*HUAWEI-aaa] **local-user admin1234 service-type terminal**

[*HUAWEI-aaa] **commit**


Console 用户界面配置完成后，用户可以通过 Console 口使用 aaa 方式登录设备，登录
设备的具体过程请参见 配置用户通过 **Console** 口登录系统 。


步骤 **5** 验证配置结果


上述配置完成后，执行命令 **display user-interface** ，可以查看 Console0 的状态。


<HUAWEI> **display user-interface 0**
Idx Type  Tx/Rx Modem Privi ActualPrivi Auth Int
+0  CON 0 4800 -   3   3      A  16

+ : Current UI is active.
F : Current UI is active and work in async mode.
Idx : Absolute index of UIs.
Type : Type and relative index of UIs.
Privi : The privilege of UIs.
ActualPrivi : The actual privilege of user-interface.
Auth : The authentication mode of UIs.

A : Authenticate use AAA.
P : Authenticate use current UI's password.
Int : The physical location of UIs.


**----**
结束

##### 配置文件


#

sysname HUAWEI
#

aaa
local-user admin1234 password irreversible-cipher $1d$g8wLJ`LjL!$CyE(V{3qg5DdU:PM[6=6O
$UF-.fQ,Q}>^)OBzgoU$
local-user admin1234 service-type terminal
local-user admin1234 level 3

#

user-interface con 0

authentication-mode aaa
history-command max-size 20
idle-timeout 30 0

databits 6
parity even
stopbits 2
speed 4800
screen-length 30
#

return
