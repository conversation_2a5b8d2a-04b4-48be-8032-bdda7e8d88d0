

Telnet 是客户          - 服务器应用程序，他使用户能够登录到远程设备上，从而使用户能够对
远程设备进行管理与维护。

##### 应用环境


网络中有大量设备需要管理与维护，用户不可能为每台设备连接用户终端，特别是终
端与需要管理的设备之间无可达路由时，用户可以使用 Telnet 方式从当前设备登录到网
络上另一台设备，从而实现对远程设备的管理与维护。


如 图 **1-62** 所示，用户可以在 PC 上通过 Telnet 登录到 Telnet client ，但是由于 PC 与 Telnet
server 之间无可达路由，用户无法远程管理 Telnet server ，此时用户可以在 Telnet
client 上通过 Telnet 方式登录到 Telnet server ，实现对 Telnet server 的远程管理。


图 **1-62** 当前设备访问其他设备示意图

##### 前置任务


在通过 Telnet 登录其他设备之前，需要完成以下任务：


       - 成功 配置用户通过 **Telnet** 登录系统 。


       - Telnet 客户端与 Telnet 服务器之间有可达路由。
