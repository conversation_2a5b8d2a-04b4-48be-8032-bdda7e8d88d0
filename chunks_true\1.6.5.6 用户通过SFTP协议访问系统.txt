

完成以上配置后，用户可以使用 SFTP 方式从终端安全地访问设备，从而实现对设备上
文件的管理。

##### 背景信息


从终端通过 SFTP 访问设备，可以选择使用第三方软件。此处以使用第三方软件
OpenSSH 和 Windows 命令行提示符为例进行配置。


在 PC 上安装 OpenSSH 软件后，请在 PC 上进行以下配置。


说明


OpenSSH 软件的安装请参考该软件的安装说明。


使用 OpenSSH 软件从终端通过 SFTP 登录到系统时，需要使用 OpenSSH 的命令，命令的使用可以
参见该软件的帮助文档。

##### 操作步骤


步骤 **1** 进入 Windows 的命令行提示符。


步骤 **2** 执行 OpenSSH 命令，通过 SFTP 方式访问设备。


当出现 SFTP 客户端视图的命令行提示符，如 sftp> ，此时用户进入了 SFTP 服务器的工作
目录。


**----**
结束
