

如 图 **1-80** 所示，某网络中新增两台空配置设备 RouterA 和 RouterB ，连接到现网设备
RouterC 上。 RouterC 作为 RouterA 和 RouterB 的出口网关。 RouterC 与 DHCP 服务器、
文件服务器之间路由可达。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 358


HUAWEI NetEngine40E
配置指南 1 基础配置


用户希望空配置的 RouterA 和 RouterB 在上电启动后能够自动加载相应的系统软件和配
置文件，完成开局部署，以降低现场配置的人力、时间成本。


图 **1-80** 配置 ZTP 组网图


说明


本例中 interface1 ， interface2 分别表示 GE1/0/1 ， GE1/0/2 。

##### 配置思路


采用如下的思路配置：


1. 配置文件服务器，将文件服务器作为 SFTP Server ，存放中间文件及系统软件、配
置文件。


说明


使用 FTP 协议存在安全风险，建议使用 SFTP 进行文件传输。


2. 编辑 Python 、 ini 或者 cfg 格式的中间文件，使不同的设备能够通过中间文件获取相
应的系统软件和配置文件。


3. 配置 DHCP 服务器和中继，使空配置设备可以获得 DHCP 服务器发送的 DHCP 信
息。


4. 将 RouterA 和 RouterB 上电，启动 ZTP 流程。

##### 操作步骤


步骤 **1** 配置文件服务器


       - 使用设备作为文件服务器，配置可参考 *********** 通过 **SFTP** 进行文件操作示例 。


       - 使用第三方服务器作为文件服务器，配置的具体方法请参见第三方服务器的操作
指导，设置 PC 上 SFTP 的工作目录为 D:\ztp 。文件服务器配置完成后，将设备需要
加载的系统软件和配置文件放在 D:\ztp 目录下。


步骤 **2** 编辑中间文件


请按照 *********** 编辑中间文件 中的要求编辑中间文件，这里以 cfg 为例，文件名称为
**ztp_script.cfg** ，内容请参见 cfg 格式的中间文件。


中间文件编辑完成后存放至文件服务器的 D:\ztp 目录下。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 359


HUAWEI NetEngine40E
配置指南 1 基础配置


步骤 **3** 配置 DHCP 服务器


# 配置 DHCP 服务器分配给客户端的 IP 地址池，并参照 表 **1-45** 配置 DHCP 服务器 Option
选项的值。具体的配置方法请参见相应的产品资料对应 DHCP 服务器配置相关章节。


表 **1-45** DHCP 服务器 Option 选项取值












|Option编<br>号|含义|取值|
|---|---|---|
|1|IP地址的子网掩码|*************|
|3|DHCP客户端的出口<br>网关|********|
|67|文件服务器地址及中<br>间文件名|sftp://client001:YsHsjx_202206@********/<br>ztp_script.cfg|



# 设置 DHCP 服务器的 IP 地址及网关，要求能够与 RouterA 、 RouterB 的网关之间路由
可达。


步骤 **4** 配置 DHCP 中继


# 配置 RouterC 的 DHCP 中继功能，同时配置 RouterC 与 RouterA 、 RouterB 相连的接口
IP 地址为 ******** ，作为 RouterA 、 RouterB 的缺省网关。


<HUAWEI> **system-view**

[ ~ HUAWEI] **sysname RouterC**

[*HUAWEI] **commit**

[ ~ RouterC] **interface GigabitEthernet 1/0/1**

[ ~ RouterC-GigabitEthernet1/0/1] **ip address ******** ***************

[*RouterC-GigabitEthernet1/0/1] **undo shutdown**

[*RouterC-GigabitEthernet1/0/1] **commit**

[ ~ RouterC-GigabitEthernet1/0/1] **quit**

[ ~ RouterC] **interface GigabitEthernet 1/0/2**

[ ~ RouterC-GigabitEthernet1/0/2] **ip address ******** ***************

[*RouterC-GigabitEthernet1/0/2] **dhcp select relay**

[*RouterC-GigabitEthernet1/0/2] **ip relay address **********

[*RouterC-GigabitEthernet1/0/2] **undo shutdown**

[*RouterC-GigabitEthernet1/0/2] **commit**

[ ~ RouterC-GigabitEthernet1/0/2] **quit**


步骤 **5** 将 RouterA 、 RouterB 上电，启动 ZTP 流程


步骤 **6** 验证配置结果


# 设备启动完成后，可以登录到设备后执行命令 **display startup** 查看设备当前的系统
软件、配置文件是否与预期的一致。以 RouterA 为例。
<RouterA> **display startup**
MainBoard:
Configured startup system software:    cfcard:/ **V800R023C10SPC500B140_0424_new.cc**
Startup system software:          cfcard:/ **V800R023C10SPC500B140_0424_new.cc**
Next startup system software:       cfcard:/ **V800R023C10SPC500B140_0424_new.cc**
Startup saved-configuration file:     cfcard:/ **vrpcfg.cfg**
Next startup saved-configuration file:   cfcard:/ **vrpcfg.cfg**
Startup paf file:             default
Next startup paf file:           default
Startup patch package:           cfcard:/ **NE40EV800R023C10SPC500.PAT**
Next startup patch package:        cfcard:/ **NE40EV800R023C10SPC500.PAT**


**----**
结束


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 360


HUAWEI NetEngine40E
配置指南 1 基础配置

##### 配置文件


**ztp_script.cfg** 文件


说明


下列文件中的 SHA256 校验码只是举例，配置时以实际计算的值为准。

#sha256sum=" **ffcd63f5e31f0891a0349686969969c1ee429dedeaf7726ed304f2d08ce1bc7** "
fileserver= **sftp://username:password@hostname:port/path/** ;
mac= **00e0-fc12-3456** ;esn= **2102351931P0C3000154** ;devicetype= **DEFAULT** ;systemversion= **V800R023C10SPC500** ;boot_python_file= **V800R023C10SPC500.py** ;systemsoftware= **V800R023C10SPC500.cc** ;system-config= **V800R023C10SPC500.cfg** ;systempat= **V800R023C10SPC500SPH001.PAT** ;


**vrpcfg.cfg** 文件


说明


下列文件以配置接口 IP 和路由为例，实际使用中根据需要进行修改。


#

sysname HUAWEI
#
ip vpn-instance __LOCAL_OAM_VPN__
ipv4-family
#
interface Ethernet0/0/0
undo shutdown
ip binding vpn-instance __LOCAL_OAM_VPN__
ip address ************** *************
#
ip route-static vpn-instance __LOCAL_OAM_VPN__ 0.0.0.0 0.0.0.0 **************
#


**RouterC** 的配置文件


#

sysname RouterC
#
interface GigabitEthernet1/0/1
undo shutdown
ip address ******** *************
#
interface GigabitEthernet1/0/2
undo shutdown
ip address ******** *************
dhcp select relay
ip relay address ********
#

return


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 361

