

通过 Console 口从 PC 登录设备，实现对第一次上电的设备进行配置和管理。

##### 背景信息


用户需要根据设备上的 Console 口的物理属性（包括传输速率、数据位、校验位、停止
位、流控方式），配置终端登录时的相关参数。由于是首次登录设备，终端属性的各
参数值均采用缺省值。


客户端已安装 PuTTY.exe 软件。

##### 操作步骤


步骤 **1** 打开 PuTTY.exe 程序，出现如 图 **1-1** 所示客户端配置界面。选择 Serial 选项。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 8


HUAWEI NetEngine40E
配置指南 1 基础配置


图 **1-1** 客户端配置界面


步骤 **2** 选择客户端配置界面左侧目录树（ Category ）中的连接协议 Serial ，出现如 图 **1-2** 所示
界面。设置端口通信参数，与设备的缺省值保持一致。（下图仅为示例，配置时以设
备的实际缺省为准）


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 9


HUAWEI NetEngine40E
配置指南 1 基础配置


图 **1-2** 设置连接端口示意图


步骤 **3** 单击“ Open ”，如果连接正常则会提示用户配置验证密码，系统会自动保存此密码配
置，如 图 **1-3** 所示。


图 **1-3** 登录界面


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 10


HUAWEI NetEngine40E
配置指南 1 基础配置


密码设置成功后，系统将出现用户视图的命令行提示符，如 <HUAWEI> ，至此用户进
入了用户视图配置环境。


此时用户可以键入命令，配置设备或查看设备运行状态，需要帮助可以随时键入
“ ? ”。


说明


设置的密码必须满足以下要求：


          - 密码采取交互式输入，系统不回显输入的密码。


          - 输入的密码为字符串形式，区分大小写，长度范围是 8 ～ 16 。输入的密码至少包含两种类型
字符，包括大写字母、小写字母、数字及特殊字符。


          - 特殊字符不包括“？”和空格。但是，当输入的密码两端使用双引号时，可在密码中间输入
空格。


–
如果使用双引号设置带空格密码，双引号之间不能再使用双引号。


–
如果使用双引号没有设置带空格密码，双引号之间可以再使用双引号。


例如， "Aa 123"45"" 为不合法密码， "Aa123"45"" 为合法密码。


配置文件中将以密文形式体现设置的密码。


**----**
结束
