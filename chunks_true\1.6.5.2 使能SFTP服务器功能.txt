

在通过 SFTP 方式访问设备之前，需要首先使能设备的 SFTP 服务器功能。

##### 背景信息


请在作为 SSH 服务器的设备上进行如下的配置。


说明


为了保证更好的安全性，建议不要使用小于 3072 位的 RSA 算法，建议您使用更安全的
RSA_SHA2_256 、 RSA_SHA2_512 认证算法。

##### 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** （可选）执行命令 **ssh server publickey** { **dsa** | **ecc** | **rsa** | **sm2** | **x509v3-ssh-rsa** |
**rsa-sha2-256** | **rsa-sha2-512** **| x509v3-rsa2048-sha256** | **x509v3-ecdsa-sha2** } [*] ，
配置 SSH 服务器允许使用的公钥加密算法。


步骤 **3** （可选）创建密钥对的最大数。请根据使用的密钥实际情况选择如下操作之一。


       - 执行命令 **rsa key-pair maximum** max-keys ，配置允许创建的 RSA 密钥对的最大
数。


       - 执行命令 **dsa key-pair maximum** max-keys ，配置允许创建的 DSA 密钥对的最大
数。


       - 执行命令 **ecc key-pair maximum** max-keys ，配置允许创建的 ECC 密钥对的最大
数。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 167


HUAWEI NetEngine40E
配置指南 1 基础配置


步骤 **4** （可选）创建 SSH 服务器密钥对。以下两种方式选择其一。


       - 方式一


–
如果用户对系统的安全性要求不是很高，可以执行命令 **rsa local-key-pair**
**create** 创建本地 RSA 密钥对，或执行命令 **dsa local-key-pair create** 创建本地
DSA 密钥对。


–
如果用户对系统的安全性要求比较高，可以执行命令 **ecc local-key-pair**
**create** ，创建本地 ECC 密钥对。


       - 方式二


–
如果用户对系统的安全性要求不是很高，可以执行命令 **rsa key-pair label**
label-name [ **modulus** modulus-bits ] 创建本地 RSA 密钥对，或执行命令 **dsa**
**key-pair label** label-name [ **modulus** modulus-bits ] ，创建本地 DSA 密钥
对。


–
如果用户对系统的安全性要求比较高，可以执行命令 **ecc key-pair label**
label-name [ **modulus** modulus-bits ] ，创建本地 ECC 密钥对，或执行命令
**sm2 key-pair label** label-name [ **modulus** modulus-bits ] ，创建本地 SM2
密钥对。


密钥生成后，执行命令 **ssh server assign** { **rsa-host-key** | **dsa-host-key** | **ecc-**
**host-key** | **sm2-host-key** } key-name ，给 SSH 服务器分配密钥对。

如果配置认证方式为 x509v3-ssh-rsa ，需执行命令 **ssh server assign** **pki** pkiname ，配置 SSH 服务器的 PKI 证书。


步骤 **5** 根据 SFTP 服务的类型，选择执行如下步骤之一：


       - 执行命令 **sftp server enable** ，使能 SFTP 服务。


       - 执行命令 **sftp ipv4 server enable** ，使能 IPv4 SFTP 服务。


       - 执行命令 **sftp ipv6 server enable** ，使能 IPv6 SFTP 服务。


说明


SSH 协议的标准侦听端口号为 22 。如果此端口 TCP 侦听功能没有打开，执行此命令，会打开此端
口 IPv4 和 IPv6 的 TCP 侦听功能。


步骤 **6** （可选）执行命令 **ssh server cipher** { **des_cbc** | **3des_cbc** | **aes128_cbc** |
**aes192_cbc** | **aes256_cbc** | **aes128_ctr** | **aes192_ctr** | **aes256_ctr** | **arcfour128** |
**arcfour256** | **aes128_gcm** | **aes256_gcm** | **blowfish_cbc** | **sm4_cbc** | **sm4_gcm** } [*] ，
配置 SSH 服务器上的加密算法。


说明


为保证更好的安全性，建议使用以下安全性更高的加密算法： aes128_ctr 、 aes256_ctr 、
aes192_ctr 、 aes128_gcm 、 aes256_gcm 。


命令中的参数 **des_cbc** 、 **3des_cbc** 、 **aes128_cbc** 、 **aes256_cbc** 、 **arcfour128** 、 **arcfour256** 、
**blowfish_cbc** 、 **aes192_cbc** 和 **sm4_cbc** 算法为弱安全算法不建议使用，如需配置，需执行 **undo**
**crypto weak-algorithm disable** 命令使能弱安全算法功能。为避免安全风险，建议改用更安全
的算法。


步骤 **7** （可选）执行命令 **ssh server hmac** { **md5** | **md5_96** | **sha1** | **sha1_96** | **sha2_256** |
**sha2_256_96** | **sha2_512** | **sm3** } [*] ，配置 SSH 服务器端 HMAC 认证算法。


说明


为保证更好的安全性，建议使用以下安全性更高的 HMAC 算法： sha2_256 、 sha2_512 。


命令中的参数 **md5** 、 **md5_96** 、 **sha1** 、 **sha1_96** 和 **sha2_256_96** 算法为弱安全算法不建议使用，
如需配置，需执行 **undo crypto weak-algorithm disable** 命令使能弱安全算法功能。为避免安
全风险，建议改用更安全的算法。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 168


HUAWEI NetEngine40E
配置指南 1 基础配置


步骤 **8** （可选）执行命令 **ssh server key-exchange** { **dh_group14_sha1** | **dh_group1_sha1**
| **dh_group_exchange_sha1** | **dh_group_exchange_sha256** | **dh_group16_sha512**
| **ecdh_sha2_nistp256** | **ecdh_sha2_nistp384** | **ecdh_sha2_nistp521** | **sm2_kep** |
**curve25519_sha256** } [*] ，配置 SSH 服务器上的密钥交换算法列表。


说明


为保证更好的安全性，建议使用以下安全性更高的密钥交换算法： curve25519_sha256 。


命令中的参数 **dh_group_exchange_sha1** 、 **dh_group1_sha1** 和 **dh_group14_sha1** 算法为弱安
全算法不建议使用，如需配置，需执行 **undo crypto weak-algorithm disable** 命令使能弱安全
算法功能。为避免安全风险，建议改用更安全的算法。


步骤 **9** （可选）执行命令 **ssh server dh-exchange min-len** min-len ，配置与 SSH 客户端进
行 Diffie-hellman-group-exchange 密钥交换时，支持的最小密钥长度。


说明


如果 SSH 客户端支持大于 1024bits 的 Diffie-hellman-group-exchange 密钥交换算法时，建议执行
**ssh server dh-exchange min-len** 命令配置最小密钥长度为 3072bits ，以提高安全性。


步骤 **10** 执行命令 **commit** ，提交配置。


**----**
结束

##### 检查配置结果


       - 执行如下命令查看本地生成的密钥对信息：


– 执行命令 **display rsa key-pair** [ **brief** | **label** label-name ] ，查看本地 RSA
密钥对信息。


– 执行命令 **display dsa key-pair** [ **brief** | **label** label-name ] ，查看本地 DSA
密钥对信息。


– 执行命令 **display ecc key-pair** [ **brief** | **label** label-name ] ，查看本地 ECC
密钥对信息。


– 执行命令 **display sm2 key-pair** [ **brief** | **label** label-name ] ，查看本地 SM2
密钥对信息。


       - 密钥对生成后，可以如下命令查看本地密钥对中的公钥信息：


– 执行命令 **display rsa local-key-pair public** ，查看本地 RSA 密钥对中的公钥
信息。


– 执行命令 **display dsa local-key-pair public** ，查看本地 DSA 密钥对中的公钥
信息。


– 执行命令 **display ecc local-key-pair public** ，查看本地 ECC 密钥对中的公钥
信息。
