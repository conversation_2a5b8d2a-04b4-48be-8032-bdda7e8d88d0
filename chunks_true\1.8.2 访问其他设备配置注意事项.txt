
##### 特性限制


表 **1-37** 本特性的使用限制






|特性限制|系列|涉及产品|
|---|---|---|
|SSL加载证书文件（身份证书、CA、吊销列表）存在文<br>件大小限制，文件大小不能超过（包含）50K。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK|
|配置使用SSL证书时，单个SSL策略最多加载1本证书、<br>4本信任证书、2个证书吊销列表；<br>配置使用PKI域下证书时，单个SSL策略最多使用1本证<br>书、64本信任证书、64个证书吊销列表。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 234


HUAWEI NetEngine40E
配置指南 1 基础配置












|特性限制|系列|涉及产品|
|---|---|---|
|出于安全性考虑，不建议使用该特性提供的弱安全算法<br>或弱安全协议。<br>如果需要使用弱安全算法，需执行undo crypto weak-<br>algorithm disable命令使能弱安全算法功能后才能使<br>用。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK|
|创建SSL策略，DH模数默认值为3072，可配置2048、<br>3072、4096；签名算法默认开启ed25519，ed448，<br>rsa-pss-pss-sha256，rsa-pss-pss-sha384，rsa-pss-<br>pss-sha512，rsa-pss-rsae-sha256，rsa-pss-rsae-<br>sha384，rsa-pss-rsae-sha512，可单独配置，增加安<br>全性。<br>创建SSL策略后，如果是签名算法不匹配或者DH模数长<br>度过长导致的SSL握手失败，可以通过dife-hellman<br>modulus命令调整DH模数长度；通过signature<br>algorithm-list命令调整签名算法。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK|
|HTTP请求报文的最大长度是10M - 1字节。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK|
|HTTP请求行的最大长度为8192字节。<br>HTTP请求行（HTTP request line）的最大长度为8192<br>bytes;|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 235


HUAWEI NetEngine40E
配置指南 1 基础配置

|特性限制|系列|涉及产品|
|---|---|---|
|HTTP请求报文头总大小为4096字节。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK|

