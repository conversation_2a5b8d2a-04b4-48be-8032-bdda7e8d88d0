

正则表达式描述了一种字符串匹配的模式，由普通字符（例如字符 a 到 z ）和特殊字符
（或称“元字符”）组成。正则表达式作为一个模板，将某个字符模式与所搜索的字
符串进行匹配。


正则表达式一般具有以下功能：


       - 检查字符串中符合某个规则的子字符串，并可以获取该子字符串。


       - 根据匹配规则对字符串进行替换操作。


正则表达式由普通字符和特殊字符组成。


       - 普通字符


普通字符匹配的对象是普通字符本身。包括所有的大写和小写字母、数字、标点
符号以及一些特殊符号。例如： a 匹配 abc 中的 a ， @ 匹配 <EMAIL> 中的 @ 。


       - 特殊字符


特殊字符配合普通字符匹配复杂或特殊的字符串组合。如， ^10 匹配 10.10.10.1 ，
不匹配 2.2.2.2 。


表 **1-4** 是对特殊字符及其语法意义的使用描述。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 20


HUAWEI NetEngine40E
配置指南 1 基础配置


表 **1-4** 特殊字符及其语法意义描述













|特殊字符|功能|举例|
|---|---|---|
|\|转义字符。将下一个字符（特殊<br>字符或者普通字符）标记为普通<br>字符。|“\*”匹配“*”|
|^|匹配行首的位置。|“^10”匹配“10.10.10.1”，不<br>匹配“2.2.2.2”|
|$|匹配行尾的位置。|“1$”匹配“10.10.10.1”，不<br>匹配“10.10.10.2”|
|*|匹配前面的子正则表达式零次或<br>多次。|“10*”可以匹配“1”、<br>“10”、“100”、“1000”、<br>……<br>“(10)*”可以匹配空、<br>“10”、“1010”、<br>“101010”、……|
|+|匹配前面的子正则表达式一次或<br>多次。|“10+”可以匹配“10”、<br>“100”、“1000”、……<br>“(10)+”可以匹配“10”、<br>“1010”、“101010”、……|
|?|匹配前面的子正则表达式零次或<br>一次。<br>说明<br>当前，在华为公司数据通信设备上<br>通过命令行运用正则表达式输入？<br>时，系统显示为命令行帮助功能。<br>华为公司数据通信设备不支持正则<br>表达式输入？特殊字符。|“10?”可以匹配“1”或者<br>“10”<br>“(10)?”可以匹配空或者<br>“10”|
|.|匹配任意单个字符。|“a.b”匹配任何一个以“a”开<br>头，以“b”结尾含有三个字符<br>的字符串<br>“0.0”可以匹配“0x0”、<br>“020”、……<br>“.oo.”可以匹配“book”、<br>“look”、“tool”、……|


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 21


HUAWEI NetEngine40E
配置指南 1 基础配置












|特殊字符|功能|举例|
|---|---|---|
|()|一对圆括号内的正则表达式作为<br>一个子正则表达式，匹配子表达<br>式并获取这一匹配。<br>如果圆括号中内容为空，则等价<br>于空串。<br>如果模式串只有()，则可匹配任<br>意字符串。<br>如果模式串中的右括号没有匹配<br>的左括号，则右括号就作为普通<br>字符。<br>如果模式串中的左括号没有匹配<br>的右括号，则为非法模式串。|“100(200)+”可以匹配<br>“100200”、<br>“100200200”、……<br>“(ab)”匹配“abcab”<br>“()”匹配任意字符串<br>“a()b”匹配“12ab12”<br>“a)b”匹配“za)bc”<br>“a(b”为非法模式串|
|x|y|匹配x或y。|“100|200”匹配“100”或者<br>“200”<br>“1(2|3)4”匹配“124”或者<br>“134”，而不匹配“1234”、<br>“14”、“1224”、“1334”|
|[xyz]|匹配正则表达式中的任意一个字<br>符。不可同时匹配多个字符，也<br>不可匹配同一个字符多次。|“[123]”匹配“255”中的<br>“2”<br>“[abc]”匹配字符“a”、<br>“b”、“c”|
|[^xyz]|匹配字符串中非“x”、“y”、<br>“z”的字符。只要字符串中有<br>非“x”、“y”、“z”的字<br>符，就能匹配到。|“[^123]”匹配除“1”、<br>“2”、“3”之外的任何字符<br>“[^abc]”匹配除“a”、<br>“b”、“c”之外的任何字符|
|[a-z]|匹配正则表达式指定范围内的任<br>意一个字符。不可同时匹配多个<br>字符，也不可匹配同一个字符多<br>次。|“[0-9]”匹配指定范围内的任意<br>数字<br>“[a-z]”匹配指定范围内的任意<br>字母<br>“[z-a]”为非法模式串|
|[^a-d]|匹配字符串中除“a”、“b”、<br>“c”、“d”以外的其他字符。<br>只要字符串中有a～d范围外的字<br>符，就能匹配到。|“[^0-9]”匹配所有非数字字符<br>“[^a-z]”匹配除字母以外的其<br>他任意字符<br>“[^z-a]”为非法模式串|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 22


HUAWEI NetEngine40E
配置指南 1 基础配置



|特殊字符|功能|举例|
|---|---|---|
|{x,y}|匹配前面的子正则表达式x~y<br>次。此处的x、y应小于1000，否<br>则编译报错。x和y数值越大，模<br>式串编译的速度越慢。<br>如果仅有单个大括号（左或<br>右），则单个大括号作为普通字<br>符。<br>如果大括号中仅有一个数字，则<br>该数字表示前面的子正则表达式<br>精确匹配的次数。<br>如果左右大括号中间的逗号左边<br>没有数字，右边有数字，则左右<br>大括号及其包含的字符都作为普<br>通字符。<br>如果左右大括号中间的逗号左边<br>有数字，右边没有数字，则该数<br>字表示前面的子正则表达式匹配<br>的最少次数，没有上限。|“a{3,5}”匹配“aaa”、<br>“aaaa”、“aaaaa”<br>“a{3”匹配“za{3bc”<br>“a3}”匹配“za3}bc”<br>“ab{3}”匹配“abbbc”<br>“(ab){3}”匹配“abababc”<br>“a{,3}bc”匹配“za{,3}bc”<br>“a{3,}bc”匹配"aaabc”、<br>"aaaaabc”、……|


说明




##### 字符的使用




    - 除非特别说明，上表中涉及到的字符指的是可以打印的字符，包括字母、数字、空格及
特殊符号等。


- 特殊字符的退化


某些特殊字符如果处在如下的正则表达式的特殊位置时，会引起退化，成为普通
字符。


–
特殊字符处在转义符号‘ \ ’之后，则发生转义，变为匹配该字符本身。


– 特殊字符“   - ”、“ + ”、“ ? ”，处于正则表达式的第一个字符位置。例如：
+45 匹配 +45 ， abc(*def) 匹配 abc*def 。


– 特殊字符“ ^ ”，不在正则表达式的第一个字符位置。例如： abc^ 匹配
abc^ 。


–
特殊字符“ $ ”，不在正则表达式的最后一个字符位置。例如： 12$2 匹配
12$2 。


–
右括号“ ) ”或者“ ] ”没有对应的左括号“ ( ”或“ [ ”。例： abc) 匹配 abc) ，
0-9] 匹配 0-9] 。


说明


除非特别说明，以上正则表达式包括括号“（）”内包含的子正则表达式。


- 普通字符与特殊字符的组合使用


实际应用中，往往不是一个普通字符加上一个特殊字符配合使用，而是由多个普
通字符和特殊字符组合，匹配某些特征的字符串。



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 23


HUAWEI NetEngine40E
配置指南 1 基础配置

##### 正则表达式的过滤条件


NE40E 支持通过在命令中指定过滤条件来应用正则表达式，目前所有的 display 命令均
支持正则表达式。按过滤条件进行查询时，显示内容的第一行信息中，以包含该字符
串的整条信息作为起始，而非过滤字符串作为起始。


系统支持使用 **| count** 显示使用过滤条件后输出的结果的行数，使用 **| no-more** 表示显
示输出不分屏，这些过滤均既可以继续与下面的三种过滤方式配合使用，也可以单独
使用。


系统也支持使用 **| ignore-case** 表示过滤字符串时不区分大小写，使用 **| section** 显示使
用过滤条件后输出的结果段信息，此种方式必须与下面的三种过滤方式配合使用，不
可以单独使用。


在支持正则表达式的命令中，有三种过滤方式可供选择：


       - **| begin** regular-expression


输出以匹配指定正则表达式的行开始的所有行。即，过滤掉所有待输出字符串，
直到出现指定的字符串（此字符串区分大小写）为止，其后的所有字符串都会显
示到界面上。


       - **| exclude** regular-expression


输出不匹配指定正则表达式的所有行。即，待输出的字符串中没有包含指定的字
符串（此字符串区分大小写），则会显示到界面上；否则过滤不显示。


       - **| include** regular-expression


只输出匹配指定正则表达式的所有行。即，待输出的字符串中如果包含指定的字
符串（此字符串区分大小写），则会显示到界面上；否则过滤不显示。


说明


支持多级管道符对命令行进行筛选过滤。按照用户输入的先后顺序，上一级管道处理的输出作为
下一级管道处理的输入，最多支持 32 级过滤。例如，使用如下命令可以过滤出包含“ ip ”，且不
包含“ address 10.1 ”以及“ description ”的结果段配置信息：

**display current-configuration | section include ip | exclude address 10.1 | exclude description**


按过滤条件进行查询时，有以下注意事项：


       - 显示内容的第一行信息中，并不是以过滤字符串作为开始，而是以包含该字符串
的整条信息开始的。


       - 对于某些配置命令，虽然用户已经配置，但如果这些配置的功能没有生效，则命
令行界面不予显示。


NE40E 还支持将 **display** 命令显示的结果重定向到指定的文件。有两种重定向方式可供
选择：


       - **>** filename


将 **display** 命令显示的结果输出到指定的文件。如果目标文件已经存在，则覆盖该
文件的原有内容。

       - **>>** filename


将 **display** 命令显示的结果追加到指定文件的末尾，原文件的内容仍保留。


系统支持使用 **| refresh** 周期刷新查询结果。使用 **| refresh** 设备每隔一段时间显示一次
查询结果，默认查询间隔是 5 秒。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 24


HUAWEI NetEngine40E
配置指南 1 基础配置


说明


          - 命令刷新周期间隔过短，会引起 CPU 使用率上升，请尽量大的配置查询周期间隔。


          - 如果设备剩余的 VTY 通道数小于 4 ，则无法新增执行 **| refresh** 周期查询命令，已执行的周期查
询命令不受影响。


          - 只有以 **display** 开头的查询命令支持使用 **| refresh** 周期查询功能。
