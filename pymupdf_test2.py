#!/usr/bin/env python3
# pdf2md_outline.py
import fitz  # pymupdf
import re
from pathlib import Path

PDF_FILE  = '2.pdf'
MD_FILE   = '2.md'

# --------------------------------------------------
# 1. 读取目录（含层级、标题、页码）
# --------------------------------------------------
def get_toc_list(doc):
    """返回 [(level, title, page_idx), ...]，page_idx 从 0 开始"""
    return [(lvl, title, page - 1)           # get_toc 返回页码从 1 开始
            for lvl, title, page in doc.get_toc(simple=True)]

# --------------------------------------------------
# 2. 把标题对应的文本内容切出来
# --------------------------------------------------
def text_between_pages(doc, start_page, end_page):
    """提取从 start_page 到 end_page-1 的全部文本"""
    return '\n'.join(doc[i].get_text("text") for i in range(start_page, end_page))

def sanitize_filename(title):
    """去掉标题中的非法字符，做锚点/文件名用"""
    return re.sub(r'[\\/:*?"<>|]', '_', title)

# --------------------------------------------------
# 3. 主流程
# --------------------------------------------------
def pdf_outline_to_markdown(pdf_path, md_path):
    doc = fitz.open(pdf_path)
    toc = get_toc_list(doc)
    md_lines = []
    print(toc)

    # 在末尾加一个哨兵，保证最后一章有结束页
    toc.append((999, 'EOF', doc.page_count))

    for idx, (lvl, title, start_p) in enumerate(toc[:-1]):
        _, _, next_p = toc[idx + 1]           # 下一章起始页
        content = text_between_pages(doc, start_p, next_p).strip()

        # Markdown 标题：1 级 -> #，2 级 -> ## ...
        prefix = '#' * lvl
        md_lines.append(f'{prefix} {title}\n')
        if content:
            md_lines.append("```\n"+content+"\n```")
            md_lines.append('\n\n')

    Path(md_path).write_text('\n'.join(md_lines), encoding='utf-8')
    print(f'已生成 {md_path}  ({len(toc)-1} 个章节)')

if __name__ == '__main__':
    pdf_outline_to_markdown(PDF_FILE, MD_FILE)