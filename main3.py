#!/usr/bin/env python3
"""
PDF转Markdown转换器 - 基于raw markdown的优雅方案
使用pymupdf生成基础markdown，然后基于TOC重新组织内容结构

作者: AI Assistant
版本: 3.0
"""

import fitz  # pymupdf
import re
import sys
import os
import logging
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass
from difflib import SequenceMatcher

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('pdf_converter.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class TitleInfo:
    """标题信息"""
    level: int
    text: str
    page_num: int
    original_text: str  # TOC中的原始文本

class PDFToMarkdownConverter:
    """PDF转Markdown转换器 - 基于raw markdown的方案"""
    
    def __init__(self, pdf_path: str, config: Dict[str, Any] = None):
        self.pdf_path = pdf_path
        self.doc = None
        self.toc_titles: List[TitleInfo] = []
        self.raw_markdown = ""
        
        # 默认配置
        self.config = {
            'title_similarity_threshold': 0.6,  # 标题匹配相似度阈值
            'max_title_search_lines': 50,       # 搜索标题的最大行数
            'content_min_length': 10,           # 最小内容长度
            'debug_mode': False,
            'preserve_formatting': True,        # 保持原始格式
            'clean_extra_newlines': True,       # 清理多余换行
        }
        
        if config:
            self.config.update(config)
    
    def __enter__(self):
        self.doc = fitz.open(self.pdf_path)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.doc:
            self.doc.close()
    
    def generate_raw_markdown(self) -> str:
        """使用pymupdf生成基础markdown - 改进版本"""
        logger.info("生成基础markdown...")

        raw_content = []
        toc_pages = set()  # 记录目录页面

        # 首先识别目录页面
        for page_num in range(min(10, len(self.doc))):  # 只检查前10页
            page = self.doc[page_num]
            page_text = page.get_text()

            # 如果页面包含大量的点号和页码，可能是目录页
            if page_text.count('...') > 10 or page_text.count('。。。') > 10:
                toc_pages.add(page_num)
                if self.config['debug_mode']:
                    logger.debug(f"识别页面 {page_num + 1} 为目录页")

        # 提取所有页面的文本，但优先处理非目录页
        for page_num in range(len(self.doc)):
            page = self.doc[page_num]

            # 尝试多种文本提取方法
            page_text = ""

            # 对于目录页，使用简单提取
            if page_num in toc_pages:
                try:
                    page_text = page.get_text()
                    if self.config['debug_mode']:
                        logger.debug(f"目录页 {page_num + 1} 使用普通文本提取")
                except Exception as e:
                    logger.warning(f"目录页 {page_num + 1} 提取失败: {e}")
            else:
                # 对于内容页，尝试更丰富的提取方法

                # 方法1: 尝试markdown格式
                try:
                    page_markdown = page.get_text("markdown")
                    if page_markdown and page_markdown.strip():
                        page_text = page_markdown
                        if self.config['debug_mode']:
                            logger.debug(f"内容页 {page_num + 1} 使用markdown格式提取成功")
                except Exception as e:
                    if self.config['debug_mode']:
                        logger.debug(f"内容页 {page_num + 1} markdown提取失败: {e}")

                # 方法2: 降级到普通文本
                if not page_text:
                    try:
                        page_text = page.get_text()
                        if self.config['debug_mode']:
                            logger.debug(f"内容页 {page_num + 1} 使用普通文本格式提取")
                    except Exception as e:
                        logger.warning(f"内容页 {page_num + 1} 文本提取失败: {e}")
                        continue

                # 方法3: 如果还是没有内容，尝试字典格式
                if not page_text or not page_text.strip():
                    try:
                        text_dict = page.get_text("dict")
                        page_text = self._extract_text_from_dict(text_dict)
                        if self.config['debug_mode']:
                            logger.debug(f"内容页 {page_num + 1} 使用字典格式提取")
                    except Exception as e:
                        logger.warning(f"内容页 {page_num + 1} 字典格式提取失败: {e}")

            if page_text and page_text.strip():
                # 添加页面标记，便于调试
                page_marker = f"\n\n--- 页面 {page_num + 1} ---\n"
                raw_content.append(page_marker + page_text)

        self.raw_markdown = "\n\n".join(raw_content)
        logger.info(f"生成的raw markdown长度: {len(self.raw_markdown)} 字符")
        logger.info(f"识别到 {len(toc_pages)} 个目录页面")

        return self.raw_markdown

    def _extract_text_from_dict(self, text_dict: dict) -> str:
        """从字典格式中提取文本"""
        text_parts = []

        for block in text_dict.get("blocks", []):
            if "lines" in block:
                for line in block["lines"]:
                    line_text = ""
                    for span in line.get("spans", []):
                        line_text += span.get("text", "")
                    if line_text.strip():
                        text_parts.append(line_text.strip())

        return "\n".join(text_parts)
    
    def extract_toc_titles(self) -> List[TitleInfo]:
        """提取TOC标题信息"""
        logger.info("提取TOC标题信息...")
        
        toc = self.doc.get_toc()
        if not toc:
            logger.error("PDF中没有找到目录信息")
            return []
        
        titles = []
        for item in toc:
            level, title, page_num = item
            
            # 清理标题文本
            clean_title = self.clean_title_text(title)
            
            title_info = TitleInfo(
                level=level,
                text=clean_title,
                page_num=page_num,
                original_text=title
            )
            titles.append(title_info)
        
        self.toc_titles = titles
        logger.info(f"提取到 {len(titles)} 个标题")
        
        return titles
    
    def clean_title_text(self, title: str) -> str:
        """清理标题文本"""
        # 移除多余的空格和特殊字符
        title = re.sub(r'\s+', ' ', title.strip())
        
        # 移除可能的页码信息
        title = re.sub(r'\s*\.\.\.\s*\d+\s*$', '', title)
        title = re.sub(r'\s*\d+\s*$', '', title)
        
        return title.strip()
    
    def find_title_in_markdown(self, title_info: TitleInfo) -> Tuple[int, int]:
        """在raw markdown中查找标题位置 - 从尾部往前搜索

        Returns:
            Tuple[int, int]: (start_pos, end_pos) 如果找不到返回 (-1, -1)
        """
        lines = self.raw_markdown.split('\n')
        title_text = title_info.text.strip()

        # 按优先级尝试匹配策略，从文档尾部往前搜索
        strategies = [
            self._exact_match_reverse,
            self._hash_line_match_reverse,
            self._fuzzy_match_reverse
        ]

        for strategy in strategies:
            result = strategy(lines, title_text, title_info)
            if result != (-1, -1):
                if self.config['debug_mode']:
                    logger.debug(f"标题 '{title_text}' 使用策略 {strategy.__name__} 找到位置: {result}")
                return result

        logger.warning(f"无法在markdown中找到标题: {title_text}")
        return (-1, -1)
    
    def _exact_match_reverse(self, lines: List[str], title_text: str, title_info: TitleInfo) -> Tuple[int, int]:
        """精确匹配策略 - 从尾部往前搜索，智能处理空格差异"""

        def normalize_title(text):
            """智能标准化标题文本"""
            # 1. 基本清理
            text = text.strip()
            # 2. 标准化空格
            text = re.sub(r'\s+', ' ', text)
            # 3. 处理中英文之间的空格差异（这是关键！）
            # Console口 vs Console 口 - 移除英文字母和中文字符之间的空格
            text = re.sub(r'([a-zA-Z])\s+([^\sa-zA-Z0-9])', r'\1\2', text)
            # Tab键 vs Tab 键 - 移除英文字母和中文字符之间的空格
            text = re.sub(r'([a-zA-Z])\s+([一-龯])', r'\1\2', text)
            return text

        title_normalized = normalize_title(title_text)

        # 从后往前遍历，避免目录干扰
        for i in range(len(lines) - 1, -1, -1):
            line = lines[i].strip()
            if not line:
                continue

            # 标准化行文本
            line_normalized = normalize_title(line)

            # 精确匹配
            if title_normalized == line_normalized:
                start_pos = sum(len(l) + 1 for l in lines[:i])
                end_pos = start_pos + len(lines[i])
                return (start_pos, end_pos)

            # 匹配去除点号后的标题（处理目录格式）
            line_clean = re.sub(r'\.+\s*\d*$', '', line).strip()
            line_clean_normalized = normalize_title(line_clean)
            if title_normalized == line_clean_normalized:
                start_pos = sum(len(l) + 1 for l in lines[:i])
                end_pos = start_pos + len(lines[i])
                return (start_pos, end_pos)

        return (-1, -1)

    def _hash_line_match_reverse(self, lines: List[str], title_text: str, title_info: TitleInfo) -> Tuple[int, int]:
        """匹配所有带#的行，去除空格后精准匹配 - 从尾部往前搜索"""
        # 提取所有带#的行
        hash_lines = []
        for i, line in enumerate(lines):
            if '#' in line:
                hash_lines.append((i, line))

        # 智能标准化（与精确匹配使用相同的逻辑）
        def normalize_for_hash_match(text):
            text = re.sub(r'\s+', '', text)  # 完全去除空格
            return text

        title_normalized = normalize_for_hash_match(title_text)

        # 从后往前匹配
        for i, line in reversed(hash_lines):
            line_normalized = normalize_for_hash_match(line)
            if title_normalized in line_normalized:
                start_pos = sum(len(l) + 1 for l in lines[:i])
                end_pos = start_pos + len(lines[i])
                return (start_pos, end_pos)

        return (-1, -1)

    def _fuzzy_match_reverse(self, lines: List[str], title_text: str, title_info: TitleInfo) -> Tuple[int, int]:
        """模糊匹配策略 - 从尾部往前搜索，智能处理关键词"""

        def extract_smart_keywords(text):
            """智能提取关键词"""
            # 分割文本
            parts = re.split(r'[\s\.]+', text.strip())
            keywords = []
            for part in parts:
                if len(part) > 1:  # 长度大于1的词
                    keywords.append(part)
            return keywords

        keywords = extract_smart_keywords(title_text)
        if not keywords:
            return (-1, -1)

        # 从后往前搜索
        for i in range(len(lines) - 1, -1, -1):
            line = lines[i].strip()
            if not line:
                continue

            # 去除目录格式的点号
            line_clean = re.sub(r'\.+\s*\d*$', '', line).strip()

            # 计算匹配的关键词数量
            matches = 0
            for keyword in keywords:
                # 更宽松的匹配，处理空格差异
                if keyword in line_clean or keyword.replace(' ', '') in line_clean.replace(' ', ''):
                    matches += 1

            # 如果匹配度超过阈值
            if matches >= len(keywords) * 0.7:  # 提高到70%匹配度
                start_pos = sum(len(l) + 1 for l in lines[:i])
                end_pos = start_pos + len(lines[i])
                return (start_pos, end_pos)

        return (-1, -1)
    

    

    
    def extract_content_between_titles(self) -> List[Dict[str, Any]]:
        """按顺序提取标题之间的内容 - 改进版本"""
        logger.info("提取标题之间的内容...")

        if not self.toc_titles or not self.raw_markdown:
            logger.error("缺少必要的数据：TOC标题或raw markdown")
            return []

        content_sections = []

        for i, current_title in enumerate(self.toc_titles):
            logger.info(f"处理标题 {i+1}/{len(self.toc_titles)}: {current_title.text}")

            # 确定下一个标题（无论什么级别）
            next_title = None
            if i + 1 < len(self.toc_titles):
                next_title = self.toc_titles[i + 1]

            # 提取当前标题和下一个标题之间的内容
            content = self._extract_content_between_two_titles(current_title, next_title)

            content_sections.append({
                "title": current_title,
                "content": content,
                "status": "success" if content != "*[暂无内容]*" else "no_content"
            })

            if self.config['debug_mode']:
                logger.debug(f"标题 '{current_title.text}' 内容长度: {len(content)} 字符")

        return content_sections

    def _extract_content_between_two_titles(self, current_title: TitleInfo, next_title: Optional[TitleInfo]) -> str:
        """提取两个标题之间的内容"""
        # 找到当前标题的位置
        current_start, current_end = self.find_title_in_markdown(current_title)
        if current_start == -1:
            if self.config['debug_mode']:
                logger.debug(f"未找到当前标题: {current_title.text}")
            return "*[暂无内容]*"

        # 确定内容结束位置
        if next_title:
            next_start, _ = self.find_title_in_markdown(next_title)
            if next_start != -1:
                content_end = next_start
            else:
                # 如果找不到下一个标题，取到文档末尾
                content_end = len(self.raw_markdown)
        else:
            # 最后一个标题，取到文档末尾
            content_end = len(self.raw_markdown)

        # 提取内容
        content = self.raw_markdown[current_end:content_end].strip()

        if not content:
            return "*[暂无内容]*"

        # 清理内容
        content = self._clean_extracted_content(content)

        return content

    def _clean_extracted_content(self, content: str) -> str:
        """清理提取的内容"""
        lines = content.split('\n')
        cleaned_lines = []

        for line in lines:
            # 跳过页面标记行
            if re.match(r'^--- 页面 \d+ ---$', line.strip()):
                continue

            # 转义#号为//，避免影响markdown标题结构
            line = self._escape_hash_symbols(line)
            cleaned_lines.append(line)

        result = '\n'.join(cleaned_lines).strip()

        # 如果内容为空，返回占位符
        if not result:
            return "*[暂无内容]*"

        return result

    def clean_content(self, content: str, title_info: TitleInfo) -> str:
        """清理提取的内容"""
        if not content:
            return ""
        
        # 移除开头的标题行（如果存在）
        lines = content.split('\n')
        cleaned_lines = []
        
        skip_first_title = True
        for line in lines:
            line_stripped = line.strip()

            # 跳过第一个可能的标题行
            if skip_first_title and self._is_likely_title_line(line_stripped, title_info):
                skip_first_title = False
                continue

            skip_first_title = False

            # 转义#号为//，避免影响markdown标题结构
            line = self._escape_hash_symbols(line)
            cleaned_lines.append(line)

        content = '\n'.join(cleaned_lines)

        # 清理多余的换行符
        if self.config['clean_extra_newlines']:
            content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)

        # 移除首尾空白
        content = content.strip()

        return content

    def _escape_hash_symbols(self, line: str) -> str:
        """智能转义行中的#号为//"""
        stripped = line.strip()
        if not stripped.startswith('#'):
            return line

        # 检查是否是真正的markdown标题
        if self._is_real_markdown_header(stripped):
            # 这是真正的markdown标题，保留不变
            return line
        else:
            # 这是注释行，转义#号
            leading_spaces = len(line) - len(line.lstrip())
            escaped_content = stripped.replace('#', '//', 1)
            return ' ' * leading_spaces + escaped_content

    def _is_real_markdown_header(self, line: str) -> bool:
        """判断是否是真正的markdown标题"""
        # 真正的markdown标题格式：# 标题内容
        # 注释行格式：# 注释内容（通常是配置说明）

        # 检查是否符合markdown标题格式
        header_match = re.match(r'^(#{1,6})\s+(.+)$', line)
        if not header_match:
            return False

        hashes, content = header_match.groups()

        # 如果内容看起来像配置注释，则不是标题
        comment_patterns = [
            r'^(配置|查看|使能|设置|创建|新建|将|为|在|执行|启动)',  # 中文动词开头
            r'^(第一次|攻击者|客户端|服务器端)',  # 特定场景描述
            r'(命令|配置|功能|服务|地址|密钥|用户|界面)',  # 技术术语
            r'\.exe$',  # 文件名
            r'^\w+\s+(生成|配置|连接)',  # 英文+中文动词
        ]

        for pattern in comment_patterns:
            if re.search(pattern, content):
                return False

        # 如果内容看起来像真正的标题
        title_patterns = [
            r'^\d+(\.\d+)*\s+',  # 数字编号开头 (1.2.3 标题)
            r'^[A-Z][a-z]+\s+',   # 英文单词开头
            r'^[一-龯]{2,}',      # 中文词汇开头
        ]

        for pattern in title_patterns:
            if re.search(pattern, content):
                return True

        # 默认情况下，如果不确定，保守地认为是注释
        return False
    
    def _is_likely_title_line(self, line: str, title_info: TitleInfo) -> bool:
        """判断是否可能是标题行"""
        if not line:
            return False
        
        # 检查是否包含标题的关键词
        title_words = set(word.lower() for word in title_info.text.split() if len(word) > 2)
        line_words = set(word.lower() for word in line.split() if len(word) > 2)
        
        if title_words and line_words:
            overlap = len(title_words & line_words) / len(title_words)
            return overlap > 0.5
        
        return False

    def generate_structured_markdown(self, content_sections: List[Dict[str, Any]]) -> str:
        """生成结构化的markdown"""
        logger.info("生成结构化markdown...")

        markdown_parts = []

        for section in content_sections:
            title = section['title']
            content = section['content']
            status = section['status']

            # 生成markdown标题
            title_prefix = '#' * title.level
            title_line = f"{title_prefix} {title.text}"

            markdown_parts.append(title_line)
            markdown_parts.append("")  # 空行

            if status == "success" and content:
                markdown_parts.append(content)
            elif status == "title_not_found":
                markdown_parts.append("*[内容提取失败：无法定位标题]*")
            else:
                markdown_parts.append("*[暂无内容]*")

            markdown_parts.append("")  # 章节间空行
            markdown_parts.append("")

        final_markdown = "\n".join(markdown_parts)

        # 最终清理
        final_markdown = self.final_cleanup(final_markdown)

        return final_markdown

    def final_cleanup(self, markdown: str) -> str:
        """最终清理markdown"""
        # 移除过多的空行
        markdown = re.sub(r'\n{4,}', '\n\n\n', markdown)

        # 确保标题前有适当的空行
        markdown = re.sub(r'\n(#{1,6}\s)', r'\n\n\1', markdown)

        # 移除首尾多余空行
        markdown = markdown.strip()

        return markdown

    def convert(self) -> str:
        """执行完整的转换流程"""
        logger.info(f"开始转换PDF: {self.pdf_path}")

        try:
            # 步骤1: 生成基础markdown
            self.generate_raw_markdown()

            # 步骤2: 提取TOC标题
            self.extract_toc_titles()

            if not self.toc_titles:
                logger.error("没有找到TOC信息，无法进行结构化转换")
                return self.raw_markdown  # 返回原始markdown

            # 步骤3: 提取标题间内容
            content_sections = self.extract_content_between_titles()

            # 步骤4: 生成结构化markdown
            structured_markdown = self.generate_structured_markdown(content_sections)

            # 统计信息
            success_count = sum(1 for s in content_sections if s['status'] == 'success')
            total_count = len(content_sections)

            logger.info(f"转换完成！成功处理 {success_count}/{total_count} 个标题")
            logger.info(f"最终markdown长度: {len(structured_markdown)} 字符")

            return structured_markdown

        except Exception as e:
            logger.error(f"转换过程中发生错误: {e}")
            raise

def convert_pdf_to_markdown(pdf_path: str, output_path: str = None, config: Dict[str, Any] = None) -> str:
    """
    转换PDF为Markdown的便捷函数

    Args:
        pdf_path: PDF文件路径
        output_path: 输出文件路径（可选）
        config: 配置选项

    Returns:
        str: 转换后的markdown内容
    """
    if not os.path.exists(pdf_path):
        raise FileNotFoundError(f"PDF文件不存在: {pdf_path}")

    with PDFToMarkdownConverter(pdf_path, config) as converter:
        markdown_content = converter.convert()

        if output_path:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(markdown_content)
            logger.info(f"Markdown已保存到: {output_path}")

        return markdown_content

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("使用方法: python main3.py <PDF文件路径> [输出文件路径] [配置选项]")
        print("\n示例:")
        print("  python main3.py document.pdf")
        print("  python main3.py document.pdf output.md")
        print("  python main3.py document.pdf output.md --debug")
        print("\n配置选项:")
        print("  --debug: 启用调试模式")
        print("  --threshold=0.8: 设置标题匹配阈值")
        sys.exit(1)

    pdf_path = sys.argv[1]
    output_path = sys.argv[2] if len(sys.argv) > 2 and not sys.argv[2].startswith('--') else None

    # 解析配置选项
    config = {}
    for arg in sys.argv[2:]:
        if arg == '--debug':
            config['debug_mode'] = True
        elif arg.startswith('--threshold='):
            config['title_similarity_threshold'] = float(arg.split('=')[1])

    # 如果没有指定输出路径，自动生成
    if not output_path:
        base_name = os.path.splitext(os.path.basename(pdf_path))[0]
        output_path = f"{base_name}_converted.md"

    try:
        print(f"开始转换PDF: {pdf_path}")
        print(f"输出文件: {output_path}")

        if config.get('debug_mode'):
            print("调试模式已启用")

        markdown_content = convert_pdf_to_markdown(pdf_path, output_path, config)

        print(f"\n✅ 转换成功！")
        print(f"📄 输出文件: {output_path}")
        print(f"📊 内容长度: {len(markdown_content):,} 字符")

        # 统计标题数量
        title_count = len([line for line in markdown_content.split('\n') if line.strip().startswith('#')])
        print(f"📋 标题数量: {title_count}")

        # 显示预览
        print(f"\n--- 内容预览 ---")
        preview_lines = markdown_content.split('\n')[:20]
        for line in preview_lines:
            print(line)

        if len(markdown_content.split('\n')) > 20:
            print("...")

        print(f"\n完整内容已保存到: {output_path}")

    except Exception as e:
        print(f"❌ 转换失败: {e}")
        if config.get('debug_mode'):
            import traceback
            traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
