

在使用快捷键时，会接触到两类快捷键，自定义的快捷键和系统快捷键。了解了分类
后，可以更快速和准确的使用快捷键。


系统中的快捷键分成两类：


       - 提供给用户、可以自由定义的快捷键：包括 CTRL_G 、 CTRL_L 、 CTRL_O 和
CTRL_U 。用户可以根据自己的需要将这几个快捷键与任意命令进行关联，当使用
快捷键时，系统自动执行它所对应的命令。定义此类快捷键的方法请参见 **1.3.6.2**
定义快捷键 。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 29


HUAWEI NetEngine40E
配置指南 1 基础配置


       - 系统快捷键：是系统中固定的。这种快捷键不由用户自定义，代表固定功能。系
统包括的主要快捷键如 表 **1-7** 所示。


说明


由于不同的终端软件对于某些键的解释不同，具体终端上实际可用的快捷键与本节所列举的按键
组合可能略有差异。


表 **1-7** 系统快捷键

|功能键|功能|
|---|---|
|CTRL_A|移动光标到当前行的开头。|
|CTRL_B|将光标向左移动一个字符。|
|CTRL_C|停止当前正在执行的功能。|
|CTRL_D|删除当前光标所在位置的字符。|
|CTRL_E|移动光标到当前行的末尾。|
|CTRL_F|将光标向右移动一个字符。|
|CTRL_H|删除光标左边的一个字符。|
|CTRL_I|功能等同于TAB键。|
|CTRL_J|功能等同于回车键。|
|CTRL_K|在连接建立阶段终止呼出的连接。|
|CTRL_M|功能等同于回车键。|
|CTRL_N|显示历史命令缓冲区中的后一条命令。|
|CTRL_P|显示历史命令缓冲区中的前一条命令。|
|CTRL_R|重新显示当前行。|
|CTRL_T|终止呼出的连接。|
|CTRL_V|粘贴剪贴板的内容。|
|CTRL_W|删除光标左侧的字。|
|CTRL_X|删除光标左侧所有字符。|
|CTRL_Y|删除光标右侧所有字符。|
|CTRL_Z|返回到用户视图。|
|CTRL_]|终止呼入的连接或重定向连接。|
|ESC_B|将光标向左移动一个字。|
|ESC_D|删除光标右侧的字。|
|ESC_F|将光标向右移动一个字。|
|ESC_N|移动光标到下一行。|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 30


HUAWEI NetEngine40E
配置指南 1 基础配置

|功能键|功能|
|---|---|
|ESC_P|移动光标到上一行。|
|ESC_<|将光标所在位置指定为剪贴板的开始位置。|
|ESC_>|将光标所在位置指定为剪贴板的结束位置。|

