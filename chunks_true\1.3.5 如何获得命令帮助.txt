

输入命令行或进行配置业务时，命令帮助可以提供配置手册之外的实时帮助。


NE40E 的命令行接口提供如下两种在线帮助。

##### 完全帮助


命令行的完全帮助可以通过以下三种方式获取。


       - 在任一命令视图下，键入“ ? ”获取该命令视图下所有的命令及其简单描述。


<HUAWEI> **?**


       - 键入一条命令，后接以空格分隔的“ ? ”，如果该位置为关键字，则列出全部关键
字及其简单描述。举例如下：


<HUAWEI> **terminal ?**
debugging Enable/disable debug information to terminal
logging  Enable/disable log information to terminal
其中“ debugging ”、“ logging ”是关键字，后面语句是对关键字的描述。


       - 键入一条命令，后接以空格分隔的“ ? ”，如果该位置为参数，则列出参数取值的
说明和参数作用的描述。举例如下：


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 28


HUAWEI NetEngine40E
配置指南 1 基础配置


[*HUAWEI] **ftp timeout ?**
INTEGER<1-35791> The value of FTP timeout, the default value is 10 minutes

[*HUAWEI] **ftp timeout 35 ?**

<cr>


其中，“ INTEGER<1-35791> ”是参数取值的说明，“ The value of FTP timeout,
the default value is 10 minutes ”是对参数作用的简单描述； <cr> 表示该位置无
参数，直接键入回车即可执行。

##### 部分帮助


命令行的部分帮助可以通过以下三种方式获取。


       - 键入一字符串，其后紧接输入“ ? ”，列出以该字符串开头的所有关键字。


<HUAWEI> **d?**
debugging                delete
dir                   display


       - 键入一条命令，关键字不唯一时紧接输入“ ? ”，则列出以该字符串开头的所有关
键字。


<HUAWEI> **display c?**
car                   clock
configuration              control-flap
cpu-defend               cpu-monitor
cpu-usage                current-configuration


       - 输入命令的某个关键字的前几个字母，按下 <tab> 键，可以显示完整的关键字。如
果关键字不唯一，可以连续按下 <tab> 键，则会出现不同的关键字，用户可以从中
选择需要的关键字。
