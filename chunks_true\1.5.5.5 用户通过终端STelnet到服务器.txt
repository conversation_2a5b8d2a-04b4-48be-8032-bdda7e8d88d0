

当通过 Console 口登录设备完成相关配置后，用户可以使用 SSH 协议从终端登录到设
备，实现对设备的远程维护。

##### 背景信息


从终端（ SSH Client ）通过 STelnet 方式登录服务器，如 图 **1-14** 所示， PC 终端上运行支
持 SSH1.5 及以上版本的客户端软件，与设备建立本地连接。


这里以 PuTTY 为例介绍 SSH 客户端通过 STelnet 方式登录服务器的方法。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 86


HUAWEI NetEngine40E
配置指南 1 基础配置


图 **1-14** 配置 SSH 终端服务组网

##### 数据准备


已安装 PuTTYGen.exe 和 PuTTY.exe 软件。


表 **1-18** 数据准备






##### 操作步骤

|设<br>备|接口|IP地址|
|---|---|---|
|SS<br>H<br>Ser<br>ver|GE1/0/0|**************/24|
|PC|PC的网口|**************/24|



步骤 **1** SSH 客户端上创建 SSH 密钥对。


1. 进入 Windows 的命令行提示符。


2. 输入 PuTTYGen ，单击“ Generate ”，产生密钥对，如 图 **1-15** 所示。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 87


HUAWEI NetEngine40E
配置指南 1 基础配置


图 **1-15** 生成客户端密钥对（ 1 ）


在产生密钥对的过程中需不停的移动鼠标，鼠标移动仅限于窗口中除绿色标记进
程条外的地方。否则进程条的显示会不动，密钥对将停止产生，如 图 **1-16** 所示。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 88


HUAWEI NetEngine40E
配置指南 1 基础配置


图 **1-16** 生成客户端密钥对（ 2 ）


3. 密钥对产生后，在“ Key passphrase ”文本框中输入密码，并在“ Confirm
passphrase ”文本框中再次输入该密码，作为 SSH 终端用户登录 SSH 服务器的密
码。单击“ Save private key ”，输入存储私钥的文件名 private ，单击保存。将生
成的公钥全部拷贝，粘贴至记事本中，并命名为 public.txt 。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 89


HUAWEI NetEngine40E
配置指南 1 基础配置


图 **1-17** 生成客户端密钥对（ 3 ）


步骤 **2** 配置 SSH 客户端。


1. 打开 PuTTY.exe 程序，出现如 图 **1-18** 所示客户端配置界面。为确保安全，请使用
PuTTY 的 0.58 及以上版本。在“ Host Name (or IP address) ”文本框中输入 SSH 服
务器的 IP 地址。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 90


HUAWEI NetEngine40E
配置指南 1 基础配置


图 **1-18** SSH 客户端配置界面（ 1 ）


2. 选择 SSH 客户端配置界面左侧目录树（ Category ）中的连接协议（ Connection ）
中的“ SSH ”，出现如 图 **1-19** 所示界面。在“ Protocol options ”区域中，选择
“ SSH protocol version ”参数的值为 2 。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 91


HUAWEI NetEngine40E
配置指南 1 基础配置


图 **1-19** SSH 客户端配置界面（ 2 ）


3. 继续选择“ SSH ”下的“ Auth ”，出现如 图 **1-20** 所示界面，单击“ Browse ”，导
入存储的私钥文件“ private.ppk ”。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 92


HUAWEI NetEngine40E
配置指南 1 基础配置


图 **1-20** SSH 客户端配置界面（ 3 ）


4. 单击“ Open ”。如果连接正常则会提示用户输入用户名，如 图 **1-21** 所示。


图 **1-21** SSH 客户端登录认证界面


**----**
结束

##### 后续处理


当网络管理员需要将某个登录用户与设备连接断开时，可通过命令 **kill user-interface**
{ ui-number | { { **console** | **vty** | **nca** | **rpc** } ui-number1 | interface-name } } 清除在
线用户。


**console** 参数仅在 Admin-VS 支持。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 93


HUAWEI NetEngine40E
配置指南 1 基础配置
