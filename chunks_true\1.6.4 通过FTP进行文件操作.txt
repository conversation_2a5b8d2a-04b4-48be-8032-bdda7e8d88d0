

FTP 用于在远端服务器和本地客户端之间传输文件。

##### 应用环境


随着设备的逐步稳定，应用范围在不断扩大，设备升级的范围也越来越大，设备的可
维护性要求也越来越高，特别是设备在线远程加载升级。这不仅可以丰富设备升级维
护手段，方便远程在线升级，还可以节约升级成本，减少客户等待升级的时间，在一
定程度上提高了客户的满意度。但是，实际网络上的丢包、时延、抖动影响了数据传
输。为了可靠的保证在线升级、数据传输，可利用基于 TCP 的 FTP 进行在线升级、传输
文件。


说明


此协议不安全，建议使用 SFTP 。

##### 前置任务


在通过 FTP 进行文件操作之前，需要完成以下任务：


       - 终端与设备之间三层路由可达。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 148


HUAWEI NetEngine40E
配置指南 1 基础配置
