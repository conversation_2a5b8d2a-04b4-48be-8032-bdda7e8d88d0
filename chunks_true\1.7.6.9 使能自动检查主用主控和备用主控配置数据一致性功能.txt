

使能自动检查主用主控和备用主控配置数据一致性功能，让系统定时检查配置数据的
一致性，出现数据不一致，设备立即上报告警，提示用户及时分析这类差异给设备造
成的影响。

##### 背景信息


主用主控和备用主控上均有配置数据的存储，正常情况下两者的配置数据一致。如果
出现主用主控和备用主控中配置数据不一致，此时若发生主备倒换，会导致主用主控
上原有配置丢失。因此需要使能自动检查主用主控和备用主控配置数据一致性功能，
让系统定时检查配置数据的一致性。如果出现数据不一致，设备立即上报告警，提示
用户及时分析配置数据不一致给设备造成的影响，这时用户可以尝试保存配置后重启
设备进行修复。


说明


该配置过程仅在 Admin-VS 支持。

##### 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **configuration inconsistent slave detect enable** ，使能自动检查主用主控
和备用主控上的配置数据是否一致功能。


步骤 **3** 执行命令 **commit** ，提交配置。


**----**
结束
