

用户可以保存配置到配置文件，支持自动保存和手工保存两种方式。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 202


HUAWEI NetEngine40E
配置指南 1 基础配置

##### 背景信息


为了防止设备掉电或者意外重启导致配置信息丢失，系统同时提供手动和自动保存配
置功能。


自动保存配置时，可将配置文件保存到本设备，也可以根据需要将配置文件保存到备
份服务器中。当本地数据库空间不足或损坏时，可将配置文件保存到备份服务器中，
提高设备数据库的安全性。


要保存配置文件，需要在设备上进行如下配置。

##### 操作步骤


       - 自动保存配置。


a. 执行命令 **system-view** ，进入系统视图。

b. 执行命令 **set save-configuration** [ **interval** interval | **cpu-limit** cpu-usage
| **delay** delay-interval ] [*] ，配置系统定时保存配置。

#### ▪ 如果需要指定定时保存配置时间间隔，则指定参数 interval interval 。 ▪ 为了防止自动保存影响系统性能，可以指定参数 cpu-limit cpu-usage 配

置自动保存时系统 CPU 使用率上限。在自动保存定时器触发时，检测到
系统的 CPU 占用率高于配置的值，系统将取消本次自动保存。

#### ▪ 若需要指定系统自动备份配置的延时时间，则选择参数 delay delay
interval ，系统将在到达配置发生变更后的指定延时时间后自动保存配
置。

#### ▪ 如果同时配置参数 interval interval 和 delay delay-interval ，则哪个参数

配置的时间先到，则由哪个参数触发配置保存操作，另一个参数配置的
时间到的时候会再次检查配置，发现没有变更则不执行保存操作。


配置系统定时自动保存功能后，不管是否有手动执行过 **save** 操作，只要当前
运行配置和下次启动文件中的配置有差异， **interval** interval 参数配置的时间
到了之后会触发保存配置到下次启动配置文件操作。

c. （可选）执行命令 **set save-configuration backup-to-server** **server**

[ **ipv6** ] server-ip [ **vpn-instance** vpn-instance-name ] **transport-type**
{ **tftp** | { **ftp** | **sftp** } [ **port** port-value ] **user** user-name **password**
password } [ **path** folder ] ，配置将当前配置文件自动保存到指定的服务
器。


说明


              - 使用 TFTP 传输方式保存配置文件时，可执行命令 **tftp client-source** 配置路由器的
Loopback 接口作为客户端源地址。


              - 系统最多支持配置 5 个文件服务器。各服务器相互独立，互不影响。如果某个服务
器路径上传失败，则系统将向网管上报告警，并在设备上记录日志。

              - 容灾备份场景下，可通过多次执行 **set save-configuration backup-to-server** 命
令配置多个文件服务器。在配置此命令前，请先配置 **set save-configuration** 命
令使能自动保存功能，并在服务器上开启传输协议服务（ FTP 、 SFTP 或 TFTP ）。


              - 设备支持配置 IP 地址相同， VPN 实例不同的服务器。即对于同一个 IP 地址的服务
器，如果该服务器配置了不同的 VPN 实例，则设备可以向其中任意一个 VPN 实例
中发送配置文件。


       - 手动保存配置。


执行命令 **save** [ configuration-file ] ，保存当前配置。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 203


HUAWEI NetEngine40E
配置指南 1 基础配置


配置文件必须以“ .dat ”、“ .zip ”或“ .cfg ”作为扩展名。


说明


在第一次保存配置文件时，如果不指定可选参数 configuration-file ，则路由器将提示是否将文
件名保存为“ vrpcfg.zip ”。“ vrpcfg.zip ”是系统缺省的配置文件，初始状态是空配置。


**----**
结束
