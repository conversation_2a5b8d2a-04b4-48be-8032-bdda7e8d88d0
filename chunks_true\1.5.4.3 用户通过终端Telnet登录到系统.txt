

当通过 Console 口登录设备完成相关配置后，用户可以使用 Telnet 协议从终端登录到远
端设备，实现对设备的远程维护。

##### 背景信息


从终端通过 Telnet 访问系统，可以选择使用 Windows 命令行提示符或第三方软件。此
处以 Windows 命令行提示符为例进行演示。


请在 PC 上进行以下配置。


说明


使用 VTY 通道登录设备时，为防止 VTY 连接数量超限导致用户无法接入，用户可执行如下操作查
看并配置允许通过 VTY 通道登录的用户数。


1. 执行命令 **display user-interface maximum-vty** 查看设备允许通过 VTY 连接登录设备的最大
用户数。


2. 执行命令 **display user-interface** 查看用户界面信息。其中，“ + ”表示被占用的通道。如果
通道被占满，会导致后续用户无法登录，如果已登录用户登出后，可能会因为通道被其他用
户占用而无法再次登录成功。此时，可执行命令 **user-interface maximum-vty** number 设
置允许登录的最大用户数目。

##### 操作步骤


步骤 **1** 进入 Windows 的命令行提示符。


步骤 **2** 执行 Windows 命令 **telnet** ip-address ，通过 Telnet 方式登录设备。


1. 键入 Telnet 服务器的 IP 地址。


2. 按 Enter 键，出现用户视图的命令行提示符，如 <HUAWEI> ；至此用户登录到了
Telnet 服务器。


**----**
结束

##### 后续处理


当网络管理员需要将某个登录用户与设备连接断开时，可通过命令 **kill user-interface**
{ ui-number | ui-type ui-number1 } 清除在线用户。


**Console** 参数仅在 Admin-VS 支持。
