

配置 Telnet 、 SSH 协议报文的优先级，可以通过修改 DSCP 字段的值来实现。

##### 背景信息


设备可以发出 NETCONF 、 Telnet 、 SSH 等多种协议报文，可以通过命令 **host-packet**
**type** 统一配置这些协议报文的 DSCP 值，这种情况下，同时发送大量协议报文可能造成
网络拥塞，为解决这一问题，可配置设备发出去的协议报文使用不同的 DSCP 。

##### 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 根据服务类型和待配置的协议报文类型，选择执行下列命令：


1. 执行命令 **ssh client dscp** value ，配置客户端 SSH 协议报文的 DSCP 值。


2. 执行命令 **telnet client dscp** value ，配置客户端 Telnet 协议报文的 DSCP 值。


3. 执行命令 **ssh server dscp** value ，配置服务器端 SSH 协议报文的 DSCP 值。


4. 执行命令 **telnet server dscp** value ，配置服务器端 Telnet 协议报文的 DSCP 值。


步骤 **3** 执行命令 **commit** ，提交配置。


**----**
结束

##### 任务示例


执行命令 **display current-configuration** ，可以查看配置的 DSCP 值。


<HUAWEI> **system-view**

[ ~ HUAWEI] **display current-configuration include-default | include dscp**
Info: It will take a long time if the content you search is too much or the string you input is too long, you
can press CTRL_C to break.
telnet server dscp 10
telnet client dscp 10
ssh server dscp 10
ssh client dscp 10
