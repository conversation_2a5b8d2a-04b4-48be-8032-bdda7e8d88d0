

在本示例中，通过从 HTTP 客户端登录 HTTP 服务器，实现从 HTTP 服务器中下载证书。

##### 组网需求


当 HTTP 客户端需要进行从 HTTP 服务器端下载证书，可以使用 HTTP 协议。如 图 **1-79** 所
示， HTTP 客户端的设备和 HTTP 服务器之间路由可达，用户可通过从 HTTP 客户端登录
HTTP 服务器，实现从 HTTP 服务器中下载证书到客户端。


HTTP 服务器支持 SSL 策略，为了提高数据传输的安全性，建议 HTTP 客户端配置 SSL 策
略。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 348


HUAWEI NetEngine40E
配置指南 1 基础配置


图 **1-79** 配置通过 HTTP 访问其他设备文件组网图

##### 配置思路


采用如下的思路配置通过 HTTP 访问其他设备文件：


1. 配置 HTTP 客户端的 SSL 策略。


2. 配置 HTTP 客户端。

##### 数据准备


为完成此配置示例，需准备如下的数据：


       - HTTP 客户端的 SSL 策略名称 policy1 。


       - domain 域名 domain1

##### 操作步骤


步骤 **1** 配置 HTTP 客户端的 SSL 策略。


# 配置 PKI 域


<HUAWEI> **system-view**

[ ~ HUAWEI] **pki domain domain1**

[*HUAWEI-pki-domain-domain1] **commit**

[ ~ HUAWEI-pki-domain-domain1] **quit**

[ ~ HUAWEI] **pki import-certificate ca domain domain1 filename test.crt**


说明


这里以 CA 证书为例，用户在实际配置过程中需要将 **ca** 和 **test.crt** 替换为设备上已有的证书类型和
名称。用户可以自行将证书上传至设备中进行安装，也可以通过申请和下载后进行安装，详细过
程请参考《 PKI 配置》中的“获取证书”章节。


# 配置 SSL 绑定 PKI 域


[ ~ HUAWEI] **ssl policy policy1**

[*HUAWEI-ssl-policy-policy1] **pki-domain domain1**

[*HUAWEI-ssl-policy-policy1] **commit**

[ ~ HUAWEI-ssl-policy-policy1] **quit**


步骤 **2** 配置 HTTP 客户端。


[ ~ HUAWEI] **http**

[*HUAWEI-http] **client ssl-policy policy1**

[*HUAWEI-http] **client ssl-verify peer**

[*HUAWEI-http] **commit**

[ ~ HUAWEI-http] **quit**


步骤 **3** 查看 HTTP 客户端是否配置成功。


[ ~ HUAWEI] **display ssl policy**


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 349


HUAWEI NetEngine40E
配置指南 1 基础配置


SSL Policy Name: policy1
PKI domain: domain1
Policy Applicants: HTTP-CLIENT
Key-pair Type:
Certificate File Type:
Certificate Type:
Certificate Filename:
Key-file Filename:
CRL File:

Trusted-CA File:


**----**
结束

##### 配置文件


       - HTTP 客户端的配置文件


#
ssl policy policy1
pki-domain domain1
#
http
client ssl-policy policy1
client ssl-verify peer
#

return
