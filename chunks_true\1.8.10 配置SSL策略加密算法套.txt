

客户端与服务端之间进行认证时，会为 SSL 算法协商提供加密算法列表。本文介绍如何
配置加密算法套允许使用的加密算法，使用安全的算法可增强系统安全性。

##### 背景信息


客户端与服务端之间进行认证时，会为 SSL 算法协商提供加密算法列表。对于安全性要
求较高的系统，使用更安全的加密算法可增强系统安全性。

##### 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **ssl cipher-suite-list** customization-policy-name ，创建 SSL 策略加密算法套
并进入 SSL 算法套定制视图。


步骤 **3** 执行命令 **set cipher-suite** { **tls1_ck_rsa_with_aes_256_sha** |
**tls1_ck_rsa_with_aes_128_sha** | **tls1_ck_dhe_rsa_with_aes_256_sha** |
**tls1_ck_dhe_dss_with_aes_256_sha** | **tls1_ck_dhe_rsa_with_aes_128_sha** |
**tls1_ck_dhe_dss_with_aes_128_sha** | **tls12_ck_rsa_aes_128_cbc_sha** |
**tls12_ck_rsa_aes_256_cbc_sha** | **tls12_ck_rsa_aes_128_cbc_sha256** |
**tls12_ck_rsa_aes_256_cbc_sha256** | **tls12_ck_dhe_dss_aes_128_cbc_sha** |
**tls12_ck_dhe_rsa_aes_128_cbc_sha** | **tls12_ck_dhe_dss_aes_256_cbc_sha** |
**tls12_ck_dhe_rsa_aes_256_cbc_sha** | **tls12_ck_dhe_dss_aes_128_cbc_sha256** |
**tls12_ck_dhe_rsa_aes_128_cbc_sha256** | **tls12_ck_dhe_dss_aes_256_cbc_sha256** |
**tls12_ck_dhe_rsa_aes_256_cbc_sha256** |
**tls12_ck_rsa_with_aes_128_gcm_sha256** |
**tls12_ck_rsa_with_aes_256_gcm_sha384** |
**tls12_ck_dhe_rsa_with_aes_128_gcm_sha256** |
**tls12_ck_dhe_rsa_with_aes_256_gcm_sha384** |
**tls12_ck_dhe_dss_with_aes_128_gcm_sha256** |
**tls12_ck_dhe_dss_with_aes_256_gcm_sha384** |
**tls12_ck_ecdhe_rsa_with_aes_128_gcm_sha256** |


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 262


HUAWEI NetEngine40E
配置指南 1 基础配置


**tls12_ck_ecdhe_rsa_with_aes_256_gcm_sha384** | **tls13_aes_128_gcm_sha256** |
**tls13_aes_256_gcm_sha384** | **tls13_chacha20_poly1305_sha256** |
**tls13_aes_128_ccm_sha256** | **tls1_ck_ecdhe_ecdsa_with_aes_128_gcm_sha256** |
**tls1_ck_ecdhe_ecdsa_with_aes_256_gcm_sha384** } ，配置 SSL 策略加密算法套中支
持的加密算法。


说明


以下算法是弱安全的，不建议使用 : tls12_ck_dhe_dss_aes_128_cbc_sha ，
tls12_ck_dhe_dss_aes_128_cbc_sha256 ， tls12_ck_dhe_dss_aes_256_cbc_sha ，
tls12_ck_dhe_dss_aes_256_cbc_sha256 ， tls12_ck_dhe_rsa_aes_128_cbc_sha ，
tls12_ck_dhe_rsa_aes_128_cbc_sha256 ， tls12_ck_dhe_rsa_aes_256_cbc_sha ，
tls12_ck_dhe_rsa_aes_256_cbc_sha256 ， tls12_ck_rsa_aes_128_cbc_sha ，
tls12_ck_rsa_aes_128_cbc_sha256 ， tls12_ck_rsa_aes_256_cbc_sha ，
tls12_ck_rsa_aes_256_cbc_sha256 ， tls12_ck_rsa_with_aes_128_gcm_sha256 ，
tls12_ck_rsa_with_aes_256_gcm_sha384 ， tls12_ck_ecdhe_rsa_with_aes_128_gcm_sha256 ，
tls12_ck_ecdhe_rsa_with_aes_256_gcm_sha384 ， tls1_ck_dhe_dss_with_aes_128_sha ，
tls1_ck_dhe_dss_with_aes_256_sha ， tls1_ck_dhe_rsa_with_aes_128_sha ，
tls1_ck_dhe_rsa_with_aes_256_sha ， tls1_ck_rsa_with_aes_128_sha ，
tls1_ck_rsa_with_aes_256_sha 。


如果确实需要使用，请先执行 **undo crypto weak-algorithm disable** 命令使能弱安全算法功
能。


步骤 **4** 执行命令 **commit** ，提交配置。


**----**
结束
