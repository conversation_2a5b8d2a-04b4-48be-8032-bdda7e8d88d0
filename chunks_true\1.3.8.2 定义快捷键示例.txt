

如果登录的路由器上定义了快捷键，那么所有的用户均可以使用，不区分用户级别。

##### 组网需求


网络中的任意一台路由器。

##### 配置注意事项


如果用户没有快捷键里面定义的命令的使用权限，那么执行此快捷键后对应的命令不
生效。

##### 配置思路


采用如下思路配置定义快捷键的示例：


1. 定义快捷键 CTRL_U ，与命令 **display ip routing-table** 进行关联。


2. 在提示符 [~HUAWEI] 下键入快捷键“ Ctrl+U ”即显示。

##### 数据准备


为完成此配置例，需准备如下的数据：


       - 快捷键名


       - 需要关联快捷键的命令名

##### 操作步骤


步骤 **1** 定义快捷键 CTRL_U ，与命令 **display ip routing-table** 进行关联，并执行。


<HUAWEI> **system-view**

[ ~ HUAWEI] **hotkey ctrl_u "display ip routing-table"**

[*HUAWEI] **commit**


步骤 **2** 在提示符 [~HUAWEI] 下键入快捷键“ Ctrl+U ”即显示。


[ ~ HUAWEI] **display ip routing-table**
Route Flags: R - relay, D - download to fib, T - to vpn-instance, B - black hole route

-----------------------------------------------------------------------------
Routing Table: Public
Destinations : 8    Routes : 8
Destination/Mask  Proto Pre Cost   Flags NextHop     Interface
********/32  Direct 0  0      D 127.0.0.1    InLoopBack0
********/16  Direct 0  0      D ***********   GigabitEthernet0/0/0
***********/32  Direct 0  0      D 127.0.0.1    InLoopBack0
************/32  Direct 0  0      D 127.0.0.1    InLoopBack0
*********/8  Direct 0  0      D 127.0.0.1    InLoopBack0
127.0.0.1/32 Direct 0  0      D 127.0.0.1    InLoopBack0
***************/32 Direct 0  0      D 127.0.0.1    InLoopBack0
***************/32 Direct 0  0      D 127.0.0.1    InLoopBack0


**----**
结束


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 34


HUAWEI NetEngine40E
配置指南 1 基础配置

##### 配置文件


无
