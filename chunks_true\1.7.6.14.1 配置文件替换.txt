

多台设备同源管理场景下，使用此功能批量替换设备上的配置，实现多台设备配置一
致。

##### 应用环境


管理服务器管理节点设备场景下，管理服务器上存放了节点设备需要的配置，如果管
理服务器的配置发生变更，则相应节点设备的配置也需要同步修改，此时可以加载管
理服务器上的配置文件，替换当前设备上的配置，使节点设备上的配置与管理服务器
上配置保持一致。


配置相同的多台设备，如果其中一台设备的配置发生变更，为保持配置一致，其他设
备的配置需同步变更，此时可以加载配置发生变更的服务器上的配置，替换其他设备
上的配置文件，使所有设备上的配置保持一致。


此功能可替换当前设备上的整个配置文件，也可以只替换某个视图下的配置，这取决
于加载的源配置文件。如果加载的源文件中包含整个设备的配置，则替换当前设备上
的所有配置，如果加载的源文件是某个视图下保存的配置（视图下保存的配置自动携
带 <replace/> 标签），则替换相应视图下的配置。


此功能只能在两阶段生效模式下生效。

##### 操作步骤


步骤 **1** 保存配置文件。


       - 如果加载本地设备配置文件替换当前正在运行的配置，则在本地设备上执行以下
命令：

– 用户视图下执行命令 **save** [ configuration-file ] ，保存整个设备的配置文件。

– 业务视图下执行命令 **save** configuration-file ，保存相应视图下的配置文件。
执行完成后退出到用户视图。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 213


HUAWEI NetEngine40E
配置指南 1 基础配置


说明


加载本地配置文件替换当前设备配置时，本地配置文件必须存放在设备的根目录下。 **save**
命令默认保存配置文件至根目录。


       - 如果加载远端设备配置文件替换当前设备正在运行的配置，则在指定的远端设备
上执行以下命令：

– 用户视图下执行命令 **save** [ configuration-file ] ，保存整个设备的配置文件。

– 业务视图下执行命令 **save** configuration-file ，保存相应视图下的配置文件。
执行完成后退出到用户视图。


说明


非用户视图下执行本命令，必须指定 configuration-file 名称，且文件的扩展名只能是 *.zip 或者
*.cfg 。


步骤 **2** 加载配置文件，替换当前运行配置。


1. 执行命令 **system-view** ，进入系统视图。


2. 根据待加载的源配置文件所在位置不同，选择执行以下命令：

– 执行命令 **load configuration** **file** filename **replace** [ **relative** ] ，加载本地
设备上的配置文件，替换当前设备上正在运行的配置。

– 执行命令 **load configuration** { **server** ip-address | **server ipv6** ipv6address } [ **vpn-instance** vpn-instance-name ] **transport-type** { **ftp** |
**sftp** } **username** user-name **password** password **file** filename **replace**

[ **relative** ] ，加载指定的远端服务器上的配置文件，替换当前设备上正在运
行的配置。


–
执行命令 **load configuration** **server http url** url-address [ **vpn-instance**
vpn-instance-name ] [ **file** filename ] **replace** [ **relative** ] ，根据 URL 获取
远端服务器上的配置文件，替换当前设备上正在运行的配置。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 214


HUAWEI NetEngine40E
配置指南 1 基础配置


说明


指定的待加载配置文件必须存在，且必须满足以下条件：


– 配置文件中只能包含配置命令、视图切换命令、 # ，其他类型（如 display 查询命令、
reset/save/ping 等维护命令、 quit 、 commit 、 return 、升级兼容命令、一阶段命令
等）命令执行时设备会报错，并继续加载后续命令。


–
配置文件中的交互类型命令仅支持 Y/N 自动交互。


–
配置文件中命令的缩进要正确。系统视图下的命令需顶格，系统视图下的一级视图需
顶格，一级视图下的命令需缩进一个空格，多级视图依次缩进一格。


– # 号如果顶格，表示回退到系统视图；非顶格，则只是用来隔离命令块，但缩进需正
确，和其下方的命令块对齐。如果使用错误，可能会导致配置丢失或者命令在非预期
视图执行。


– 配置文件必须以 *.zip 、 *.cfg 、 *.txt 、 *.dat 、 *.bat 作为扩展名，或者不带扩展名。 FTP
或者 SFTP 模式下，支持带服务器目录文件名。文件名不包括特殊字符“ ~ ”“ ? ”
“         - ”“ / ”“ \ ”“ : ” “ " ” “ | ”“ < ”“         - ”“ [ ”“ ] ”。其中，

#### ▪ *.cfg 和 *.txt 为纯文本格式，可直接查看其内容。指定为待加载的配置文件后，替

换时系统对里面的命令逐条进行恢复。

#### ▪ *.zip 是 *.cfg 格式文件的压缩，占用空间较小。指定为待加载的配置文件后，系统

先将文件解压成 .cfg 格式，然后逐条恢复。 *.cfg 文件名必须和 *.zip 文件名一致，
否则会导致配置文件加载失败。

#### ▪ *.dat 是压缩文件格式，可以是二进制格式或者文本格式文件。只能执行从华为

设备导出的 *.dat 文件，且不能手工修改，否则会导致配置文件加载失败。

#### ▪ *.bat 是批处理文件，为纯文本格式文件，可以手工修改。

3. （可选）执行命令 **display configuration candidate** ，查看替换后的配置是否符
合预期：


– 如果符合预期，请继续执行步骤 d 。


–
如果不符合预期，请执行命令 **clear configuration candidate** 清除替换后的
内容，重新执行步骤 b 加载正确的配置文件。


4. 执行命令 **commit** ，提交配置。


**----**
结束

##### 检查配置结果

       - 使用命令 **display configuration replace failed** ，查看配置替换失败信息。

       - 使用命令 **display configuration replace file** ，查看配置替换后和目标配置的差
异内容。

