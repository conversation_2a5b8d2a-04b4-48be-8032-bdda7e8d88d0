# 目  录


**1** 接口与链路 **.................................................................................................................................. 1**


1.1 前 言 ............................................................................................................................................................................................. 1


1.2 接口管理配置 ............................................................................................................................................................................. 6


1.2.1 接口管理概述 ..........................................................................................................................................................................6


1.2.2 接口管理配置注意事项 ......................................................................................................................................................15


1.2.3 接口基础配置 ....................................................................................................................................................................... 20


******* 进入接口视图 ....................................................................................................................................................................20


1.2.3.2 （可选）配置接口参数 .................................................................................................................................................. 21


******* 打开接口 ............................................................................................................................................................................ 24


******* （可选）配置接口物理状态变化时向网管发送 Trap............................................................................................. 25


******* （可选）配置主接口 IPv4 和 IPv6 分类流量统计 ................................................................................................... 25


******* （可选）配置光放模块的功率锁定和增益锁定功能 .............................................................................................. 25


******* 配置 mp 接口或是 global-mp 接口的丢包率告警功能 ......................................................................................... 26


******* （可选）配置端口散列模式 ..........................................................................................................................................26


******* 检查配置结果 ....................................................................................................................................................................27


1.2.4 物理链路检测配置 .............................................................................................................................................................. 27


******* 配置接口 CRC 错误、 SDH 错误、接收错误、发送错误、光模块光功率异常告警功能 ...............................28


******* 配置接口接收 PAUSE 帧计数异常告警功能 ..............................................................................................................30


1.2.4.3 配置接口 SDH B1 、 SDH B2 错误告警功能 ..............................................................................................................31


1.2.4.4 配置接口接收坏包字节数越限告警功能 .................................................................................................................... 31


1.2.4.5 配置端口闪断告警功能 .................................................................................................................................................. 32


1.2.4.6 检查配置结果 ....................................................................................................................................................................32


1.2.5 配置 MAC Accounting 功能 .............................................................................................................................................33


1.2.6 配置 EVC 子接口的精确剪枝功能 ................................................................................................................................... 34


1.2.7 配置接口延迟发出信号 ......................................................................................................................................................35


1.2.8 配置接口统计计数缓存周期 ............................................................................................................................................. 35


1.2.9 配置全局接口流量突变告警阈值 .................................................................................................................................... 36


1.2.10 光模块激光器配置 ............................................................................................................................................................ 36


1.2.10.1 配置自动或手动关闭光模块激光器的发光功能 .................................................................................................... 37


1.2.10.2 （可选）配置手动打开光模块激光器发光功能 .................................................................................................... 37


1.2.10.3 检查配置结果 ................................................................................................................................................................. 38


1.2.11 使能光模块告警门限标准化 ...........................................................................................................................................38


1.2.12 配置光模块禁止告警检测 ............................................................................................................................................... 39


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 ii



HUAWEI NetEngine40E
配置指南 目 录


1.2.13 管理第三方光模块 ............................................................................................................................................................ 39


1.2.14 控制接口震荡配置 ............................................................................................................................................................ 40


1.2.15 逻辑接口配置 .....................................................................................................................................................................42


******** 创建全局虚拟以太网接口 ............................................................................................................................................43


******** 配置信道化子接口 .........................................................................................................................................................43


******** 创建 Loopback 接口并配置 IP 地址 ......................................................................................................................... 44


******** 进入 NULL 接口视图 .................................................................................................................................................... 44


******** 检查配置结果 ................................................................................................................................................................. 45


1.2.16 FlexE 接口配置 .................................................................................................................................................................. 45


******** 激活单板的灵活以太端口 License............................................................................................................................ 46


******** 配置标准 Ethernet 接口为灵活以太模式 ................................................................................................................46


1.2.16.3 配置 FlexE 物理接口的 PHY Number......................................................................................................................46


******** 创建 FlexE Group 并绑定 FlexE 物理接口 ..............................................................................................................47


1.2.16.5 配置 FlexE Group 的 Group ID................................................................................................................................. 48


1.2.16.6 （可选）配置 FlexE 卡的子时隙粒度 ...................................................................................................................... 48


1.2.16.7 （可选）配置 FlexE 卡的模式 ................................................................................................................................... 49


1.2.16.8 创建 FlexE Client 并配置 Client ID 和带宽 ............................................................................................................ 49


1.2.16.9 配置 Ethernet 业务场景下增加 FlexE 网元 ............................................................................................................ 59


********0 配置 FlexE 业务场景下新增 FlexE 或 Ethernet 网元 .........................................................................................61


********1 （可选）配置 FlexE 物理接口的时间同步模式 ...................................................................................................64


********2 检查配置结果 ...............................................................................................................................................................64


1.2.17 端口组配置 ......................................................................................................................................................................... 65


1.2.18 配置接口监控组 ................................................................................................................................................................ 66


1.2.19 配置去使能板间心跳联动端口状态表功能 .................................................................................................................68


1.2.20 维护接口 ............................................................................................................................................................................. 69


1.2.20.1 清除统计信息 ................................................................................................................................................................. 69


1.2.20.2 监控接口信息 ................................................................................................................................................................. 69


1.2.21 接口管理配置举例 ............................................................................................................................................................ 70


1.2.21.1 管理接口示例 ................................................................................................................................................................. 70


******** 配置 FlexE 接口示例 .....................................................................................................................................................71


1.3 端口扩展配置 ...........................................................................................................................................................................77


1.3.1 端口扩展配置注意事项 ......................................................................................................................................................78


1.3.2 建立端口扩展系统 .............................................................................................................................................................. 82


1.3.2.1 配置 Master 的基本功能 ............................................................................................................................................... 82


******* 在 Master 上配置 AP 的基本功能 ............................................................................................................................... 83


******* 配置端口扩展的认证方案 .............................................................................................................................................. 84


******* 配置内联接口 ....................................................................................................................................................................86


1.3.2.5 配置端口扩展接口 ........................................................................................................................................................... 88


******* （可选）配置端口扩展系统和外部路由互引 ........................................................................................................... 89


1.3.2.7 检查配置结果 ....................................................................................................................................................................90


1.3.3 AP 的升级和管理 .................................................................................................................................................................90


1.3.3.1 升级软件包 ........................................................................................................................................................................ 91


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 iii



HUAWEI NetEngine40E
配置指南 目 录


1.3.3.2 安装补丁 ............................................................................................................................................................................ 92


1.3.3.3 重启 AP...............................................................................................................................................................................93


******* 配置 AP 使用 SNMP 与网管通信 .................................................................................................................................94


1.3.3.5 配置 AP 向 syslog 服务器发送信息 .............................................................................................................................96


1.3.3.6 配置 AP 设备信息的时间格式 ...................................................................................................................................... 97


1.3.3.7 配置 AP 信息输出方式 ................................................................................................................................................... 98


******* 配置 AP 设备接口的流量统计时间间隔 ..................................................................................................................... 99


1.3.3.9 配置 AP 设备接口的带宽利用率告警阈值 .................................................................................................................99


1.3.4 维护端口扩展系统 ............................................................................................................................................................ 100


1.3.4.1 清除端口扩展接口统计信息 ....................................................................................................................................... 100


1.3.5 端口扩展配置举例 ............................................................................................................................................................ 101


******* 配置端口扩展系统示例 ................................................................................................................................................ 101


1.4 传输告警定制与抑制配置 .................................................................................................................................................. 111


1.4.1 传输告警定制与抑制概述 ............................................................................................................................................... 111


1.4.2 传输告警定制与抑制配置注意事项 ..............................................................................................................................112


1.4.3 配置传输告警的定制功能 ............................................................................................................................................... 112


1.4.3.1 配置可以影响接口的物理层状态的告警类型 .........................................................................................................112


1.4.3.2 （可选）配置 b1tca 、 b2tca 、 b3tca 、 sdbere 和 sfbere 的告警门限值 ........................................................114


1.4.3.3 检查配置结果 ................................................................................................................................................................. 114


1.4.4 配置传输告警的过滤时间间隔 ...................................................................................................................................... 115


1.4.5 配置传输告警的抑制功能 ............................................................................................................................................... 116


1.4.6 维护传输告警定制与抑制 ............................................................................................................................................... 117


1.4.6.1 查看误码速率值 ............................................................................................................................................................. 117


1.4.6.2 清除传输告警的运行信息 ............................................................................................................................................117


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 iv



HUAWEI NetEngine40E
配置指南 1 接口与链路

# 1 接口与链路


1.1 前 言


1.2 接口管理配置
用户通过对接口进行管理，便于更好地实现设备与设备间快速而准确地通信。


1.3 端口扩展配置
配置端口扩展，可以使设备用较少的槽位提供高密度的以太类接口。


1.4 传输告警定制与抑制配置
配置传输告警特性，可以降低网络中传输告警的频繁发生对网络稳定性的影响。

## 1.1 前 言

//#//#//#//#//# 概述


本文档介绍了接口与链路的基本概念、在不同应用场景中的配置过程和配置举例。

//#//#//#//#//# License 依赖


License 的详细信息，请查阅 License 使用指南。


       - 企业网用户：


– NE40E License 使用指南： **License** [使用指南](https://support.huawei.com/enterprise/zh/doc/EDOC1100194059)


– NE40E-X8AK License 使用指南： **License** [使用指南](https://support.huawei.com/enterprise/zh/doc/EDOC1100194062)

//#//#//#//#//# 产品版本


与本文档相对应的产品版本如下所示。

|产品名称|产品版本|
|---|---|
|HUAWEI NetEngine40E|V800R023C10SPC500|
|iMaster NCE-IP|V100R023C10SPC100|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 1



HUAWEI NetEngine40E
配置指南 1 接口与链路

//#//#//#//#//# 读者对象


本文档主要适用于以下工程师：


       - 数据配置工程师


       - 调测工程师


       - 网络监控工程师


       - 系统维护工程师

//#//#//#//#//# 安全声明


       - 受限公开声明


产品资料中主要介绍了您在使用华为设备时，在网络部署及维护时，需要使用的
命令。对用于生产、装备、返厂检测维修的接口、命令，不在资料中说明。


对于部分仅用于工程实施、定位故障的高级命令以及升级兼容命令，如使用不
当，将可能导致设备异常或者业务中断，建议较高权限的工程师使用。如您需
要，请向华为公司申请。


       - 加密算法声明


使用加密算法时， DES/3DES/RSA （ 3072 位以下 )/MD5 （数字签名场景和口令加
密） /SHA1 （数字签名场景）加密算法安全性低，存在安全风险，在协议支持的
加密算法选择范围内，建议使用更安全的加密算法，例如 AES/RSA （ 3072 位及以
上） /SHA2/HMAC-SHA2 。


出于安全性考虑，不建议使用不安全协议 Telnet 、 FTP 、 TFTP ；不建议使用特性
BGP 、 LDP 、 PCEP 、 MSDP 、 DCN 、 TCP-AO 、 MSTP 、 VRRP 、 E-trunk 、 AAA 、
IPSEC 、 BFD 、 QX 、端口扩展、 SSH 、 SNMP 、 IS-IS 、 RIP 、 SSL 、 NTP 、 OSPF 、
Keychain 中的弱安全算法。如果确实需要使用，请执行 undo crypto weakalgorithm disable 命令使能弱安全算法功能。详细步骤请参见《配置指南》。


出于安全性考虑，不建议使用该特性中的弱安全算法，若当前系统已关闭弱安全
算法功能，配置弱安全算法会提示 Error 信息。如果确实需要使用弱安全算法，请
先执行 **undo crypto weak-algorithm disable** 命令使能弱安全算法功能。


       - 密码配置声明


– 当密码加密方式为 cipher 时，输入以 %^%//#......%^%//# 为起始和结束符的合法
密文（本设备可以解密的密文）时，在设备上查看配置文件时显示的是和配
置相同的密文，请不要采用该方式直接配置密码。


–
为保证设备安全，请定期修改密码。


       - MAC 地址、公网 IP 地址使用的声明


– 出于特性介绍及配置示例的需要，产品资料中会使用真实设备的 MAC 地址、
公网的 IP 地址，如无特殊说明，出现的真实设备的 MAC 地址、公网的 IP 地址
均为示意，不指代任何实际意义。


– 因开源及第三方软件中自带公网地址（包括公网 IP 地址、公网 URL 地址 / 域
名、邮箱地址），本产品没有使用这些公网地址，这遵循业界实践，也符合
开源软件使用规范。


–
出于功能特性实现的需要，设备会使用如下公网地址


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 2



HUAWEI NetEngine40E
配置指南 1 接口与链路


表 **1-1** 公网地址列表

|公网地址|说明|
|---|---|
|http://www.huawei.com|华为官方网站地址|
|<EMAIL>|华为企业用户服务邮箱|



       - 个人数据声明


–
您购买的产品、服务或特性在业务运营或故障定位的过程中将可能获取或使
用用户的某些个人数据，因此您有义务根据所适用国家的法律制定必要的用
户隐私政策并采取足够的措施以确保用户的个人数据受到充分的保护。


–
废弃、回收或者再利用设备时，请注意根据需要备份或清除设备中的数据，
避免数据泄露的安全风险。如需支持，请联系售后技术支持人员。


       - 预置证书使用声明


在生产阶段预置于华为设备的华为证书是华为设备必备的出厂身份凭证，对其使
用声明如下：


–
华为预置证书仅用于部署阶段为设备接入客户网络建立初始安全通道以及设
备对接，华为不对预置证书的安全性做承诺与保证。


–
对于将华为预置证书作为业务证书使用而导致的安全风险和安全事件，由客
户自行处置并承担后果。


– 华为预置证书有效期自 2041 年起开始过期，可以通过 **display pki cert_list**
**domain default** 命令查看实际的有效期。


–
预置证书过期后，使用预置证书的业务会中断。


– 华为建议客户通过部署 PKI 系统对现网设备、软件签发证书并做好证书的生命
周期管理（为保证安全性推荐使用短有效期的证书）。


– 华为产品中用于产品入网初始化配置和连接时使用的华为 PKI 根证书支持禁用
（当验证华为新网元入网时，可配置重启该证书）。建议客户完成产品入网
配置并为产品配置客户 CA 签发的证书后，将该根证书禁用。对于客户未禁用
华为 PKI 根证书而带来的安全风险和安全事件，由客户自行处置并承担后果。


       - 产品生命周期政策


华为公司对产品生命周期的规定以“产品生命周期终止政策”为准，该政策的详
细内容请参见如下网址： **[https://support.huawei.com/ecolumnsweb/zh/](https://support.huawei.com/ecolumnsweb/zh/warranty-policy)**
**[warranty-policy](https://support.huawei.com/ecolumnsweb/zh/warranty-policy)** 。


       - 漏洞处理流程


华为公司对产品漏洞管理的规定以“漏洞处理流程”为准，该流程的详细内容请
参见如下网址： **[https://www.huawei.com/cn/psirt/vul-response-process](https://www.huawei.com/cn/psirt/vul-response-process)**


如企业客户须获取漏洞信息，请参见如下网址： **[https://](https://securitybulletin.huawei.com/enterprise/cn/security-advisory)**
**[securitybulletin.huawei.com/enterprise/cn/security-advisory](https://securitybulletin.huawei.com/enterprise/cn/security-advisory)**


       - 华为企业业务最终用户许可协议 (EULA)


本最终用户许可协议是最终用户（个人、公司或其他任何实体）与华为公司就华
为软件的使用所缔结的协议。最终用户对华为软件的使用受本协议约束，该协议
的详细内容请参见如下网址： **[https://e.huawei.com/cn/about/eula](https://e.huawei.com/cn/about/eula)** 。


       - 产品资料生命周期策略


华为公司针对随产品版本发布的售后客户资料（产品资料），发布了“产品资料
生命周期策略”，该策略的详细内容请参见如下网址： **[https://](https://support.huawei.com/enterprise/zh/bulletins-website/ENEWS2000017760)**


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 3



HUAWEI NetEngine40E
配置指南 1 接口与链路


**[support.huawei.com/enterprise/zh/bulletins-website/](https://support.huawei.com/enterprise/zh/bulletins-website/ENEWS2000017760)**
**[ENEWS2000017760](https://support.huawei.com/enterprise/zh/bulletins-website/ENEWS2000017760)** 。


       - 华为预置证书权责说明


华为公司对随设备出厂的预置数字证书，发布了“华为设备预置数字证书权责说
明”，该说明的详细内容请参见如下网址： **[https://support.huawei.com/](https://support.huawei.com/enterprise/zh/bulletins-service/ENEWS2000015766)**
**[enterprise/zh/bulletins-service/ENEWS2000015766](https://support.huawei.com/enterprise/zh/bulletins-service/ENEWS2000015766)** 。


       - 设备升级、打补丁的声明


对设备进行升级或打补丁操作时，请使用软件数字签名（ OpenPGP ）验证工具验
证软件。为避免软件被篡改或替换，防止给用户带来安全风险，建议用户进行此
项操作。


       - 特性声明


– NetStream 功能，出于对网络流量的统计管理，可能涉及对最终用户的通信
内容分析，建议您在所适用法律法规允许的目的和范围内方可启用相应的功
能。在采集、存储用户通信内容的过程中，您应采取足够的措施以确保用户
的通信内容受到严格保护。


–
镜像功能，可能基于运维目的需要对某些最终用户的通信内容进行分析，建
议您在所适用法律法规允许的目的和范围内方可启用相应的功能。在采集、
存储用户通信内容的过程中，您应采取足够的措施以确保用户的通信内容受
到严格保护。


–
报文头获取功能，出于检测通信传输中的故障和错误的目的，可能涉及采集
或存储个人用户某些通信内容。本公司无法单方采集或存储用户通信内容。
建议您只有在所适用法律法规允许的目的和范围内方可启用相应的功能。在
采集、存储用户通信内容的过程中，您应采取足够的措施以确保用户的通信
内容受到严格保护。


       - 可靠性设计声明


对于网络规划和站点设计，必须严格遵守可靠性设计原则，具备设备级和方案级
保护。设备级保护包括双网双平面，双机、跨板双链路的规划原则，避免出现单
点，单链路故障。方案级指 FRR 、 VRRP 等快速收敛保护机制。在应用方案级保护
时，应避免保护方案的主备路径经过相同链路或者传输，以免方案级保护不生
效。

//#//#//#//#//# 特别声明


       - 本文档中包含了 NE40E 支持的所有产品内容，如果需要了解在本国销售的设备或
单板等硬件相关信息，请查阅硬件描述章节。


       - 本手册仅作为使用指导，其内容依据实验室设备信息编写。手册提供的内容具有
一般性的指导意义，并不确保涵盖所有使用场景。因版本升级、设备型号不同、
板卡限制不同、配置文件不同等原因，可能造成手册中提供的内容与用户使用的
设备界面不一致。请以用户设备界面的信息为准，本手册不再针对前述情况造成
的差异一一说明。


       - 本手册中提供的最大值是设备在实验室特定场景（例如被测试设备上只有某种类
型的单板，或者只配置了某一种协议）达到的最大值。在现实网络中，由于设备
硬件配置不同、承载的业务不同等原因可能会使设备测试出的最大值与手册中提
供的数据不一致。


       - 本手册中出现的接口编号仅作示例，并不代表设备上实际具有此编号的接口，实
际使用中请以设备上存在的接口编号为准。


       - 本手册中的硬件照片仅供参考，具体请以发货的硬件实物为准。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 4



HUAWEI NetEngine40E
配置指南 1 接口与链路


       - 本手册中体现设备支持的相关硬件板卡，存在特定客户定制的需求，实际支持以
售前销售界面为准。


       - 出于特性介绍及配置示例的需要，产品资料中会使用公网 IP 地址，如无特殊说
明，资料里出现的公网 IP 地址均为示意，不指代任何实际意义。


       - 本手册中配置指南出现的“ XX 配置注意事项”，请结合产品的实际特性支持情况
来使用。


       - 本手册中的日志参考和告警参考，记录的是对应产品上注册的日志和告警信息。
实际应用中可触发的日志和告警，取决于当前产品所支持的业务功能。


       - 本文档中描述的所有设备尺寸数据均为设计尺寸，不包含尺寸公差。在部件制造
过程中，由于加工或测量等因素的影响，实际尺寸存在一定的偏差。

//#//#//#//#//# 符号约定


在本文中可能出现下列标志，它们所代表的含义如下。

//#//#//#//#//# 命令格式约定

|符号|说明|
|---|---|
||表示如不避免则将会导致死亡或严重伤害的具有高等<br>级风险的危害。|
||表示如不避免则可能导致死亡或严重伤害的具有中等<br>级风险的危害。|
||表示如不避免则可能导致轻微或中度伤害的具有低等<br>级风险的危害。|
||用于传递设备或环境安全警示信息。如不避免则可能<br>会导致设备损坏、数据丢失、设备性能降低或其它不<br>可预知的结果。<br>“须知”不涉及人身伤害。|
||对正文中重点信息的补充说明。<br>“说明”不是安全警示信息，不涉及人身、设备及环<br>境伤害信息。|


|格式|意义|
|---|---|
|粗体|命令行关键字（命令中保持不变、必须照输的部分）采用<br>加粗字体表示。|
|斜体|命令行参数（命令中必须由实际值进行替代的部分）采用<br>斜体表示。|
|[ ]|表示用“[ ]”括起来的部分在命令配置时是可选的。|
|{ x | y | ... }|表示从两个或多个选项中选取一个。|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 5



HUAWEI NetEngine40E
配置指南 1 接口与链路

//#//#//#//#//# 修订记录

|格式|意义|
|---|---|
|[ x | y | ... ]|表示从两个或多个选项中选取一个或者不选。|
|{ x | y | ... }*|表示从两个或多个选项中选取多个，最少选取一个，最多<br>选取所有选项。|
|[ x | y | ... ]*|表示从两个或多个选项中选取多个或者不选。|
|&<1-n>|表示符号&前面的参数可以重复1～n次。|
|//#|由“//#”开始的行表示为注释行。|



修改记录累积了每次文档变更的说明。最新版本的文档包含以前所有文档版本的更新
内容。

|产品版本|文档版本|发布日期|
|---|---|---|
|V800R023C10SPC500|02|2024-06-30|
|V800R023C10SPC500|01|2024-03-30|


## 1.2 接口管理配置


用户通过对接口进行管理，便于更好地实现设备与设备间快速而准确地通信。

### 1.2.1 接口管理概述


通过本章节，您可以了解到 NE40E 支持的各种物理接口、逻辑接口、接口视图和提示
符、常用的链路协议和接入技术等。

//#//#//#//#//# 接口类型


接口是设备与网络中的其他设备交换数据并相互作用的部件，分为物理接口和逻辑接
口两类。


       - 物理接口


物理接口是真实存在、有器件支持的接口。物理接口分为两种：


–
局域网接口：路由器可以通过它与局域网中的网络设备交换数据。


–
广域网接口：路由器可以通过它与远距离的外部网络设备交换数据。


       - 逻辑接口


逻辑接口是指能够实现数据交换功能但物理上不存在、需要通过配置建立的接
口。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 6



HUAWEI NetEngine40E
配置指南 1 接口与链路


须知


主控板上的管理网口不支持配置转发业务。

//#//#//#//#//# 接口视图和提示符


NE40E 支持的物理接口的命令视图和提示符如 表 **1-2** 所示，逻辑接口的命令视图和提示
符如 表 **1-3** 所示。


表 **1-2** NE40E 支持的物理接口命令视图和提示符






|接口名称|命令视图|进入<br>命令|提示符|
|---|---|---|---|
|千兆以太网接口|GE接口视图|在系<br>统视<br>图下<br>键入<br>**inter**<br>**face**<br>**giga**<br>**bitet**<br>**hern**<br>**et**<br>2/0/0|[~HUAWEI-<br>GigabitEthernet2/0/0]|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 7



HUAWEI NetEngine40E
配置指南 1 接口与链路






|接口名称|命令视图|进入<br>命令|提示符|
|---|---|---|---|
|10GE接口|10GE接口视图|在系<br>统视<br>图下<br>键入<br>**inter**<br>**face**<br>**giga**<br>**bitet**<br>**hern**<br>**et**<br>1/0/0<br>说明<br>通过<br>**disp**<br>**lay**<br>**inte**<br>**rfac**<br>**e**<br>**brie**<br>**f**命<br>令查<br>看到<br>带有<br>“1<br>0G<br>”标<br>识的<br>Giga<br>bitE<br>ther<br>net<br>接口<br>为<br>10G<br>带宽<br>的<br>Giga<br>bitE<br>ther<br>net<br>接<br>口。|[~HUAWEI-<br>GigabitEthernet1/0/0]|
|25GE接口|25GE接口视图|在系<br>统视<br>图下<br>键入<br>**inter**<br>**face**<br>**25GE**<br>2/0/0|[~HUAWEI-25GE2/0/0]|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 8



HUAWEI NetEngine40E
配置指南 1 接口与链路












|接口名称|命令视图|进入<br>命令|提示符|
|---|---|---|---|
|40GE接口|40GE接口视图|在系<br>统视<br>图下<br>键入<br>**inter**<br>**face**<br>**40GE**<br>2/0/0|[~HUAWEI-40GE2/0/0]|
|100GE接口|100GE接口视图|在系<br>统视<br>图下<br>键入<br>**inter**<br>**face**<br>**100G**<br>**E**<br>1/0/0|[~HUAWEI-100GE1/0/0]|
|200GE接口|200GE接口视图|在系<br>统视<br>图下<br>键入<br>**inter**<br>**face**<br>**200G**<br>**E**<br>1/0/0|[~HUAWEI-200GE1/0/0]|
|400GE接口|400GE接口视图<br>说明<br>该接口在NE40E-X8AK设备上<br>不支持。|在系<br>统视<br>图下<br>键入<br>**inter**<br>**face**<br>**400G**<br>**E**<br>1/0/0|[~HUAWEI-400GE1/0/0]|
|1200GE接口|1200GE接口视图|在系<br>统视<br>图下<br>键入<br>**inter**<br>**face**<br>**1200**<br>**GE**<br>1/0/0|[~HUAWEI-1200GE1/0/0]|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 9



HUAWEI NetEngine40E
配置指南 1 接口与链路




















|接口名称|命令视图|进入<br>命令|提示符|
|---|---|---|---|
|XGigabitEtherne<br>t接口|XGigabitEthernet接口视图|在系<br>统视<br>图下<br>键入<br>**inter**<br>**face**<br>**XGig**<br>**abitE**<br>**thern**<br>**et**<br>1/0/0|[~HUAWEI-<br>XGigabitEthernet1/0/0]|
|CPOS接口|CPOS接口视图|在系<br>统视<br>图下<br>键入<br>**contr**<br>**oller**<br>**cpos**<br>3/0/0|[~HUAWEI-Cpos3/0/0]|
|POS接口|POS接口视图|在系<br>统视<br>图下<br>键入<br>**inter**<br>**face**<br>**pos**<br>3/0/0|[~HUAWEI-Pos3/0/0]|
|OpticalAmplife<br>r接口|OpticalAmplifer接口视图<br>说明<br>该接口仅在X8A、X16A框上<br>支持。|在系<br>统视<br>图下<br>键入<br>**contr**<br>**oller**<br>**Optic**<br>**alAm**<br>**plife**<br>**r**<br>1/0/0|[~HUAWEI-<br>OpticalAmplifer1/0/0]|
|50GE接口|50GE接口视图|在系<br>统视<br>图下<br>键入<br>**inter**<br>**face**<br>**50GE**<br>1/0/0|[~HUAWEI-50GE1/0/0]|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 10



HUAWEI NetEngine40E
配置指南 1 接口与链路
















|接口名称|命令视图|进入<br>命令|提示符|
|---|---|---|---|
|50|100GE接口|50|100GE接口视图<br>说明<br>该类型接口默认速率是<br>50G，可以切换到100G。|在系<br>统视<br>图下<br>键入<br>**inter**<br>**face**<br>**50|**<br>**100G**<br>**E**<br>1/0/0|[~HUAWEI-50|<br>100GE1/0/0]|
|FlexE-50G接口|FlexE-50G接口视图|在系<br>统视<br>图下<br>键入<br>**inter**<br>**face**<br>**FlexE**<br>**-50G**<br>1/0/0|[~HUAWEI-<br>FlexE-50G1/0/0]|
|FlexE-100G接口|FlexE-100G接口视图|在系<br>统视<br>图下<br>键入<br>**inter**<br>**face**<br>**FlexE**<br>**-100**<br>**G**<br>1/0/0|[~HUAWEI-<br>FlexE-100G1/0/0]|
|FlexE-50|100G<br>接口|FlexE-50|100G接口视图|在系<br>统视<br>图下<br>键入<br>**inter**<br>**face**<br>**FlexE**<br>**-50|**<br>**100G**<br>1/0/0|[~HUAWEI-FlexE-50|<br>100G1/0/0]|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 11



HUAWEI NetEngine40E
配置指南 1 接口与链路











|接口名称|命令视图|进入<br>命令|提示符|
|---|---|---|---|
|FlexE-400G接口|FlexE-400G接口视图<br>说明<br>该接口在NE40E-X8AK设备上<br>不支持。|在系<br>统视<br>图下<br>键入<br>**inter**<br>**face**<br>**FlexE**<br>**-400**<br>**G**<br>1/0/0|[~HUAWEI-<br>FlexE-400G1/0/0]|
|FlexE-10G接口|FlexE-10G接口视图|在系<br>统视<br>图下<br>键入<br>**inter**<br>**face**<br>**FlexE**<br>**-10G**<br>1/0/0|[~HUAWEI-<br>FlexE-10G1/0/0]|


表 **1-3** 逻辑接口命令视图和提示符














|接口名称|命令视图|进入命令|提示符|
|---|---|---|---|
|子接口|子接口视图|在系统视图下<br>键入**interface**<br>**gigabitethern**<br>**et 1/0/0.1**|[~HUAWEI-<br>GigabitEthernet1/0/0.1]|
|Eth-Trunk接口|Eth-Trunk接口视图|在系统视图下<br>键入**interface**<br>**eth-trunk 2**|[~HUAWEI-Eth-Trunk2]|
|VE接口|VE接口视图|在系统视图下<br>键入**interface**<br>**virtual-**<br>**ethernet**<br>**1/0/0**|[~HUAWEI-Virtual-<br>Ethernet**1/0/0**]|
|Global-VE接<br>口|Global-VE接口视图|在系统视图下<br>键入**interface**<br>**global-ve 0**|[~HUAWEI-Global-VE0]|
|VLANIF接口|VLANIF接口视图|在系统视图下<br>键入**interface**<br>**vlanif 2**|[~HUAWEI-Vlanif2]|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 12



HUAWEI NetEngine40E
配置指南 1 接口与链路



|接口名称|命令视图|进入命令|提示符|
|---|---|---|---|
|Loopback接口|Loopback接口视图|在系统视图下<br>键入**interface**<br>**loopback 2**|[~HUAWEI-LoopBack2]|
|NULL接口|NULL接口视图|在系统视图下<br>键入**interface**<br>**null 0**|[~HUAWEI-NULL0]|
|IP-Trunk接口|IP-Trunk接口视图|在系统视图下<br>键入**interface**<br>**ip-trunk 2**|[~HUAWEI-Ip-Trunk2]|
|Tunnel接口|Tunnel接口视图|在系统视图下<br>键入**interface**<br>**tunnel 2**|[~HUAWEI-Tunnel 2]|
|NVE接口|NVE接口视图|在系统视图下<br>键入**interface**<br>**nve 1**|[~HUAWEI-Nve1]|
|FlexE接口|FlexE接口视图|在系统视图下<br>键入**interface**<br>**FlexE** 2/0/5|[~HUAWEI-FlexE2/0/5]|
|PW-VE接口|PW-VE接口视图|在系统视图下<br>键入**interface**<br>**pw-ve 1**|[~HUAWEI-pw-ve1]|
|ServiceIf接口|ServiceIf接口视图|在系统视图下<br>键入**interface**<br>**ServiceIf 1**|[~HUAWEI-ServiceIf1]|

//#//#//#//#//# 常用的链路层协议和接入技术





链路层负责无差错地将数据从一个站点发送到相邻的站点。它从网络层接收数据包，
然后将它封装到称为“帧”的数据单元里，再传给物理层，进行传输。


下面介绍 NE40E 支持的几种主要的链路层协议。


       - Ethernet


现在的局域网主要是指以太网。以太网是一种广播类型的网络。它因为灵活、简
单且易于扩展而被大规模应用


       - Trunk


Trunk 接口分为 Eth-Trunk 和 IP-Trunk 两种。 Eth-Trunk 只能由以太网链路构成。 IPTrunk 只能由 POS 链路构成。


Trunk 技术有如下优点：


–
增加带宽：接口的带宽是各成员接口带宽的总和。


–
提高可靠性：当某个成员链路出现故障时，流量会自动的切换到其他可用的
链路上，从而提供整个 Trunk 链路的可靠性。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 13



HUAWEI NetEngine40E
配置指南 1 接口与链路


       - PPP


点到点协议 PPP （ Point-to-Point Protocol ）是在串行链路上封装 IP 数据报文，既
支持数据位为 8 位且无奇偶校验的异步模式，也支持面向比特的同步链接。


PPP 主要包括建立、配置及测试数据链路的链路控制协议（ Link Control
Protocol ）和针对不同网络层协议的网络控制协议（ Network Control
Protocol ）。


       - HDLC


高级数据链路控制 HDLC （ High-Level Data Link Control ）是一组用于在网络节
点间传送数据的协议，是在数据链路层中最广泛使用的协议之一。


在 HDLC 中，数据单元（称为帧）通过网络发送，并由接收方确认收到。 HDLC 协
议也管理数据流和数据发送的间隔时间。

//#//#//#//#//# 抑制接口震荡


网络应用中，由于物理信号干扰、链路层配置错误等原因可能导致设备接口频繁地交
替出现 Up 和 Down 状态，造成路由协议、 MPLS 等反复震荡，对设备和网络产生较严重
影响，甚至可能造成部分设备瘫痪，网络不可使用。控制接口震荡特性对接口频繁
Up 、 Down 事件进行控制，使其小于一定的频率，以减小对设备及网络稳定性的影
响。


目前支持两种控制方式。


表 **1-4** 控制接口震荡功能






|功能|功能描述|选择原则|
|---|---|---|
|control-fap|对网络层以下接口频繁<br>Up/Down事件进行控制，<br>以减小对设备及网络稳定<br>性的影响。|●可以指定接口进行配<br>置；<br>●从网络层开始抑制向路<br>由管理模块通知接口震<br>荡，是专门针对网络层<br>稳定性的功能配置；<br>●用户可以根据业务需求<br>对参数进行精确配置；<br>●需要充分理解该算法，<br>才能配置，对用户要求<br>高，命令行易用性差。|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 14



HUAWEI NetEngine40E
配置指南 1 接口与链路



|功能|功能描述|选择原则|
|---|---|---|
|damp-interface|对物理层接口频繁Up/<br>Down事件进行控制，以<br>减小对设备及网络稳定性<br>的影响。|●该功能支持全局性配置<br>也支持指定接口进行配<br>置；<br>●从物理层开始抑制，这<br>样一旦接口物理震荡，<br>整个链路层和网络层都<br>能得到有效抑制；<br>●从物理层接口震荡进行<br>控制，可以防止上层业<br>务频繁的使能、去使<br>能，引发系统CPU、内<br>存等过度消耗；<br>●不需要了解复杂算法，<br>对用户要求低，命令行<br>易用性好。|

### 1.2.2 接口管理配置注意事项

//#//#//#//#//# 特性限制

表 **1-5** 本特性的使用限制










|特性限制|系列|涉及产品|
|---|---|---|
|LPUF-50/LPUF-50-L/LPUI-21-L/LPUI-51-L/<br>LPUF-51/LPUF-51-B/LPUI-51/LPUI-51-B/<br>LPUS-51/LPUF-101/LPUF-101-B/LPUI-101/<br>LPUI-101-B/LPUS-101单板不支持VE主接口的流<br>量统计，即对于slot VE无计数，对于Global VE不<br>会统计LPUF-50/LPUF-50-L/LPUI-21-L/LPUI-51-<br>L/LPUF-51/LPUF-51-B/LPUI-51/LPUI-51-B/<br>LPUS-51/LPUF-101/LPUF-101-B/LPUI-101/<br>LPUI-101-B/LPUS-101单板上成员口的计数。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 15



HUAWEI NetEngine40E
配置指南 1 接口与链路












|特性限制|系列|涉及产品|
|---|---|---|
|当L2VE接入L3VE，同时L2VE与Eth-trunk绑定相<br>同本地VSI时，Eth-trunk与L3VE之间如果要三层<br>互通，需要手工修改L3VE接口或Eth-trunk接口的<br>MAC地址，否则会导致二层网络MAC跳变。<br>具体配置方法如下：<br>在VE接口视图下面执行命令行mac-address<br><mac-address>修改Virtual-Ethernet接口的MAC<br>地址。<br>在slot视图下执行命令行set access-ve-mac<br><mac-address>修改L3VE接口的MAC地址。<br>在Eth-trunk接口视图下执行命令行mac-address<br><mac-address>修改Eth-trunk接口的MAC地址。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|
|设备配置slot-ve后slot-ve所在的slice不支持节<br>能；如果已经slice节能生效，再配置slot-ve，则<br>会唤醒节能的slice。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|
|vlanif接口双栈统计中不支持组播统计|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|
|LPUF-1TH单板单NP上一对Global-VE接口的转发<br>带宽最大为120Gbps。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|
|NP独立复位过程中VE口状态不会联动变化，依赖<br>VE口状态变化做切换的业务不生效。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 16



HUAWEI NetEngine40E
配置指南 1 接口与链路






|特性限制|系列|涉及产品|
|---|---|---|
|VE主接口统计不包含下行协议报文的统计。原<br>因：下行的协议报文在转发流程中是透传，转发<br>面无法统计。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|
|LPUF-50/LPUF-50-L/LPUI-21-L/LPUI-51-L/<br>LPUF-51/LPUF-51-B/LPUI-51/LPUI-51-B/<br>LPUS-51/LPUF-101/LPUF-101-B/LPUI-101/<br>LPUI-101-B/LPUS-101单板不支持VE主接口的流<br>量统计，即对于slot VE无计数，对于Global VE不<br>会统计LPUF-50/LPUF-50-L/LPUI-21-L/LPUI-51-<br>L/LPUF-51/LPUF-51-B/LPUI-51/LPUI-51-B/<br>LPUS-51/LPUF-101/LPUF-101-B/LPUI-101/<br>LPUI-101-B/LPUS-101单板上成员口的计数。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|
|在配置Trunk内联接口添加或删除成员接口时，需<br>要先将成员接口shutdown后，再进行添加或删除<br>操作，确保AP上的对应的Trunk成员接口已正确<br>添加或删除，再将成员接口undo shutdown，否<br>则可能导致业务流量丢失。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|
|网管上会将Master和AP作为两个独立的网元进行<br>管理。<br>用户在网管上看到的是Master和AP两个网元。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|
|Master和AP的软件版本必须一致，否则可能导致<br>AP上线后业务不可用。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|
|同一Trunk内联接口的成员接口，只能与同一AP<br>的内联接口绑定。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 17



HUAWEI NetEngine40E
配置指南 1 接口与链路












|特性限制|系列|涉及产品|
|---|---|---|
|不支持AP手动上线，建议用户不要在AP设备上手<br>动进行上线配置，否则可能导致AP上线失败或业<br>务异常。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|
|不支持通过Master给AP加载动态模块（对应<br>install-module命令）。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|
|Trunk内联接口仅支持手工负载分担模式。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|
|端口扩展模式和普通的虚拟接入模式不能直接切<br>换，必须去使能虚拟接入后，再配置端口扩展模<br>式。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|
|需保证inner-connect命令配置的接口绑定关系与<br>Master和AP之间的实际物理连接是一致的。否则<br>会导致业务不可用，并可能导致AP无法上线。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|
|不支持端口扩展接口与本地普通接口加入到同一<br>Eth-Trunk接口中。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 18



HUAWEI NetEngine40E
配置指南 1 接口与链路






|特性限制|系列|涉及产品|
|---|---|---|
|在虚拟接入端口扩展视图下，网络侧接口不支持<br>配置MPLS特性。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|
|在Master上创建端口扩展接口时必须指定与内联<br>接口或Trunk内联接口的绑定关系，目前不支持修<br>改此绑定关系，必须先删除端口扩展接口（此时<br>该端口扩展接口下的配置会被清除），再重新创<br>建端口扩展接口并指定新的绑定关系。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|
|对于IP地址冲突检测去使能和冲突抢占功能：<br>1、配置非抢占模式，对于同一IP地址，设备重启<br>后不保证和重启前选择的接口一致。<br>2、VRRP虚地址不支持配置冲突抢占功能。<br>3、当业务指定的源接口IP地址被抢占时，接口协<br>议状态为DOWN，会导致业务中断。<br>4、IP地址抢占生效后，由于接口切换会导致流量<br>短暂中断。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|
|display counters rate仅显示接口状态UP的接口<br>收发报文流量速率信息，状态DOWN的接口不显<br>示。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|
|接口固定索引的功能依赖device.sys文件，当<br>device.sys文件中的数据被删除时，无法保证设备<br>重启后接口的索引不变。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|
|display interface相关的查询接口信息的命令，不<br>支持显示离线的接口。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 19



HUAWEI NetEngine40E
配置指南 1 接口与链路



|特性限制|系列|涉及产品|
|---|---|---|
|如果设备上配置为set service-mode fe-mode<br>optimal模式，LPUF120A/LPUF240A/LPUF480B<br>单板不支持软环回，仅支持硬环回；<br>如果部署VE接口、全局VE接口同时在接口下配置<br>forward-mode through（包括已有配置或新配<br>置）时，配置不生效，实际按照forward-mode<br>loopback生效为性能折半模式，升级后可能由于<br>性能问题导致业务流量丢包|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|
|如果设备上配置为set service-mode fe-mode<br>optimal模式，LPUF120A/LPUF240A/LPUF480B<br>单板不支持软环回，仅支持硬环回；<br>如果部署PW VE接口同时在系统视图下配置<br>global-gre forward-mode through（包括已有配<br>置或新配置）时，配置不生效，实际按照global-<br>gre forward-mode loopback生效为性能折半模<br>式，升级后可能由于性能问题导致业务流量丢包|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|
|对于三层接口借用Loopback接口的IPv4地址功<br>能：<br>1、只支持借用后的IGP协议和IGP协议基础上的<br>BGP协议，用于远端的路由学习。<br>2、不支持三层接口借用Loopback口IPv4地址之<br>后的ARP学习、直连路由、直连路由基础上的<br>BGP协议，以及底层流量转发。因为此时同一IP<br>地址对应多个出接口，下一跳出口不确定<br>三层接口借用Loopback接口的IPv4地址功能|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|

### 1.2.3 接口基础配置




//#//#//#//#//# 应用环境 前置任务



通过了解常见接口类型、各种可配置的接口参数等概念，方便用户对接口进行管理。


为了保证网络中各设备间更好地通信，需要物理接口和逻辑接口配合使用，并且根据
不同的实际需求对各接口进行参数的配置，如配置描述信息、 MTU （ Maximum
Transfer Unit ）值、接口出入带宽利用率的告警阈值、流量统计时间间隔以及使能接
口协议状态变化时向网管发送 Trap 、控制接口震荡等功能。


在进行接口基础配置之前，需完成以下任务：


- 设备加电并正常启动。


#### ******* 进入接口视图

根据不同接口的物理特性，需要用不同的命令进入接口。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 20



HUAWEI NetEngine40E
配置指南 1 接口与链路

//#//#//#//#//# 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **interface** interface-type interface-number ，进入接口视图。


其中， interface-type 是接口类型， interface-number 是接口号。


步骤 **3** （可选）执行命令 **commit** ，提交配置。


如果接口不是被创建，只是进入接口视图，则不用执行 **commit** 。


**----**
结束

#### 1.2.3.2 （可选）配置接口参数


接口下可配置的参数需要根据实际的需求进行配置，包括是否需要配置描述信息、修
改 MTU 值等。

//#//#//#//#//# 背景信息


接口参数如 表 **1-6** 所示，请依据实际需要进行相应的选择。


表 **1-6** 接口参数







|参数类型|说明|
|---|---|
|接口描述信息|当需要维护的接口较多时，需要对接口进行描述，以便能够<br>准确快速地识别接口。|
|接口MTU值|通过配置接口MTU值，设备会根据接口MTU的大小，对该<br>接口发送的报文进行分片。<br>说明<br>Loopback接口和NULL接口不支持配置MTU。|
|设置网管在MIB上获<br>取的接口带宽|可以通过设置网管在MIB上获取的接口带宽大小来计算接口<br>的带宽利用率。|
|接口协议状态变化时<br>是否向网管发送Trap|使能接口协议状态变化时主动向网管端发送Trap功能，可以<br>方便网管实时监控接口状态。<br>但是当接口处于震荡状态时将频繁向网管发送Trap信息，会<br>显著增加网管设备的处理负担。此时可以关闭接口协议状态<br>变化发送Trap的功能。|
|流量统计时间间隔|设置统计接口流量的时间间隔，可以方便用户统计接口流量<br>和速率。|
|是否控制接口震荡|控制接口震荡特性对接口频繁Up、Down事件进行控制，使<br>其小于一定的频率，以减小对设备及网络稳定性的影响。<br>说明<br>Loopback接口和NULL接口等不支持使能控制接口震荡功能。|


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 21



HUAWEI NetEngine40E
配置指南 1 接口与链路






//#//#//#//#//# 操作步骤

|参数类型|说明|
|---|---|
|是否调整接口的差分<br>四相相移键控<br>(DQPSK)的模式|100G光接口使用的编码类型为DQPSK，当100G光接口对接<br>时，需要DQPSK的差分编码、解码模式相同才能对接成<br>功。当和其他设备100G光口第一次对接时，如果光口不<br>通，可以调整接口的DQPSK模式。一共有8种差分编码模<br>式，如图1所示。<br>图**1-1** 差分编码模式<br>|
|配置接口MSS值|网络中，NE40E是用户端和运营商端之间的中间设备。两端<br>在TCP握手过程中，会对后续TCP传输的MSS值进行协商。<br>握手成功后，两端建立了TCP连接，NE40E即会在两端之间<br>进行TCP报文的转发。由于NE40E在转发TCP报文时，可能<br>会封装隧道报文头，这样就使得TCP报文长度可能超过两端<br>TCP握手过程中协商出的MSS值，从而造成报文分片，降低<br>传输效率。NE40E上配置**tcp adjust-mss inbound**命令以<br>后，在两端TCP握手过程中协商MSS值时，会将SYN/SYN<br>+ACK报文中的MSS值与NE40E上**tcp adjust-mss inbound**<br>命令配置的MSS值进行比较。如果报文中的MSS值大于<br>NE40E配置的MSS值，则将报文中的MSS值修改为NE40E配<br>置的MSS值；否则，不做修改。|



步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **interface** interface-type interface-number ，进入相应的接口视图。


其中， interface-type 是接口类型， interface-number 是接口号。


步骤 **3** 配置接口参数，根据需要，可执行如 表 **1-7** 中的一个或多个操作。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 22



HUAWEI NetEngine40E
配置指南 1 接口与链路


表 **1-7** 配置接口参数






|接口参数|操作|
|---|---|
|配置接口描述信息|执行命令**description** regular-expression，配置接口的描述<br>信息。|
|配置接口MTU值|执行命令**mtu** mtu或者执行命令**ipv6 mtu** mtu，配置接口<br>的MTU。<br>执行命令**mtu** mtu**spread**或者执行命令**ipv6 mtu** mtu<br>**spread**，配置主接口的MTU并且扩散至子接口。<br>说明<br>●<br>使用**mtu**或者**mtu spread**命令改变POS接口的最大传输单元<br>MTU后，需要在接口视图下执行命令**shutdown**和**undo**<br>**shutdown**或只执行命令**restart**，重启接口以保证配置的MTU<br>生效。<br>●<br>如果接口上配置的是IPv4属性，请选择**mtu**命令或者**mtu**<br>**spread**，配置接口上发送IPv4报文时的MTU值。<br>●<br>如果接口上配置的是IPv6属性，请选择**ipv6 mtu**或者**ipv6 mtu**<br>**spread**命令，配置接口上发送IPv6报文时的MTU值。|
|设置接口的配置带宽|执行命令**bandwidth**，设置接口的配置带宽。<br>说明<br>网管可以通过IF-MIB中的ifSpeed和ifHighSpeed两个节点查看此配<br>置。<br>默认情况下，业务通常只使用物理带宽作为带宽参数进行协议选路<br>计算，可以通过**bandwidth-confg efect service enable**命令将<br>配置带宽作为带宽参数参与协议选路计算。|
|配置接口协议状态变<br>化时是否向网管发送<br>Trap|执行命令**enable snmp trap updown**，使能接口协议状态<br>变化时向网管发送Trap的功能。<br>说明<br>默认情况下接口协议状态变化自动向网管设备发送Trap功能是使能<br>的，当接口处于振荡状态时将频繁向网管发送Trap信息，会显著增<br>加网管设备的处理负担。此时可使用**undo enable snmp trap**<br>**updown**，关闭接口协议状态变化发送Trap的功能。|
|配置流量统计时间间<br>隔|执行命令**set fow-stat interval** interval，配置接口流量统<br>计时间间隔。<br>说明<br>●<br>若需要配置全局流量统计时间间隔，则直接在系统视图下进行<br>配置，不用执行命令**interface** interface-type interface-<br>number。配置全局流量统计时间间隔对所有没有设置接口流量<br>统计时间间隔的接口都有效，方便用户一次配置多个接口。<br>●<br>新的时间间隔将在原时间间隔超时后生效。针对逻辑接口，流<br>量显示将在新的时间间隔生效后第二个周期更新。针对物理接<br>口，流量显示立即更新。|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 23



HUAWEI NetEngine40E
配置指南 1 接口与链路







|接口参数|操作|
|---|---|
|配置是否控制接口震<br>荡|执行命令**control-fap** [ suppress reuse ceiling decay-ok<br>decay-ng ]，使能接口的控制接口震荡功能。<br>suppress是接口抑制门限的1000倍，取值范围是1～<br>20000，缺省值是2000，其值必须大于reuse值且小于<br>ceiling的值。<br>reuse是接口重用门限的1000倍，取值范围是1～20000，缺<br>省值是750，其值必须小于suppress的值。<br>ceiling是接口抑制惩罚值的最大值的1000倍，取值范围是<br>1001～20000，缺省值是6000，其值必须大于suppress的<br>值。<br>decay-ok是接口UP时，惩罚值半衰期。取值范围是1～900<br>秒，缺省值是54秒。<br>decay-ng是接口Down时，惩罚值半衰期。取值范围是1～<br>900秒，缺省值是54秒。|
|配置是否调整接口的<br>调制模式|执行命令**signal-coding** {**dqpsk** |**qpsk** }，配置接口的调<br>制模式。|
|配置接口MSS值|执行命令**tcp adjust-mss** mss-value**inbound**，配置TCP连<br>接中能够给对方发送的最大报文长度MSS值。|


步骤 **4** 执行命令 **commit** ，提交配置。


**----**
结束

#### ******* 打开接口


通常情况下，在设备上电时，初始化并启动各物理接口。

//#//#//#//#//# 背景信息


说明


该配置过程仅在 Admin-VS 支持。

//#//#//#//#//# 操作步骤


       - 缺省情况下，接口是打开的。


       - 若接口已被关闭，请执行如下操作重新打开接口。


a. 执行命令 **system-view** ，进入系统视图。


b. 执行命令 **interface** interface-type interface-number ，进入接口视图。


c. 执行命令 **undo shutdown** ，启动接口。


d. 执行命令 **commit** ，提交配置。


**----**
结束


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 24



HUAWEI NetEngine40E
配置指南 1 接口与链路

#### ******* （可选）配置接口物理状态变化时向网管发送Trap


使能接口物理状态变化时主动向网管端发送 Trap 功能，可以方便网管实时监控接口状
态。

//#//#//#//#//# 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **interface** interface-type interface-number ，进入相应的接口视图。


步骤 **3** 执行命令 **enable snmp trap physical-updown** ，使能接口物理状态变化时向网管发
送 Trap 的功能。


步骤 **4** 执行命令 **commit** ，提交配置。


**----**
结束

#### ******* （可选）配置主接口IPv4和IPv6分类流量统计


配置主接口的 IPv4 和 IPv6 分类流量统计，对所有物理主接口都有效。

//#//#//#//#//# 背景信息


在需要配置接口 IPv4 和 IPv6 分类流量统计的路由器上进行以下配置。

//#//#//#//#//# 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **interface** interface-type interface-number ，进入指定接口的接口视图。


步骤 **3** 执行命令 **statistic enable** ，使能指定主接口的 IPv4 和 IPv6 分类流量统计功能。


步骤 **4** 执行命令 **statistic mode** **forward** ，配置指定接口 IPv4 和 IPv6 分类流量统计。


步骤 **5** （可选）执行命令 **statistic accelerate enable** ，使能接口的统计加速功能。当主接口
使能双栈统计功能时，若想提升主接口转发性能，可以配置此命令。


步骤 **6** 执行命令 **commit** ，提交配置。


**----**
结束

#### ******* （可选）配置光放模块的功率锁定和增益锁定功能


配置光放模块的功率锁定和增益锁定，实现光功率的放大。

//#//#//#//#//# 背景信息


在需要配置光放大模块的功率锁定和增益锁定的路由器上进行以下配置。

//#//#//#//#//# 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 请根据实际情况选择以下配置。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 25



HUAWEI NetEngine40E
配置指南 1 接口与链路


       - 执行命令 **interface** interface-type interface-number ，进入指定接口的接口视
图。

       - 执行命令 **controller OpticalAmplifier** interface-number ，进入光放接口视图。


说明


OpticalAmplifier 接口只在 X8A ， X16A 框上支持。


步骤 **3** 执行命令 **work-mode** { **agc** agc-value | **apc** apc-value } ，配置光放模块通道的自动增
益锁定值和自动功率锁定值，当光放光模块只有 1 个光放时，可配置 0 号通道。


步骤 **4** 执行命令 **commit** ，提交配置。


**----**
结束

#### ******* 配置mp接口或是global-mp接口的丢包率告警功能


当需要配置 mp 接口或是 global-mp 接口的丢包率告警的产生门限和恢复门限时执行此
命令，系统会将产生的告警信息上送到网管系统。

//#//#//#//#//# 背景信息


当接口频繁丢包时，说明链路质量较差，可能无法保证正常业务的可靠传输。如果用
户想要监测链路的质量，可以使能 mp 接口或是 global-mp 接口的丢包率告警的产生门
限和恢复门限告警功能。

//#//#//#//#//# 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **interface** interface-type interface-number ，进入接口视图。


支持的接口类型为 mp 接口或是 global-mp 接口。


步骤 **3** 执行命令 **trap-threshold mp-lospkt-exc** **trigger-threshold** coefficient-value

power-value [ **resume-threshold** resume-coefficient-value resume-powervalue ] ，配置接口丢包率越限告警的门限值。


步骤 **4** 执行命令 **commit** ，提交配置。


**----**
结束

#### ******* （可选）配置端口散列模式


通过端口散列模式配置实现端口的带宽模式的切换。

//#//#//#//#//# 背景信息


通过端口散列模式配置实现端口的带宽模式的切换，增加组网灵活性，减少购置成
本。


说明


VS 模式下，该配置过程仅在 Admin VS 支持。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 26



HUAWEI NetEngine40E
配置指南 1 接口与链路

//#//#//#//#//# 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **port split dimension interface** { { ifName1 | ifType ifNum1 } [ **to**
{ ifName2 | ifType ifNum2 } ] } &<1–32> **split-type** splitType ，将端口进行散列命令
配置。


步骤 **3** 执行命令 **commit** ，提交配置。


**----**
结束

#### ******* 检查配置结果


管理接口的相关配置完成后，您可以查看到接口的状态信息、统计信息、控制接口震
荡等内容。

//#//#//#//#//# 操作步骤


       - 执行命令 **display interface** [ interface-type interface-number ] 可以查看接口的
状态信息和统计信息。

       - 执行命令 **display control-flap** **interface** interface-type interface-number ，可
以查看接口配置的控制接口震荡特性。


       - 执行命令 **display counters** [ **bit** ] [ **inbound** | **outbound** ] [ **interface**
interface-type [ interface-number ] ] [ **slot** slot-id ] ，查看接口的流量统计信
息。


       - 执行命令 **display counters** [ **bit** ] **rate** [ **inbound** | **outbound** ] [ **interface**
interface-type [ interface-number | **slot** slot-id ] | **slot** slot-id ] ，查看接口的流
量速率。


       - 执行命令 **display port split** 或 **display port split slot** ，查看端口的散列能力和散
列状态。


       - 执行命令 **display interface neighbor** [ interface-type interface-number | **slot**
slot-id [ **card** card-number ] ] ，查看设备上物理接口的邻居设备和邻居接口信
息。


       - 执行命令 **display interface description** [ interface-type [ interface-number ] |
**slot** slot-id [ **card** card-number ] ][ **full-name** ] ，查看接口的描述信息。


**----**
结束

### 1.2.4 物理链路检测配置


配置物理链路检测功能，可以避免由于链路上产生大量的错误告警时，使系统处理各
种告警而降低性能的情况发生。

//#//#//#//#//# 应用环境


避免当链路上产生大量的错误告警时，使系统处理各种告警而降低性能。通过设置告
警阈值，在超过阈值后将产生告警信息，采取必要的故障处理措施，保证正常业务流
量传输。

//#//#//#//#//# 前置任务


在配置物理链路检测之前，需完成以下任务：


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 27



HUAWEI NetEngine40E
配置指南 1 接口与链路


       - 路由器上电，自检正常。


       - 完成路由器接口物理属性的配置。

#### ******* 配置接口CRC错误、SDH错误、接收错误、发送错误、光模块光功率异常告警功能


本节介绍接口 CRC 错误、 SDH 错误、接收错误、发送错误、光模块光功率异常告警功
能的配置。

//#//#//#//#//# 背景信息


使能接口 CRC 错误、 SDH 错误、接收错误、发送错误、光模块光功率异常告警功能，
当超出设置的阈值，或从阈值以上降到阈值以下时，系统将产生告警信息。通过配置
系统处理接口告警的类型，以及告警上送网管的门限值和间隔时间，从而避免由于链
路上产生大量的错误告警时，使系统处理各种告警而降低性能的情况发生。

//#//#//#//#//# 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **snmp-agent trap enable port** { **crcexc-error** | **input-error** | **output-error**
| **sdh-error-rising** | **optical-module-abnormal** } ，使能接口告警功能。


此配置对所有支持此告警的物理端口生效。


请根据实际情况设置接口的告警类型。


       - 如果使能接口 CRC 错误阈值告警，设置阈值告警类型为 **crcexc-error** 。


       - 如果使能接口 SDH 错误阈值告警，设置阈值告警类型为 **sdh-error-rising** 。


       - 如果使能接口光模块功率异常告警功能，设置告警类型为 **optical-module-**
**abnormal** 。


VS 模式下，该命令仅在 Admin VS 支持。


步骤 **3** 执行命令 **interface** interface-type interface-number ，进入接口视图。


步骤 **4** 可以根据需要，配置不同类型的接口告警阈值与检测时间间隔。


       - 配置出、入带宽利用率的告警阈值：


–
执行命令 **trap-threshold** { **input-rate** | **output-rate** } bandwidth-in-use

[ **resume-rate** resume-threshold ] ，设置接口出、入带宽利用率的告警阈
值。


为了避免告警震荡， bandwidth-in-use 和 resume-threshold 的取值尽量保持
差距。

– 执行命令 **set flow-stat interval** interval ，配置接口流量统计时间间隔。


新的时间间隔将在原时间间隔超时后生效。针对逻辑接口，流量显示将在新
的时间间隔生效后第二个周期更新。针对物理接口，流量显示立即更新。


配置接口流量统计时间间隔只对本接口有效，方便查看某一接口的流量和速
率。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 28



HUAWEI NetEngine40E
配置指南 1 接口与链路


说明


可以配置全局流量统计时间间隔对所有没有设置接口流量统计时间间隔的接口都有
效，方便用户一次配置多个接口。在系统视图下执行命令 **set flow-stat interval**
interval ，接口流量统计时间间隔单位为秒。在接口下的统计时间间隔设置优先级会
高于全局下的统计时间间隔设置。


       - 配置 CRC 告警（以下两种方式任选其一）：


– 执行命令 **trap-threshold crc-error** threshold **interval-second** interval

[ **shutdown** ] ，配置在指定时间内 CRC 错误告警的门限值。


– 执行命令 **trap-threshold crc-error** **high-threshold** high-threshold **low-**
**threshold** low-threshold **interval-second** interval [ **shutdown** ] ，配置
CRC 错误告警的高门限值和低门限值以及检测时间间隔。


说明


可以在系统视图下执行命令 **trap-threshold** **slot** slot-id **card** card-id **crc-error** **high-**
**threshold** high-threshold **low-threshold** low-threshold **interval-second**
interval ，配置完成后将对该子卡上的所有端口生效。


       - 配置 SDH 告警（以下两种方式任选其一）：


– 执行命令 **trap-threshold sdh-error** threshold **interval-second** interval ，配
置在指定时间内 sdh 错误告警的门限值。


– 执行命令 **trap-threshold sdh-error** **high-threshold** high-threshold **low-**
**threshold** low-threshold **interval-second** interval ，配置 sdh 错误告警的高
门限值和低门限值以及检测时间间隔。


说明


可以在系统视图下执行命令 **trap-threshold** **slot** slot-id **card** card-id **sdh-error**
**high-threshold** high-threshold **low-threshold** low-threshold **interval-second**
interval ，配置完成后将对该子卡上的所有端口生效。


       - 配置 symbol 告警：


–
执行命令 **trap-threshold symbol-error** **high-threshold** high-threshold
**low-threshold** low-threshold **interval-second** interval ，配置 symbol 错误
告警的高门限值和低门限值以及检测时间间隔。


说明


可以在系统视图下执行命令 **trap-threshold** **slot** slot-id **card** card-id **symbol-error**
**high-threshold** high-threshold **low-threshold** low-threshold **interval-second**
interval ，配置完成后将对该子卡上的所有端口生效。


       - 配置接收发送告警（所有以太和 POS 接口支持）：


–
执行命令 **trap-threshold** { **input-error** | **output-error** } **high-threshold**
high-threshold **low-threshold** low-threshold **interval-second** interval ，
设置接口 input 错误告警和 output 错误告警。


说明


可以在系统视图下执行命令 **trap-threshold** **slot** slot-id **card** card-id { **input-error** |
**output-error** } **high-threshold** high-threshold **low-threshold** low-threshold
**interval-second** interval ，配置完成后将对该子卡上的所有端口生效。


       - 配置 bip8-sd 错误告警（仅 40GE/100GE 接口支持）：


– 执行命令 **trap-threshold** **bip8-sd** bip8-sd ，设置 40GE 和 100GE 接口 bip8-sd
错误告警阈值。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 29



HUAWEI NetEngine40E
配置指南 1 接口与链路


       - 配置 CRC 错包率超门限告警的产生门限和恢复门限 :


– 执行命令 **trap-threshold crc-error** **packet-error-ratio** **alarm-threshold**
alarm-coefficient-value alarm-power-value [ **resume-threshold** resumecoefficient-valueresume-power-value ] [ **trigger-lsp** | **trigger-section** ], 配
置 CRC 错包率超门限告警的产生门限和恢复门限。


       - 配置 CRC 错包率算法参数 :


–
执行命令 **crc-error packet-error-ratio algorithm-parameter** samplewindow-factor child-window-max-number child-window-alarm-number
child-window-resume-number, 设置 CRC 错包率算法参数。


–
执行命令 **crc-error packet-error-ratio algorithm-parameter realtime-**
**factor** template-number, 指定接口的 CRC 误码算法影响因素的模板号。


       - 配置 CRC 错误告警的百分比：


– 执行命令 **trap-threshold crc-error percent** percent-value ，配置 CRC 错误告
警的百分比。


步骤 **5** （可选）执行命令 **port-alarm down** { **crc-error** | **sdh-error** | **symbol-error** | **input-**
**error** | **output-error** | **bip8-sd** } ，使能错误告警联动接口物理 DOWN 。


说明


          - 路由器同时还支持在系统视图下执行命令 **port-alarm down** **slot** slot-id **card** card-id { **crc-**
**error** | **sdh-error** | **symbol-error** | **input-error** | **output-error** | **bip8-sd** } ，该配置将对该
子卡上的所有接口都生效。


          - 执行联动功能后，可以执行命令 **port-alarm clear** { **crc-error** | **sdh-error** | **symbol-error** |
**input-error** | **output-error** | **bip8-sd** } ，手动清除物理端口的产生的告警。


步骤 **6** 执行命令 **commit** ，提交配置。


**----**
结束

#### ******* 配置接口接收PAUSE帧计数异常告警功能


本节介绍接口接收 PAUSE 帧计数异常告警功能的配置。

//#//#//#//#//# 背景信息


连续三个检测时间间隔，当 pause-frame 错误报文条数达到指定高门限值时，系统将产
生告警信息上送到网管系统；连续三个检测时间间隔，当 pause-frame 错误报文条数降
到低门限值以下时，系统才将告警恢复信息上送到网管系统。

//#//#//#//#//# 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **interface** interface-type interface-number ，进入接口视图。


步骤 **3** 执行命令 **trap-threshold pause-frame** **high-threshold** high-threshold **low-**
**threshold** low-threshold **interval-second** interval ，配置接口接收 PAUSE 帧计数异常
告警阈值与检测时间间隔。


步骤 **4** 执行命令 **commit** ，提交配置。


**----**
结束


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 30



HUAWEI NetEngine40E
配置指南 1 接口与链路

#### 1.2.4.3 配置接口SDH B1、SDH B2错误告警功能


当需要基于接口接收的 SDH B1 、 SDH B2 错误报文数检测链路质量时，可以配置接口
SDH B1 、 SDH B2 错误告警功能。

//#//#//#//#//# 背景信息


当接口频繁产生大量 SDH B1 或 SDH B2 错误报文时，说明链路质量较差，可能无法保
证正常业务的流量传输。如果用户想要检测链路的质量，可以使能 SDH B1 或 SDH B2
错误告警功能。设备会每隔一段时间检测接口接收的 SDH B1 或 SDH B2 错误报文数，
当接口接收的 SDH B1 或 SDH B2 错误报文数达到一定的门限值，设备将产生告警信息
上送到网管系统，通知管理员进行接口维护和故障定位；当接口接收的 SDH B1 或 SDH
B2 错误报文数下降到一定的门限值，设备将产生告警恢复信息上送到网管系统，通知
管理员故障已恢复。

//#//#//#//#//# 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **interface** interface-type interface-number ，进入接口视图。


步骤 **3** 执行命令 **trap-threshold** { **sdh-b1-error** | **sdh-b2-error** } threshold **interval-**
**second** interval ，配置 SDH B1 、 SDH B2 错误告警的门限值和检测时间间隔。


步骤 **4** 执行命令 **commit** ，提交配置。


**----**
结束

#### 1.2.4.4 配置接口接收坏包字节数越限告警功能


当需要基于接口接收的坏包字节数检测链路质量时，可以配置接口接收坏包字节数越
限告警功能。

//#//#//#//#//# 背景信息


当接口频繁接收大量坏包时，说明链路质量较差，可能无法保证正常业务的可靠传
输。如果用户想要检测链路的质量，可以使能接口接收坏包字节数越限告警功能。设
备会每隔一段时间检测接口接收坏包字节数，当接口接收坏包字节数达到一定的门限
值，设备将产生告警信息上送到网管系统，通知管理员进行接口维护和故障定位；当
接口接收坏包字节数下降到一定的门限值，设备将产生告警恢复信息上送到网管系
统，通知管理员故障已恢复。

//#//#//#//#//# 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **snmp-agent trap enable port bad-bytes** ，使能接口接收坏包字节数越限
告警功能。


步骤 **3** 执行命令 **interface** interface-type interface-number ，进入接口视图。


步骤 **4** 执行命令 **trap-threshold bad-bytes** trap-threshold **interval-second** interval ，配置
接口接收坏包字节数越限告警的门限值和检测时间间隔。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 31



HUAWEI NetEngine40E
配置指南 1 接口与链路


步骤 **5** 执行命令 **commit** ，提交配置。


**----**
结束

#### 1.2.4.5 配置端口闪断告警功能


本节介绍端口闪断告警功能并配置端口闪断次数超限告警的产生和恢复阈值以及检测
周期。

//#//#//#//#//# 背景信息


当设备上配置了端口延时 Down 功能后，若链路出现闪断，此时可能会存在有些告警无
法上报的情况，若想解决此问题，可以使能端口闪断告警功能，配置此功能后，实现
周期内统计传输闪断次数并根据阈值上报告警。

//#//#//#//#//# 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **interface** interface-type interface-number ，进入接口视图。


步骤 **3** 执行命令 **carrier down-hold-time flapping** [ **interval** interval-value ] [ **alarm-**
**threshold** alarm-threshold-value **resume-threshold** resume-threshold-value ] ，配
置端口闪断告警功能并配置端口闪断次数超限告警的产生和恢复阈值以及检测周期。


说明


          - 对于 LAN 接口，需要在对应端口或者全局配置 **carrier down-hold-time** hold-time 命令后端
口闪断告警才会生效。

          - 对于 WAN 和 OTN 接口，需要在对应端口或者全局配置 **transmission-alarm holdoff-timer**
holdoff-timer 命令后端口闪断告警才会生效。


步骤 **4** 执行命令 **commit** ，提交配置。


**----**
结束

#### 1.2.4.6 检查配置结果


物理链路检测配置功能完成之后，可以查看接口配置及状态信息。

//#//#//#//#//# 背景信息


完成物理链路检测功能配置。

//#//#//#//#//# 操作步骤


       - 在接口视图下执行命令 **display trap-info** 或者在系统视图下执行命令 **display**
**trap-info** { interface-type interface-number | interface-name | **slot** slot-id
**card** card-id } ，查看接口告警使能状态、告警门限值、告警时间间隔、告警阻断
情况、当前告警状态、当前告警统计计数。


       - 接口视图下执行命令 **display port-error-info** **interface** { interface-type
interface-number | interface-name } 查看接口的错包或误码告警信息。


**----**
结束


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 32



HUAWEI NetEngine40E
配置指南 1 接口与链路

### 1.2.5 配置MAC Accounting功能


配置 Mac Accounting 功能，开启设备按照接口和流量报文 MAC 进行二三层流量统计的
功能。

//#//#//#//#//# 应用环境


图 **1-2** Mac Accounting 应用场景组网图


MAC Accounting 功能有两种典型应用场景：


       - 当设备作为 IXP 角色与其它运营商互连， IXP 连接的网络是其它运营商提供的网
络。当 ISP 借用其它多个运营商的网络，例如 Network1 和 Network2 ，这些其它运
营商按流量收取 ISP 的费用。此时可以在 IXP 的主接口上使能 MAC Accounting 特
性，来查看具体 MAC 的流量，获取对端某个 Router 上的具体流量，便于 ISP 用户分
析核对流量。


       - 当 IXP 受到 DDOS 攻击时，通过 MAC Accounting 特性可以查看具体 MAC 地址对应
设备的流量，由此可以了解到对端每个 Router 的具体流量，如果某条线路流量很
大，则可以初步确定攻击流量来自何处。

//#//#//#//#//# 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **interface** interface-type interface-number ，进入用户侧主接口视图。


步骤 **3** 执行命令 **mac accounting enable** ，使能 MAC Accounting 功能。


步骤 **4** 执行命令 **commit** ，提交配置。


**----**
结束


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 33



HUAWEI NetEngine40E
配置指南 1 接口与链路

//#//#//#//#//# 检查配置结果


完成配置后，可以执行 **display mac accounting** 命令，查看主接口或子接口 MAC
Accounting 统计数据。


当经过一段时间后，如需重新查看 MAC Account 统计数据，建议先执行 **reset mac**
**accounting** 命令，清除 MAC Accounting 统计数据。再执行 **display mac accounting**
命令查看统计数据，以保证查看的数据准确。

### 1.2.6 配置EVC子接口的精确剪枝功能


介绍 EVC 子接口的精确剪枝功能的基本配置。

//#//#//#//#//# 应用环境


在 BD 场景中，如果路由器同一主接口下两个子接口，其中一个封装方式为 dot1q ，另
一种封装方式为 default ，此时如果封装 dot1q 的子接口上有报文通过，封装 default 的
子接口可能也会转发出一份流量，造成回流的假象。同样，如果路由器上有两个主接
口，其中一个主接口配置了一个子接口，并在子接口上指定报文的封转方式为 dot1q ；
另一个主接口下配置了两个子接口，封装方式分别为 dot1q 和 default 。此时如果第一
个子接口中有流量通过，第二个主接口就同时也会转发出两份流量（两个子接口各转
发出去一份）。这种情况就可能会产生流量复制，导致资源浪费，降低了单板的转发
效率。为了使报文能够更加准确和精细的从指定的接口转发，可以使能精确剪枝功
能。

//#//#//#//#//# 前置任务


在配置接口的精确剪枝功能之前，需完成路由器接口物理属性的配置。

//#//#//#//#//# 操作步骤


       - 使能全局精确剪枝功能


a. 执行命令 **system-view** ，进入系统视图。

b. 执行命令 **ethernet egress-strict-filter enable** ，使能全局精确剪枝功能。


c. 执行命令 **commit** ，提交配置。


       - 使能 EVC 子接口下的流量剪枝功能


a. 执行命令 **system-view** ，进入系统视图。


b. 执行命令 **interface** interface-type interface-number.sub-interface-number
**mode l2** ，进入 EVC 子接口视图。

c. 执行命令 **ethernet egress-strict-filter enable** ，使能 EVC 子接口下的精确剪
枝功能。


d. 执行命令 **commit** ，提交配置。


说明


使能 EVC 子接口下的精确剪枝功能的优先级高于使能全局精确剪枝功能。即在 EVC 子
接口下配置了精确剪枝功能后，无论全局是否使能，系统都会优先按照 EVC 子接口下
的配置生效。


**----**
结束


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 34



HUAWEI NetEngine40E
配置指南 1 接口与链路

### 1.2.7 配置接口延迟发出信号


配置接口延迟发出信号功能，可有效减少因链路未同步切换或设备未完成配置恢复导
致的数据丢失。

//#//#//#//#//# 应用环境


设备重启或更换单板时，若接口初始化后立即发出信号，可能会因为链路未同步切换
或未完成配置恢复等造成数据丢失，这种情况下可以配置接口延迟发出信号功能。


说明


          - 接口延迟发出信号功能仅实际的物理接口支持，逻辑接口不支持该功能。


          - 在接口上配置延迟发出信号，对已经开始发送信号的接口没有影响，该配置会在接口下次初
始化时生效。

//#//#//#//#//# 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **interface** interface-type interface-number 或 **controller** interface-type
interface-number ，进入对应接口视图。


步骤 **3** 执行命令 **port-tx-enabling-delay** port-tx-delay-time ，配置接口延迟发出信号。


步骤 **4** 执行命令 **commit** ，提交配置。


**----**
结束

//#//#//#//#//# 检查配置结果


完成配置后，可以执行命令 **display port-tx-enabling-delay** 查看接口延迟发出信号的
延迟时间信息。

### 1.2.8 配置接口统计计数缓存周期


通过配置接口统计计数缓存周期，用户可实时获取最新的统计计数。

//#//#//#//#//# 应用环境


网管获取接口统计计数的方式有 get-next 模式和 get 模式。 get-next 模式下，设备收到
网管获取接口统计计数的请求后，会直接从缓存中获取接口统计计数，然后上报网
管。默认情况下，缓存中统计计数的刷新周期为 50 秒。


用户通过 MIB 表节点 ifTable 和 ifXTable 获取接口接收或者发送报文的统计计数时，可以
减小缓存周期，从而实现实时获取最新的统计计数。

//#//#//#//#//# 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **snmp-agent** ，使能 SNMP Agent 。


步骤 **3** 执行命令 **snmp-agent get-next-cache if-mib age-time** time-value ，配置网管通过
get-next 方式获取接口统计计数的缓存周期。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 35



HUAWEI NetEngine40E
配置指南 1 接口与链路


步骤 **4** 执行命令 **commit** ，提交配置。


**----**
结束

### 1.2.9 配置全局接口流量突变告警阈值


配置流量突变告警阈值，可以调整触发接口流量突变告警的接口流量突变百分比。

//#//#//#//#//# 应用环境


当流量突变并且用户期望感知此情况时，可以配置接口流量突变告警阈值，配置此阈
值后，当流量变化百分比超过配置的告警阈值时，设备会上报告警来提醒用户。用户
可以根据需要，自行设置流量突变告警阈值，但阈值不要设置的太小，若设置的太
小，设备会频繁上报告警。


接口流量变化百分比的公式如下：


接口流量变化百分比 = 当前流量统计周期和上一个流量统计周期的接口速率的差值的
绝对值 / 上一个流量统计周期的接口速率


接口流量统计周期可以通过 **set flow-stat interval** 命令配置，缺省情况下是 300 秒。

//#//#//#//#//# 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **set flow-change-ratio** { **input-threshold** | **output-threshold** } **upper-**
**limit** threshold ，配置接口流量突变告警阈值。


配置此命令后，如果用户打开了流量突变告警开关（通过 **snmp-agent trap enable**
**feature-name** **port trap-name hwinputratechangeoverthresholdnotice** 或 **snmp-**
**agent trap enable feature-name** **port trap-name**
**hwoutputratechangeoverthresholdnotice** 配置），当流量变化百分比超过配置的告
警阈值时，会产生如下两条告警：


       - PORT_1.3.6.1.4.1.2011.5.25.157.2.219
hwInputRateChangeOverThresholdNotice


       - PORT_1.3.6.1.4.1.2011.5.25.157.2.220
hwOutputRateChangeOverThresholdNotice


步骤 **3** 执行命令 **commit** ，提交配置。


**----**
结束

### 1.2.10 光模块激光器配置


本节介绍打开或关闭光模块激光器发光功能的配置。

//#//#//#//#//# 应用环境


当线路发生故障时，设备维护人员在进行定位或恢复故障时可能会被光模块发出的激
光伤害，这时可以配置自动或手动关闭光模块激光器发光功能，从而确保设备维护人
员的人身安全。设备维护人员排除故障后，光模块需要等待一段时间，才能恢复正常
工作状态。这时可以配置手动打开光模块激光器发光功能，立即使光模块激光器发
光，检测线路是否恢复正常，并恢复正常工作状态。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 36



HUAWEI NetEngine40E
配置指南 1 接口与链路

//#//#//#//#//# 前置任务


在配置光模块激光器之前，需完成以下任务：


       - 路由器上电，自检正常。


       - 光模块已在位，且接口没有被 shutdown 。

#### 1.2.10.1 配置自动或手动关闭光模块激光器的发光功能


通过配置关闭光模块激光器的发光功能，在线路故障时，可以及时关闭发光功能，避
免激光器发光给设备维护人员带来伤害。

//#//#//#//#//# 背景信息


当线路发生故障时，设备维护人员在进行定位或恢复故障时可能会被光模块发出的激
光伤害，这时可以使能光模块激光器自动关闭发光功能，当光模块在感知到线路故障
时自动关闭发光功能，或者手动关闭光模块激光器的发光功能，从而确保设备维护人
员的人身安全。

//#//#//#//#//# 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **interface** interface-type interface-number 或 **controller** interface-type
interface-number ，进入接口视图。


步骤 **3** 请根据需要，选择其中一种配置：


       - 配置自动关闭光模块激光器的发光功能。


a. 执行命令 **laser autoshutdown enable** ，使能光模块激光器自动关闭发光功
能。


b. ( 可选 ) 执行命令 **laser auto-shutdown-interval** { **open** opentime-interval |
**close** closetime-interval } ，配置光模块激光器自动关闭发光功能的开时间间
隔和关时间间隔。


       - 配置手动关闭光模块激光器的发光功能。

执行命令 **laser turn-off** ，配置手动关闭光模块激光器发光功能。


须知


在接口正常工作的状态下，请不要随意执行 laser turn-off ，执行此命令会将光模
块激光器发光功能关闭，接口上的业务会中断。


步骤 **4** 执行命令 **commit** ，提交配置。


**----**
结束

#### 1.2.10.2 （可选）配置手动打开光模块激光器发光功能


通过手工打开光模块激光器发光功能，可以及时检测线路故障是否排除。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 37



HUAWEI NetEngine40E
配置指南 1 接口与链路

//#//#//#//#//# 背景信息


当接口上配置了光模块激光器自动关闭发光功能时，如果线路发生故障，光模块激光
器会自动关闭发光功能。设备维护人员排除故障后，光模块需要等待一段时间，才能
恢复正常工作状态。这时手动打开光模块激光器发光功能，可以立即使光模块激光器
发光，检测线路是否恢复正常，并恢复正常工作状态。

//#//#//#//#//# 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **interface** interface-type interface-number 或 **controller** interface-type
interface-number ，进入接口视图。


步骤 **3** 执行命令 **laser turn-on** [ **duration** duration ] ，配置手动打开光模块激光器发光功
能，并指定激光器自动关闭发光功能后的长开持续时间。


只有执行命令 **laser autoshutdown enable** 后，配置的长开持续时间才能生效。


步骤 **4** 执行命令 **commit** ，提交配置。


**----**
结束

#### 1.2.10.3 检查配置结果


光模块激光器配置功能完成之后，可以查看光模块激光器的状态信息。

//#//#//#//#//# 前提条件


已完成光模块激光器的所有配置。

//#//#//#//#//# 背景信息


对激光器发光状态有影响的命令及操作包括：


       - 使用命令 **laser turn-on** 手动打开光模块激光器。

       - 使用命令 **laser turn-off** 手动关闭光模块激光器。


       - 使用命令 **laser autoshutdown enable** 使能激光器自动关闭功能。


       - 使用命令 **shutdown** 关闭接口。

//#//#//#//#//# 操作步骤


       - 在任意视图下执行命令 **display laser status** ，查看光模块激光器的状态。


**----**
结束

### 1.2.11 使能光模块告警门限标准化


通过配置光模块告警门限标准化，使设备上读取出来的光模块门限值符合用户需求。

//#//#//#//#//# 背景信息


由于系统自动读取光模块厂家设定的门限值并和现网实际收发功率进行比对，如果超
出范围，就会打印告警。但是光模块厂家设定的门限值有时不符合用户需求，需要变


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 38



HUAWEI NetEngine40E
配置指南 1 接口与链路


更告警门限的获取方式。使能光模块告警门限标准化功能后，光模块的门限统一使用
标准值，该标准值根据光模块的传输距离和带宽计算得出。


说明


当前版本针对光模块功率的告警有两种类型： warning 告警和 alarm 告警。 warning 告警是比较
轻微的告警，也可认为是预警。根据光模块的不同规格，功率处在预警值也可能正常工作。如果
用户对阈值不敏感，配置此命令就可以禁止 warning 预警检测。

//#//#//#//#//# 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **optical-module alarm-threshold standardization enable** ，使能光模块
告警门限标准化功能。


步骤 **3** 执行命令 **commit** ，提交配置。


**----**
结束

### 1.2.12 配置光模块禁止告警检测


通过配置光模块禁止告警检测，可以屏蔽光模块功率告警。

//#//#//#//#//# 背景信息


由于系统自动读取光模块厂家设定的阈值并和现网实际收发功率进行比对，如果超出
范围，就会打印告警。而由于现网光纤的光功率通常都大于光模块厂家设定的阈值，
而造成告警频繁。鉴于维护的困难，现网光纤上安装衰减器是不可行的。用户可以通
过配置此命令，控制设备上某个端口的光模块告警是否使能，比如用户不想收到光模
块接收功率高预警，可以关闭光模块接收功率高预警选项，其他告警还是可以正常上
报。


说明


当前版本针对光模块功率的告警有两种类型： warning 告警和 alarm 告警。 warning 告警是比较
轻微的告警，也可认为是预警。根据光模块的不同规格，功率处在预警值也可能正常工作。如果
用户对阈值不敏感，配置此命令就可以禁止 warning 预警检测。

//#//#//#//#//# 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **interface** interface-type interface-number ，进入接口视图。


步骤 **3** 执行命令 **port-alarm disable optical-module** { **rx-power-high-warning** | **rx-**
**power-low-warning** | **tx-power-high-warning** | **tx-power-low-warning** |
**voltage-high-warning** | **voltage-low-warning** } [*] ，配置光模块禁止告警检测。


步骤 **4** 执行命令 **commit** ，提交配置。


**----**
结束

### 1.2.13 管理第三方光模块


通过配置管理第三方光模块，可以屏蔽第三方光模块非认证告警并且联动端口 down 功
能。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 39



HUAWEI NetEngine40E
配置指南 1 接口与链路

//#//#//#//#//# 背景信息


当端口中插入第三方非华为公司认证的光模块时，系统会自动上报光模块非认证告
警，当用户不想看到此告警时，可以通过配置第三方光模块非认证告警上报功能，屏
蔽第三方光模块非认证告警，同时也可以将插入第三方模块的端口配置为 down ，禁止
用户使用。

//#//#//#//#//# 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **transceiver non-certified-alarm disable** ，关闭光模块非认证告警上报。

步骤 **3** 执行命令 **transceiver non-certified-alarm port-down enable** ，开启插入第三方光
模块的端口 down 功能。


步骤 **4** 执行命令 **commit** ，提交配置。


**----**
结束

### 1.2.14 控制接口震荡配置


简单介绍控制接口震荡的基本配置。

//#//#//#//#//# 应用环境


当接口频繁交替出现 Up 和 Down 状态造成路由协议、 MPLS （ Multiprotocol Label
Switching ）等反复震荡，影响整个网络的稳定性时，可以配置控制接口震荡特性，减
少接口的频繁交替出现 Up 和 Down 状态。


抑制接口震荡特性（ control-flap ）对接口频繁 Up 、 Down 事件进行控制，使其小于一
定的频率，以减小对设备及网络稳定性的影响。


接口震荡抑制的相关概念和原理请参考 抑制接口震荡 。

//#//#//#//#//# 前置任务


在配置控制接口震荡之前，需完成路由器接口物理属性的配置。

//#//#//#//#//# 操作步骤

       - 配置 control-flap 功能：

a. 执行命令 **system-view** ，进入系统视图。


b. 执行命令 **interface** interface-type interface-number ，进入接口视图。


说明


NULL 接口和 Loopback 接口不支持 control-flap 特性。

c. 执行命令 **control-flap** [ suppress reuse ceiling decay-ok decay-ng ] ，使能
接口的 control-flap 功能。


suppress 是接口抑制门限的 1000 倍，取值范围是 1 ～ 20000 ，缺省值是 2000 ，
其值必须大于 reuse 值且小于 ceiling 的值。


reuse 是接口重用门限的 1000 倍，取值范围是 1 ～ 20000 ，缺省值是 750 ，其值
必须小于 suppress 的值。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 40



HUAWEI NetEngine40E
配置指南 1 接口与链路


～
ceiling 是接口抑制惩罚值的最大值的 1000 倍，取值范围是 1001 20000 ，缺
省值是 6000 ，其值必须大于 suppress 的值。


decay-ok 是接口 Up 时，惩罚值半衰期。取值范围是 1 ～ 900 秒，缺省值是 54
秒。


decay-ng 是接口 Down 时，惩罚值半衰期。取值范围是 1 ～ 900 秒，缺省值是
54 秒。


d. 执行命令 **commit** ，提交配置。


       - 配置接口的 damp-interface 功能：


a. 执行命令 **system-view** ，进入系统视图。

b. （可选）执行命令 **interface** interface-type interface-number ，进入某种接
口视图。


c. 执行命令 **damp-interface enable** 命令使能 damp-interface 功能。

d. （可选）执行命令 **damp-interface level** { **light** | **middle** | **heavy** | **manual**
{ half-life-period suppress reuse max-suppress-time } } ，配置接口的
damp-interface 功能的抑制级别。

//#//#//#//# ▪ light ：接口震荡轻度抑制，只有接口频繁快速震荡的时候系统才会触发

抑制流程。该级别是系统默认抑制级别，主要是针对系统影响最大的接
口震荡场景。

//#//#//#//# ▪ heavy ：接口震荡重度抑制，只要系统识别接口状态开始震荡，即使接

口状态震荡不是很严重，也会触发接口状态的震荡抑制功能，即接口较
容易进入震荡抑制状态。该级别主要是针对接口震荡特别敏感的业务部
署场景，防止由于接口震荡导致业务受损以及系统资源紧张。

//#//#//#//# ▪ middle ：接口震荡中度抑制，抑制强度等级介于 light 和 heavy 之间。 ▪ manual ：当 light 、 middle 、 heavy 三个量级的抑制不能满足需求时，选

用此选项手动配置。

e. （可选）执行命令 **damp-interface mode tx-off** ，配置当接口为物理震荡抑
制状态时关闭发送信号。


在接口进入物理震荡抑制状态时，如果需要让对端感知本端接口不可用，可
以执行本命令关闭发送信号。


接口关闭发送信号后，对端接口会按照本端接口已经 Down 的状态进行处理。


当接口物理震荡抑制状态解除时，本端接口会重新自动开启发送信号，可以
让对端接口按照本端接口已经 Up 的状态进行处理。


f. 执行命令 **commit** ，提交配置。


**----**
结束

//#//#//#//#//# 检查配置结果


完成配置后，可以执行命令来检查功能的配置结果。


执行命令 **display control-flap** **interface** interface-type interface-number ，可以看
到接口配置的控制接口震荡特性。


执行命令 **display damp-interface** [ **interface** interface-type interface-number ] ，
可以查看接口物理状态震荡抑制特性的当前运行状态和统计信息。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 41



HUAWEI NetEngine40E
配置指南 1 接口与链路

### 1.2.15 逻辑接口配置


逻辑接口是指能够实现数据交换功能但物理上不存在、需要通过配置建立的接口。

//#//#//#//#//# 应用环境


逻辑接口的应用环境请参见《 NE40E 特性描述     - 接口管理》中“逻辑接口”


表 **1-8** 逻辑接口列表

























|所属分册|接口名称|配置指南|
|---|---|---|
|系统管理|DCN-Serial接口|该接口由系统自动生成，不涉及配置。|
|接口管理|VE接口（Virtual<br>Ethernet，虚拟以太<br>网接口）|创建L2VE接口<br>创建L3VE接口|
|接口管理|Global-VE接口<br>（Global Virtual<br>Ethernet，全局虚拟<br>以太网接口）|创建全局虚拟以太网接口|
|接口管理|Loopback接口（环<br>回接口）|创建**Loopback**接口并配置**IP**地址|
|接口管理|NULL0接口|进入**NULL**接口视图|
|局域网与城域<br>网接入|以太网子接口|配置以太网子接口支持VLAN间的通信|
|局域网与城域<br>网接入|ETH-Trunk接口|Eth-Trunk接口配置|
|局域网与城域<br>网接入|VLANIF接口|配置VLANIF接口实现三层互通|
|广域网接入|IP-Trunk接口|配置IP-Trunk接口|
|广域网接入|POS-Trunk接口|配置POS-Trunk接口|
|广域网接入|CPOS-Trunk接口|创建CPOS-Trunk接口|
|广域网接入|MP-Group接口|配置MP|
|广域网接入|Global-MP-Group<br>接口|创建Global-MP-Group接口并添加成员接<br>口|
|广域网接入|IMA-Group接口|ATM IMA配置|
|广域网接入|Global-IMA-Group<br>接口|创建Global-IMA-Group接口并添加成员接<br>口|
|MPLS|Tunnel接口|MPLS TE配置|


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 42



HUAWEI NetEngine40E
配置指南 1 接口与链路

//#//#//#//#//# 前置任务


在配置逻辑接口之前，需完成以下任务：


       - 连接接口并配置接口的物理参数，使接口的物理层状态为 Up 。

#### ******** 创建全局虚拟以太网接口


全局虚拟以太网接口不依赖于物理接口板，只要路由器上电，就可以创建全局虚拟以
太网接口。

//#//#//#//#//# 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **interface global-ve** ve-number ，创建并进入全局虚拟以太网接口视图。


步骤 **3** 执行命令 **commit** ，提交配置。


**----**
结束

#### ******** 配置信道化子接口


介绍信道化子接口的配置。

//#//#//#//#//# 背景信息


为避免不同业务之间相互影响，需要有一种能够隔离不同类型业务的机制。不同业务
流量可以归属到不同的 Dot1q 封装方式的 VLAN 信道化子接口上，每个信道化子接口可
以实现独立的 HQoS 调度，从而实现不同类型业务之间的隔离。

//#//#//#//#//# 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **interface** interface-type interface-number.subinterface-number ，进入指
定物理子接口。


步骤 **3** 按照需要配置对应的子接口：


       - 对于以太子接口，


– 执行命令 **vlan-type dot1q** vlanid ，配置子接口的封装方式为 Dot1q 方式。


–
执行命令 **encapsulation qinq-termination** ， **qinq termination pe-vid** pevlanid **ce-vid** ce-vlanid ，配置子接口的封装方式为 QinQ 终结方式。


       - 对于 POS 子接口，执行命令 **fr dlci** dlci-value ，配置子接口的封装方式为 POS FR
方式。该命令依赖于 POS 主接口的配置： **link-protocol fr** 。


步骤 **4** 执行命令 **quit** ，退回系统视图。


步骤 **5** 执行命令 **license** ，进入 License 视图。


说明


GE 接口无需激活信道化子接口的 License 。


步骤 **6** 执行命令 **active port-mode-channel** ，激活设备信道化子接口的 License 。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 43



HUAWEI NetEngine40E
配置指南 1 接口与链路


步骤 **7** 执行命令 **quit** ，退回系统视图。


步骤 **8** 执行命令 **interface** interface-type interface-number.subinterface-number ，进入指
定物理子接口。


步骤 **9** 执行命令 **mode channel enable** ，使能子接口信道化功能。


步骤 **10** （可选）执行命令 **mode channel bandwidth** bwvalue ，配置信道化子接口的带宽。


步骤 **11** 执行命令 **commit** ，提交配置。


**----**
结束

#### ******** 创建Loopback接口并配置IP地址


配置 Loopback 接口时一般都会为其配置 IP 地址，利用其一直处于 Up 状态的特点与其他
设备进行通信。

//#//#//#//#//# 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **interface loopback** loopback-number ，创建并进入 Loopback 接口。


用户可以创建或删除 Loopback 接口。 Loopback 接口一旦被创建，除了在 Loopback 接
口监视接口监控组场景中， Loopback 接口可能会联动接口监控组状态变为 Down ，其
他情况下其链路层协议状态将一直是 Up 。


步骤 **3** 执行命令 **ip address** ip-address { mask | mask-length } ，配置 Loopback 接口的 IP 地
址。


步骤 **4** 执行命令 **commit** ，提交配置。


**----**
结束

#### ******** 进入NULL接口视图


系统自动创建一个 NULL0 接口，不需要手工创建。 NULL 接口主要用于防止路由环路和
过滤流量。

//#//#//#//#//# 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **interface null** **0** ，进入 NULL 接口视图。


NULL 接口的状态一直是 Up ，但不能转发数据包，也不能配置 IP 地址或封装其他协议。


**----**
结束

//#//#//#//#//# 后续处理


对于 NULL 接口，主要用于防止路由环路和过滤流量，例如：在系统视图下配置命令 **ip**
**route-static *********** *********** NULL 0** ，当前设备会丢弃所有去往网段

～
*********** *************** 的报文。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 44



HUAWEI NetEngine40E
配置指南 1 接口与链路

#### ******** 检查配置结果


配置 Global-VE 接口、 FlexE 接口 license 、 FlexE 接口、 Loopback 接口和 NULL 接口后，
需要检查配置结果，保证配置正确。

//#//#//#//#//# 前提条件


已经完成 Global-VE 接口、 FlexE 接口 license 、 FlexE 接口、 Loopback 接口或 NULL 接口
的所有配置。

//#//#//#//#//# 操作步骤


       - 使用 **display interface global-ve** [ ve-number ] 命令查看 Global-VE 接口的状态
信息。

       - 使用 **display license resource usage port-flexe** { **all** | **slot** slot-id } [ **active** |
**deactive** ] 命令查看单板的灵活以太端口 License 。


       - 使用 **display license resource usage port-mode-channel** { **all** | **slot** slotid }

[ **active** | **deactive** ] 命令查看单板上的信道化子接口 License 的授权情况。


       - 使用 **display interface loopback** [ loopback-number ] 命令查看 Loopback 接口的
状态信息。


       - 使用 **display interface null** [ **0** ] 命令查看 NULL 接口的状态信息。

       - 使用 **display flexe group information** **slot** slot-id **card** card-id 命令查看 FlexE 子
卡上的 Group 信息、加入 Group 的 FlexE 物理口信息和 Group 内时隙分配情况。


**----**
结束

### 1.2.16 FlexE接口配置


FlexE 接口就是指 FlexE Client ，对应于网络中外在观察到的各种用户接口，每个 FlexE
Client 可以灵活的从 Group 资源池分配带宽，同时可以进行带宽调整。 VS 模式下，该特
性仅在 Admin VS 支持。

//#//#//#//#//# 应用环境


随着 5G 的建设，网络发展对移动承载带宽提出更高的需求，同时用户也希望通过统一
的网络来承载各种不同的业务，包括家庭宽带业务、专线接入业务、移动承载等，这
些需求对电信网络接口也提出了更高的要求。 FlexE 技术通过接口带宽隔离，即可以实
现业务隔离。 FlexE 接口之间可以完全隔离互不影响，流量在物理层隔离，业务在同一
张物理网络上进行网络分片。


FlexE 技术可应用在接入层、汇聚层、核心层，随着 5G 业务的起步、发展、成熟，其业
务量是逐步增长的，承载网可以通过 FlexE 进行平滑升级。

//#//#//#//#//# 前提条件


在配置 FlexE 接口之前，需完成以下任务：


       - 设备上电，自检正常。


       - 设备上已有支持 FlexE 功能的单板。


       - 激活单板的灵活以太端口 License 。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 45



HUAWEI NetEngine40E
配置指南 1 接口与链路

#### ******** 激活单板的灵活以太端口License


如果要配置灵活以太业务，必须先激活单板的灵活以太端口 License 。

//#//#//#//#//# 前置任务


在激活单板的灵活以太端口 License 之前，需要完成以下任务：


1. 执行命令 **license active** file-name ，激活主控板上的 License 文件。


2. 执行命令 **system-view** ，进入系统视图。


3. 执行命令 **license** ，进入 License 视图。


4. 执行命令 **active port-basic** **slot** slotid **card** cardid **port** portlist ，激活端口基本
硬件 License 。

//#//#//#//#//# 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **license** ，进入 License 视图。


步骤 **3** 执行命令 **active port-flexe** **slot** slotid **card** cardid **port** portlist ，激活单板的灵活以
太端口 License 。


步骤 **4** 执行命令 **commit** ，提交配置。


**----**
结束

#### ******** 配置标准Ethernet接口为灵活以太模式


标准 Ethernet 接口的带宽是固定的，如果需要实现接口带宽可灵活指定，就需要将接
口从标准以太模式切换为灵活以太模式。

//#//#//#//#//# 背景信息


标准 Ethernet 接口切换为灵活以太模式后，系统会自动创建 FlexE 物理接口，原先的标
准 Ethernet 接口会被删除，包括该物理接口上的已配置业务也会被同时删除。

//#//#//#//#//# 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **flexe enable port** posStr ，将 Ethernet 接口从标准以太模式切换为灵活以太
模式。


步骤 **3** 执行命令 **commit** ，提交配置。


**----**
结束

#### 1.2.16.3 配置FlexE物理接口的PHY Number


为保证对接的两端设备正常通信，用户需要在两端设备的 FlexE 物理接口上分别配置相
同的 PHY Number 值。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 46



HUAWEI NetEngine40E
配置指南 1 接口与链路

//#//#//#//#//# 背景信息


不同的 FlexE 物理接口可以配置相同的 PHY Number 值，但是具有相同 PHY Number 值
的 FlexE 物理接口不能加入同一个 FlexE Group ，且同一个 FlexE 物理接口只能加入一个
FlexE Group 。

//#//#//#//#//# 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **interface** interface-type interface-number ，进入 FlexE 物理接口视图，例如
FlexE-50G 1/1/1 接口。


步骤 **3** 执行命令 **phy-number** phyNum ，配置 FlexE 物理接口的 PHY Number 值。


步骤 **4** （可选）执行命令 **management-channel mode** { **union** | **section** | **shim-to-shim** |
**shim-to-shim-op2** } ，配置 FlexE 物理接口的管理通道模式。


步骤 **5** （可选）执行命令 **down-filter disable** ，关闭 FlexE 物理接口的 Down 中断抑制功能。


步骤 **6** （可选）执行命令 **switch-mode** { **manual** | **auto** } ，配置 FlexE 物理接口在手动模式
和光模块自适应模式之间切换。


说明


该命令只支持在 2 端口 50G 子卡的 0 号端口配置。


步骤 **7** （可选）执行命令 **port-speed** { **50GE** | **100G** } ，配置 FlexE 物理接口在 50GE 和 100GE
模式之间切换。


说明


该命令只支持在 2 端口 50G 子卡的 0 号端口配置。


步骤 **8** 执行命令 **commit** ，提交配置。


**----**
结束

#### ******** 创建FlexE Group并绑定FlexE物理接口


创建 FlexE Group 后，用户可以绑定一组 FlexE 物理接口到 Group ，按照子时隙粒度，为
FlexE Client 灵活分配带宽。

//#//#//#//#//# 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **flexe group** group-index ，创建 FlexE Group 或者进入指定已创建的 FlexE
Group 视图。


步骤 **3** （可选）执行命令 **description** text ，配置 FlexE Group 的描述信息。


为了方便记忆和管理，可以配置此步骤用来对特定的 FlexE Group 进行详细描述。


步骤 **4** 执行命令 **binding interface** interface-type interface-number ，绑定 FlexE 物理接口到
FlexE Group 。


步骤 **5** （可选）执行命令 **padding enable** ，使能 padding 功能。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 47



HUAWEI NetEngine40E
配置指南 1 接口与链路


步骤 **6** （可选）执行命令 **timeslot-negotiation mode disable** ，去使能时隙协商模式。


说明


针对 FlexE Group 中绑定的 PHY 所在的 FlexE 卡上配置的是时隙模式，在去使能时隙协商模式前，
需要保证当前 Group 下的 FlexE Client 没有绑定子时隙。


          - 对接设备不支持时隙协商模式时，需要在本端 FlexE Group 视图下配置时隙协商去使能。


          - 对接设备两端的 FlexE Group 上，要求配置相同的时隙协商模式，否则在执行可靠性操作
（如主备倒换、复位子卡、 **shutdown** / **undo shutdown** 端口等）之后可能导致 FlexE 接口不
up 或流量不通。


          - 对接设备两端的 FlexE Group 都配置时隙协商去使能后，两端对应的 FlexE Client 需要配置完
全相同的时隙编号，否则可能导致 FlexE 接口不 up 或流量不通。


步骤 **7** 执行命令 **commit** ，提交配置。


**----**
结束

#### 1.2.16.5 配置FlexE Group的Group ID


为保证对接的两端设备正常通信，用户需要将两端设备上 FlexE 物理接口加入的 FlexE
Group 配置相同的 Group ID 。

//#//#//#//#//# 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **flexe group** group-index ，创建 FlexE Group 或者进入指定已创建的 FlexE
Group 视图。


步骤 **3** 执行命令 **flexe-groupnum** groupNum ，配置 FlexE Group 的 Group ID 值。


步骤 **4** 执行命令 **commit** ，提交配置。


**----**
结束

#### 1.2.16.6 （可选）配置FlexE卡的子时隙粒度


FlexE 卡的子时隙粒度约束着 FlexE Client 的带宽配置，缺省情况下， FlexE 卡的子时隙
粒度为 5GE 。

//#//#//#//#//# 背景信息


如果 FlexE Client 需要配置小于 5GE 的带宽，就需要先配置 FlexE 卡的子时隙粒度为 1GE
或 1.25GE 。


基于不同子时隙粒度的 FlexE Client 的带宽配置规则如下：


       - 当子时隙粒度为默认值 5GE 时， FlexE Client 的带宽值可配置为 5GE 的整数倍，例
如 5GE 、 10GE 、 15GE 等。


       - 当子时隙粒度为 1GE 时， FlexE Client 的带宽值可配置为 1GE 、 2GE 、 3GE 和 4GE ，
以及 5GE 的整数倍。


       - 当子时隙粒度为 1.25GE 时， FlexE Client 的带宽值可配置为 1.25GE 、 2.5GE 和
3.75GE ，以及 5GE 的整数倍。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 48



HUAWEI NetEngine40E
配置指南 1 接口与链路

//#//#//#//#//# 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **set flexe sub-time-slot granula** **slot** slotID **card** cardID { **1G** | **1.25G** |
**5G** } ，配置 FlexE 卡的子时隙粒度。


说明


针对 NE40E-X8AK 设备，不支持 1.25g 。


步骤 **3** 执行命令 **commit** ，提交配置。


**----**
结束

#### 1.2.16.7 （可选）配置FlexE卡的模式


FlexE 卡的模式包括时隙模式和带宽模式，应用中推荐采用带宽模式。

//#//#//#//#//# 背景信息


       - 时隙模式：指在配置 FlexE Client 时静态指定该 Client 分配的时隙号。


       - 带宽模式：指在配置 FlexE Client 时只指定需要的带宽，具体对应的时隙由设备自
动分配。


说明


缺省情况下， FlexE 卡的模式为带宽模式。

//#//#//#//#//# 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **slot** slot-id ，进入槽位视图。


步骤 **3** 执行命令 **flexe config-mode** **card** cardID { **bandwidth** | **timeslot** } ，将 FlexE 卡的模
式设置为带宽模式或时隙模式。


步骤 **4** 执行命令 **commit** ，提交配置。


**----**
结束

#### 1.2.16.8 创建FlexE Client并配置Client ID和带宽


FlexE Client 对应于外在观察到的用户接口，每个 FlexE Client 可以灵活的从 Group 资源
池分配带宽，同时可以进行带宽调整。

//#//#//#//#//# 背景信息


为保证对接的两端设备的 FlexE Client 正常通信，用户需要将两端设备上 FlexE Client 的
Client ID 和带宽配置一致。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 49



HUAWEI NetEngine40E
配置指南 1 接口与链路


表 **1-9** 不同型号子卡对应的能加入同一 Group 的物理口编号和 port-id 参数的取值范
围










|型号|能加入同一<br>Group的物理<br>口编号|port-id取值范围|
|---|---|---|
|CR5DE2VE1N<br>70|0,1|129-148, 1000-3000|
|CR5DE4NE8V<br>70|0,1|8-15, 1000-3000|
|CR5DE4NE8V<br>70|2,3|16-23, 1000-3000|
|CR5DE4NE8V<br>70|4,5|24-31, 1000-3000|
|CR5DE4NE8V<br>70|6,7|32-39, 1000-3000|
|CR5DE8VE4N<br>70|0,1|8-15, 1000-3000|
|CR5DE8VE4N<br>70|2,3|16-23, 1000-3000|
|CR5DE8VE4N<br>70|4,5|24-31, 1000-3000|
|CR5DE8VE4N<br>70|6,7|32-39, 1000-3000|
|CR5DEDVE8N<br>71|0,1|8-15, 1000-3000|
|CR5DEDVE8N<br>71|2,3|16-23, 1000-3000|
|CR5DEDVE8N<br>71|4,5|24-31, 1000-3000|
|CR5DEDVE8N<br>71|6,7|32-39, 1000-3000|
|CR5D0E2NBA<br>70|0,1|4-23, 1000-3000|
|CR5D0E2NBA<br>70|2,3|24-43, 1000-3000|
|CR5D0E4NBA<br>70|0,1|4-23, 1000-3000|
|CR5D0E4NBA<br>70|2,3|24-43, 1000-3000|
|CR5D0E2NBA<br>72|0,1|4-23, 1000-3000|
|CR5D0E2NBA<br>72|2,3|24-43, 1000-3000|
|CR5D0E4NBA<br>72|0,1|4-23, 1000-3000|
|CR5D0E4NBA<br>72|2,3|24-43, 1000-3000|
|CR5D0E2NBA<br>71|0,1|4-23, 1000-3000|
|CR5D0E2NBA<br>71|2,3|24-43, 1000-3000|
|CR5D0E4NBA<br>71|0,1|4-23, 1000-3000|
|CR5D0E4NBA<br>71|2,3|24-43, 1000-3000|
|CR5D00EENB<br>72|0,1|20-39, 1000-3000|
|CR5D00EENB<br>72|2,3|40-59, 1000-3000|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 50



HUAWEI NetEngine40E
配置指南 1 接口与链路










|型号|能加入同一<br>Group的物理<br>口编号|port-id取值范围|
|---|---|---|
||4|60-69, 1000-3000|
||5|70-79, 1000-3000|
||6,7|80-99, 1000-3000|
||8,9|100-119, 1000-3000|
||10,11|120-139, 1000-3000|
||12,13|140-159, 1000-3000|
||14|160-169, 1000-3000|
||15|170-179, 1000-3000|
||16,17|180-199, 1000-3000|
||18,19|200-219, 1000-3000|
|CR5D00EENB<br>7Q|0,1|20-39, 1000-3000|
|CR5D00EENB<br>7Q|2,3|40-59, 1000-3000|
|CR5D00EENB<br>7Q|4|60-69, 1000-3000|
|CR5D00EENB<br>7Q|5|70-79, 1000-3000|
|CR5D00EENB<br>7Q|6,7|80-99, 1000-3000|
|CR5D00EENB<br>7Q|8,9|100-119, 1000-3000|
|CR5D00EENB<br>7Q|10,11|120-139, 1000-3000|
|CR5D00EENB<br>7Q|12,13|140-159, 1000-3000|
|CR5D00EENB<br>7Q|14|160-169, 1000-3000|
|CR5D00EENB<br>7Q|15|170-179, 1000-3000|
|CR5D00EENB<br>7Q|16,17|180-199, 1000-3000|
|CR5D00EENB<br>7Q|18,19|200-219, 1000-3000|
|CR5D00EENB<br>73|0,1|20-39, 1000-3000|
|CR5D00EENB<br>73|2,3|40-59, 1000-3000|
|CR5D00EENB<br>73|4|60-69, 1000-3000|
|CR5D00EENB<br>73|5|70-79, 1000-3000|
|CR5D00EENB<br>73|6,7|80-99, 1000-3000|
|CR5D00EENB<br>73|8,9|100-119, 1000-3000|
|CR5D00EENB<br>73|10,11|120-139, 1000-3000|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 51



HUAWEI NetEngine40E
配置指南 1 接口与链路










|型号|能加入同一<br>Group的物理<br>口编号|port-id取值范围|
|---|---|---|
||12,13|140-159, 1000-3000|
||14|160-169, 1000-3000|
||15|170-179, 1000-3000|
||16,17|180-199, 1000-3000|
||18,19|200-219, 1000-3000|
|CR5DE2NBF<br>M70|0,1,2,3|4-23, 1000-3000|
|CR5DE1NBF<br>M70|0,1|129-148, 1000-3000|
|CR5D0E2NBF<br>70|0,1|2-21, 1000-3000|
|CR5D0E2NBF<br>71|0,1|2-21, 1000-3000|
|CR5D0E1NBF<br>70|0,1|129-158, 1000-3000|
|CR5DE4NE8V<br>71|0,1,2,3|8-37, 1000-3000|
|CR5DE4NE8V<br>71|4,5,6,7|38-67, 1000-3000|
|CR5D0E4NBF<br>70|0,1,2,3|4-33, 1000-3000|
|CR5D0E2NBF<br>73|0,1,2,3|4-23, 1000-3000|
|CR5DE4NE8V<br>72|0,1,2,3|8-37, 1000-3000|
|CR5DE4NE8V<br>72|4,5,6,7|38-67, 1000-3000|
|CR5D0E2NBF<br>72|0,1,2,3|4-33, 1000-3000|
|CR5D0E2NBF<br>74|0,1,2,3|4-33, 1000-3000|
|CR5DE2VE1N<br>73|0,1|129-148,1000-3000|
|CR5D00E2NF<br>74<br>（LPUF480N<br>）|0|2-21,1000-3000|
|CR5D00E2NF<br>74<br>（LPUF480N<br>）|1|22-41,1000-3000|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 52



HUAWEI NetEngine40E
配置指南 1 接口与链路










|型号|能加入同一<br>Group的物理<br>口编号|port-id取值范围|
|---|---|---|
|CR5D00E2NF<br>70<br>（LPUF480N<br>）|0|2-21,1000-3000|
|CR5D00E2NF<br>70<br>（LPUF480N<br>）|1|22-41,1000-3000|
|CR5D0E2NFE<br>70<br>（LPUF480N<br>）<br>（03034LKP<br>）|0|2-21,1000-3000|
|CR5D0E2NFE<br>70<br>（LPUF480N<br>）<br>（03034LKP<br>）|1|22-41,1000-3000|
|CR5D0E2NBE<br>71<br>（LPUF480N<br>）|0|2-21,1000-3000|
|CR5D0E2NBE<br>71<br>（LPUF480N<br>）|1|22-41,1000-3000|
|CR5D00E4NB<br>70<br>（LPUF480N<br>）|0|2-21,1000-3000|
|CR5D00E4NB<br>70<br>（LPUF480N<br>）|1|22-41,1000-3000|
|CR5D00E2NF<br>74<br>（LPUF480B,<br>LPUF480D）|0|2-11,1000-3000|
|CR5D00E2NF<br>74<br>（LPUF480B,<br>LPUF480D）|1|12-21,1000-3000|
|CR5D00E2NF<br>70<br>（LPUF480B,<br>LPUF480D）|0|2-11,1000-3000|
|CR5D00E2NF<br>70<br>（LPUF480B,<br>LPUF480D）|1|12-21,1000-3000|
|CR5D0E2NFE<br>70<br>（LPUF480B,<br>LPUF480D）<br>（03034LKP<br>）|0|2-11,1000-3000|
|CR5D0E2NFE<br>70<br>（LPUF480B,<br>LPUF480D）<br>（03034LKP<br>）|1|12-21,1000-3000|
|CR5D0E2NBE<br>71<br>（LPUF480B,<br>LPUF480D）|0|2-11,1000-3000|
|CR5D0E2NBE<br>71<br>（LPUF480B,<br>LPUF480D）|1|12-21,1000-3000|
|CR5D00E4NB<br>70<br>（LPUF480B,<br>LPUF480D）|0|2-11,1000-3000|
|CR5D00E4NB<br>70<br>（LPUF480B,<br>LPUF480D）|1|12-21,1000-3000|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 53



HUAWEI NetEngine40E
配置指南 1 接口与链路










|型号|能加入同一<br>Group的物理<br>口编号|port-id取值范围|
|---|---|---|
|CR5D00E1ND<br>73<br>（03032HGG-<br>002）|0|1000-3000|
|CR5D00E1ND<br>76|0|1000-3000|
|CR5D00E1ND<br>78|0|1000-3000|
|CR5D00E1ND<br>74|0|1000-3000|
|CR5D00E1ND<br>75|0|1000-3000|
|CR5D00E1ND<br>77|0|1000-3000|
|CR5D0LBXFM<br>71|0|1000-3000|
|CR5D0LBXFM<br>71|1|1000-3000|
|CR5D0LBXFM<br>71|2|1000-3000|
|CR5D0LBXFM<br>71|3|1000-3000|
|CR5D0LBXFM<br>71|4|1000-3000|
|CR5D0LBXFM<br>71|5|1000-3000|
|CR5D0LBXFM<br>71|6|1000-3000|
|CR5D0LBXFM<br>71|7|1000-3000|
|CR5D0LBXFM<br>71|8|1000-3000|
|CR5D0LBXFM<br>71|9|1000-3000|
|CR5D0LBXFM<br>71|10|1000-3000|
|CR5D0LBXFM<br>71|11|1000-3000|
|CR5DE2VE1N<br>72|0,1|129-148,1000-3000|
|CR5D00E2ND<br>70|0|1000-3000|
|CR5DE4NE8V<br>73|0,1|4-23,1000-3000|
|CR5DE4NE8V<br>73|2,3|24-43,1000-3000|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 54



HUAWEI NetEngine40E
配置指南 1 接口与链路










|型号|能加入同一<br>Group的物理<br>口编号|port-id取值范围|
|---|---|---|
|CX6DE4NE8V<br>10|0,1|4-23,1000-3000|
|CX6DE4NE8V<br>10|2,3|24-43,1000-3000|
|CR5DE2NBF<br>M72|0,1|4-23,1000-3000|
|CX6DE2NBFM<br>11|0,1|4-23,1000-3000|
|CR5D00E8NB<br>7R|0,1|8-15, 1000-3000|
|CR5D00E8NB<br>7R|2,3|16-23, 1000-3000|
|CR5D00E8NB<br>7R|4,5|24-31, 1000-3000|
|CR5D00E8NB<br>7R|6,7|32-39, 1000-3000|
|CR5DE1NBF<br>MK0|0,1|129-148, 1000-3000|
|CR2DE1NBF<br>ME0|0,1|129-148, 1000-3000|
|CR5D00E1KB<br>70|0|1000-3000|
|CR5D00E1KB<br>71|0|1000-3000|
|CR5D00E1KB<br>72|0|1000-3000|
|CR5D00E1KB<br>73|0|1000-3000|
|CR5D00E1KB<br>74|0|1000-3000|
|CR5D00E1KB<br>75|0|1000-3000|
|CR5D00LFXF7<br>0|0|2-21, 1000-3000|
|CR5D00LFXF7<br>0|1|22-41, 1000-3000|
|CR5D00E8NB<br>7S|0,1|8-15, 1000-3000|
|CR5D00E8NB<br>7S|2,3|16-23, 1000-3000|
|CR5D00E8NB<br>7S|4,5|24-31, 1000-3000|
|CR5D00E8NB<br>7S|6,7|32-39, 1000-3000|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 55



HUAWEI NetEngine40E
配置指南 1 接口与链路










|型号|能加入同一<br>Group的物理<br>口编号|port-id取值范围|
|---|---|---|
|CR5D00EENB<br>7E|0,1|20-39, 1000-3000|
|CR5D00EENB<br>7E|2,3|40-59, 1000-3000|
|CR5D00EENB<br>7E|4|60-69, 1000-3000|
|CR5D00EENB<br>7E|5|70-79, 1000-3000|
|CR5D00EENB<br>7E|6,7|80-99, 1000-3000|
|CR5D00EENB<br>7E|8,9|100-119, 1000-3000|
|CR5D00EENB<br>7E|10,11|120-139, 1000-3000|
|CR5D00EENB<br>7E|12,13|140-159, 1000-3000|
|CR5D00EENB<br>7E|14|160-169, 1000-3000|
|CR5D00EENB<br>7E|15|170-179, 1000-3000|
|CR5D00EENB<br>7E|16,17|180-199, 1000-3000|
|CR5D00EENB<br>7E|18,19|200-219, 1000-3000|
|CR5D00EENB<br>7S|0,1|20-39, 1000-3000|
|CR5D00EENB<br>7S|2,3|40-59, 1000-3000|
|CR5D00EENB<br>7S|4|60-69, 1000-3000|
|CR5D00EENB<br>7S|5|70-79, 1000-3000|
|CR5D00EENB<br>7S|6,7|80-99, 1000-3000|
|CR5D00EENB<br>7S|8,9|100-119, 1000-3000|
|CR5D00EENB<br>7S|10,11|120-139, 1000-3000|
|CR5D00EENB<br>7S|12,13|140-159, 1000-3000|
|CR5D00EENB<br>7S|14|160-169, 1000-3000|
|CR5D00EENB<br>7S|15|170-179, 1000-3000|
|CR5D00EENB<br>7S|16,17|180-199, 1000-3000|
|CR5D00EENB<br>7S|18,19|200-219, 1000-3000|
|CR5D00EENB<br>K3（NE40E-<br>X8AK）|0,1|20-39, 1000-3000|
|CR5D00EENB<br>K3（NE40E-<br>X8AK）|2,3|40-59, 1000-3000|
|CR5D00EENB<br>K3（NE40E-<br>X8AK）|4|60-69, 1000-3000|
|CR5D00EENB<br>K3（NE40E-<br>X8AK）|5|70-79, 1000-3000|
|CR5D00EENB<br>K3（NE40E-<br>X8AK）|6,7|80-99, 1000-3000|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 56



HUAWEI NetEngine40E
配置指南 1 接口与链路










|型号|能加入同一<br>Group的物理<br>口编号|port-id取值范围|
|---|---|---|
||8,9|100-119, 1000-3000|
||10,11|120-139, 1000-3000|
||12,13|140-159, 1000-3000|
||14|160-169, 1000-3000|
||15|170-179, 1000-3000|
||16,17|180-199, 1000-3000|
||18,19|200-219, 1000-3000|
|CR5D00EENB<br>K4（NE40E-<br>X8AK）|0,1|20-39, 1000-3000|
|CR5D00EENB<br>K4（NE40E-<br>X8AK）|2,3|40-59, 1000-3000|
|CR5D00EENB<br>K4（NE40E-<br>X8AK）|4|60-69, 1000-3000|
|CR5D00EENB<br>K4（NE40E-<br>X8AK）|5|70-79, 1000-3000|
|CR5D00EENB<br>K4（NE40E-<br>X8AK）|6,7|80-99, 1000-3000|
|CR5D00EENB<br>K4（NE40E-<br>X8AK）|8,9|100-119, 1000-3000|
|CR5D00EENB<br>K4（NE40E-<br>X8AK）|10,11|120-139, 1000-3000|
|CR5D00EENB<br>K4（NE40E-<br>X8AK）|12,13|140-159, 1000-3000|
|CR5D00EENB<br>K4（NE40E-<br>X8AK）|14|160-169, 1000-3000|
|CR5D00EENB<br>K4（NE40E-<br>X8AK）|15|170-179, 1000-3000|
|CR5D00EENB<br>K4（NE40E-<br>X8AK）|16,17|180-199, 1000-3000|
|CR5D00EENB<br>K4（NE40E-<br>X8AK）|18,19|200-219, 1000-3000|
|CR5D00EENB<br>7D|0,1|20-39, 1000-3000|
|CR5D00EENB<br>7D|2,3|40-59, 1000-3000|
|CR5D00EENB<br>7D|4|60-69, 1000-3000|
|CR5D00EENB<br>7D|5|70-79, 1000-3000|
|CR5D00EENB<br>7D|6,7|80-99, 1000-3000|
|CR5D00EENB<br>7D|8,9|100-119, 1000-3000|
|CR5D00EENB<br>7D|10,11|120-139, 1000-3000|
|CR5D00EENB<br>7D|12,13|140-159, 1000-3000|
|CR5D00EENB<br>7D|14|160-169, 1000-3000|
|CR5D00EENB<br>7D|15|170-179, 1000-3000|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 57



HUAWEI NetEngine40E
配置指南 1 接口与链路










|型号|能加入同一<br>Group的物理<br>口编号|port-id取值范围|
|---|---|---|
||16,17|180-199, 1000-3000|
||18,19|200-219, 1000-3000|
|CR5D00EENB<br>7B|0,1|20-39, 1000-3000|
|CR5D00EENB<br>7B|2,3|40-59, 1000-3000|
|CR5D00EENB<br>7B|4|60-69, 1000-3000|
|CR5D00EENB<br>7B|5|70-79, 1000-3000|
|CR5D00EENB<br>7B|6,7|80-99, 1000-3000|
|CR5D00EENB<br>7B|8,9|100-119, 1000-3000|
|CR5D00EENB<br>7B|10,11|120-139, 1000-3000|
|CR5D00EENB<br>7B|12,13|140-159, 1000-3000|
|CR5D00EENB<br>7B|14|160-169, 1000-3000|
|CR5D00EENB<br>7B|15|170-179, 1000-3000|
|CR5D00EENB<br>7B|16,17|180-199, 1000-3000|
|CR5D00EENB<br>7B|18,19|200-219, 1000-3000|
|CR5DE1NBF<br>M76|0,1|129-148, 1000-3000|
|CR5DE2NBF<br>M77<br>（LPUF480K,<br>LPUF480M,<br>LPUF480MC1,<br>LPUF480Q）|0,1|1000-3000|
|CR5DE2NBF<br>M78<br>（LPUF480K,<br>LPUF480M,<br>LPUF480MC1,<br>LPUF480Q）|0,1|1000-3000|
|CR5DE2NBF<br>M79<br>（LPUF480K,<br>LPUF480M,<br>LPUF480MC1,<br>LPUF480Q）|0,1|1000-3000|
|CR5DE2NBF<br>M77|0|1000-3000|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 58



HUAWEI NetEngine40E
配置指南 1 接口与链路














|型号|能加入同一<br>Group的物理<br>口编号|port-id取值范围|
|---|---|---|
|（LPUF480H<br>）|1|1000-3000|
|CR5DE2NBF<br>M78<br>（LPUF480H<br>）|0|1000-3000|
|CR5DE2NBF<br>M78<br>（LPUF480H<br>）|1|1000-3000|
|CR5DE2NBF<br>M79<br>（LPUF480H<br>）|0<br>1|1000-3000<br>1000-3000|


//#//#//#//#//# 操作步骤

步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **flexe client-instance** clientIndex [ **flexe-group** groupIndex **flexe-type**

**full-function** [ **port-id** portId ] ] ，创建并进入 FlexE Client 视图。


步骤 **3** 执行命令 **flexe-clientid** clientid ，配置 FlexE Client 的 Client ID 值。


步骤 **4** 配置 FlexE Client 的带宽。

       - 如果 FlexE 卡的模式为带宽模式，则执行命令 **flexe-bandwidth** { **1** | **1.25** | **2** | **2.5**
| **3** | **3.75** | **4** | bandwidth-value } ，配置 FlexE Client 的带宽。


       - 如果 FlexE 卡的模式为时隙模式，则执行命令 **binding interface** interface-type
interface-number **time-slot** timeslot-list [ **sub-time-slot** subtime-slot ] ，将一
个或多个子时隙绑定到 FlexE Client ，绑定的时隙即构成 FlexE Client 的带宽。


步骤 **5** （可选）执行命令 **minimal available bandwidth** bandPercent ，配置 FlexE Client 的
最小可用带宽比例值。


步骤 **6** 执行命令 **commit** ，提交配置。


**----**
结束

#### 1.2.16.9 配置Ethernet业务场景下增加FlexE网元


在现网运行 Ethernet 业务场景下增加 FlexE 网元，会涉及到 FlexE 物理接口与标准
Ethernet 口对接，如果 FlexE 物理接口的 DCN 自协商功能使能，则无需手动干预，两者
能够自动连通，网管即可管理到新增网元。

//#//#//#//#//# 应用环境


如 图 **1-3** 所示，现网网元 DeviceA 和 DeviceB 是标准 Ethernet 模式，且不支持 FlexE 物理
接口的 DCN 自协商功能；新增的网元 DeviceC 为 FlexE 模式，并支持 FlexE 物理接口的
DCN 自协商功能。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 59



HUAWEI NetEngine40E
配置指南 1 接口与链路


图 **1-3** 现网运行 Ethernet 业务场景增加 FlexE 网元


网元 DeviceC 加入后，因为命令 **undo dcn-auto-negotiation disable** 默认使能，即
FlexE 物理接口的 DCN 自协商功能默认使能，所以 FlexE 物理接口的底层工作模式会在
10 秒左右的时间，自动从 FlexE 模式切换成标准 Ethernet 模式，实现 DCN 连通，网管可
以管理到 DeviceC 。


       - 用户如果在 20 分钟内，针对 DeviceC 执行如下命令，可立即生效，让 DCN 持续连
通。


a. 执行命令 **force-physical-mode ethernet** ，强制切换 FlexE 物理接口的底层工
作模式为标准 Ethernet 模式。


说明


缺省情况下， FlexE 物理接口的底层工作模式回切时间是 20 分钟，即 20 分钟后会自动
从标准 Ethernet 模式回切成 FlexE 模式。


b. 如果需要修改 FlexE 物理接口的底层工作在标准 Ethernet 模式的时间或需要让
FlexE 物理接口的底层工作在标准 Ethernet 模式不回切，用户可执行如下操
作。

//#//#//#//# ▪ 执行命令 phyautoclear forcephysicalmode enable cleartime ，设置

FlexE 物理接口的底层工作模式从标准 Ethernet 模式回切成 FlexE 模式的时
间。

//#//#//#//# ▪ 执行命令 phyautoclear forcephysicalmode disable ，取消 FlexE 物理接

口的底层工作模式从标准 Ethernet 模式回切成 FlexE 模式。


       - 用户如果在 20 分钟内，针对 DeviceC 没有执行上述命令，则在第 20 分钟的时候，
DeviceC 的 FlexE 物理接口底层工作模式会从标准 Ethernet 模式回切成 FlexE 模式，
然后继续等待 10 秒再进行 DCN 自协商，实现 DCN 连通。从而造成 DeviceC 会暂时
脱管 10 秒左右的时间，如此反复。


如果用户想要修改现网业务为 FlexE ，请按如下操作步骤执行。

//#//#//#//#//# 操作步骤


步骤 **1** 在 DeviceA 上执行命令 **flexe enable port** port-position ，将 Ethernet 接口从标准以太
模式切换为灵活以太模式。由于 DeviceC 的 FlexE 物理接口底层工作模式已切换成标准
Ethernet 模式，此时 DeviceA 和 DeviceC 之间 DCN 不通， DeviceC 会脱管。


步骤 **2** 手动修改 DeviceA 的 FlexE 物理接口状态，状态的改变会触发与其相连的 DeviceC 的
FlexE 物理接口底层模式快速回切成 FlexE 模式。用户可执行如下操作。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 60



HUAWEI NetEngine40E
配置指南 1 接口与链路


       - 在 DeviceA 上执行命令 **laser turn-off** ，手动关闭光模块激光器发光功能，然后再
执行命令 **laser turn-on** ，手动打开光模块激光器发光功能。


       - 在 DeviceA 上执行命令 **shutdown** ，关闭接口，然后再执行命令 **undo**
**shutdown** ，启动该接口。


完成上述操作后， DeviceC 上与 DeviceA 相连的 FlexE 物理接口会回切成 FlexE 模式，两
者 FlexE 对接成功，网管可以继续管理 DeviceC 。


说明


上述场景中， DeviceB 的操作同 DeviceA 。


**----**
结束

#### ********0 配置FlexE业务场景下新增FlexE或Ethernet网元


在现网运行 FlexE 业务场景下增加 Ethernet 网元，会涉及到 FlexE 物理接口与标准
Ethernet 口对接，如果 FlexE 物理接口的 DCN 自协商功能使能，则无需手动干预，两者
能够自动连通，网管即可管理到新增网元。

//#//#//#//#//# 应用环境


       - 现网运行 FlexE 业务场景增加 FlexE 网元


如 图 **1-4** 所示，现网网元 DeviceA 和 DeviceB 是 FlexE 模式，新增的网元 DeviceC 也是
FlexE 模式。


图 **1-4** 现网运行 FlexE 业务场景增加 FlexE 网元


网元 DeviceC 加入后，因为现网运行的网元和 DeviceC 都是 FlexE 模式，所以
DeviceC 会跟 DeviceA 和 DeviceB 直接对接成功，中间也不会出现 FlexE 物理接口的
底层工作模式从 FlexE 模式自适应成标准 Ethernet 模式的过程，网管可以直接管理
到 DeviceC 。


       - 现网运行 FlexE 业务场景增加 Ethernet 网元


如 图 **1-5** 所示，现网网元 DeviceA 和 DeviceB 是 FlexE 模式，新增的网元 DeviceC 是
Ethernet 模式。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 61



HUAWEI NetEngine40E
配置指南 1 接口与链路


图 **1-5** 现网运行 FlexE 业务场景增加 Ethernet 网元


此种情况有以下几种场景，用户可按如下操作步骤执行相应的操作，确保新增的
Ethernet 网元能对接成功并被网管管理。


说明


下述场景中， DeviceB 的操作同 DeviceA 。

//#//#//#//#//# 操作步骤


       - 如果 DeviceA 支持 FlexE 物理接口的 DCN 自协商功能， DeviceC 不支持。


当 DeviceC 加入后，由于 DeviceA 上的命令 **undo dcn-auto-negotiation disable**
默认使能，即 FlexE 物理接口的 DCN 自协商功能默认使能， FlexE 物理接口的底层
工作模式会在 10 秒左右的时间，自动从 FlexE 模式切换成标准 Ethernet 模式，实现
DCN 连通，网管可以管理到 DeviceC 。


– 用户如果在 20 分钟内，针对 DeviceA 执行如下命令，可立即生效，让 DCN 持
续连通。


i. 执行命令 **system-view** ，进入系统视图。

ii. 执行命令 **interface** interface-type interface-number ，进入 FlexE 物理接
口视图，例如 FlexE-50G 1/1/1 接口。


iii. 执行命令 **force-physical-mode ethernet** ，强制切换 FlexE 物理接口的底
层工作模式为标准 Ethernet 模式。


说明


缺省情况下， FlexE 物理接口的底层工作模式回切时间是 20 分钟，即 20 分钟后会
自动从标准 Ethernet 模式回切成 FlexE 模式。


iv. 如果需要修改 FlexE 物理接口的底层工作在标准 Ethernet 模式的时间或需
要让 FlexE 物理接口的底层工作在标准 Ethernet 模式不回切，用户可执行
如下操作。


           - 执行命令 **phyautoclear forcephysicalmode** **enable** cleartime ，
设置 FlexE 物理接口的底层工作模式从标准 Ethernet 模式回切成 FlexE
模式的时间。


           - 执行命令 **phyautoclear forcephysicalmode disable** ，取消 FlexE 物
理接口的底层工作模式从标准 Ethernet 模式回切成 FlexE 模式。


– 用户如果在 20 分钟内，针对 DeviceA 没有执行上述命令，则在第 20 分钟的时
候， DeviceA 的 FlexE 物理接口底层工作模式会从标准 Ethernet 模式回切成


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 62



HUAWEI NetEngine40E
配置指南 1 接口与链路


FlexE 模式，然后继续等待 10 秒再进行 DCN 自协商，实现 DCN 连通。从而造成
DeviceC 会暂时脱管 10 秒左右的时间，如此反复。


如果用户想要现网继续运行 FlexE 业务，需要做如下操作。


a. 在 DeviceC 上执行命令 **flexe enable port** port-position ，将 Ethernet 接口从
标准以太模式切换为灵活以太模式。由于 DeviceA 的 FlexE 物理接口底层工作
模式已切换成标准 Ethernet 模式，此时 DeviceC 和 DeviceA 之间 DCN 不通，
DeviceC 会脱管。


b. 手动修改 DeviceC 的 FlexE 物理接口状态，状态的改变会触发与其相连的
DeviceA 的 FlexE 物理接口底层模式快速回切成 FlexE 模式。用户可执行如下操
作。

//#//#//#//# ▪ 在 DeviceC 上执行命令 laser turn-off ，手动关闭光模块激光器发光功

能，然后再执行命令 **laser turn-on** ，手动打开光模块激光器发光功能。

//#//#//#//# ▪ 在 DeviceC 上执行命令 shutdown ，关闭接口，然后再执行命令 undo

**shutdown** ，启动该接口。


完成上述操作后， DeviceA 上与 DeviceC 相连的 FlexE 物理接口会回切成 FlexE 模
式，两者 FlexE 对接成功，网管可以继续管理 DeviceC 。


       - 如果 DeviceA 支持 FlexE 物理接口的 DCN 自协商功能， DeviceC 也支持。


当 DeviceC 加入后，由于 DeviceA 上的命令 **undo dcn-auto-negotiation disable**
默认使能，即 FlexE 物理接口的 DCN 自协商功能默认使能， FlexE 物理接口的底层
工作模式会在 10 秒左右的时间，自动从 FlexE 模式切换成标准 Ethernet 模式，实现
DCN 连通，网管可以管理到 DeviceC 。


因为 DeviceC 支持 FlexE 物理接口的 DCN 自协商功能且默认使能，所以用户想要现
网继续运行 FlexE 业务，就在 DeviceC 上执行命令 **flexe enable port** portposition ，将 Ethernet 接口从标准以太模式切换为灵活以太模式即可。


       - 如果 DeviceA 不支持 FlexE 物理接口的 DCN 自协商功能， DeviceC 支持。


当 DeviceC 加入后，用户在 DeviceA 上执行命令 **force-physical-mode ethernet** ，
强制切换 FlexE 物理接口的底层工作模式为标准 Ethernet 模式，实现 DeviceC 和
DeviceA 的 DCN 互通，并让网管可以管理到 DeviceC 。


如果用户想要现网继续运行 FlexE 业务，需要做如下操作。


a. 在 DeviceC 上执行命令 **flexe enable port** port-position ，将 Ethernet 接口从
标准以太模式切换为灵活以太模式。因为 DeviceC 支持 FlexE 物理接口的 DCN
自协商功能且默认使能，所以 FlexE 物理接口的底层工作模式会在 10 秒左右的
时间，自动从 FlexE 模式切换成标准 Ethernet 模式。


b. 在 DeviceA 上执行命令 **undo force-physical-mode** ，恢复 FlexE 物理接口的底
层工作模式为 FlexE 模式。


c. 手动修改 DeviceA 的 FlexE 物理接口状态，状态的改变会触发与其相连的
DeviceC 的 FlexE 物理接口底层模式快速回切成 FlexE 模式。用户可执行如下操
作。

//#//#//#//# ▪ 在 DeviceA 上执行命令 laser turn-off ，手动关闭光模块激光器发光功

能，然后再执行命令 **laser turn-on** ，手动打开光模块激光器发光功能。

//#//#//#//# ▪ 在 DeviceA 上执行命令 shutdown ，关闭接口，然后再执行命令 undo

**shutdown** ，启动该接口。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 63



HUAWEI NetEngine40E
配置指南 1 接口与链路


完成上述操作后， DeviceC 上与 DeviceA 相连的 FlexE 物理接口会回切成 FlexE 模
式，两者 FlexE 对接成功，网管可以继续管理 DeviceC 。


**----**
结束

#### ********1 （可选）配置FlexE物理接口的时间同步模式


FlexE 标准定义了两种 1588v2 报文承载模式： OH （ OverHead ）模式和 Client 模式，缺
省情况下， 1588v2 报文通过 OH 模式承载。

//#//#//#//#//# 背景信息


       - OH 模式：指时钟报文走 FlexE 的开销时隙，时钟同步的相关配置和标准 Ethernet
接口下的配置相同。


       - Client 模式：指时钟报文走 FlexE Client ，需要在部署时钟业务的 FlexE 物理接口上
绑定承载时钟业务的 FlexE 接口才能生效。


说明


该功能在 NE40E-X8AK 产品上不支持。

//#//#//#//#//# 前置任务


不管 1588v2 报文采用哪种模式承载，首先需要完成 1588v2 配置，具体配置过程请参见

                                  《 HUAWEI NetEngine40E 路由器配置指南 系统管理》。


在 FlexE 物理接口下完成 1588v2 配置后， 1588v2 报文承载模式即是默认的 OH 模式，如
果用户想把 1588v2 报文承载模式变成 Client 模式，可以执行如下步骤。

//#//#//#//#//# 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **interface** interface-type interface-number ，进入 FlexE 物理接口视图，例如
FlexE-50G 1/0/0 接口。


步骤 **3** 执行命令 **clock binding flexe** **interface** iftype ifnum ，配置 FlexE 物理接口绑定承载
时钟业务的 FlexE 接口。


步骤 **4** 执行命令 **commit** ，提交配置。


**----**
结束

#### ********2 检查配置结果


配置 FlexE 接口后，需要检查配置结果，保证配置正确。

//#//#//#//#//# 前提条件


已经完成 FlexE 接口的所有配置。

//#//#//#//#//# 操作步骤

       - 使用 **display license resource usage port-flexe** { **all** | **slot** slotid } [ **active** |
**deactive** ] 命令查看单板的灵活以太端口 License 的授权情况。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 64



HUAWEI NetEngine40E
配置指南 1 接口与链路


       - 使用 **display flexe group information** **slot** slot-id **card** card-id 命令查看 FlexE 卡
的 Group 信息。


       - 请选择如下命令之一查看 FlexE 业务口信息、 FlexE 物理口信息以及绑定 FlexE
Group 中的物理口信息：

– **display flexe client information** [ **interface** { interface-type interfacenumber | interface-name } ]

– **display flexe client information** [ **index** clientindex ]

– **display flexe physical-interface information** [ **interface** { interface-type
interface-number | interface-name } } ]


       - 使用 **display interface ethernet brief** 命令查看 FlexE 接口的简要信息。

       - 使用 **display interface flexe** interface-number 命令查看 FlexE 接口的运行状态和
统计信息。


       - 使用 **display lldp neighbor brief** 命令查看 FlexE 接口的 LLDP 邻居节点的简要信
息。


**----**
结束

### 1.2.17 端口组配置


端口组实现为多个接口批量配置命令的功能，减少单独配置的输入错误，同时节省人
力。

//#//#//#//#//# 背景信息


端口组可以分为永久端口组和临时端口组，它们实现的功能基本相同，即都可以将多
个接口加入到端口组中，实现在这些接口下批量配置命令。其区别在于：


       - 用户退出临时端口组后，该临时端口组被系统自动删除；而永久端口组不会被自
动删除，需要通过 **undo port-group** 命令删除。


       - 永久端口组的信息可以通过命令 **display port-group** 查看，而临时端口组的信息
无法查看。


       - 永久端口组配置会生成配置文件，但是临时端口组配置后不会生成。

//#//#//#//#//# 操作步骤


       - 配置永久端口组：


a. 执行命令 **system-view** ，进入系统视图。


b. 执行命令 **port-group** port-group-name ，创建一个永久端口组并进入端口组
视图。


c. 执行命令 **group-member** { interface-type interface-number1 [ **to**
interface-type interface-number2 ] } &<1-10> ，将指定的接口添加到永久
端口组中。


d. 执行命令 **commit** ，提交配置。


       - 配置临时端口组：


a. 执行命令 **system-view** ，进入系统视图。


b. 执行命令 **port-group group-member** { interface-type-start interfacenumber-start [ **to** interface-type-end | interface-number-end ] }
&<1-10> ，创建一个临时端口组，并将指定的接口添加到临时端口组中。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 65



HUAWEI NetEngine40E
配置指南 1 接口与链路


说明


            - 永久端口组下，下发 **shutdown** 或者 **undo shutdown** 命令， commit 之后不保存到配置
文件。


c. 执行命令 **commit** ，提交配置。


**----**
结束

//#//#//#//#//# 检查配置结果


完成配置后，可以执行命令 **display port-group** [ **all** | port-group-name ] 查看端口组
的状态信息。

### 1.2.18 配置接口监控组


在双机备份的场景中，通过配置接口监控组，根据网络侧接口的状态变化来触发接入
侧接口的状态变化，以此达到接入侧主备链路切换的目的。

//#//#//#//#//# 应用环境


在双机备份的场景中，当网络侧接口故障时，接入侧设备感知不到故障的发生，不会
将业务切换到备份链路，可能导致流量过载或者转发不通。在这种情况下，可以配置
接口监控组，通过接口监控组管理网络侧接口的状态，使接入侧接口能够及时感知到
故障已发生，从而业务及时触发主备链路倒换，避免流量过载或者丢失的情况发生。


如 图 **1-6** 1 所示， PE2 是 PE1 的备份设备， M 个 NPE 双归属到 2 个 PE 设备来实现链路负载
分担，网络侧 PE 设备接入到 N 个路由器。当 PE1 和 DeviceA 之间、 PE1 和 DeviceB 之间的
链路均故障时，网络侧仅剩余 PE1 和 DeviceN 之间的链路， NPE 设备感知不到该故障的
产生，不会相应切换接入侧链路到 PE2 ，仍然通过 PE1 向 DeviceN 发送报文，而由于网
络侧可用链路数量的减少，可能会造成流量过载。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 66



HUAWEI NetEngine40E
配置指南 1 接口与链路


图 **1-6** 接口监控组典型应用组网图


这种情况下，在 PE 设备上部署接口监控组，将网络侧 PE 设备的多个接口加入接口监控
组中。当网络侧发生链路故障时，通过接口监控组监控网络侧接口的状态，实现一定
比例的网络侧接口状态变化时， PE 设备接入侧相应接口的状态变化，使接入侧链路发
生主备链路切换，从而避免流量过载，保障业务的通畅。

//#//#//#//#//# 前置任务


在配置接口监控组之前，需完成路由器接口物理属性的配置。

//#//#//#//#//# 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **monitor-group** monitor-group-name ，创建接口监控组，并进入接口监控
组视图。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 67



HUAWEI NetEngine40E
配置指南 1 接口与链路


步骤 **3** 执行命令 **binding interface** interface-type interface-number [ **down-weight**
down-weight-value ] ，将接口加入到接口监控组中。


加入接口监控组中的接口称为 Binding 接口。 Binding 接口一般是网络侧接口， Track 接
口一般是接入侧接口。接口监控组通过对 Binding 接口状态的监控，实现 Track 接口状
态的变化。


重复执行本步骤，可以将多个 Binding 接口加入同一个接口监控组中。


步骤 **4** 执行命令 **quit** ，退出接口监控组视图。


步骤 **5** 执行命令 **interface** interface-type interface-number ，进入接入侧指定接口视图。


步骤 **6** 执行命令 **track monitor-group** monitor-group-name [ **trigger-down-weight**
trigger-down-weight-value ] ，配置接口监视接口监控组。


监视接口监控组的接口称为 Track 接口。


重复执行第 5 步和第 6 步，可以将多个 Track 接口监控同一个接口监控组。


当接口监控组中所有 Binding 接口的 Down 权重值之和大于或等于 Track 接口的 triggerdown-weight-value 值时，就会触发对应 Track 接口的状态变为 Down ，从而将业务切
换到备份链路上；当接口监控组中所有 Binding 接口的 Down 权重值之和小于 Track 接口
的 trigger-down-weight-value 值时，对应 Track 接口的状态恢复为 Up ，业务回切到主
用链路。


步骤 **7** 执行命令 **quit** ，退出接口视图。


步骤 **8** 执行命令 **monitor-group** monitor-group-name ，进入已创建的接口监控组视图。


步骤 **9** （可选）执行命令 **trigger-up-delay** trigger-up-delay-value ，配置 Track 接口延迟恢复
Up 的时间。


步骤 **10** 执行命令 **monitor enable** ，启动接口监控组和接口的联动功能。


步骤 **11** 执行命令 **commit** ，提交配置。


**----**
结束

//#//#//#//#//# 检查配置结果


执行命令 **display monitor-group** [ monitor-group-name ] ，查看接口监控组的相关
信息。

### 1.2.19 配置去使能板间心跳联动端口状态表功能


板间心跳联动端口状态表（ PST ）功能默认开启，可通过配置去使能板间心跳联动端口
状态表功能进行关闭。

//#//#//#//#//# 应用环境


板间心跳是通过单板间定时发送心跳报文来检测硬件故障的机制。当设备上某单板发
生故障时，其他单板可通过板间心跳快速感知此单板故障，从而联动刷新与此故障单
板相关的端口状态表为 Down ，使流量快速切换到非故障链路。若联动功能出现异常
时，可以去使能板间心跳联动端口状态表功能。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 68



HUAWEI NetEngine40E
配置指南 1 接口与链路

//#//#//#//#//# 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **pst board-fast-switch disable** ，去使能板间心跳联动端口状态表功能。


步骤 **3** 执行命令 **commit** ，提交配置。


**----**
结束

### 1.2.20 维护接口


使用清除统计信息命令，对定位接口故障有帮助。

#### 1.2.20.1 清除统计信息


当需要统计一定时间内某接口的流量信息，这时必须在统计开始前清除该接口原有的
统计信息，使接口重新进行统计。

//#//#//#//#//# 背景信息


须知


清除计数器信息后，以前的信息将无法恢复，务必仔细确认。

//#//#//#//#//# 操作步骤


       - 在确认需要清除接口的流量统计信息后，请在用户视图下执行命令 **reset**
**counters interface** 。


       - 如果需要清除接口历史保存的峰值速率值，获取后续时间内的峰值速率，请在用
户视图下执行命令 **reset counters peak-rate interface** 。


**----**
结束

#### 1.2.20.2 监控接口信息


监控接口统计信息，方便用户通过流量和速率分析网络状况。

//#//#//#//#//# 操作步骤


       - 在任意视图下执行命令 **monitor interface-statistics** interface-type interfacenumber &<1–5> [ **interval** interval-value | **times** { times-value | **infinity** } ]
                   - ，监控接口当前的流量统计信息。


       - 在任意视图下执行命令 **monitor interface-statistics batch** [ interface-type

[ interface-number-begin [ **to** interface-number-end ] ] ] [ **interval** intervalvalue | **times** { times-value | **infinity** } ] [*] [ **main** ] ，批量监控接口当前的流量统
计信息。


       - 在任意视图下执行命令 **monitor interface-information** **interface** interfacetype interface-number [ **interval** interval-value | **times** { times-value |
**infinity** } ] [*] ，监控指定接口的详细信息，包括运行状态和统计信息。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 69



HUAWEI NetEngine40E
配置指南 1 接口与链路


       - 在任意视图下执行命令 **monitor counters** **bit** [ **rate** ] **interface** interface-type
interface-number [ **interval** interval-value | **times** { times-value | **infinity** } ]
                   - ，监控接口的报文个数或报文速率，包括接口入方向 / 出方向的总字节数以及单
播、组播和广播报文总数或报文速率。


**----**
结束

### 1.2.21 接口管理配置举例


通过以下举例，您可以了解到接口管理的基本使用。

#### 1.2.21.1 管理接口示例


通过接口管理示例，您可以了解到如何配置接口的描述信息、 MTU 、流量统计时间间
隔等内容。

//#//#//#//#//# 组网需求


为了保证网络中各设备间更好地通信，需要物理接口和逻辑接口配合使用，并且根据
不同的实际需求对各接口进行参数的配置，如配置描述信息、 MTU 值、接口出入带宽
利用率的告警阈值、流量统计时间间隔以及使能接口协议状态变化时向网管发送
Trap 、控制接口震荡等功能。

//#//#//#//#//# 配置思路


采用如下的思路配置接口：


1. 配置接口的描述信息，便于用户识别接口。


2. 配置接口的 MTU ，保证接口每次发送的报文都能够畅通无阻的到达接收端，确保
报文发送一次成功。


3. 配置全局流量统计时间间隔，便于用户统计接口流量和速率。


4. 创建子接口并配置 MTU ，保证子接口每次发送的报文都能够畅通无阻的到达接收
端，确保报文发送一次成功。

//#//#//#//#//# 数据准备


为完成此配置举例，需要准备如下数据：


       - 接口名称


       - 接口的描述信息


       - 接口的 MTU


       - 全局流量统计时间间隔


       - 子接口的 MTU

//#//#//#//#//# 操作步骤


步骤 **1** 配置接口的描述信息。


<HUAWEI> **system-view**

[ ~ HUAWEI] **interface gigabitethernet 2/0/0**

[ ~ HUAWEI-GigabitEthernet2/0/0] **description for IFM**

[*HUAWEI-GigabitEthernet2/0/0] **commit**


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 70



HUAWEI NetEngine40E
配置指南 1 接口与链路


步骤 **2** 配置接口 MTU 。


[ ~ HUAWEI-GigabitEthernet2/0/0] **mtu 1000**

[*HUAWEI-GigabitEthernet2/0/0] **commit**

[ ~ HUAWEI-GigabitEthernet2/0/0] **quit**


步骤 **3** 配置全局流量统计时间间隔。

[ ~ HUAWEI] **set flow-stat interval 100**

[*HUAWEI] **commit**


步骤 **4** 创建子接口并配置子接口的 MTU 。


[ ~ HUAWEI] **interface gigabitethernet 2/0/0.1**

[*HUAWEI-GigabitEthernet2/0/0.1] **mtu 800**

[*HUAWEI-GigabitEthernet2/0/0.1] **commit**


**----**
结束

//#//#//#//#//# 配置文件


//#
set flow-stat interval 100
//#
interface Gigabitethernet2/0/0
description for IFM
mtu 1000

//#
interface Gigabitethernet2/0/0.1
mtu 800

//#

return

#### ******** 配置FlexE接口示例


本例介绍采用 FlexE 技术对接的两端设备上的接口配置。

//#//#//#//#//# 组网需求


如 图 **1-7** 所示， DeviceA 与 DeviceB 之间通过 FlexE 技术创建 FlexE Client 进行通信，不同
的 FlexE Client 配置不同的带宽，满足多样化的业务和应用场景，要求 FlexE Client1 的
带宽为 4G ， FlexE Client2 的带宽为 5G ， FlexE Client3 的带宽为 15G ， FlexE Client4 的
带宽为 20G 。


图 **1-7** 配置 FlexE 接口组网图


说明


本示例中 interface0 ， interface1 ， interface2 ， interface3 ， interface4 分别代表 FlexE-50G
1/1/1 ， FlexE 1/1/129 ， FlexE 1/1/130 ， FlexE 1/1/131 ， FlexE 1/1/132 。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 71



HUAWEI NetEngine40E
配置指南 1 接口与链路

//#//#//#//#//# 配置注意事项


在配置过程中，需注意以下事项：


       - 为保证对接的两端设备正常通信，用户需要在两端设备的 FlexE 物理接口上分别配
置相同的 PHY Number 值。


       - 为保证对接的两端设备正常通信，用户需要将两端设备上 FlexE 物理接口加入的
FlexE Group 配置相同的 Group ID 。


       - 为保证对接的两端设备的 FlexE Client 正常通信，用户需要将两端设备上 FlexE
Client 的 Client ID 和带宽配置一致。

//#//#//#//#//# 配置思路


配置思路如下：


1. 激活单板的灵活以太端口 License 。


2. 配置标准 Ethernet 接口为灵活以太模式。


3. 配置 FlexE 物理接口的 PHY Number 。


4. 创建 FlexE Group 并绑定 FlexE 物理接口。


5. 配置 FlexE Group 的 Group ID 。


6. 配置 FlexE 卡的子时隙粒度。


7. 创建 FlexE Client 并配置 Client ID 和带宽。


8. 配置各接口的 IP 地址。

//#//#//#//#//# 数据准备


为完成此配置例，需准备如下的数据：


       - FlexE 物理接口的 PHY Number 为 5 。


       - FlexE Group 的索引为 1 。


       - FlexE Group 的 Group ID 为 2345 。


       - FlexE 卡的子时隙粒度为 1G 。


       - 四个 FlexE 接口的 Port ID 为 interface1 、 interface2 、 interface3 和 interface4 的接口
编号最后一位。


       - 四个 FlexE Client 的 Client ID 为 1 、 2 、 3 、 4 ，分别对应带宽为 4G 、 5G 、 15G 、
20G 。

//#//#//#//#//# 操作步骤


步骤 **1** 激活单板的灵活以太端口 License 。


<DeviceA> **license active XXXXX.dat**
<DeviceA> **system-view**

[ ~ DeviceA] **license**

[ ~ DeviceA-license] **active port-basic slot 1 card 1 port 1**

[*DeviceA-license] **active port-flexe slot 1 card 1 port 1**

[*DeviceA-license] **commit**

[ ~ DeviceA-license] **quit**


步骤 **2** 配置标准 Ethernet 接口为灵活以太模式。

[ ~ DeviceA] **flexe enable port 1/1/1**
Warning: This operation will delete interface 50G1/1/1 and related services. Continue? [Y/N]:y

[*DeviceA] **commit**


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 72



HUAWEI NetEngine40E
配置指南 1 接口与链路


步骤 **3** 配置 FlexE 物理接口的 PHY Number 。


[ ~ DeviceA] **interface FlexE-50G 1/1/1**

[ ~ DeviceA-FlexE-50G1/1/1] **phy-number 5**
Warning: The traffic on this interface may be interrupted if the operation is performed. Continue? [Y/N]:y

[*DeviceA-FlexE-50G1/1/1] **commit**

[ ~ DeviceA-FlexE-50G1/1/1] **quit**


步骤 **4** 创建 FlexE Group 并绑定 FlexE 物理接口。

[ ~ DeviceA] **flexe group 1**

[*DeviceA-flexe-group-1] **binding interface FlexE-50G1/1/1**


步骤 **5** 配置 FlexE Group 的 Group ID 。

[*DeviceA-flexe-group-1] **flexe-groupnum 2345**
Warning: The traffic on related clients may be interrupted if the operation is performed. Continue? [Y/N]:y

[*DeviceA-flexe-group-1] **commit**

[ ~ DeviceA-flexe-group-1] **quit**


步骤 **6** 配置 FlexE 卡的子时隙粒度。

[ ~ DeviceA] **set flexe sub-time-slot granula slot 1 card 1 1g**

[*DeviceA] **commit**


步骤 **7** 创建 FlexE Client 并配置 Client ID 和带宽。

[ ~ DeviceA] **flexe client-instance 1 flexe-group 1 flexe-type full-function port-id 129**

[*DeviceA-flexe-client-1] **flexe-clientid 1**
Warning: The traffic on this interface may be interrupted if the operation is performed. Continue? [Y/N]:y

[*DeviceA-flexe-client-1] **flexe-bandwidth 4**

[*DeviceA-flexe-client-1] **commit**

[ ~ DeviceA-flexe-client-1] **quit**

[ ~ DeviceA] **flexe client-instance 2 flexe-group 1 flexe-type full-function port-id 130**

[*DeviceA-flexe-client-2] **flexe-clientid 2**
Warning: The traffic on this interface may be interrupted if the operation is performed. Continue? [Y/N]:y

[*DeviceA-flexe-client-2] **flexe-bandwidth 5**

[*DeviceA-flexe-client-2] **commit**

[ ~ DeviceA-flexe-client-2] **quit**

[ ~ DeviceA] **flexe client-instance 3 flexe-group 1 flexe-type full-function port-id 131**

[*DeviceA-flexe-client-3] **flexe-clientid 3**
Warning: The traffic on this interface may be interrupted if the operation is performed. Continue? [Y/N]:y

[*DeviceA-flexe-client-3] **flexe-bandwidth 15**

[*DeviceA-flexe-client-3] **commit**

[ ~ DeviceA-flexe-client-3] **quit**

[ ~ DeviceA] **flexe client-instance 4 flexe-group 1 flexe-type full-function port-id 132**

[*DeviceA-flexe-client-4] **flexe-clientid 4**
Warning: The traffic on this interface may be interrupted if the operation is performed. Continue? [Y/N]:y

[*DeviceA-flexe-client-4] **flexe-bandwidth 20**

[*DeviceA-flexe-client-4] **commit**

[ ~ DeviceA-flexe-client-4] **quit**


步骤 **8** 配置各接口的 IP 地址（略）。


步骤 **9** 在 DeviceB 上，重复操作如上 DeviceA 的全部步骤，具体配置请参见 配置文件 。


步骤 **10** 检查配置结果


上述配置成功后，在 DeviceA 和 DeviceB 上执行 **display flexe group information** 命
令，查看 FlexE 卡的 Group 信息。以 DeviceA 显示为例。


说明


该命令针对 NE40E-X8AK ，该命令不需要带 card 关键字。


[ ~ DeviceA] **display flexe group information slot 17 card    1**
FlexE Card Info:

=============================================================
FlexE Config Mode        : Bandwidth

=============================================================


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 73



HUAWEI NetEngine40E
配置指南 1 接口与链路


FlexE Group Info:

=============================================================

GroupID  Total Bandwidth(M)  Valid Bandwidth(M)

1        50000        50000

=============================================================


FlexE Group Binding Interfaces Capability:

=============================================================

GroupID  Interfaces already bound Interfaces can be bound

1     FlexE-50G1/1/1

=============================================================


FlexE Phy Info:

=============================================================

Port No       : FlexE-50G1/1/1
Active Status    : 1
Cfg Group ID    : 1
Cfg Group No    : 2345
Real TX Group No  : 2345
Real RX Group No  : 2345
Remote Group No   : 2345
Cfg Phy No     : 5
Real TX Phy No   : 5
Real RX Phy No   : 5
Remote Phy No    : 5

=============================================================


FlexE Time Slot Info:

=============================================================


port-no       : FlexE-50G1/1/1
ts-num       : 10

sub-ts-num     : 5

time-slot-id   ts-port-map

0:        [129][129][129][129][-]
1:        [130][130][130][130][130]
2:        [131][131][131][131][131]
3:        [131][131][131][131][131]
4:        [131][131][131][131][131]
5:        [132][132][132][132][132]
6:        [132][132][132][132][132]
7:        [132][132][132][132][132]
8:        [132][132][132][132][132]
9:        [-][-][-][-][-]


=============================================================


FlexE Client Info:

=============================================================

Instance Index      Port Name

129            FlexE1/1/129
130 FlexE1/1/130
131            FlexE1/1/131
132            FlexE1/1/132

=============================================================


在 DeviceA 和 DeviceB 上执行 **display interface ethernet brief** 命令，查看 FlexE 接口的
简要信息。以 DeviceA 显示为例。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 74



HUAWEI NetEngine40E
配置指南 1 接口与链路


[ ~ DeviceA] **display interface ethernet brief**
PHY: Physical
*down: administratively down
^down: standby
(l): loopback
(b): BFD down
(d): Dampening Suppressed
(p): port alarm down
InUti/OutUti: input utility/output utility
Interface          PHY  Auto-Neg Duplex Bandwidth InUti OutUti Trunk
FlexE1/1/129        up  -    full     4G 0.01% 0.01%  -FlexE1/1/130        up  -    full     5G 0.01% 0.01%  -FlexE1/1/131        up  -    full     15G 0.01% 0.01%  -FlexE1/1/132        up  -    full     20G 0.01% 0.01%  -FlexE-50G1/1/1       up  -    full     50G   --   --  -

在 DeviceA 和 DeviceB 上执行 **display lldp neighbor brief** 命令，查看 FlexE 接口的 LLDP
邻居节点的简要信息。以 DeviceA 显示为例。

[ ~ DeviceA] **display lldp neighbor brief**
Local Intf           Neighbor Dev     Neighbor Intf    Exptime (sec)

FlexE1/1/129          DeviceB       FlexE1/1/129          114
FlexE1/1/130          DeviceB       FlexE1/1/130          114
FlexE1/1/131          DeviceB       FlexE1/1/131          114
FlexE1/1/132          DeviceB       FlexE1/1/132          114
FlexE-50G1/1/1         DeviceB       FlexE-50G1/1/1         95


在 DeviceA 和 DeviceB 上执行 **display interface flexe** interface-number 命令，查看
FlexE 接口的运行状态和统计信息。以 DeviceA 的 FlexE1/1/129 接口显示为例。

[ ~ DeviceA] **display interface flexe 1/1/129**
FlexE1/1/129 current state : UP (ifindex: 285)
Line protocol current state : UP
Last line protocol up time : 2021-03-11 09:11:24
Link quality grade : GOOD
Description:
Route Port,The Maximum Transmit Unit is 1500
Internet protocol processing : disabled
IP Sending Frames' Format is PKTFMT_ETHNT_2, Hardware address is 00e0-fc12-3456
Port BW: 4G

Pause Flowcontrol: Receive Enable and Send Enable

Client-id Match State: Match
Last physical up time  : 2021-03-10 15:11:46
Last physical down time : 2021-03-10 15:11:29
Current system time: 2021-03-11 11:36:52
Statistics last cleared:never
Last 300 seconds input rate: 10031 bits/sec, 1 packets/sec
Last 300 seconds output rate: 10041 bits/sec, 1 packets/sec
Input peak rate 125150137 bits/sec, Record time: 2021-03-10 09:25:57
Output peak rate 125757954 bits/sec, Record time: 2021-03-10 09:25:57
Input: 7006191780 bytes, 52343200 packets
Output: 7024402448 bytes, 52482810 packets
Input:
Unicast: 52334185 packets, Multicast: 9010 packets
Broadcast: 5 packets, JumboOctets: 0 packets
CRC: 0 packets, Symbol: 0 packets
Overrun: 0 packets, InRangeLength: 0 packets
LongPacket: 0 packets, Jabber: 0 packets, Alignment: 0 packets
Fragment: 0 packets, Undersized Frame: 0 packets
RxPause: 0 packets
Output:
Unicast: 52473465 packets, Multicast: 9334 packets
Broadcast: 11 packets, JumboOctets: 0 packets
Lost: 0 packets, Overflow: 0 packets, Underrun: 0 packets
System: 0 packets, Overruns: 0 packets
TxPause: 0 packets
Last 300 seconds input utility rate: 0.01%
Last 300 seconds output utility rate: 0.01%


**----**
结束


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 75



HUAWEI NetEngine40E
配置指南 1 接口与链路

//#//#//#//#//# 配置文件


       - DeviceA 的配置文件

//#
sysname DeviceA
//#
set flexe sub-time-slot granula slot 1 card 1 1g
flexe enable port 1/1/1
//#
flexe group 1
flexe-groupnum 2345
binding interface FlexE-50G1/1/1
//#
flexe client-instance 1 flexe-group 1 flexe-type full-function port-id 129
flexe-clientid 1
flexe-bandwidth 4
//#
flexe client-instance 2 flexe-group 1 flexe-type full-function port-id 130
flexe-clientid 2
flexe-bandwidth 5
//#
flexe client-instance 3 flexe-group 1 flexe-type full-function port-id 131
flexe-clientid 3
flexe-bandwidth 15
//#
flexe client-instance 4 flexe-group 1 flexe-type full-function port-id 132
flexe-clientid 4
flexe-bandwidth 20
//#

license
active port-basic slot 1 card 1 port 1
active port-flexe slot 1 card 1 port 1
//#
interface FlexE-50G1/1/1
undo shutdown

undo dcn
phy-number 5
//#

return

       - DeviceB 的配置文件

//#
sysname DeviceB
//#
set flexe sub-time-slot granula slot 1 card 1 1g
flexe enable port 1/1/1
//#
flexe group 1
flexe-groupnum 2345
binding interface FlexE-50G1/1/1
//#
flexe client-instance 1 flexe-group 1 flexe-type full-function port-id 129
flexe-clientid 1
flexe-bandwidth 4
//#
flexe client-instance 2 flexe-group 1 flexe-type full-function port-id 130
flexe-clientid 2
flexe-bandwidth 5
//#
flexe client-instance 3 flexe-group 1 flexe-type full-function port-id 131
flexe-clientid 3
flexe-bandwidth 15
//#
flexe client-instance 4 flexe-group 1 flexe-type full-function port-id 132
flexe-clientid 4
flexe-bandwidth 20
//#

license
active port-basic slot 1 card 1 port 1
active port-flexe slot 1 card 1 port 1


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 76



HUAWEI NetEngine40E
配置指南 1 接口与链路


//#
interface FlexE-50G1/1/1
undo shutdown

undo dcn
phy-number 5
//#

return

## 1.3 端口扩展配置


配置端口扩展，可以使设备用较少的槽位提供高密度的以太类接口。

//#//#//#//#//# 应用场景


说明


该特性仅在 Admin-VS 支持。


在 IP Core 网络中，为了满足接入侧和网络侧越来越多的业务部署需求，需要 PE 设备或
P 设备提供高密度的以太类接口。此时，可以部署端口扩展系统，将一台性能较高的设
备设置为 Master ，将大量的支持以太类接口的低端设备设置为 AP ，把 AP 上的以太类接
口映射为 Master 上的端口扩展接口，业务配置只需在 Master 的端口扩展接口上完成，
从而使 Master 设备具备高密度的以太类接口。这样既可以充分利用已有的支持以太类
接口的设备，又避免购买支持大量以太类接口的设备，显著降低了建网成本。同时，
端口扩展系统可以实现 AP 的即插即用，在一台 Master 上可以对所有 AP 进行配置和管
理，简化了网络和业务部署，降低了运维难度。端口扩展的典型组网如 图 **1-8** 所示。


图 **1-8** 端口扩展组网图


说明


完成端口扩展系统的部署后，在 Master 上，端口扩展接口支持的业务特性范围与本地的普通以
太类接口是基本一致的。

//#//#//#//#//# 部署指导


为了完整的部署端口扩展系统，并按需开展维护和管理工作，需要在 Master 上进行以
下配置：


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 77



HUAWEI NetEngine40E
配置指南 1 接口与链路

### 1.3.1 端口扩展配置注意事项

//#//#//#//#//# 特性限制


表 **1-10** 本特性的使用限制






|特性限制|系列|涉及产品|
|---|---|---|
|设备作为端口扩展的AP时，无法配置容量提升<br>RTU。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|
|端口扩展场景下，Master和AP设备的传输模式<br>（路由器模式/PTN模式）要保持一致，否则AP无<br>法上线。<br>请合理规划Master和AP的设备模式，保证两者相<br>同。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|
|1、PTN6900设备，在PTN模式下支持作为<br>Master/AP，在非PTN模式下不支持作为<br>Master/AP。<br>2、非PTN6900设备，在PTN模式下不支持作为<br>Master/AP，在非PTN模式下支持作为<br>Master/AP。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|
|在配置Trunk内联接口添加或删除成员接口时，需<br>要先将成员接口shutdown后，再进行添加或删除<br>操作，确保AP上的对应的Trunk成员接口已正确<br>添加或删除，再将成员接口undo shutdown，否<br>则可能导致业务流量丢失。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|
|网管上会将Master和AP作为两个独立的网元进行<br>管理。<br>用户在网管上看到的是Master和AP两个网元。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 78



HUAWEI NetEngine40E
配置指南 1 接口与链路







|特性限制|系列|涉及产品|
|---|---|---|
|同一Trunk内联接口的成员接口，只能与同一AP<br>的内联接口绑定。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|
|Master和AP的软件版本必须一致，否则可能导致<br>AP上线后业务不可用。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|
|不支持AP手动上线，建议用户不要在AP设备上手<br>动进行上线配置，否则可能导致AP上线失败或业<br>务异常。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|
|不支持通过Master给AP加载动态模块（对应<br>install-module命令）。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|
|端口扩展模式和普通的虚拟接入模式不能直接切<br>换，必须去使能虚拟接入后，再配置端口扩展模<br>式。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|
|Trunk内联接口仅支持手工负载分担模式。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 79



HUAWEI NetEngine40E
配置指南 1 接口与链路






|特性限制|系列|涉及产品|
|---|---|---|
|在Master上创建端口扩展接口时必须指定与内联<br>接口或Trunk内联接口的绑定关系，目前不支持修<br>改此绑定关系，必须先删除端口扩展接口（此时<br>该端口扩展接口下的配置会被清除），再重新创<br>建端口扩展接口并指定新的绑定关系。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|
|在虚拟接入端口扩展视图下，网络侧接口不支持<br>配置MPLS特性。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|
|需保证inner-connect命令配置的接口绑定关系与<br>Master和AP之间的实际物理连接是一致的。否则<br>会导致业务不可用，并可能导致AP无法上线。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|
|不支持端口扩展接口与本地普通接口加入到同一<br>Eth-Trunk接口中。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|
|Master设备内联口只支持ETM子卡，若内联口所<br>在单板更换为非ETM子卡时，绑定到ETM子卡内<br>联口上的扩展端口会被删除，扩展端口的业务也<br>被删除。<br>如何判断是否为ETM子卡请咨询技术工程师。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|
|内联口Trunk场景下，流量以外联口为粒度在内联<br>口上Hash，流量无法保证在内联成员口上均匀分<br>布，存在内联口承载的流量超过带宽的风险,导致<br>流量丢包。需要合理规划业务，保证内联口带宽<br>足够大。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 80



HUAWEI NetEngine40E
配置指南 1 接口与链路












|特性限制|系列|涉及产品|
|---|---|---|
|Eth-trunk信道化子接口与端口扩展内联口trunk<br>互斥。互斥的功能不能同时配置。<br>建议合理规划配置。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|
|当Master设备上的单板拔出时，扩展接口以及扩<br>展接口加入Trunk后Trunk主子接口的流量统计数<br>据会有丢失。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|
|当Master设备上的单板拔出时，扩展接口以及扩<br>展接口加入Trunk后Trunk主子接口的流量统计数<br>据会有丢失。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|
|端口扩展不支持ISSU升级|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|
|Eth-trunk信道化子接口与端口扩展外联口trunk<br>互斥。互斥的功能不能同时配置。<br>建议合理规划配置。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|
|外联Eth-Trunk接口的成员口跨AP时，若AP整机<br>发生故障，故障恢复之后，非静态LACP模式的<br>ETH-Trunk不支持快速回切；<br>非LACP模式的ETH-Trunk包括：普通手工ETH-<br>Trunk、手工1：1主备ETH-Trunk。<br>建议在AP故障场景下，使用静态LACP ETH-<br>Trunk。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 81



HUAWEI NetEngine40E
配置指南 1 接口与链路



|特性限制|系列|涉及产品|
|---|---|---|
|执行"display interface"命令查看不到端口扩展接<br>口的单、组播流量统计、报文错误、报文丢弃等<br>信息；查看对应AP设备的外联口的远端统计信<br>息，可执行"display interface <ifname><br>remote"命令查看。但由于信息需要向AP设备查<br>询获取，设备显示可能会有卡顿。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|

### 1.3.2 建立端口扩展系统

本节介绍建立端口扩展系统的基本配置任务。

//#//#//#//#//# 背景信息




//#//#//#//#//# 前置任务



端口扩展系统由 Master 和 AP 两种角色构成。为了简化业务部署、方便运维和管理，端
口扩展系统的控制平面在 Master 上，因此，建立端口扩展系统的基本配置都在 Master
上完成。同时， AP 支持即插即用， Master 通过 ESN 号自动识别 AP 并管理它，然后通过
内部的 NETCONF 通道将相关的基本配置下发到 AP 上，用户无需到 AP 上进行任何配
置。


在建立端口扩展系统之前，需完成以下任务：


- AP 设备需保持默认配置并加载预配置文件。


- 获取 AP 设备的 ESN 号。

- 请在 Master 设备上使能 SSH 客户端首次认证功能（ **ssh client first-time**
**enable** ）。


#### 1.3.2.1 配置Master的基本功能

Master 是端口扩展系统的主体，建立端口扩展系统需先配置 Master 。

//#//#//#//#//# 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **virtual-access port-extend** ，使能端口扩展能力，并进入端口扩展视图。


步骤 **3** 执行命令 **role master** ，将当前节点的角色配置为 Master 。


执行此步骤后：


       - 本地的所有物理接口将变成四维接口，例如： GigabitEthernet1/1/0/1 ，其中第一
维为 1 表示该接口为本地接口。


       - 设备将根据本地的特性配置具体情况，自动使能全局 BFD 能力并创建端口扩展系
统内部的 IS-IS 进程，具体规则如下：


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 82



HUAWEI NetEngine40E
配置指南 1 接口与链路






|初始情况|子分类|创建的IS-IS进程|
|---|---|---|
|不存在任何IS-IS进程|缺省情况|自动创建一个进程号为<br>65534的IS-IS进程。|
|存在一个或多个IS-IS<br>进程|进程65534被使用|将在65534到1之间查找一<br>个未被占用且离65534最<br>近的进程号作为端口扩展<br>的IS-IS进程号。|
|存在一个或多个IS-IS<br>进程|进程65534未被使用|自动创建一个进程号为<br>65534的IS-IS进程。|



步骤 **4** 执行命令 **admin** ip-address ，配置 Master 的管理 IP 地址。


Master 的管理 IP 地址将通过内部 IS-IS 扩散给 AP ，用于与 AP 建立内部控制和管理通道。
执行此步骤后， Master 本地将自动生成一个 IP 地址为 ip-address 的 Loopback 接口。


步骤 **5** 根据实际情况选择相应的命令，配置 Master 与 AP 建立 STelnet 连接的用户名、密码和备
用密码。


       - 执行命令 **ap default login-user** user-name { **login-password** password | **slave**
slave-password-value }* 。


       - 执行命令 **ap default login-user** user-name **login-password** 。


       - 执行命令 **ap default login-user** user-name **slave-password** 。


说明


该用户名和密码需要配置成 AP 的默认用户名和密码。


密码需要符合密码复杂度规则：密码必须包含数字、大写字母、小写字母、特殊字符，并且长度
不能小于 8 。


步骤 **6** 执行命令 **isis authentication-mode** **hmac-sha256** **key-id** key-id **cipher** cipher-text

[ **send-only** ] ，配置 IS-IS 认证。


为提高端口扩展系统的安全性，需配置 **isis authentication-mode** 命令，对 IS-IS 接收
的 Hello 报文、 LSP 报文以及 SNP 报文进行一定规则的验证并对发送的上述报文附加认
证信息，使只有通过验证或加密的报文才可以在系统中进行转发，避免非法报文给系
统带来干扰。


步骤 **7** （可选）执行命令 **control-tunnel authentication keychain** keychain-name ，配置
控制通道的 Keychain 认证。


AP 与 Master 之间的控制通道用于两者交互控制信息，为了提升控制通道的安全性，可
以在 Master 上执行此步骤，配置 Keychain 认证。


配置此步骤之前，需要先创建名称为 keychain-name 的 Keychain 。


步骤 **8** 执行命令 **commit** ，提交配置。


**----**
结束

#### ******* 在Master上配置AP的基本功能


AP 可以看作是 Master 的远端板卡， AP 的基本功能配置可以在 Master 上完成。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 83



HUAWEI NetEngine40E
配置指南 1 接口与链路

//#//#//#//#//# 背景信息


在端口扩展系统中，一个 Master 可以同时管理多个 AP 。在 Master 上重复执行下述步
骤，可以为多个 AP 配置基本功能。

//#//#//#//#//# 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **ap-id** ap-id ，在 Master 上配置 AP 的 ID 并进入 AP 视图。


步骤 **3** 执行命令 **esn** esn-number ，配置当前 AP 的 ESN 号。


端口扩展系统支持 AP 即插即用。在 AP 启动之后，会自动使能 DCN （ Data
Communication Network ）功能，并通过内部的 OSPF 扩散 AP 的 ESN 号和 PNP （ Plug
and Play ）初始状态； Master 在发现 AP 并识别 AP 的 PNP 状态为初始状态之后，查询本
地是否配置了对应的 ESN 号，如果配置了，则启动即插即用流程。 ESN 号是 Master 用
来唯一识别一个 AP 的，因此不同 AP 的 ESN 号不能相同。


步骤 **4** （可选）执行命令 **sysname** host-name ，设置 AP 的主机名。


步骤 **5** 执行命令 **commit** ，提交配置。


步骤 **6** 执行命令 **admin** ip-address ，配置当前 AP 的管理 IP 地址。


AP 的管理 IP 地址用于与 Master 建立内部控制和管理通道。该配置会在 AP 的即插即用流
程中，由 Master 下发给 AP 。然后 AP 会自动生成一个 IP 地址为 ip-address 的 Loopback 接
口。


此命令执行后无需提交，立即生效。


**----**
结束

#### ******* 配置端口扩展的认证方案


为了保证端口扩展系统的安全性，必须配置认证方案。

//#//#//#//#//# 背景信息


为了建立起端口扩展系统，需要在 Master 与 AP 之间建立 Stelnet 、 SFTP 、 NETCONF 等
通道。为了保证安全性，必须配置登录 AP 的认证方案。当前认证方案支持的认证模式
包括：


       - 本地认证：如果当前网络中没有部署 HWTACACS 服务器，则可以采用本地方式进
行认证。本地方式进行认证的优点是速度快，可以降低运营成本；缺点是存储信
息量受设备硬件条件限制。


       - HWTACACS 认证：采用 HWTACACS 方式进行认证，可以防止非法用户对端口扩展
系统的攻击。与本地认证相比， HWTACACS 具有更加可靠的传输和加密特性。


请在 Master 上执行下列操作步骤。

//#//#//#//#//# 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **ap-id** ap-id ，进入 AP 视图。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 84



HUAWEI NetEngine40E
配置指南 1 接口与链路


步骤 **3** 执行命令 **login-user** user-name **login-password** password ，配置 Master 登录到 AP 时
所需的用户名和口令。


步骤 **4** 执行命令 **commit** ，提交配置。


步骤 **5** 执行命令 **login-user** user-name **sftp-directory** sftp-directory ，配置 Master 登录到 AP
时所需的用户名和 SFTP 目录。


步骤 **6** 执行命令 **authentication-mode** { **hwtacacs** | **local** } [*] ，配置当前认证方案使用的认
证模式。


其中 **local** 表示采用本地认证， **hwtacacs** 表示采用 HWTACACS 认证。缺省情况下采用
本地认证。


在一个认证方案中可以同时采用两种认证模式，系统将按照 **local** 与 **hwtacacs** 配置的顺
序进行认证：


       - 当配置的认证方式是先本地认证后 HWTACACS 认证时


如果步骤 3 中的用户名在 AP 上没有创建，将转入 HWTACACS 认证。


如果步骤 3 中的用户名在 AP 和 HWTACACS 服务器都已经创建，本地认证时由于口
令错误导致的认证失败，将不再转入 HWTACACS 认证。


       - 当配置的认证方式是先 HWTACACS 认证后本地认证时


如果步骤 3 中的用户名在 HWTACACS 服务器上没有创建，但是在 AP 上是存在的，
经过 HWTACACS 认证时，将被认为认证失败，不再转入本地认证。


只有在 HWTACACS 认证服务器 Down 时，才会转入本地认证。


推荐采用先 HWTACACS 认证后本地认证。


步骤 **7** 根据选择的认证模式，执行相应的操作步骤。


       - 如果采用了 HWTACACS 认证，则需进行如下配置：


a. 执行命令 **hwtacacs** ，进入端口扩展 HWTACACS 视图。


b. 执行命令 **hwtacacs-server shared-key** { **cipher** cipher-string | keystring } ，配置与 AP 的 HWTACACS 服务器通信的全局共享密钥。


设置密钥可以提高 Master 与 AP 的 HWTACACS 服务器通信的安全性。


c. 执行命令 **hwtacacs-server** ip-address [ port ] [ **shared-key** { key-string |
**cipher** cipher-string } ] [ **secondary** ] ，配置 AP 的主备 HWTACACS 公共服务
器。


其中：

//#//#//#//# ▪ 如果不指定 shared-key { key-string | cipher cipher-string } ，则使用全

局共享密钥。

//#//#//#//# ▪ 主用服务器和备用服务器的 IP 地址必须不同，否则配置失败。


说明


如果采用 HWTACACS 认证，则需要保证步骤 3 中 **login-user** 配置的用户名和口令与
HWTACACS 服务器上的用户名和口令一致，否则 AP 功能无法正常使用。


       - 如果采用了本地认证，则需进行如下配置：


a. 执行命令 **ap-user** ，进入虚拟接入 AP-USER 视图。

b. 执行命令 **local-user** user-name **password** **cipher** password ，在 AP 上创建
一个本地用户名并配置登录口令。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 85



HUAWEI NetEngine40E
配置指南 1 接口与链路


说明


如果采用本地认证，则需要保证步骤 3 中 **login-user** 配置的用户名和口令与 **local-user** 配置
的用户名和口令一致，否则 AP 功能无法正常使用。


步骤 **8** 执行命令 **commit** ，提交配置。


**----**
结束

#### ******* 配置内联接口


为了建立起端口扩展系统，需在 Master 与 AP 之间配置内联接口。

//#//#//#//#//# 背景信息


在端口扩展系统内部，数据流量需要通过 AP 与 Master 内联接口之间的内部转发通道进
行传输，为了建立起该通道，需要将 Master 与 AP 之间物理直连的接口设置为物理内联
接口。当需要增加带宽或提升可靠性时，还可以将 Master 和 AP 之间的 Eth-Trunk 接口设
置为 Trunk 内联接口。


说明


物理内联接口和 Trunk 内联接口都不支持配置具体业务，也不支持创建子接口。


当用户希望查看与 Master 上的物理内联口相连的 AP 上的物理内联口时，可以在用户视图下执行
**link detect** **interface** interface-type interface-number ，触发 Master 发送 LAD 报文，然后执行
**display link neighbor** **interface** interface-type interface-number 命令查看 Master 和 AP 之间
内联口的邻居信息。

//#//#//#//#//# 操作步骤


       - 配置物理内联接口


a. 执行命令 **system-view** ，进入系统视图。


b. 执行命令 **interface** interface-type interface-number ，进入 Master 与 AP 之间
物理直连的接口视图。


c. 执行命令 **virtual-access enable** ，使能接口的端口扩展能力，设置为物理内
联接口。


执行此步骤后，对应的接口将自动使能端口扩展的 IS-IS 进程，并自动配置 **isis**
**circuit-type p2p** 命令，将当前接口模拟为 P2P 类型。


说明


              - 如果用户先在当前接口下手动配置 **isis enable** process-id 命令，然后再配置
**virtual-access enable** 命令，则当前接口不再自动使能端口扩展的 IS-IS 进程，也
不会自动配置 **isis circuit-type p2p** 命令。此时需要用户确保进程号为 process-id
的 IS-IS 进程已使能端口扩展能力，并在当前接口下手动配置 **isis circuit-type p2p**
命令，配置工作量较大。因此，建议用户采用自动配置的方案，不要在配置
**virtual-access enable** 命令之前手动配置 **isis enable** process-id 命令。


              - AP 自动上线成功之后， AP 上的所有以太接口自动使能端口扩展能力，同时 AP 会在
对应的接口下自动保存 **virtual-access enable** 的配置，无需用户配置。


d. 执行命令 **dcn** 命令，使能内联接口的 DCN 功能。


e. 执行命令 **commit** ，提交配置。


f. 执行命令 **quit** ，退回到系统视图。


g. 执行命令 **ap-id** ap-id ，进入 AP 视图。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 86



HUAWEI NetEngine40E
配置指南 1 接口与链路


h. 执行命令 **inner-connect** ap-interface-type ap-interface-number **binding**
master-interface-type master-interface-number ，配置 AP 与 Master 之间内
联接口的绑定关系。


其中

//#//#//#//# ▪ 参数 ap-interface-type ap-interface-number 指定的是 AP 上的物理内联

接口， master-interface-type master-interface-number 指定的是
Master 上的物理内联接口。

//#//#//#//# ▪ 此步骤配置的接口绑定关系必须与实际物理连接是一致的。


i. 执行命令 **commit** ，提交配置。


       - 配置 Trunk 内联接口


a. 配置手工负载分担模式 **Eth-Trunk** 接口


其中：

//#//#//#//# ▪ 配置三层 Eth-Trunk 接口。 ▪ 在配置向 Eth-Trunk 接口中加入成员接口时，将 Master 上与 AP 直连的物

理接口加入到 Eth-Trunk 中。


b. 执行命令 **interface eth-trunk** trunk-id ，进入 Eth-Trunk 接口视图。


c. 执行命令 **virtual-access enable** ，使能 Eth-Trunk 接口的端口扩展能力，设置
为 Trunk 内联接口。


执行此步骤后，当前 Eth-Trunk 接口将自动使能端口扩展的 IS-IS 进程，并自动
配置 **isis circuit-type p2p** 命令，将当前接口模拟为 P2P 类型。同时，该 EthTrunk 的成员接口自动继承内联接口属性。


说明


如果用户先在 Eth-Trunk 接口下手动配置 **isis enable** process-id 命令，然后再配置
**virtual-access enable** 命令，则 Eth-Trunk 接口不再自动使能端口扩展的 IS-IS 进程，
也不会自动配置 **isis circuit-type p2p** 命令。此时需要用户确保进程号为 process-id 的
IS-IS 进程已使能端口扩展能力，并在 Eth-Trunk 接口下手动配置 **isis circuit-type p2p**
命令，配置工作量较大。因此，建议用户采用自动配置的方案，不要在配置 **virtual-**
**access enable** 命令之前手动配置 **isis enable** process-id 命令。


d. 执行命令 **commit** ，提交配置。


e. 执行命令 **quit** ，退回到系统视图。


f. 执行命令 **ap-id** ap-id ，进入 AP 视图。


g. 执行命令 **inner-connect** ap-interface-type ap-interface-number **binding**
master-interface-type master-interface-number ，配置 AP 与 Master 之间内
联接口的绑定关系。


其中

//#//#//#//# ▪ 参数 ap-interface-type ap-interface-number 指定的是 AP 上的物理内联

接口， master-interface-type master-interface-number 指定的是
Master 上 Trunk 内联接口的成员接口。

//#//#//#//# ▪ 同一 Trunk 内联接口的成员接口，只能与同一 AP 的内联接口绑定。 ▪ 此步骤配置的接口绑定关系必须与实际物理连接是一致的。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 87



HUAWEI NetEngine40E
配置指南 1 接口与链路


执行此步骤之后， AP 上会自动完成相应的 Trunk 内联接口的配置，无需用户
配置。


h. 执行命令 **commit** ，提交配置。


**----**
结束

#### 1.3.2.5 配置端口扩展接口


通过配置端口扩展接口，可以将 AP 上的外联接口映射为 Master 上的虚拟口，达到提升
Master 端口密度的目的。

//#//#//#//#//# 背景信息


在端口扩展系统中， AP 可以看作是用于扩展 Master 接口的板卡，接收 Master 下发的配
置和转发表项，并对外提供外联接口。为了提升 Master 上的接口密度，需要在 Master
上配置 AP 的外联接口与 Master 的内联接口之间的绑定关系，创建相应的端口扩展接
口。


当 AP 上的外联接口需要配置 Eth-Trunk 时，可以在 Master 上将端口扩展接口加入到普通
的 Eth-Trunk 接口中，作为端口扩展 Trunk 接口。

//#//#//#//#//# 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **ap-id** ap-id ，进入 AP 视图。


步骤 **3** 执行命令 **remote-interface** ap-interface-type ap-interface-number [ **to** max-portnumber ] **binding** master-interface-type master-interface-number ，配置 AP 的外联
接口与 Master 的内联接口之间的绑定关系，并创建相应的端口扩展接口。


其中：


       - 参数 ap-interface-type ap-interface-number 指定的是 AP 上的外联接口， masterinterface-type master-interface-number 指定的是 Master 上的物理内联接口或
Trunk 内联接口。


       - 参数 max-port-number 指定 AP 上外联接口的端口编号最大值。配置该参数时，表
示配置一组 AP 外联接口与 Master 内联接口的绑定关系，并创建一组端口扩展接
口。其中，被绑定的外联接口端口编号范围是 ap-interface-number 最后一维数字
到 max-port-number 。


端口扩展接口是一种四维接口，它是指定 AP 的外联接口映射在当前 Master 上的扩展端
口，例如： GigabitEthernet1025/1/0/1 ，表示该端口扩展接口对应的是 AP ID 为 1025
的 AP 上的外联接口 GigabitEthernet1/0/1 。通过端口扩展接口，用户可以将 AP 上的外
联接口当作 Master 本地的接口一样进行各种业务配置。


说明


在 AP 上线成功后， AP 上的所有以太接口都自动使能端口扩展能力，即默认为内联接口。在
Master 上执行本步骤后，对应 AP 上的外联接口将自动去使能端口扩展能力，因为同一接口不能
既作内联接口又作外联接口。


步骤 **4** 执行命令 **commit** ，提交配置。


步骤 **5** （可选）执行命令 **quit** ，退回到系统视图。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 88



HUAWEI NetEngine40E
配置指南 1 接口与链路


步骤 **6** （可选）配置端口扩展 Trunk 接口。根据业务需要，选择执行其中一个配置任务：


       - 配置手工负载分担模式 **Eth-Trunk** 接口


       - 配置静态 **LACP** 模式 **Eth-Trunk** 接口


       - 配置手工 **1:1** 主备模式 **Eth-Trunk** 接口


在执行上述任务时，需将已创建的端口扩展接口加入到 Eth-Trunk 接口中。


说明


目前不支持端口扩展接口与本地普通接口加入到同一 Eth-Trunk 接口中。


**----**
结束

#### ******* （可选）配置端口扩展系统和外部路由互引


为了便于网管直接管理 Master 和 AP ，需要配置端口扩展系统和外部路由互引。

//#//#//#//#//# 背景信息


端口扩展系统是通过 DCN 功能来实现 AP 即插即用的，而 DCN 网络具有路由隔离的特
性，因此，在网管参与管理 Master 和 AP 的场景中，需要在 Master 上配置端口扩展系统
与外部的路由互引。

//#//#//#//#//# 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **virtual-access port-extend** ，进入端口扩展视图。


步骤 **3** 执行命令 **import admin-ip** **to** { **bgp** [ **vpn-instance** vpn-instance-name ] | **isis**
process-id [ **level-1** | **level-1-2** | **level-2** ] | **ospf** process-id | **public** } ，将 Master 和
AP 的管理 IP 地址路由引入到其他协议的路由中。


此步骤的作用是将端口扩展系统内的路由引入到 DCN 外部的网络。执行此步骤前，需
要根据引入路由协议的类型，在 Master 上完成相应协议的配置。


说明


当引入路由协议为 IS-IS 或 OSPF 时，需要确保网络规划正确，进程号 process-id 没有引入其他路
由，否则将配置失败。


步骤 **4** 执行命令 **import** { **bgp** [ **vpn-instance** vpn-instance-name ] | **static** [ **vpn-instance**
vpn-instance-name ] | **isis** process-id | **ospf** process-id } **to dcn-ospf** [ **route-policy**
route-policy-name ] ，将其他协议的路由引入到 DCN 中。


此步骤的作用是将 Master 到网管之间的路由引入到端口扩展系统内部的 DCN OSPF 路
由中。执行此步骤前，需要根据被引入路由协议的类型以及采用的路由策略名称，在
Master 上完成相应协议及路由策略的配置。


步骤 **5** 执行命令 **commit** ，提交配置。


**----**
结束


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 89



HUAWEI NetEngine40E
配置指南 1 接口与链路

#### 1.3.2.7 检查配置结果


在端口扩展系统建立成功后，可以在 Master 上查看到 AP 的基本信息、 AP 的统计信息、
AP 接口信息、端口扩展接口信息、 NVTAG 分配信息等。

//#//#//#//#//# 前提条件


已经完成建立端口扩展系统的所有配置。

//#//#//#//#//# 操作步骤


       - 使用 **display virtual-access ap** [ ap-id ] 命令，查看 AP 的基本信息。


       - 使用 **display virtual-access ap statistics** 命令，查看 AP 的统计信息。


       - 使用 **display virtual-access ap-interface** [ **ap-id** ap-id ] 命令，查看 AP 的接口信
息。


       - 使用 **display interface** { interface-name | interface-type interface-number }
**remote** 命令，查看外联口对应 Master 上的端口扩展接口信息。


       - 使用 **display remote interface** interface-type interface-number 命令，在
Master 上查看指定端口扩展接口的信息。


       - 使用 **display virtual-access bindinfo** [ **ap** ap-id | **interface** interface-type
interface-number ] 命令，在 Master 上查看内联接口与端口扩展接口的绑定信
息。


**----**
结束

### 1.3.3 AP的升级和管理


本节介绍对 AP 进行升级和管理的相关配置。

//#//#//#//#//# 背景信息


在端口扩展系统中，控制和管理平面集中在 Master 上。因此，如下 AP 的升级和管理配
置都可在 Master 上完成：


       - 升级软件包


       - 安装补丁


       - 重启 AP


       - 配置 AP 使用 SNMP 与网管通信


       - 配置 AP 向 syslog 服务器发送信息


       - 配置 AP 设备接口的流量统计时间间隔


       - 配置 AP 设备接口的带宽利用率告警阈值


       - 配置 AP 设备信息的时间格式


       - 配置 AP 信息输出方式


配置完成后， Master 通过 NETCONF 通道将上述配置下发到 AP 上，由 AP 自动执行。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 90



HUAWEI NetEngine40E
配置指南 1 接口与链路


说明


          - 在端口扩展系统软件升级过程中，需先升级 AP ，然后再逐个升级 Master 。对 Master 的升级
与普通设备的升级过程相同。


          - 请保证 Master 和 AP 设备的软件版本一致。 Master 和 Ap 的软件版本不一致可能导致 AP 上线后
业务不可用。


          - 对于上述升级和管理配置， AP 也支持作为独立网元的原有操作方式。

//#//#//#//#//# 前置任务


在进行 AP 的升级和管理之前，需完成以下任务：


       - 建立端口扩展系统


       - 将升级用的软件包或补丁文件放置在 Master 或指定服务器上

#### 1.3.3.1 升级软件包


AP 软件包升级可以实现 AP 设备原有功能的优化、新功能的增加以及解决当前运行版本
更新不及时的问题。

//#//#//#//#//# 操作步骤


步骤 **1** 将用于升级的软件包上传到 AP 上，有以下几种方式：



|软件包<br>来源|AP范围|执行命令|
|---|---|---|
|Maste<br>r|指定范围<br>内的AP|**upgrade download-package** packagename**ap-id**<br>{ startapid [**to** endapid ] } &<1-10>|
|Maste<br>r|所有AP|**upgrade download-package** packagename**all-ap**|
|指定服<br>务器|指定范围<br>内的AP|**upgrade download-package** packagename**server-ip** ip-<br>address**server-port** port-number**ap-id** { startapid [**to**<br>endapid ] } &<1-10>|
|指定服<br>务器|所有AP|**upgrade download-package** packagename**server-ip** ip-<br>address**server-port** port-number**all-ap**|


说明


















          - 执行 **upgrade download-package** 命令后，需要用户按提示输入 SFTP 用户名和口令，用于
AP 与 Master 或指定服务器之间的 SFTP 认证。


          - 如果软件包来源是指定服务器，则需先配置端口扩展系统与服务器所在网络的路由互引，保
证 AP 与服务器之间的路由互通。


          - 为了节省系统的存储空间，用户可以在 Master 上执行 **upgrade delete-package type** 命令删
除 AP 上多余的、不再需要使用的软件包。


步骤 **2** 执行命令 **display patch-information ap-id** ap-id ，查看待升级的 AP 是否存在状态为
“ Running ”的补丁。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 91



HUAWEI NetEngine40E
配置指南 1 接口与链路


步骤 **3** （可选）执行命令 **patch delete** **all** { **all-ap** | **ap-id** { startapid [ **to** endapid ] }
&<1-10> } ，删除待升级 AP 上正在运行的补丁文件，直到所有待升级的 AP 上都没有补
丁在运行。


对 AP 进行软件包升级，必须保证 AP 上没有正在运行的补丁，否则无法顺利完成系统软
件的升级操作。若检查出待升级的 AP 上存在正在运行的补丁，当进行非增量型打补丁
时，执行本步操作，否则请跳过此步执行下一步操作。


步骤 **4** 执行命令 **startup system-software** system-file { **all-ap** | **ap-id** { startapid [ **to**
endapid ] } &<1-10> } ，指定 AP 在下次启动的系统软件。


其中：


       - **all-ap** ：配置该 Master 管理的所有 AP 下次启动的系统软件。


       - **ap-id** { startapid [ **to** endapid ] } &<1-10> ：配置在指定 AP ID 范围内的 AP 下次
启动的系统软件。


步骤 **5** 等待一段时间后，执行命令 **display startup ap-id** ap-id 查询设置结果。


**----**
结束

//#//#//#//#//# 后续处理


为了使更新的软件包立即生效，请进行 **AP** 重启 。

#### 1.3.3.2 安装补丁


通过为 AP 安装补丁，可以优化 AP 的系统功能或增加小型新需求。

//#//#//#//#//# 操作步骤


步骤 **1** 将补丁文件上传到 AP 上，有以下几种方式：

















|补丁文<br>件来源|AP范围|执行命令|
|---|---|---|
|Maste<br>r|指定范围<br>内的AP|**upgrade download-package** packagename**ap-id**<br>{ startapid [**to** endapid ] } &<1-10>|
|Maste<br>r|所有AP|**upgrade download-package** packagename**all-ap**|
|指定服<br>务器|指定范围<br>内的AP|**upgrade download-package** packagename**server-ip** ip-<br>address**server-port** port-number**ap-id** { startapid [**to**<br>endapid ] } &<1-10>|
|指定服<br>务器|所有AP|**upgrade download-package** packagename**server-ip** ip-<br>address**server-port** port-number**all-ap**|


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 92



HUAWEI NetEngine40E
配置指南 1 接口与链路


说明


          - 执行 **upgrade download-package** 命令后，需要用户按提示输入 SFTP 用户名和口令，用于
AP 与 Master 或指定服务器之间的 SFTP 认证。


          - 如果软件包来源是指定服务器，则需先配置端口扩展系统与服务器所在网络的路由互引，保
证 AP 与服务器之间的路由互通。


          - 为了节省系统的存储空间，用户可以在 Master 上执行 **upgrade delete-package type** 命令删
除 AP 上多余的、不再需要使用的补丁包。


步骤 **2** 为 AP 安装补丁有以下两种方式：


       - 不中断业务的补丁安装

a. 执行命令 **patch load** file-name **all** **run** { **all-ap** | **ap-id** { startapid [ **to**
endapid ] } &<1-10> } ，为 AP 加载指定补丁文件。



|参数选择|说明|
|---|---|
|**run**|补丁加载后处于运行状态，补丁安装已完成。|
|**all-ap**|为该Master管理的所有AP加载补丁文件。|
|**ap-id** { startapid<br>[ **to** endapid ] }<br>&<1-10>|为指定AP ID范围内的AP加载补丁文件。|



- 下次启动生效的补丁安装




//#//#//#//#//# 后续处理



a. 执行命令 **startup patch** packagename **all** { **all-ap** | **ap-id** { startapid [ **to**
endapid ] } &<1-10> } ，指定 AP 下次启动安装的补丁文件。


AP 重启之后，补丁永久生效。


**----**
结束


- 在采用不中断业务的补丁安装方式时：


–
若补丁安装后，出现异常需要进行补丁包文件的删除，可以执行命令 **patch**
**delete** **all** { **all-ap** | **ap-id** { startapid [ **to** endapid ] } &<1-10> } ，删除 AP
的补丁文件。


- 在采用下次启动生效的补丁安装方式时：


–
若指定下次启动的补丁包文件后，希望取消该文件在下次启动后生效，可以
执行命令 **reset patch-configure** **next-startup** { **all-ap** | **ap-id** { startapid

[ **to** endapid ] } &<1-10> } ，清除 AP 重启后系统启用的补丁包文件。


– 为了使更新的补丁文件立即生效，请进行 **AP** 重启 。


#### 1.3.3.3 重启AP

配置了软件包升级或下次启动生效补丁安装后，重启 AP 才能使得新的系统软件或补丁
及时生效，同时也能快速验证软件包升级或补丁安装是否成功。

//#//#//#//#//# 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 93



HUAWEI NetEngine40E
配置指南 1 接口与链路


步骤 **2** 执行命令 **ap-id** ap-id ，进入相应的 AP 视图。


步骤 **3** 执行命令 **reboot** ，重新启动 AP 。


**----**
结束

#### ******* 配置AP使用SNMP与网管通信


当需要通过网管统一管理 AP 时，可以在 Master 上配置其管理的 AP 使用 SNMP 与网管通
信。

//#//#//#//#//# 背景信息


在端口扩展场景中，如果想通过网管统一管理 AP ，需要在 Master 的端口扩展视图下配
置 SNMP 功能。配置完成后，只要网管与 AP 之间的 IP 路由可达，就可以进行如下交
互：


       - AP 通过 Trap 向网管上报故障告警。


       - AP 上记录活动告警，网管可以通过 MIB 到 AP 上查询或同步活动告警。


说明


          - 在端口扩展系统中，所有的业务配置都在 Master 上，因此由 Master 统一上报业务告警。 AP
独立上报的是自身设备的故障告警。


          - Master 与网管之间使用 SNMP 通信，配置过程与普通场景下的配置相同。


请在 Master 上执行如下操作步骤。

//#//#//#//#//# 前置任务


在配置 AP 使用 SNMP 与网管通信之前，需先完成以下任务：


       - 建立端口扩展系统


在配置上述任务时，请执行可选任务“ *********** （可选）配置端口扩展系统和外部
路由互引 ”，使网管与 AP 之间路由可达。

//#//#//#//#//# 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **virtual-access port-extend** ，进入端口扩展视图。


步骤 **3** 执行命令 **snmp-agent** ，启动 SNMP Agent 服务。


步骤 **4** 执行命令 **snmp-agent sys-info** **version** { **v1** | **v2c** | **v3** | **all** } [*] ，配置 SNMP 的协议版
本。


步骤 **5** 根据 SNMP 的协议版本进行配置。建议使用安全性更高的 SNMPv3 协议版本。


       - SNMPv1 或 SNMPv2 协议版本。


执行命令 **snmp-agent community** { **read** | **write** } **cipher** community-name

[ **mib-view** view-name ] ，配置 AP 设备的读写团体名以及对应的 MIB 视图。


团体是 NMS 和 SNMP Agent 的集合，用团体名来标志。团体名相当于密码，团体
内的设备通信时需要使用团体名来进行认证。只有 NMS 和 SNMP Agent 上配置的
团体名相同时，才能互相访问。执行此步骤配置 AP 设备的 SNMP 团体名，从而实


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 94



HUAWEI NetEngine40E
配置指南 1 接口与链路


现网管与 AP 之间的通信。同时还可以通过 **snmp-agent community** 命令的参数设
置团体名可访问的 MIB 视图等。


       - SNMPv3 协议版本。


a. 执行命令 **snmp-agent group v3** group-name { **authentication** | **privacy** |
**noauthentication** } [ **read-view** read-view | **write-view** write-view |
**notify-view** notify-view ] ，配置 SNMP 用户组。


当网管和设备处在不安全的网络环境中时，比如容易遭受攻击，建议用户配
置参数 **authentication** 或 **privacy** ，使能数据的认证和加密功能。


用户可以选择的认证加密模式如下：

//#//#//#//# ▪ 不配置 authentication 和 privacy 参数或配置 noauthentication 参数：

不认证不加密。适用于网络环境安全，且管理员比较固定的情况下。

//#//#//#//# ▪ 配置 authentication 参数：只认证不加密。适用于网络环境安全，但管

理员个数多，管理员对设备交叉操作比较频繁的情况下。通过认证可以
限制拥有权限的管理员才可以访问该设备。

//#//#//#//# ▪ 配置 authentication 和 privacy 参数：既认证又加密。适用于网络环境不

太安全，管理员交叉操作多的情况下。通过认证和加密既可以限制特定
的管理员访问设备，并且使网络数据以加密形式发送，避免网络数据被
窃取，造成关键数据泄露。


希望网管在指定视图下具有只读权限时（比如级别比较低的管理员），使用
**read-view** 参数。


希望网管在指定视图下具有读写权限时（比如级别比较高的管理员），使用
**write-view** 参数。


当希望过滤不相关告警并配置被管理设备只向网管发送指定 MIB 节点的告警
信息，使用 **notify-view** 参数。如果配置了该参数，只有 **notify-view** 视图下
的 MIB 节点的告警会发送到网管。


b. 执行命令 **snmp-agent usm-userv3** user-name [ **group** group-name ] 为一
个 SNMP 组添加一个新 USM 用户。


c. 执行命令 **snmp-agent usm-user** ，配置 SNMP USM 用户的认证方式、加密
方式和密码。


说明


snmp-agent usm-user 命令中的 md5 、 sha 、 sha2-224 、 DES56 和 3DES168 算法为弱
安全算法，推荐使用其他安全的算法。为避免安全风险，可以执行 crypto weakalgorithm disable 命令去使能弱安全算法功能。


步骤 **6** 执行命令 **snmp-agent target-host** **host-name** host-name **trap** **address** **udp-**
**domain** ip-address **params** **securityname** **cipher** cipher-name [ [ **v1** | **v2c** ] |
**private-netmanager** | **ext-vb** ] [*] ，设置 AP 发送 Trap 消息的目的地。


该步骤是设置 AP 发送的 Trap 消息的目的网管地址。其中，如果接收 Trap 的目标主机是
华为网管，则请指定关键字 **private-netmanager** 。如果 Trap 消息中会携带扩展绑定变
量，请指定关键字 **ext-vb** 。


步骤 **7** （可选）执行命令 **snmp-agent extend error-code enable** ，打开 AP 设备扩展错误码
开关。


只有当网管是华为网管，被管理 AP 设备是华为设备时，才能配置 SNMP 扩展错误码功
能对标准错误码进行扩展，方便用户能够快速准确地定位故障。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 95



HUAWEI NetEngine40E
配置指南 1 接口与链路


步骤 **8** （可选）执行命令 **snmp-agent mib-view** type view-name oid-tree ，创建 MIB 视图
并限定网管监控和管理的 MIB 节点。


       - 需要网管管理 AP 上的绝大部分 MIB 节点，只有一少部分节点不允许网管管理时，
或者在现有的 MIB 视图中希望取消网管对某些节点的访问权限时，使用参数
excluded ，排除这些 MIB 节点。


       - 需要网管管理 AP 上的一少部分 MIB 节点，绝大部分节点不允许网管管理时，或者
在现有的 MIB 视图中添加网管对某些节点的访问权限时，使用参数 included ，添加
这些允许管理的 MIB 节点。


步骤 **9** （可选）执行命令 **snmp-agent trap enable** ，使能 AP 发送 Trap 报文。


使能 AP 向网管发送 Trap ，可以方便用户定位重要的问题。执行此步骤，可以打开 AP 上
所有模块发送 Trap 的开关。


步骤 **10** 执行命令 **commit** ，提交配置。


**----**
结束

#### 1.3.3.5 配置AP向syslog服务器发送信息


当需要在 Syslog 服务器中查看 AP 的运行信息时，可以在 Master 上配置其管理的 AP 向
syslog 服务器发送信息。

//#//#//#//#//# 背景信息


AP 运行时，系统会实时记录 AP 运行情况并产生一些信息。启动信息管理功能后，在
Master 上配置其管理的 AP 向 syslog 服务器发送信息，以备存储和查阅，为网络管理员
监控 AP 的运行情况和诊断网络故障提供依据。


请在 Master 上执行如下操作步骤。

//#//#//#//#//# 前置任务


在配置 AP 向 syslog 服务器发送信息之前，需先完成以下任务：


       - 建立端口扩展系统


在配置上述任务时，请执行可选任务“ *********** （可选）配置端口扩展系统和外部
路由互引 ”，使网管与 AP 之间路由可达。

//#//#//#//#//# 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **virtual-access port-extend** ，进入端口扩展视图。


步骤 **3** 执行命令 **info-center loghost source** interface-type interface-number ，配置 AP 设备
向 syslog 服务器发送信息的源接口信息。


配置成功后，如果 AP 设备向 Syslog 服务器发送信息， Syslog 服务器就可以通过源接口
地址判断信息消息是从哪个 AP 设备发出的，从而便于 Syslog 服务器对收到的信息消息
检索。


步骤 **4** 执行命令 **info-center loghost** ipv4-address [ { **local-time** | **utc** } | **facility** localnumber | **port** port-number | **level** log-level | **transport** { **udp** | **tcp** } ] [*] ，配置当前
Master 管理的所有 AP 向指定 syslog 服务器发送信息。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 96



HUAWEI NetEngine40E
配置指南 1 接口与链路


重复执行此步骤，可以配置 AP 向多个 syslog 服务器发送消息，实现 Syslog 服务器间相
互备份的功能。


步骤 **5** （可选）执行命令 **info-center source** { module-name | **default** } **channel**
{ channel-number | channel-name } [ **log** { **state** { **off** | **on** } | **level** severity } [*] |
**trap** { **state** { **off** | **on** } | **level** severity } [*] | **debug** { **state** { **off** | **on** } | **level**
severity } [*] ] [*] ，配置 AP 向信息通道中输出信息的规则。


步骤 **6** 执行命令 **commit** ，提交配置。


**----**
结束

#### 1.3.3.6 配置AP设备信息的时间格式


如果用户希望为了适应自身习惯或者本地时间而需要调整 AP 设备信息的时间格式时，
可以进行此配置。

//#//#//#//#//# 应用环境


为了满足各地不同的时间习惯，用户可以在 Master 上设置 AP 设备上信息的时间格式。
进行配置后， AP 设备上新的信息会按照所设置的时间格式生成。


说明


此配置中的信息指的是日志信息、 Trap 信息和调试信息。

//#//#//#//#//# 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **virtual-access port-extend** ，进入端口扩展视图。


步骤 **3** 配置下列各类信息的时间格式，用户可以根据自己所需的信息选择其中一项或几项进
行配置：


       - 执行命令 **info-center timestamp** **log** { **boot** | { **date** | **short-date** | **format-**
**date** | **rfc-3339** } [ **precision-time** { **tenth-second** | **millisecond** | **second** } ] }

[ **without-timezone** ] ，配置日志信息的时间格式。


       - 执行命令 **info-center timestamp** **trap** { **boot** | { **date** | **short-date** | **format-**
**date** | **rfc-3339** } [ **precision-time** { **tenth-second** | **millisecond** | **second** } ] }

[ **without-timezone** ] ，配置 Trap 信息的时间格式。


       - 执行命令 **info-center timestamp** **debugging** { **boot** | { **date** | **short-date** |
**format-date** | **rfc-3339** } [ **precision-time** { **tenth-second** | **second** |
**millisecond** } ] } [ **without-timezone** ] ，配置调试信息的时间格式。


当采用 **date** 类型时，时间格式各部分介绍如下表所示。


表 **1-11** date 类型的时间格式各部分描述

|字段|含义|取值|
|---|---|---|
|yyyy|年份|4位数字格式。|
|mm|月份|Jan，Feb，Mar，Apr，May，Jun，Jul，Aug，<br>Sep，Oct，Nov，Dec。|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 97



HUAWEI NetEngine40E
配置指南 1 接口与链路



|字段|含义|取值|
|---|---|---|
|dd|日期|如果日期的值小于10，则在日期前面补一个空<br>格，例如“ 7”。|
|hh:mm:ss|本地时间，时:分:<br>秒|hh采用24小时制，取值范围是00～23，mm和ss<br>取值范围都是00～59。|


步骤 **4** 执行命令 **commit** ，提交配置。


**----**
结束

#### 1.3.3.7 配置AP信息输出方式




//#//#//#//#//# 背景信息 操作步骤



用户可以通过此配置，使 AP 上的信息输出到本地文件或显示区，还可以设置文件和显
示区的一些属性。


AP 设备以日志信息的形式实时记录 AP 运行时出现的各种情况。通过配置信息输出方
式，用户可以登录 AP 设备查看相关日志信息，了解 AP 的运行情况。


请在 Master 上执行如下操作步骤。


- 配置信息输出到显示区


a. 执行命令 **system-view** ，进入系统视图。


b. 执行命令 **virtual-access port-extend** ，进入端口扩展视图。

c. （可选）执行命令 **info-center logbuffer size** buffersize ，配置 AP 设备上日
志信息的显示数目。


d. 执行命令 **info-center source** { module-name | **default** } **channel**
{ channel-number | channel-name } **log** { **state** { **off** | **on** } | **level**
severity } [*] ，配置 AP 向信息通道中输出日志信息的规则。


e. 执行命令 **commit** ，提交配置。


- 配置信息输入到文件


a. 执行命令 **system-view** ，进入系统视图。


b. 执行命令 **virtual-access port-extend** ，进入端口扩展视图。

c. （可选）执行命令 **info-center debugfile-name** file-name ，设置 AP 的
debug 日志文件名称。


在 AP 上， debug 日志默认生成的日志文件名为 debug.log 的文本文件，此步骤
可以配置 debug 日志文件名称，方便根据特殊要求获取 debug 日志文件。

d. （可选）执行命令 **info-center max-debugfile-number** max-debugfilenumber ，设置 AP 的日志目录下 debug 日志文件保存的最大个数。


在 AP 上 debug 日志默认生成的日志文件名为 debug.log 的文本文件，当文件大
小超过 8M 时，日志文件会被压缩成 debug_*.log.zip 文件。此步骤可以设置日
志目录下 debug 日志文件保存的最大个数。当 ZIP 文件总数是超过设置的最大
数时，系统会删除时间最久的文件。



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 98



HUAWEI NetEngine40E
配置指南 1 接口与链路


e. 执行命令 **info-center source** { module-name | **default** } **channel**
{ channel-number | channel-name } [ **log** { **state** { **off** | **on** } | **level**
severity } [*] | **trap** { **state** { **off** | **on** } | **level** severity } [*] | **debug** { **state**
{ **off** | **on** } | **level** severity } [*] ] [*] ，配置 AP 向信息通道中输出信息的规则。

f. 执行命令 **commit** ，提交配置。


**----**
结束

#### ******* 配置AP设备接口的流量统计时间间隔


当需要调整 AP 设备上外联接口的流量统计时间间隔时，可以在 Master 上完成相应的配
置。

//#//#//#//#//# 背景信息


AP 设备上的外联接口都默认使能流量统计功能，有助于监控接口和业务的运行情况。
当需要调整 AP 设备上外联接口的流量统计时间间隔时，在 Master 上有以下两种方式配
置：


       - 配置全局流量统计时间间隔


在相应的 AP 视图下执行，完成后配置将下发至对应的 AP 上，配置生效后， AP 设备
上未在对应端口扩展接口下配置流量统计时间间隔的外联接口，将统一使用全局
的时间间隔。


       - 配置指定外联接口的流量统计时间间隔


在相应的端口扩展接口（包括端口扩展 Trunk 接口）视图下执行，完成后配置将下
发至对应的 AP 上，并在对应的外联接口上设置接口的流量统计时间间隔。此配置
的优先级高于全局配置。

//#//#//#//#//# 操作步骤


       - 配置全局流量统计时间间隔


a. 执行命令 **system-view** ，进入系统视图。


b. 执行命令 **ap-id** ap-id ，进入相应的 AP 视图。

c. 执行命令 **set flow-stat interval** interval ，设置 AP 设备上全局流量统计的时
间间隔。


d. 执行命令 **commit** ，提交配置。


       - 配置指定外联接口的流量统计时间间隔


a. 执行命令 **system-view** ，进入系统视图。


b. 执行命令 **interface** interface-type interface-number ，进入端口扩展接口或
端口扩展 Trunk 接口视图。

c. 执行命令 **set flow-stat interval** interval ，设置对应 AP 上指定外联接口的流
量统计时间间隔。


d. 执行命令 **commit** ，提交配置。


**----**
结束

#### 1.3.3.9 配置AP设备接口的带宽利用率告警阈值


当需要调整 AP 设备上外联接口的带宽利用率告警阈值，可以在 Master 上完成相应的配
置。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 99



HUAWEI NetEngine40E
配置指南 1 接口与链路

//#//#//#//#//# 背景信息


通过带宽利用率能够了解 AP 设备的负载，如果带宽利用率超过一定阈值则表明带宽资
源已经难以满足当前的业务需求，需对 AP 设备进行扩容，或将业务迁移到其他 AP 设备
上。 AP 设备上外联接口默认的带宽利用率告警阈值为 90% ，如果带宽利用率超过 90%
产生告警，此时由于带宽利用率已经接近饱和，从发现到扩容或迁移业务这段时间很
可能会造成流量丢失。为了避免上述问题，可以调整 AP 设备上外联接口的带宽利用率
告警阈值，例如：将入带宽的告警阈值设置为 80% ，这样在入带宽利用率达到 80% 时
通过告警提醒用户及时处理，避免影响业务。

//#//#//#//#//# 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **interface** interface-type interface-number ，进入端口扩展接口视图。


步骤 **3** 执行命令 **trap-threshold** { **input-rate** | **output-rate** } bandwidth-in-use [ **resume-**
**rate** resume-threshold ] ，设置 AP 设备上对应外联接口的出、入带宽利用率的告警阈
值。


执行此步骤时，需要注意：


       - resume-threshold 需要小于等于 bandwidth-in-use 。


       - 若不指定 **resume-rate** resume-threshold ，则系统会自动将恢复告警带宽利用率
的阈值设置为与产生告警的带宽利用率阈值相等。


       - 为了避免告警震荡， bandwidth-in-use 和 resume-threshold 的取值尽量保持差
距。


步骤 **4** 执行命令 **commit** ，提交配置。


**----**
结束

### 1.3.4 维护端口扩展系统


本节介绍端口扩展系统的维护操作。

#### 1.3.4.1 清除端口扩展接口统计信息


当您需要统计一定时间内端口扩展接口的流量信息，这时必须在统计开始前清除该接
口原有的统计信息，使接口重新进行统计。

//#//#//#//#//# 背景信息


须知


清除计数器信息后，以前的信息将无法恢复，务必仔细确认。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 100



HUAWEI NetEngine40E
配置指南 1 接口与链路

//#//#//#//#//# 操作步骤


       - 在确认需要清除指定端口扩展接口或端口扩展 Trunk 接口对应的远端计数或远端
MIB 计数后，请在用户视图下执行命令 **reset counters** [ **if-mib** ] **interface**
{ interface-name | interface-type interface-number } **remote** 。


**----**
结束

### 1.3.5 端口扩展配置举例


本节介绍部署端口扩展系统示例。

#### ******* 配置端口扩展系统示例


本例描述了建立端口扩展系统的详细配置过程。

//#//#//#//#//# 组网需求


端口扩展系统的典型组网如 图 **1-9** 所示，用于在 IP Core 网络中提供具备高密度以太类接
口的设备。将一台性能较高的设备设置为 Master ，将大量的支持以太类接口的低端设
备设置为 AP ，把 AP 上的以太类接口映射为 Master 上的端口扩展接口，业务配置只需在
Master 的端口扩展接口上完成，从而使 Master 设备具备高密度的以太类接口。


图 **1-9** 配置端口扩展系统的典型组网图


说明


本例中 Interface1 、 Interface2 、 Interface3 分别代表 GE1/0/1 、 GE1/0/2 、 GE1/0/3 。

//#//#//#//#//# 配置思路


采用如下的思路配置端口扩展系统：


1. 配置 Master 基本功能。


2. 在 Master 上配置 AP 的基本功能。


3. 配置端口扩展的认证方案。


4. 配置内联接口。


5. 配置端口扩展接口。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 101



HUAWEI NetEngine40E
配置指南 1 接口与链路

//#//#//#//#//# 数据准备


为完成此配置例，需准备如下的数据：


       - Master 的管理 IP 地址为 ******* ， AP1 和 AP2 的管理 IP 地址分别为 ******* 、 ******* 。


       - 端口扩展系统的 IS-IS 认证密钥 ID 是 1 ，认证密码为 YsHsjx_202206 。


       - Master 与 AP 建立 STelnet 连接的默认用户名为 root ，口令为 Changeme_123 。


       - Master 与 AP1 之间的内联接口均为 GE1/0/1 ， Master 与 AP2 之间的内联接口分别为
GE1/0/2 、 GE1/0/1 。


       - AP1 的 ID 为 2000 ， ESN 号（在 AP 设备上查询）为 391092333866236 ， AP1 上的外
联接口分别为 GE1/0/2 和 GE1/0/3 。


       - AP2 的 ID 为 2001 ， ESN 号（在 AP 设备上查询）为 391092333000298 ， AP2 上的外
联接口分别为 GE1/0/2 和 GE1/0/3 。


       - Master 与 AP1 、 AP2 之间采用本地认证方式； Master 登录到 AP1 和 AP2 所需的用户
名为 sys-admin ，口令为 YsHsjx_202207 ， SFTP 目录为 cfcard:/ ； Master 在 AP1 和
AP2 上创建的本地用户名为 sys-admin ，口令为 YsHsjx_202207 。

//#//#//#//#//# 操作步骤


步骤 **1** AP 设备需保持默认配置


AP 首次上线时， Master 向 AP 下发 root 用户建连，如果：


       - AP 上线前，用户如果使用 STelnet 方式首次创建用户登录 AP 、或者使用串口登录
AP 时设置串口密码，则 AP 无法正常上线，需手动恢复空配置并加载预配置文件。
以 AP1 为例，配置如下：
<AP1> **reset saved-configuration**
Warning: The action will delete the saved configuration on the device.
The configuration will be erased to reconfigure.Continue? [Y/N]:y
Warning: Now clearing the configuration on the device.
Info: Succeeded in clearing the configuration on the device.
<AP1> **startup default-configuration default-custom_XXX_V***R***C**SPC***.defcfg**
Warning: The action will override and update the default configuration file on the device. Continue?

[Y/N]:y

...Info: Succeeded in setting the configuration for booting system.


说明


请从华为公司正规渠道获取对应产品版本的预配置文件 defaultcustom_XXX_V***R***C**SPC***.defcfg 。


       - AP 上线后，用户如果修改 AP 上 root 用户的密码，则后续 AP 无法正常与 Master 建联
对账 。


步骤 **2** 配置 Master 基本功能


<Master> **system-view**

[ ~ Master] **ssh client first-time enable**

[ ~ Master] **dcn**
Warning: This operation will enable DCN function. Continue? [Y/N]:y
Info: The operation may take a few seconds. Please wait for a moment....done.

[*Master] **commit**

[ ~ Master] **virtual-access port-extend**

[*Master-virtual-access-portextend] **role master**

[*Master-virtual-access-portextend] **admin *********

[*Master-virtual-access-portextend] **ap default login-user root login-password Changeme_123**

[*Master-virtual-access-portextend] **isis authentication-mode hmac-sha256 key-id 1 cipher**
**YsHsjx_202206**

[*Master-virtual-access-portextend] **commit**


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 102



HUAWEI NetEngine40E
配置指南 1 接口与链路


[ ~ Master-virtual-access-portextend] **quit**


步骤 **3** 在 Master 上配置 AP 的基本功能


[ ~ Master] **ap-id 2000**

[*Master-ap2000] **esn 391092333866236**

[*Master-ap2000] **commit**

[ ~ Master-ap2000] **admin *********

[ ~ Master-ap2000] **quit**

[ ~ Master] **ap-id 2001**

[*Master-ap2001] **esn 391092333000298**

[*Master-ap2001] **commit**

[ ~ Master-ap2001] **admin *********

[ ~ Master-ap2001] **quit**


步骤 **4** 配置端口扩展的认证方案


[ ~ Master] **ap-id 2000**

[ ~ Master-ap2000] **login-user sys-admin login-password YsHsjx_202207**

[*Master-ap2000] **commit**

[ ~ Master-ap2000] **login-user sys-admin sftp-directory cfcard:/**

[ ~ Master-ap2000] **ap-user**

[ ~ Master-ap2000-ap-user] **local-user sys-admin password cipher YsHsjx_202207**

[ ~ Master-ap2000-ap-user] **quit**

[ ~ Master-ap2000] **authentication-mode local**

[ ~ Master-ap2000] **quit**

[ ~ Master] **ap-id 2001**

[ ~ Master-ap2001] **login-user sys-admin login-password YsHsjx_202207**

[*Master-ap2001] **commit**

[ ~ Master-ap2001] **login-user sys-admin sftp-directory cfcard:/**

[ ~ Master-ap2001] **ap-user**

[ ~ Master-ap2001-ap-user] **local-user sys-admin password cipher YsHsjx_202207**

[ ~ Master-ap2001-ap-user] **quit**

[ ~ Master-ap2001] **authentication-mode local**

[ ~ Master-ap2001] **quit**


步骤 **5** 配置内联接口


[ ~ Master] **interface GigabitEthernet1/1/0/1**

[ ~ Master-GigabitEthernet1/1/0/1] **virtual-access enable**

[ ~ Master-GigabitEthernet1/1/0/1] **dcn**

[*Master-GigabitEthernet1/1/0/1] **quit**

[*Master] **interface GigabitEthernet1/1/0/2**

[*Master-GigabitEthernet1/1/0/2] **virtual-access enable**

[*Master-GigabitEthernet1/1/0/2] **dcn**

[*Master-GigabitEthernet1/1/0/2] **quit**

[ ~ Master] **ap-id 2000**

[ ~ Master-ap2000] **inner-connect GigabitEthernet1/0/1 binding GigabitEthernet1/1/0/1**

[*Master-ap2000] **quit**

[*Master] **ap-id 2001**

[*Master-ap2001] **inner-connect GigabitEthernet1/0/1 binding GigabitEthernet1/1/0/2**

[*Master-ap2001] **commit**

[ ~ Master-ap2001] **quit**


步骤 **6** 配置端口扩展接口


[ ~ Master] **ap-id 2000**

[ ~ Master-ap2000] **remote-interface GigabitEthernet1/0/2 binding GigabitEthernet1/1/0/1**

[*Master-ap2000] **remote-interface GigabitEthernet1/0/3 binding GigabitEthernet1/1/0/1**

[*Master-ap2000] **quit**

[*Master] **ap-id 2001**

[*Master-ap2001] **remote-interface GigabitEthernet1/0/2 binding GigabitEthernet1/1/0/2**

[*Master-ap2001] **remote-interface GigabitEthernet1/0/3 binding GigabitEthernet1/1/0/2**

[*Master-ap2001] **commit**

[ ~ Master-ap2001] **quit**


步骤 **7** 检查配置结果


在 Master 上执行 **display virtual-access ap** 命令，可以查看到该 Master 管理的 AP 基本
信息：


[ ~ Master] **display virtual-access ap**


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 103



HUAWEI NetEngine40E
配置指南 1 接口与链路


AP Information

AP Esn        : 391092333866236

AP ID        : 2000        Admin IP      : *******

Master        : *******

State        : Online

Online Time     : 2017-09-30 01:03:56


AP Esn        : 391092333000298

AP ID        : 2001        Admin IP      : *******

Master        : *******

State        : Online

Online Time     : 2017-09-30 01:31:26



在 Master 上执行 **display virtual-access ap-interface** 命令，可以查看到该 Master 管理
的 AP 接口信息：


[ ~ Master] **display virtual-access ap-interface**
Ap Interface Information
Total 20 record(s) found:

APID  APAdminIP      Interface        IfIndex        State  Type

2000  *******   GigabitEthernet1/0/1      8           Up    inner
2000  *******   GigabitEthernet1/0/2      9           Up    outer
2000  *******   GigabitEthernet1/0/3      10           Up    outer
2001  *******   GigabitEthernet1/0/1      8           Up    inner
2001  *******   GigabitEthernet1/0/2      9           Up    outer
2001  *******   GigabitEthernet1/0/3      10           Up    outer


在 Master 上执行 **display virtual-access bindinfo** 命令，可以查看到 Master 上内联接
口与端口扩展接口的绑定信息：


[ ~ Master] **display virtual-access bindinfo**

AP-ID      Inner-interface           Out-interface

2000      GigabitEthernet1/1/0/1       GigabitEthernet2000/1/0/2
2000      GigabitEthernet1/1/0/1       GigabitEthernet2000/1/0/3
2001      GigabitEthernet1/1/0/2       GigabitEthernet2001/1/0/2
2001      GigabitEthernet1/1/0/2       GigabitEthernet2001/1/0/3


**----**
结束

//#//#//#//#//# 配置文件


       - AP1 的配置文件


//#

sysname AP1
//#

virtual-access
role ap
admin *******
master admin-ip primary *******
isis authentication-mode hmac-sha256 key-id 1 cipher %^%//#OqaV.B&wk-eu\lD0(u:5ZWFN)r'k:2uIW.-/
9:NU%^%//#

//#
undo user-security-policy enable
//#
ip dcn vpn-instance __dcn_vpn__
ipv4-family
//#

bfd

//#

aaa


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 104



HUAWEI NetEngine40E
配置指南 1 接口与链路


local-user sys-admin password irreversible-cipher $1c$VW58EBdUe"$uXfj.2l)I//#za`:6tJ,w$U|
([5}MsD//#|):rU(cV/+$
local-user sys-admin service-type ssh
local-user sys-admin state block fail-times 3 interval 5
local-user sys-admin user-group manage-ug
//#

authentication-scheme default0

//#

authentication-scheme default1

//#

authentication-scheme default

authentication-mode local

//#

authorization-scheme default

//#
accounting-scheme default0
//#
accounting-scheme default1
//#

domain default0

//#

domain default1

//#
domain default_admin
authorization-scheme default

//#

isis 65534
description auto-generated for virtual-access
is-level level-2
cost-style wide
virtual-access enable
network-entity 00.38ba.33bc.a402.00
binding interface GigabitEthernet1/0/1 down-weight 10
//#
interface GigabitEthernet1/0/1
undo shutdown

isis enable 65534
isis circuit-type p2p
dcn

virtual-access enable

//#
interface GigabitEthernet1/0/2
undo shutdown

//#
interface GigabitEthernet1/0/3
undo shutdown

//#
interface LoopBack2147483646
description virtual-access loopback interface
ip binding vpn-instance __dcn_vpn__
ip address ******* ***************
//#
interface LoopBack2147483647
description DCN loopback interface
ip binding vpn-instance __dcn_vpn__
ip address ********** ***********
//#
ospf 65534 vpn-instance __dcn_vpn__
description DCN ospf create by default
opaque-capability enable
hostname
vpn-instance-capability simple
area 0.0.0.0

network 0.0.0.0 ***************

//#
!The DCN function implements the capability of plug-and-play for this device.
!A NE IP address based on the unique NE ID is automatically generated in VPN
!of DCN. It is recommended that the NE IP address be changed to the planned
!one by running the ne-ip X.X.X.X <mask> command after the device being online.


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 105



HUAWEI NetEngine40E
配置指南 1 接口与链路


dcn

bandwidth ethernet 1024
bandwidth pos 1024
//#
stelnet ipv4 server enable
sftp ipv4 server enable
snetconf ipv4 server enable
stelnet ipv6 server enable
sftp ipv6 server enable
snetconf ipv6 server enable
ssh user sys-admin
ssh user sys-admin authentication-type password
ssh user sys-admin service-type all
ssh user sys-admin sftp-directory cfcard:/
ssh authorization-type default aaa
//#
ssh server cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr
ssh server hmac sha2_512 sha2_256
ssh server key-exchange dh_group_exchange_sha256
//#
ssh server publickey rsa_sha2_256 rsa_sha2_512
//#
ssh server dh-exchange min-len 3072
//#
ssh client first-time enable
sftp client-source -a ******* -vpn-instance __dcn_vpn__
//#

user-interface con 0

//#
user-interface vty 0 4
authentication-mode aaa
protocol inbound ssh
//#

netconf

idle-timeout 0 0

//#

local-aaa-server

//#

return

       - AP2 的配置文件


//#

sysname AP2
//#

virtual-access
role ap
admin *******
master admin-ip primary *******
isis authentication-mode hmac-sha256 key-id 1 cipher %^%//#gb0N3W.{o6QZelLT!//#yIPjdj/
~ Dk8$F&p73 ~ P/x.%^%//#
//#
undo user-security-policy enable
//#
ip dcn vpn-instance __dcn_vpn__
ipv4-family
//#

bfd

//#

aaa
local-user sys-admin password irreversible-cipher $1c$2"iN;T!PrW$4H+g1J;+D>"[]=$i,Z/4(5"MWJ-Ld
%)']CO`l>Z9$
local-user sys-admin service-type ssh
local-user sys-admin state block fail-times 3 interval 5
local-user sys-admin user-group manage-ug
//#

authentication-scheme default0

//#

authentication-scheme default1

//#

authentication-scheme default


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 106



HUAWEI NetEngine40E
配置指南 1 接口与链路


authentication-mode local

//#

authorization-scheme default

//#
accounting-scheme default0
//#
accounting-scheme default1
//#

domain default0

//#

domain default1

//#
domain default_admin
authorization-scheme default

//#

isis 65534
description auto-generated for virtual-access
is-level level-2
cost-style wide
virtual-access enable
network-entity 00.38ba.33bc.a402.00
binding interface GigabitEthernet1/0/1 down-weight 10
//#
interface GigabitEthernet1/0/1
undo shutdown

isis enable 65534
isis circuit-type p2p
dcn

virtual-access enable

//#
interface GigabitEthernet1/0/2
undo shutdown

//#
interface GigabitEthernet1/0/3
undo shutdown

//#
interface LoopBack2147483646
description virtual-access loopback interface
ip binding vpn-instance __dcn_vpn__
ip address ******* ***************
//#
interface LoopBack2147483647
description DCN loopback interface
ip binding vpn-instance __dcn_vpn__
ip address ********** ***********
//#
ospf 65534 vpn-instance __dcn_vpn__
description DCN ospf create by default
opaque-capability enable
hostname
vpn-instance-capability simple
area 0.0.0.0

network 0.0.0.0 ***************

//#
!The DCN function implements the capability of plug-and-play for this device.
!A NE IP address based on the unique NE ID is automatically generated in VPN
!of DCN. It is recommended that the NE IP address be changed to the planned
!one by running the ne-ip X.X.X.X <mask> command after the device being online.
dcn

//#
stelnet ipv4 server enable
sftp ipv4 server enable
snetconf ipv4 server enable
stelnet ipv6 server enable
sftp ipv6 server enable
snetconf ipv6 server enable
ssh user sys-admin
ssh user sys-admin authentication-type password
ssh user sys-admin service-type all


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 107



HUAWEI NetEngine40E
配置指南 1 接口与链路


ssh user sys-admin sftp-directory cfcard:/
ssh authorization-type default aaa
//#
ssh server cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr
ssh server hmac sha2_512 sha2_256
ssh server key-exchange dh_group_exchange_sha256
//#
ssh server publickey rsa_sha2_256 rsa_sha2_512
//#
ssh server dh-exchange min-len 3072
//#
ssh client first-time enable
sftp client-source -a ******* -vpn-instance __dcn_vpn__
//#

user-interface con 0

//#
user-interface vty 0 4
authentication-mode aaa
protocol inbound ssh
//#

netconf

idle-timeout 0 0

//#

local-aaa-server

//#

return

       - Master 的配置文件


//#

sysname Master
//#
virtual-access port-extend
role master

admin *******
ap default login-user root login-password %^%//#gOTF'nZ=j7+odk7U&I%>xVl0+h.l8AuNHt2_Y*n ~ %^
%//#
isis authentication-mode hmac-sha256 key-id 1 cipher %^%//#RmQD<'UJ)/Nl3L6*
+L8=*&(P"e4H[B ~ JbRW!W>3A%^%//#
//#
rsa peer-public-key *******
public-key-code begin
3082010A

02820101

009FD60F 4245F341 C86A4717 BB17C282 CE090BB7 12E1A73F FFBF0D44 D51EF073

49A6CA0D 90077E7C BE173037 2851FDB5 A3390BB9 96EAE330 3F999B47 0A765780

C21BEA42 9A132975 D3A1D64B DEF6E5C2 4CD6A7F3 909F7574 9B84B0A8 BF744446

67B00D1D 440DD081 8ABDA172 F995C80C C2953A13 F8D6EECA E835A442 C650A464

BA4B96A2 15D21EBE DD71D5FC 06F559D9 7DC11AD7 3D538CFC FDD408C8 03AA4B3B

D93E4764 BBDE5FB8 9A2ACBCF 3E7188EE 81995DC4 5A2C8F63 8994F7DA 0094E410

96C3F70E 9AFBA22A 273F53A3 D152B245 636419BA 71B03A9A E488BA20 1FC18BFB

396A66A4 0F325E9C A2F1C0CD 9759E1AA ED9A27E6 68605CE2 D284F541 AAD00ED0

9F

0203

010001
public-key-code end
peer-public-key end
//#
rsa peer-public-key *******
public-key-code begin
3082010A

02820101

00CFEC57 3531F1E0 97D6D719 5A4B3D2D 707EE3DD E3FDF9FA C4B73D47 E6D439B5

3ED2E12F C63D83B7 E76C9B25 125490F5 30AB7277 3BCCB159 F3C90881 32CCDEDC

E703EB64 5A46DDD2 969A2368 16CFF8FB DA1628D3 F8989A6B 135B66E5 CDC00157

68246295 C4670F50 BFD4F1C0 48A2C195 E4243F3A BD6BA3E3 32651930 F8A8E4B5

3020F373 2A58F15E DFE003B4 1B1ACF80 9E0490C1 BE5A2997 97D8B146 3FB16FF5

9F26DFF6 F83040AF 012D59EA 943F8AB8 71E21A07 5785537F F6523D9E C3050217

FD03E7B3 BA72AEEA FD108F6D 644EFABB 7C9F2971 065EE65F DDB61426 6ED5084B

CC7C99E3 C0B6A4BF 95181DBC E2DF89DD 29A24AD8 51E55CCA 8DB1F130 7CE0798F

83


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 108



HUAWEI NetEngine40E
配置指南 1 接口与链路


0203

010001
public-key-code end
peer-public-key end
//#
rsa peer-public-key **********
public-key-code begin
3082010A

02820101

009FD60F 4245F341 C86A4717 BB17C282 CE090BB7 12E1A73F FFBF0D44 D51EF073

49A6CA0D 90077E7C BE173037 2851FDB5 A3390BB9 96EAE330 3F999B47 0A765780

C21BEA42 9A132975 D3A1D64B DEF6E5C2 4CD6A7F3 909F7574 9B84B0A8 BF744446

67B00D1D 440DD081 8ABDA172 F995C80C C2953A13 F8D6EECA E835A442 C650A464

BA4B96A2 15D21EBE DD71D5FC 06F559D9 7DC11AD7 3D538CFC FDD408C8 03AA4B3B

D93E4764 BBDE5FB8 9A2ACBCF 3E7188EE 81995DC4 5A2C8F63 8994F7DA 0094E410

96C3F70E 9AFBA22A 273F53A3 D152B245 636419BA 71B03A9A E488BA20 1FC18BFB

396A66A4 0F325E9C A2F1C0CD 9759E1AA ED9A27E6 68605CE2 D284F541 AAD00ED0

9F

0203

010001
public-key-code end
peer-public-key end
//#
rsa peer-public-key **********
public-key-code begin
3082010A

02820101

00CFEC57 3531F1E0 97D6D719 5A4B3D2D 707EE3DD E3FDF9FA C4B73D47 E6D439B5

3ED2E12F C63D83B7 E76C9B25 125490F5 30AB7277 3BCCB159 F3C90881 32CCDEDC

E703EB64 5A46DDD2 969A2368 16CFF8FB DA1628D3 F8989A6B 135B66E5 CDC00157

68246295 C4670F50 BFD4F1C0 48A2C195 E4243F3A BD6BA3E3 32651930 F8A8E4B5

3020F373 2A58F15E DFE003B4 1B1ACF80 9E0490C1 BE5A2997 97D8B146 3FB16FF5

9F26DFF6 F83040AF 012D59EA 943F8AB8 71E21A07 5785537F F6523D9E C3050217

FD03E7B3 BA72AEEA FD108F6D 644EFABB 7C9F2971 065EE65F DDB61426 6ED5084B

CC7C99E3 C0B6A4BF 95181DBC E2DF89DD 29A24AD8 51E55CCA 8DB1F130 7CE0798F

83

0203

010001
public-key-code end
peer-public-key end
//#
ip dcn vpn-instance __dcn_vpn__
ipv4-family
//#

bfd

//#

dcn

//#

isis 65534
description auto-generated for virtual-access
is-level level-2
cost-style wide
virtual-access enable
network-entity 00.38ba.1a42.1f01.00
binding interface GigabitEthernet1/1/0/1 down-weight 10
binding interface GigabitEthernet1/1/0/2 down-weight 10
//#
interface GigabitEthernet1/1/0/1
undo shutdown

isis enable 65534
isis circuit-type p2p
dcn

virtual-access enable

//#
interface GigabitEthernet1/1/0/2
undo shutdown

isis enable 65534
isis circuit-type p2p
dcn


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 109



HUAWEI NetEngine40E
配置指南 1 接口与链路


virtual-access enable

//#
interface LoopBack2147483646
description virtual-access loopback interface
ip binding vpn-instance __dcn_vpn__
ip address ******* ***************
//#
interface LoopBack2147483647
description DCN loopback interface
ip binding vpn-instance __dcn_vpn__
ip address ******** ***********
//#
ap-id 2000
sysname ap2000
esn 391092333866236

admin *******

~
login-user sys-admin login-password %^%//#(p@y@ 'n3/4m<"=;YyWDZIyvCQuK5D1JbuYk^ODQ%^%//#
login-user sys-admin sftp-directory cfcard:/
authentication-mode local

//#

ap-user
local-user sys-admin password cipher %^%//#otll=pnt1//#I_[1TE|k'F9-RT!@>rGAa%<&J@q9H&%^%//#
inner-connect GigabitEthernet1/0/1 binding GigabitEthernet1/1/0/1
remote-interface GigabitEthernet1/0/2 binding GigabitEthernet1/1/0/1
remote-interface GigabitEthernet1/0/3 binding GigabitEthernet1/1/0/1
//#
ap-id 2001
sysname ap2001
esn 391092333000298

admin *******
login-user sys-admin login-password %^%//#/md1*$flA+)0\t.0B"43,q{>+2*)f-k&PWLDzjcL%^%//#
login-user sys-admin sftp-directory cfcard:/
authentication-mode local

//#

ap-user
local-user sys-admin password cipher %^%//#(1F!FQJ[FP&+-H@%pe)G5h9r:g$)DF&19m@N\T(9%^%//#
inner-connect GigabitEthernet1/0/1 binding GigabitEthernet1/1/0/2
remote-interface GigabitEthernet1/0/2 binding GigabitEthernet1/1/0/2
remote-interface GigabitEthernet1/0/3 binding GigabitEthernet1/1/0/2
//#

interface NULL0

//#
ospf 65534 vpn-instance __dcn_vpn__
description DCN ospf create by default
opaque-capability enable
hostname
vpn-instance-capability simple
area 0.0.0.0

network 0.0.0.0 ***************

//#
!The DCN function implements the capability of plug-and-play for this device.
!A NE IP address based on the unique NE ID is automatically generated in VPN
!of DCN. It is recommended that the NE IP address be changed to the planned
!one by running the ne-ip X.X.X.X <MASK> command after the device being online.
dcn

bandwidth ethernet 1024
bandwidth pos 1024
//#
ssh authorization-type default aaa
//#
ssh client publickey dsa ecc rsa rsa_sha2_256 rsa_sha2_512
//#
ssh client cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr
ssh client hmac sha2_512 sha2_256
ssh client key-exchange dh_group_exchange_sha256
//#
ssh client first-time enable
ssh client ********** assign rsa-key **********
ssh client ********** assign rsa-key **********


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 110



HUAWEI NetEngine40E
配置指南 1 接口与链路


ssh client ******* assign rsa-key *******
ssh client ******* assign rsa-key *******
//#

user-interface con 0

//#

local-aaa-server

//#

return

## 1.4 传输告警定制与抑制配置


配置传输告警特性，可以降低网络中传输告警的频繁发生对网络稳定性的影响。

### 1.4.1 传输告警定制与抑制概述


传输告警定制功能可以有效控制告警信号对接口状态变化的影响。传输告警抑制功能
可以有效实现对告警信号进行抑制，避免接口的反复振荡。

//#//#//#//#//# 定义


当前电信级网络对 IP 设备网络可靠性的要求越来越高，因此要求网络中的设备能够快
速检测到故障信息。当接口启动快速检测功能后，告警信息上报速度加快，会引起接
口的物理层状态频繁在 Up 和 Down 之间切换，导致网络反复振荡。因而需要对告警进
行过滤和抑制，避免网络频繁振荡。


传输告警抑制功能可以有效实现对告警信号进行过滤和抑制，避免接口的反复振荡。
同时提供告警定制功能，使得告警对接口状态变化的影响可以有效控制。


传输告警定制与抑制具体实现的功能如下：


       - 传输告警定制：可以定制哪些告警才能够引起接口的物理层状态变化，起到告警
过滤效果。


       - 传输告警抑制：通过设置门限值，通过一系列算法来抑制网络反复振荡。

//#//#//#//#//# 目的


用户可以根据需求定制传输告警，从而过滤掉不在定制范围内的传输告警。传输告警
抑制功能对定制的告警设定门限值，从而忽略传输链路保护等产生的毛刺。避免网络
频繁振荡。


在骨干网或城域网中， IP 路由设备要与传输设备进行对接 ( 一般的传输设备有 SDH
（ Synchronous Digital Hierarchy ）、 SONET （ Synchronous Optical Network ）和
WDM （ Wavelength Division Multiplexing ）等 ) 。当传输设备发生故障时，在 IP 路由
设备上会接收到一些告警，之后传输设备会进行链路倒换，待传输设备的链路正常后
会向 IP 路由设备发出告警清除。传输设备从发生告警到告警消除的保护时间通常是
50ms ～ 200ms 。从 IP 路由设备上来看，这通常是一个 50ms ～ 200ms 内的毛刺。这些毛
刺会导致 IP 路由设备接口状态频繁变化，导致路由设备频繁的进行路由计算，造成路
由振荡的情况，对路由设备的性能造成影响。


从网络整体角度来看，希望 IP 路由设备能够忽略这样的毛刺，不做任何处理。也就是
说在传输设备进行维护或者传输设备进行链路保护时，路由设备要通过对传输设备产
生的告警采取定制与抑制手段，从而不产生路由振荡，保证路由设备的稳定运行。传
输告警定制功能可以使告警对接口的物理层状态的影响得到控制。传输告警抑制功能
可以有效实现对特定告警信号进行过滤和抑制，避免接口反复振荡。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 111



HUAWEI NetEngine40E
配置指南 1 接口与链路


在主备倒换过程中，系统可以正常处理传输告警的各种告警信号，传输告警的配置也
依然生效。

### 1.4.2 传输告警定制与抑制配置注意事项

//#//#//#//#//# 特性限制


无

### 1.4.3 配置传输告警的定制功能


通过本节可以了解到传输告警定制的应用环境、前置任务和数据准备。

//#//#//#//#//# 应用环境


当和传输设备相连时，如果网络不稳定，将产生大量的毛刺告警信息，而这些告警信
息将导致接口的物理层状态不断地在 Up 和 Down 状态之间切换。如果希望能指定哪些
告警可以触发接口的物理层状态为 Down ，可以配置传输告警的定制功能。

//#//#//#//#//# 前置任务


在配置传输告警的定制功能之前，需完成以下任务：


       - 路由器上电，自检正常

#### 1.4.3.1 配置可以影响接口的物理层状态的告警类型


当和传输设备相连时，如果网络不稳定，将产生大量的毛刺告警信息，而这些告警信
息将导致接口的物理层状态不断地在 Up 和 Down 状态之间切换。如果希望能指定哪些
告警可以触发接口的物理层状态为 Down ，可以配置传输告警的定制功能。

//#//#//#//#//# 背景信息


当传输设备的告警信号上报到系统后，可以通过传输告警定制功能控制告警对接口的
物理层状态的影响。在全局或者与传输设备相连的接口上进行以下配置。


传输告警过滤时间可以全局配置，也可以在接口下配置。全局配置后，对支持该功能
的接口均生效。二者之间的关系如下。


       - 配置全局告警定制功能时，若接口有非默认告警定制配置，则接口配置生效。


       - 当接口下未配置告警定制功能，则全局配置生效。


说明


VS 模式下，全局传输告警定制功能仅在 Admin VS 支持。

//#//#//#//#//# 操作步骤


       - 配置全局传输告警定制功能。


a. 执行命令 **system-view** ，进入系统视图。


b. 执行命令 **transmission-alarm down** { **wan** | **pos** } { **auais** | **b1tca** | **b2tca** |
**b3tca** | **lais** | **lcd** | **lof** | **lom** | **lop** | **los** | **lrdi** | **lrei** | **oof** | **pais** | **pplm** | **prdi** |
**prei** | **puneq** | **rdool** | **rrool** | **sdbere** | **sfbere** | **trool** } * ，配置全局 10GE


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 112



HUAWEI NetEngine40E
配置指南 1 接口与链路


WAN 接口和 POS 接口的告警定制功能，定制这些告警的产生可以触发接口的
物理层状态变为 Down 。


c. 执行命令 **commit** ，提交配置。


       - 配置接口下传输告警定制功能。


a. 执行命令 **system-view** ，进入系统视图。


b. 执行命令 **interface** interface-type interface-number 或 **controller** interfacetype interface-number ，进入对应接口视图。


c. 请根据实际情况选择以下配置。

//#//#//#//# ▪ 如果接口类型为 POS 或 10GE WAN ，执行命令 transmission-alarm

**down** { **auais** | **b1tca** | **b2tca** | **b3tca** | **lais** | **lcd** | **lof** | **lom** | **lop** | **los** |
**lrdi** | **lrei** | **oof** | **pais** | **pplm** | **prdi** | **prei** | **puneq** | **rdool** | **rrool** |
**sdbere** | **sfbere** | **trool** } [*] ，定制这些告警可以影响接口的物理层状态。

//#//#//#//# ▪ 如果接口类型为 WDM ，执行命令 transmission-alarm down { odu-ais

| **odu-lck** | **odu-oci** | **otu-ais** | **otu-lom** | **otu-sd-ber** | **otu-sf-ber** |
**pm-bdi** | **pm-tim** | **r-lof** | **r-los** | **r-oof** | **sm-bdi** | **sm-iae** | **sm-tim** |
**prefec-tca** | **local-fault** | **remote-fault** } [*] ，定制这些告警可以影响接
口的物理层状态。

//#//#//#//# ▪ 如果接口类型为 CPOS ，执行命令 transmission-alarm down { los | lof

| **oof** | **lais** | **lrdi** | **lrei** | **b1tca** | **b2tca** | **b3tca** | **sdbere** | **sfbere** | **rrool** |
**pais** | **auais** | **prdi** | **prei** | **lop** | **pplm** | **puneq** | **lom** } [*] ，定制这些告警
可以影响接口的物理层状态。

//#//#//#//# ▪ 如果接口类型为 E1 ，执行命令 transmission-alarm down { lof | los |

**pais** | **prdi** } [*] ，定制这些告警可以影响接口的物理层状态。

//#//#//#//# ▪ 如果接口类型为 Trunk-Serial 接口和串行接口，执行命令 transmission
**alarm down** { **pais** | **prdi** | **oof** } [*] ，定制这些告警可以影响接口的物理
层状态。


说明


              - 由于硬件上不同子卡支持的告警类型不同，并不是所有接口都支持这些告警。如
果配置了接口不支持的告警类型，则配置失败，并会提示该接口不支持配置哪些
告警类型。


              - 告警 wlnk 只支持查看，不支持通过命令行配置。该告警永远是打开状态的，且可
以触发接口的物理层状态变为 Down 。可使用命令 **display transmission-alarm** 查
看 wlnk 告警的状态和统计信息。


须知


LAIS 、 LOF 和 LOS 三类告警对链路状态变化提供告警，如果去使能这三类告警
会影响业务数据的准确转发，所以建议使能这几类告警。


d. 执行命令 **commit** ，提交配置。


**----**
结束


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 113



HUAWEI NetEngine40E
配置指南 1 接口与链路

#### 1.4.3.2 （可选）配置b1tca、b2tca、b3tca、sdbere和sfbere的告警门限值


可以对 b1tca 、 b2tca 、 b3tca 、 sdbere 和 sfbere 几类告警配置门限值，当告警信号没有
达到门限值时，该告警不会上报到系统。

//#//#//#//#//# 背景信息


在与传输设备相连的接口上进行以下配置。

//#//#//#//#//# 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **interface** interface-type interface-number 或 **controller** interface-type
interface-number ，进入接口视图。


步骤 **3** 执行命令 **transmission-alarm threshold** { **b1tca** b1tca | **b2tca** b2tca | **b3tca** b3tca
| **sdbere** sdbere | **sfbere** sfbere } * ，配置 b1tca 、 b2tca 、 b3tca 、 sdbere 和 sfbere 三类
告警的告警门限值。


这三类告警的门限值用指数形式 10 [-n] 表示，其中指数 n 通过命令 **transmission-alarm**
**threshold** 中配置的各类告警的参数值指定。 sdbere 的值不能小于 sfbere 的值，也就是
**sdbere** 的告警门限值不能大于 **sfbere** 的告警门限值。


步骤 **4** 执行命令 **commit** ，提交配置。


**----**
结束

#### 1.4.3.3 检查配置结果


配置传输告警的定制功能后，需要检查配置结果，保证配置正确。同时可查看接口的
告警状态和统计信息。

//#//#//#//#//# 前提条件


已经完成传输告警的定制功能的所有配置。并且，通过 **transmission-alarm**
**damping** 命令使能了抑制功能。

//#//#//#//#//# 操作步骤


       - 如果接口类型为 POS 或 10GE WAN ，使用 **display transmission-alarm** { **pos** |
**wan** } interface-number [ **auais** | **b1tca** | **b2tca** | **b3tca** | **lais** | **lcd** | **lof** | **lom** |
**lop** | **los** | **lrdi** | **lrei** | **oof** | **pais** | **prdi** | **prei** | **pplm** | **puneq** | **rdool** | **rrool** |
**sdbere** | **sfbere** | **trool** | **wlnk** ] * 命令查看接口上的告警配置信息。


       - 如果接口类型为 WDM ，使用 **display transmission-alarm** **wdm** interfacenumber [ **odu-ais** | **odu-lck** | **odu-oci** | **otu-ais** | **otu-lom** | **otu-sd-ber** | **otu-**
**sf-ber** | **pm-bdi** | **pm-tim** | **r-lof** | **r-los** | **r-oof** | **sm-bdi** | **sm-iae** | **sm-tim** |
**prefec-tca** | **odu-sd-ber** ] * 命令查看接口上的告警配置信息。


说明


该命令在 NE40E-X8AK 设备上不支持。


       - 如果接口类型为 E1 ，使用 **display transmission-alarm** **e1** interface-number

[ **los** | **lof** | **pais** | **prai** ] * 命令查看接口上的告警配置信息。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 114



HUAWEI NetEngine40E
配置指南 1 接口与链路


       - 如果接口类型为 CPOS ，使用 **display transmission-alarm** **cpos** interfacenumber [ **auais** | **b1tca** | **b2tca** | **b3tca** | **lais** | **lof** | **lom** | **lop** | **los** | **lrdi** | **lrei** |
**oof** | **pais** | **pplm** | **prdi** | **prei** | **puneq** | **rrool** | **sdbere** | **sfbere** ] * 命令查看接口
上的告警配置信息。


**----**
结束

### 1.4.4 配置传输告警的过滤时间间隔


通过本节可以了解到传输告警过滤时间间隔的应用环境、前置任务和操作步骤。如果
告警产生和消失的时间间隔小于该时间间隔，则告警信号就被认为是毛刺，并被过滤
掉。否则就是正常的告警信号。

//#//#//#//#//# 应用环境


当和传输设备相连时，如果网络不稳定，将产生大量的毛刺告警信息，而这些告警信
息将导致接口的物理层状态不断地在 Up 和 Down 状态之间切换。如果希望这些告警产
生和消除的时间间隔在一定时间范围内时忽略这些告警，就需要配置传输告警的过滤
时间间隔。


传输告警过滤时间可以全局配置，也可以在接口下配置。全局配置后，对支持该功能
的接口均生效。二者之间的关系如下。


       - 全局配置后，如果接口下存在传输告警过滤时间的非默认配置，则实际以接口配
置为准。


       - 当接口视图下未配置传输告警过滤时间时，以全局配置为准。


说明


VS 模式下，全局配置传输告警的过滤时间间隔仅在 Admin VS 支持。

//#//#//#//#//# 前置任务


在配置传输告警的过滤时间间隔之前，需完成以下任务：


       - 路由器上电，自检正常


       - 在路由器接口上 **1.4.3** 配置传输告警的定制功能


说明


对接口使能了传输告警定制功能后，接口使能过滤功能才生效。

//#//#//#//#//# 操作步骤


       - 配置全局告警过滤功能并设置告警过滤时间间隔。


a. 执行命令 **system-view** ，进入系统视图。

b. 执行命令 **transmission-alarm holdoff-timer** holdoff-time ，配置全局告警
产生过滤功能并设置过滤产生告警的时间间隔。


c. 执行命令 **transmission-alarm holdup-timer** holdup-time ，配置全局告警
消除过滤功能并设置过滤消除告警的时间间隔。


d. 执行命令 **commit** ，提交配置。


       - 配置接口下告警过滤功能并设置告警过滤时间间隔。


a. 执行命令 **system-view** ，进入系统视图。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 115



HUAWEI NetEngine40E
配置指南 1 接口与链路


b. 请根据实际情况选择以下配置。

//#//#//#//# ▪ 执行命令 interface interface-type interface-number ，进入 POS 或 10GE

WAN 接口视图。

//#//#//#//# ▪ 执行命令 controller cpos cpos-number ，进入指定 CPOS 接口的接口视

图。

//#//#//#//# ▪ 执行命令 controller wdm controller-number ，进入 WDM 接口视图。 ▪ 执行命令 controller e1 controller-number ，进入 E1 接口视图。

c. 执行命令 **transmission-alarm holdoff-timer** [ holdoff-time ] ，配置接口下
告警产生过滤功能并设置过滤产生告警的时间间隔。


如果告警产生和消失的时间间隔小于该时间间隔，则告警信号就被认为是毛
刺，并被过滤掉。否则就是正常的告警信号。


d. 执行命令 **transmission-alarm holdup-timer** [ holdup-time ] ，配置接口下
告警消除过滤功能并设置过滤消除告警的时间间隔。


e. 执行命令 **commit** ，提交配置。


**----**
结束

//#//#//#//#//# 检查配置结果


完成配置后，可以执行 **display transmission-alarm** 命令选择不同的接口检查对应的
配置结果。

### 1.4.5 配置传输告警的抑制功能


通过本节可以了解传输告警抑制的应用环境、前置任务和操作步骤。配置了传输告警
抑制门限值后，告警只有达到门限值后才会上报到系统。

//#//#//#//#//# 应用环境


当和传输设备相连时，如果网络不稳定，将产生大量的毛刺告警信息，而这些告警信
息将导致接口的物理层状态不断地在 Up 和 Down 状态之间切换。如果希望抑制这些告
警频繁振荡，或希望网络设备能够忽略这些毛刺告警信息时，就需要启动告警的抑制
功能。

//#//#//#//#//# 前置任务


在配置传输告警的抑制功能之前，需完成以下任务：


       - 路由器上电，自检正常


       - 在路由器接口上 **1.4.3** 配置传输告警的定制功能


说明


该章节在 NE40E-X8AK 设备上不支持。


只有在接口下使能了告警定制功能，接口上配置抑制功能才能生效。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 116



HUAWEI NetEngine40E
配置指南 1 接口与链路

//#//#//#//#//# 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 请根据实际情况选择以下配置。


1. 执行命令 **controller cpos** cpos-number ，进入指定 CPOS 接口的接口视图。


2. 执行命令 **controller** wdm interface-number ，进入 WDM 接口视图。


3. 执行命令 **controller** **e1** controller-number ，进入 E1 接口视图。


步骤 **3** 执行命令 **transmission-alarm damping** [ **ceiling** ceiling | **reuse** reuse | **suppress**
suppress | **decay-ok** decay-ok | **decay-ng** decay-ng ] * ，打开抑制开关并设置抑制参
数。


步骤 **4** 执行命令 **commit** ，提交配置。


**----**
结束

//#//#//#//#//# 检查配置结果


完成配置后，可以按以下指导来检查配置结果。


执行命令 **display transmission-alarm configuration** ，可以查看到接口的告警配置
信息。

### 1.4.6 维护传输告警定制与抑制


通过本节可以了解维护传输告警抑制中查看误码速率值和清除传输告警的运行信息的
相关操作步骤。

#### 1.4.6.1 查看误码速率值


当发生误码率告警，设备把接口置“ down ”时，您可以执行如下命令查询当前的误码
速率值。

//#//#//#//#//# 操作步骤


       - 查询当前接口的误码速率值，请在所有视图下执行 **display transmission-alarm**
**bit-error-rate** 命令。


**----**
结束

#### 1.4.6.2 清除传输告警的运行信息


当需要重新统计接口的传输告警信息时，需要先清除该接口的传输告警运行信息。

//#//#//#//#//# 背景信息


须知


清除接口上的告警运行信息会导致接口所有告警计数清零。务必仔细确认是否必须执
行清除接口告警运行信息的操作。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 117

