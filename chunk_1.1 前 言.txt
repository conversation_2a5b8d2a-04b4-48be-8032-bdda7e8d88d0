头标题: 1.1 前 言
尾标题: 1.2 QoS简介
内容: HUAWEI NetEngine40E配置指南 目 录## 目 录**1 QoS..............................................................................................................................................1**1.1 前 言 ............................................................................................................................................................................................. 21.2 QoS 简介 ..................................................................................................................................................................................... 61.3 流量监管和流量整形配置 ....................................................................................................................................................... 91.3.1 流量监管和流量整形简介 ....................................................................................................................................................91.3.2 流量监管和流量整形配置注意事项 ................................................................................................................................ 101.3.3 配置基于接口的流量监管 ................................................................................................................................................. 121.3.4 配置基于模板的流量监管 ................................................................................................................................................. 141.3.5 配置基于复杂流分类的流量监管 .................................................................................................................................... 151.3.5.1 定义流分类 ........................................................................................................................................................................ 161.3.5.2 定义流行为并配置监管动作 ..........................................................................................................................................181.3.5.3 定义流量策略 ....................................................................................................................................................................191.3.5.4 应用流量策略 ....................................................................................................................................................................201.3.5.5 检查配置结果 ....................................................................................................................................................................221.3.6 配置流量整形 ....................................................................................................................................................................... 221.3.7 维护流量监管、流量整形 ................................................................................................................................................. 261.3.7.1 清空 CAR 的统计信息 ..................................................................................................................................................... 261.3.8 配置举例 ................................................................................................................................................................................261.3.8.1 配置流量监管示例 ........................................................................................................................................................... 261.4 拥塞管理和拥塞避免配置 .....................................................................................................................................................291.4.1 拥塞管理和拥塞避免简介 ................................................................................................................................................. 291.4.2 拥塞管理和拥塞避免配置注意事项 ................................................................................................................................ 301.4.3 配置拥塞管理 ....................................................................................................................................................................... 301.4.4 配置拥塞避免 ....................................................................................................................................................................... 321.4.4.1 配置 WRED 模板 ..............................................................................................................................................................321.4.4.2 （可选）配置端口队列的联合流量整形 .................................................................................................................... 331.4.4.3 应用 WRED........................................................................................................................................................................341.4.4.4 检查配置结果 ....................................................................................................................................................................351.4.5 配置低速链路的队列调度 ................................................................................................................................................. 351.4.5.1 配置 PQ 调度 .................................................................................................................................................................... 351.4.5.2 配置 WFQ 调度 ................................................................................................................................................................ 361.4.5.3 （可选）配置端口队列的缓存时间 .............................................................................................................................371.4.5.4 （可选）配置端口队列的动态调整 .............................................................................................................................37文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 iiHUAWEI NetEngine40E配置指南 目 录1.4.5.5 （可选）配置队列调度的低时延震荡模式 ................................................................................................................381.4.5.6 检查配置结果 ....................................................................................................................................................................381.4.6 维护拥塞避免 ....................................................................................................................................................................... 381.4.6.1 清空端口队列的统计信息 .............................................................................................................................................. 381.4.7 配置举例 ................................................................................................................................................................................391.4.7.1 配置拥塞管理及拥塞避免示例 ..................................................................................................................................... 391.4.7.2 配置低速链路队列调度示例 ..........................................................................................................................................441.5 基于类的 QoS 配置 ................................................................................................................................................................ 461.5.1 流分类简介 ........................................................................................................................................................................... 461.5.2 基于类的 QoS 配置注意事项 ........................................................................................................................................... 491.5.3 配置 IP 报文复杂流分类的流量策略 .............................................................................................................................. 701.5.3.1 定义流分类 ........................................................................................................................................................................ 711.5.3.2 定义流行为并配置动作 .................................................................................................................................................. 741.5.3.3 定义流量策略 ....................................................................................................................................................................821.5.3.4 应用流量策略 ....................................................................................................................................................................831.5.3.5 检查配置结果 ....................................................................................................................................................................851.5.4 配置 IP 报文的优先级映射 ................................................................................................................................................861.5.5 配置 VLAN 报文复杂流分类的流量策略 ....................................................................................................................... 901.5.5.1 配置匹配 VLAN 帧优先级的规则 .................................................................................................................................901.5.5.2 配置 VLAN 优先级的值 .................................................................................................................................................. 901.5.5.3 定义流量策略 ....................................................................................................................................................................911.5.5.4 应用流量策略 ....................................................................................................................................................................911.5.5.5 检查配置结果 ....................................................................................................................................................................931.5.6 配置 VLAN 报文的优先级映射 ........................................................................................................................................ 931.5.7 配置 MPLS 报文的优先级映射 .........................................................................................................................................981.5.8 配置 VXLAN 报文复杂流分类的流量策略 .................................................................................................................. 1001.5.8.1 定义流分类 ......................................................................................................................................................................1001.5.8.2 定义流行为并配置动作 ................................................................................................................................................ 1011.5.8.3 （可选）使能匹配 VXLAN 报文的 QoS 策略 ID 模式 ......................................................................................... 1031.5.8.4 定义流量策略 ................................................................................................................................................................. 1031.5.8.5 应用流量策略 ................................................................................................................................................................. 1041.5.8.6 检查配置结果 ................................................................................................................................................................. 1051.5.9 （可选）设置协议报文的优先级 .................................................................................................................................. 1051.5.10 维护基于类的 QoS......................................................................................................................................................... 1101.5.10.1 清空流量策略的统计信息 ......................................................................................................................................... 1101.5.11 配置举例 ........................................................................................................................................................................... 1111.5.11.1 配置 IP 报文复杂流分类示例 ................................................................................................................................... 1111.5.11.2 配置 MPLS 公网口基于 IP 的复杂流分类示例 .....................................................................................................1131.5.11.3 配置 L3VPNv4 over SRv6 TE Policy 场景下应用级联策略匹配 SRv6 报文内层信息的复杂流分类示例......................................................................................................................................................................................................... 1161.5.11.4 配置双出口重定向示例 ............................................................................................................................................. 1191.5.11.5 配置 QinQ 终结子接口复杂流分类示例 ............................................................................................................... 1221.5.11.6 配置 MPLS 组网下基于复杂流分类的流量策略示例 ......................................................................................... 126文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 iiiHUAWEI NetEngine40E配置指南 目 录1.5.11.7 配置 VLAN 报文复杂流分类的流量策略示例 ...................................................................................................... 1351.5.11.8 配置二层接口基于简单流分类的优先级映射示例（ VLAN ） ..........................................................................1371.5.11.9 配置基于简单流分类的优先级映射示例（ MPLS ） ........................................................................................... 1421.5.11.10 配置 EVC 子接口基于简单流分类的优先级映射示例（ VLAN ） ..................................................................1441.6 策略路由配置 ........................................................................................................................................................................ 1511.6.1 策略路由概述 .....................................................................................................................................................................1511.6.2 策略路由配置注意事项 ................................................................................................................................................... 1521.6.3 基于重定向到 MPLS-TE P2P 隧道的策略路由配置 ..................................................................................................1521.6.3.1 配置策略路由匹配规则 ................................................................................................................................................ 1521.6.3.2 配置策略路由的动作 .................................................................................................................................................... 1521.6.3.3 应用策略路由 ................................................................................................................................................................. 1531.6.3.4 检查配置结果 ................................................................................................................................................................. 1531.6.4 配置举例 ............................................................................................................................................................................. 1541.6.4.1 配置基于重定向到 MPLS-TE P2P 隧道的策略路由示例 ..................................................................................... 1541.7 QPPB 配置 ............................................................................................................................................................................. 1571.7.1 QPPB 概述 .......................................................................................................................................................................... 1571.7.2 QPPB 配置注意事项 .........................................................................................................................................................1581.7.3 配置全局 QPPB................................................................................................................................................................. 1591.7.3.1 配置 BGP 路由发送端的路由发布策略 .................................................................................................................... 1601.7.3.2 配置接收端应用路由策略 ............................................................................................................................................1611.7.3.3 配置全局应用流策略并使能全局 QPPB.................................................................................................................. 1631.7.4 配置基于源的 QPPB.........................................................................................................................................................1641.7.4.1 配置路由发送端的路由策略 ....................................................................................................................................... 1641.7.4.2 配置接收端应用路由策略 ............................................................................................................................................1661.7.4.3 配置接收端流行为 .........................................................................................................................................................1671.7.4.4 配置 QPPB 本地策略 .................................................................................................................................................... 1681.7.4.5 在接口下应用 QPPB..................................................................................................................................................... 1691.7.4.6 检查配置结果 ................................................................................................................................................................. 1701.7.5 配置基于目的的 QPPB.................................................................................................................................................... 1701.7.5.1 配置 BGP 路由发送端的路由发布策略 .................................................................................................................... 1711.7.5.2 配置接收端应用路由策略 ............................................................................................................................................1721.7.5.3 配置接收端 QPPB 的流行为 ....................................................................................................................................... 1741.7.5.4 配置 QPPB 本地策略 .................................................................................................................................................... 1751.7.5.5 在接口下应用 QPPB..................................................................................................................................................... 1751.7.5.6 检查配置结果 ................................................................................................................................................................. 1761.7.6 维护 QPPB.......................................................................................................................................................................... 1771.7.6.1 清空 QPPB 本地策略的统计信息 .............................................................................................................................. 1771.7.7 配置举例 ............................................................................................................................................................................. 1771.7.7.1 配置 QPPB 示例（ BGP ） ............................................................................................................................................ 1771.7.7.2 配置 QPPB 示例（ ISIS ） ............................................................................................................................................. 1831.8 HQoS 配置 ............................................................................................................................................................................. 1851.8.1 HQoS 简介 ......................................................................................................................................................................... 185文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 ivHUAWEI NetEngine40E配置指南 目 录1.8.1.1 HQoS 概述 ...................................................................................................................................................................... 1851.8.1.2 HQoS 基本概念 ............................................................................................................................................................. 1891.8.2 HQoS 配置注意事项 ........................................................................................................................................................ 1901.8.3 激活 HQoS 端口 License................................................................................................................................................ 2141.8.4 配置基于 8 队列普通模式流队列的 HQoS.................................................................................................................2151.8.4.1 （可选）配置基于 8 队列普通模式的流队列模板 ................................................................................................2151.8.4.2 （可选）配置基于 8 队列普通模式的用户组队列模板 ....................................................................................... 2171.8.4.3 （可选）配置业务模板 ................................................................................................................................................ 2181.8.4.4 配置基于 QoS 模板的用户队列 ................................................................................................................................. 2191.8.4.5 应用 QoS 模板 ................................................................................................................................................................2201.8.4.6 检查配置结果 ................................................................................................................................................................. 2221.8.5 配置基于 8 队列优先级模式流队列的 HQoS............................................................................................................ 2221.8.5.1 配置基于 8 队列优先级模式的流队列模板 .............................................................................................................2231.8.5.2 配置基于 8 队列优先级模式的用户组队列模板 .................................................................................................... 2241.8.5.3 （可选）配置业务模板 ................................................................................................................................................ 2251.8.5.4 配置基于 QoS 模板的用户队列 ................................................................................................................................. 2251.8.5.5 应用 QoS 模板 ................................................................................................................................................................2271.8.5.6 检查配置结果 ................................................................................................................................................................. 2281.8.6 配置基于 8 队列增强模式流队列的 HQoS.................................................................................................................2291.8.6.1 配置基于 8 队列增强模式的流队列模板 ................................................................................................................. 2301.8.6.2 配置基于 8 队列增强模式的用户队列模板 .............................................................................................................2311.8.6.3 （可选）配置基于 8 队列增强模式的用户组队列模板 ....................................................................................... 2321.8.6.4 配置基于 QoS 模板的用户队列 ................................................................................................................................. 2331.8.6.5 应用 QoS 模板 ................................................................................................................................................................2331.8.6.6 检查配置结果 ................................................................................................................................................................. 2341.8.7 配置基于接口的 HQoS....................................................................................................................................................2351.8.7.1 （可选）配置流队列调度参数 ...................................................................................................................................2351.8.7.2 （可选）配置用户组队列调度参数 .......................................................................................................................... 2361.8.7.3 配置用户队列调度参数 ................................................................................................................................................ 2371.8.7.4 （可选）配置 Trunk 接口的 HQoS 调度模式 ........................................................................................................2381.8.7.5 检查配置结果 ................................................................................................................................................................. 2391.8.8 配置信道化子接口的 HQoS........................................................................................................................................... 2391.8.9 维护 HQoS......................................................................................................................................................................... 2401.8.9.1 清空队列统计计数信息 ................................................................................................................................................ 2411.8.10 配置举例 ........................................................................................................................................................................... 2421.8.10.1 配置基于 8 队列普通模式流队列的 HQoS 示例 ................................................................................................. 2421.8.10.2 配置基于 8 队列增强模式流队列的 HQoS 示例 ................................................................................................. 2501.9 MPLS DiffServ 模式配置 ....................................................................................................................................................2591.9.1 MPLS DiffServ 模式概述 ................................................................................................................................................ 2591.9.2 MPLS DiffServ 模式配置注意事项 ...............................................................................................................................2601.9.3 MPLS DiffServ 配置解析 ................................................................................................................................................ 2601.9.4 配置 MPLS TE 的 Uniform/Pipe 模式 ......................................................................................................................... 261文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 vHUAWEI NetEngine40E配置指南 目 录1.9.5 配置 VPN 的 Pipe/Short Pipe 模式 ..............................................................................................................................2621.9.6 配置举例 ............................................................................................................................................................................. 2641.9.6.1 配置 MPLS DiffServ 模式示例 ................................................................................................................................... 2641.10 用户接入 QoS 配置 ........................................................................................................................................................... 2771.10.1 用户接入 QoS 配置注意事项 ...................................................................................................................................... 2771.10.2 配置基于 UCL 的复杂流分类 .......................................................................................................................................2771.10.2.1 配置用户组 ................................................................................................................................................................... 2781.10.2.2 配置业务组 ................................................................................................................................................................... 2781.10.2.3 配置 UCL 用户规则 .....................................................................................................................................................2791.10.2.4 定义流分类 ................................................................................................................................................................... 2791.10.2.5 定义流行为并配置动作 ............................................................................................................................................. 2791.10.2.6 定义流量策略 ...............................................................................................................................................................2841.10.2.7 应用 UCL 用户的流分类策略 ................................................................................................................................... 2841.10.2.8 将用户组加入域 .......................................................................................................................................................... 2851.10.2.9 配置用户域下的优先级映射 .....................................................................................................................................2851.10.2.10 检查配置结果 ............................................................................................................................................................ 2871.10.3 配置普通用户的 HQoS 调度 ........................................................................................................................................2871.10.3.1 （可选）指定队列资源池 ......................................................................................................................................... 2881.10.3.2 （可选）配置流队列模板 ......................................................................................................................................... 2891.10.3.3 （可选）配置 4 队列模式流队列 ............................................................................................................................2911.10.3.4 （可选）配置 8 队列与 4 队列的映射关系 .......................................................................................................... 2911.10.3.5 （可选）配置业务模板 ............................................................................................................................................. 2921.10.3.6 配置基于 QoS 模板的用户队列 ...............................................................................................................................2931.10.3.7 （可选）配置动态修改 QoS 模板 .......................................................................................................................... 2941.10.3.8 在域下应用 QoS 模板 ................................................................................................................................................ 2941.10.3.9 定义 GQ 模板并配置调度参数 ................................................................................................................................ 2951.10.3.10 应用 GQ 模板 ............................................................................................................................................................ 2961.10.3.11 在域下配置 QoS....................................................................................................................................................... 2971.10.3.12 （可选）阶梯调整用户带宽 .................................................................................................................................. 2981.10.3.13 （可选）配置 VE 接入的用户其 HQoS 功能在 eTM 上实现 ........................................................................ 2991.10.3.14 检查配置结果 ............................................................................................................................................................ 2991.10.4 配置家庭用户的 HQoS 调度 ........................................................................................................................................3001.10.4.1 （可选）指定队列资源池 ......................................................................................................................................... 3001.10.4.2 （可选）配置流队列 ..................................................................................................................................................3011.10.4.3 （可选）配置业务模板 ............................................................................................................................................. 3031.10.4.4 配置基于 QoS 模板的用户队列 ...............................................................................................................................3041.10.4.5 （可选）配置动态修改 QoS 模板 .......................................................................................................................... 3051.10.4.6 配置业务识别策略 ...................................................................................................................................................... 3061.10.4.7 在 BAS 接口下绑定 QoS 模板和业务识别策略 ................................................................................................... 3071.10.4.8 定义 GQ 模板并配置调度参数 ................................................................................................................................ 3091.10.4.9 应用 GQ 模板 ...............................................................................................................................................................3101.10.4.10 （可选）阶梯调整用户带宽 .................................................................................................................................. 311文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 viHUAWEI NetEngine40E配置指南 目 录1.10.4.11 （可选）配置 VE 接入的用户其 HQoS 功能在 eTM 上实现 ........................................................................ 3111.10.4.12 检查配置结果 ............................................................................................................................................................ 3121.10.5 配置最后一公里 QoS.....................................................................................................................................................3121.10.5.1 最后一公里 QoS 简介 ................................................................................................................................................ 3131.10.5.1.1 最后一公里 QoS 概述 .............................................................................................................................................3131.10.5.1.2 支持的最后一公里 QoS......................................................................................................................................... 3141.10.5.2 配置最后一公里 QoS................................................................................................................................................. 3151.10.5.2.1 使能最后一公里 QoS 功能 .................................................................................................................................... 3151.10.5.2.2 配置远端链路模式及调整值 ................................................................................................................................. 3151.10.5.2.3 （可选）使能三层报文的上行 CAR 功能和报文统计功能 ............................................................................3161.10.5.2.4 检查配置结果 ........................................................................................................................................................... 3171.10.6 配置举例 ........................................................................................................................................................................... 3171.10.6.1 配置 UCL 用户的流量策略示例 ............................................................................................................................... 3171.10.6.2 配置用户域下的优先级映射示例 ............................................................................................................................ 3201.10.6.3 通过 RADIUS 下发 EDSG 业务限制带宽示例 (Traffic-policy 简化配置方式 )..............................................3251.11 组播 QoS 配置 .................................................................................................................................................................... 3331.11.1 简介 ....................................................................................................................................................................................3331.11.1.1 组播 QoS 概述 ............................................................................................................................................................. 3331.11.2 组播 QoS 配置注意事项 ............................................................................................................................................... 3341.11.3 配置组播虚拟调度 ..........................................................................................................................................................3341.11.3.1 配置组播节目列表 ...................................................................................................................................................... 3341.11.3.2 配置组播节目带宽 ...................................................................................................................................................... 3351.11.3.3 配置不复制转发组播数据 ......................................................................................................................................... 3351.11.3.4 配置单播保留带宽 ...................................................................................................................................................... 3351.11.3.5 （可选）配置用户同时观看最大节目数 ............................................................................................................... 3361.11.3.6 （可选）配置跨 VPN 的组播虚拟调度 ................................................................................................................. 3361.11.3.7 （可选）调整家庭用户的单播 CAR 带宽 ............................................................................................................. 3371.11.3.8 检查配置结果 ...............................................................................................................................................................3371.11.4 配置组播整形 .................................................................................................................................................................. 3371.11.4.1 配置组播节目列表 ...................................................................................................................................................... 3381.11.4.2 使能组播整形 ...............................................................................................................................................................3381.11.4.3 配置组播列表的带宽 ..................................................................................................................................................3381.11.4.4 配置单板的组播流量限制 ......................................................................................................................................... 3391.11.4.5 检查配置结果 ...............................................................................................................................................................3391.11.5 配置举例 ........................................................................................................................................................................... 3391.11.5.1 单边缘组播虚拟调度配置示例 ................................................................................................................................ 3401.11.5.2 双边缘组播虚拟调度配置示例 ................................................................................................................................ 3431.11.5.3 配置组播整形示例 ...................................................................................................................................................... 3461.12 VPN QoS 配置 ....................................................................................................................................................................3481.12.1 VPN QoS 概述 ................................................................................................................................................................ 3481.12.2 VPN QoS 配置注意事项 ............................................................................................................................................... 3491.12.3 配置 VPWS QoS 基本功能 ...........................................................................................................................................353文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 viiHUAWEI NetEngine40E配置指南 目 录1.12.3.1 （可选）配置 VPN 的 QoS 模板 .............................................................................................................................3541.12.3.1.1 （可选）配置流队列 WRED 对象 ....................................................................................................................... 3551.12.3.1.2 配置流队列的调度参数 .......................................................................................................................................... 3551.12.3.1.3 （可选）配置流队列到类队列的映射 ................................................................................................................ 3571.12.3.1.4 （可选）配置业务模板 .......................................................................................................................................... 3571.12.3.1.5 （可选）配置用户组队列的流量整形 ................................................................................................................ 3571.12.3.1.6 定义 VPN 的 QoS 模板并配置调度参数 ............................................................................................................ 3581.12.3.1.7 检查配置结果 ........................................................................................................................................................... 3591.12.4 配置 VPWS QoS 流量统计 ...........................................................................................................................................3591.12.5 配置 VPLS QoS 基本功能 .............................................................................................................................................3601.12.5.1 （可选）配置 VPN 的 QoS 模板 .............................................................................................................................3611.12.5.1.1 （可选）配置流队列 WRED 对象 ....................................................................................................................... 3611.12.5.1.2 配置流队列的调度参数 .......................................................................................................................................... 3621.12.5.1.3 （可选）配置流队列到类队列的映射 ................................................................................................................ 3631.12.5.1.4 （可选）配置业务模板 .......................................................................................................................................... 3631.12.5.1.5 （可选）配置用户组队列的流量整形 ................................................................................................................ 3641.12.5.1.6 定义 VPN 的 QoS 模板并配置调度参数 ............................................................................................................ 3641.12.5.1.7 检查配置结果 ........................................................................................................................................................... 3651.12.6 配置 VPLS QoS 流量统计 .............................................................................................................................................3651.12.7 配置举例 ........................................................................................................................................................................... 3671.12.7.1 配置 VPWS QoS 的基本功能示例 .......................................................................................................................... 3671.12.7.2 配置 VPWS QoS 的流量统计功能示例 ..................................................................................................................3761.13 确定性 IP 网络配置 ........................................................................................................................................................... 3861.13.1 确定性 IP 网络简介 ........................................................................................................................................................ 3861.13.2 Deterministstic IP( 确定性 IP 网络 ) 配置注意事项 ................................................................................................ 3861.13.3 配置确定性 IP 网络 ........................................................................................................................................................ 3881.13.3.1 配置时钟同步 ...............................................................................................................................................................3881.13.3.2 使能确定性 IP 网络 .....................................................................................................................................................3891.13.3.3 配置确定性 IP 网络报文复杂流分类的流量策略 .................................................................................................3901.13.3.4 检查配置结果 ...............................................................................................................................................................3911.13.4 维护确定性 IP 网络 ........................................................................................................................................................ 3921.13.5 配置举例 ........................................................................................................................................................................... 3921.13.5.1 配置 EVPN VPWS over SRv6 TE Policy 场景下的确定性 IP 网络示例 .........................................................392文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 viiiHUAWEI NetEngine40E配置指南 1 QoS# **1**## **QoS**1.1 前 言1.2 QoS 简介介绍了 QoS 的基本概念和解决方案， DiffServ 模型和相关技术。1.3 流量监管和流量整形配置介绍流量监管和流量整形的基本概念和配置指导。1.4 拥塞管理和拥塞避免配置通过调整网络的流量来解除网络拥塞并介绍当网络发生拥塞时的几种不同丢包策略。1.5 基于类的 QoS 配置本章介绍了复杂流分类中流量策略和简单流分类中优先级映射的配置和举例。1.6 策略路由配置1.7 QPPB 配置介绍了 QPPB 的基本原理、配置过程和配置举例。1.8 HQoS 配置传统的 QoS 基于端口进行流量调度，无法区分用户和业务。 HQoS 可以针对每个用户的业务流进行队列调度。1.9 MPLS DiffServ 模式配置介绍了 MPLS DiffServ 模式的基本原理、配置过程和配置举例。1.10 用户接入 QoS 配置1.11 组播 QoS 配置组播 QoS 可以保证用户流畅的观看电视节目，做到精细化区分和管理用户带宽。1.12 VPN QoS 配置介绍了 VPN QoS （ Virtual Private Network Quality of Service ）技术的原理、应用和配置。1.13 确定性 IP 网络配置文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 1HUAWEI NetEngine40E配置指南 1 QoS### 1.1 前 言##### 概述本文档介绍了 QoS 的基本概念、在不同应用场景中的配置过程和配置举例。##### License 依赖License 的详细信息，请查阅 License 使用指南。       - 企业网用户：– NE40E License 使用指南： **License** [使用指南](https://support.huawei.com/enterprise/zh/doc/EDOC1100194059)– NE40E-X8AK License 使用指南： **License** [使用指南](https://support.huawei.com/enterprise/zh/doc/EDOC1100194062)##### 产品版本与本文档相对应的产品版本如下所示。##### 读者对象|产品名称|产品版本||---|---||HUAWEI NetEngine40E|V800R023C10SPC500||iMaster NCE-IP|V100R023C10SPC100|本文档主要适用于以下工程师：       - 数据配置工程师       - 调测工程师       - 网络监控工程师       - 系统维护工程师##### 安全声明       - 受限公开声明产品资料中主要介绍了您在使用华为设备时，在网络部署及维护时，需要使用的命令。对用于生产、装备、返厂检测维修的接口、命令，不在资料中说明。对于部分仅用于工程实施、定位故障的高级命令以及升级兼容命令，如使用不当，将可能导致设备异常或者业务中断，建议较高权限的工程师使用。如您需要，请向华为公司申请。       - 加密算法声明使用加密算法时， DES/3DES/RSA （ 3072 位以下 )/MD5 （数字签名场景和口令加密） /SHA1 （数字签名场景）加密算法安全性低，存在安全风险，在协议支持的加密算法选择范围内，建议使用更安全的加密算法，例如 AES/RSA （ 3072 位及以上） /SHA2/HMAC-SHA2 。出于安全性考虑，不建议使用不安全协议 Telnet 、 FTP 、 TFTP ；不建议使用特性BGP 、 LDP 、 PCEP 、 MSDP 、 DCN 、 TCP-AO 、 MSTP 、 VRRP 、 E-trunk 、 AAA 、文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 2HUAWEI NetEngine40E配置指南 1 QoSIPSEC 、 BFD 、 QX 、端口扩展、 SSH 、 SNMP 、 IS-IS 、 RIP 、 SSL 、 NTP 、 OSPF 、Keychain 中的弱安全算法。如果确实需要使用，请执行 undo crypto weakalgorithm disable 命令使能弱安全算法功能。详细步骤请参见《配置指南》。出于安全性考虑，不建议使用该特性中的弱安全算法，若当前系统已关闭弱安全算法功能，配置弱安全算法会提示 Error 信息。如果确实需要使用弱安全算法，请先执行 **undo crypto weak-algorithm disable** 命令使能弱安全算法功能。       - 密码配置声明– 当密码加密方式为 cipher 时，输入以 %^%#......%^%# 为起始和结束符的合法密文（本设备可以解密的密文）时，在设备上查看配置文件时显示的是和配置相同的密文，请不要采用该方式直接配置密码。–为保证设备安全，请定期修改密码。       - MAC 地址、公网 IP 地址使用的声明– 出于特性介绍及配置示例的需要，产品资料中会使用真实设备的 MAC 地址、公网的 IP 地址，如无特殊说明，出现的真实设备的 MAC 地址、公网的 IP 地址均为示意，不指代任何实际意义。– 因开源及第三方软件中自带公网地址（包括公网 IP 地址、公网 URL 地址 / 域名、邮箱地址），本产品没有使用这些公网地址，这遵循业界实践，也符合开源软件使用规范。–出于功能特性实现的需要，设备会使用如下公网地址表 **1-1** 公网地址列表|公网地址|说明||---|---||http://www.huawei.com|华为官方网站地址||<EMAIL>|华为企业用户服务邮箱|       - 个人数据声明–您购买的产品、服务或特性在业务运营或故障定位的过程中将可能获取或使用用户的某些个人数据，因此您有义务根据所适用国家的法律制定必要的用户隐私政策并采取足够的措施以确保用户的个人数据受到充分的保护。–废弃、回收或者再利用设备时，请注意根据需要备份或清除设备中的数据，避免数据泄露的安全风险。如需支持，请联系售后技术支持人员。       - 预置证书使用声明在生产阶段预置于华为设备的华为证书是华为设备必备的出厂身份凭证，对其使用声明如下：–华为预置证书仅用于部署阶段为设备接入客户网络建立初始安全通道以及设备对接，华为不对预置证书的安全性做承诺与保证。–对于将华为预置证书作为业务证书使用而导致的安全风险和安全事件，由客户自行处置并承担后果。– 华为预置证书有效期自 2041 年起开始过期，可以通过 **display pki cert_list****domain default** 命令查看实际的有效期。–预置证书过期后，使用预置证书的业务会中断。– 华为建议客户通过部署 PKI 系统对现网设备、软件签发证书并做好证书的生命周期管理（为保证安全性推荐使用短有效期的证书）。– 华为产品中用于产品入网初始化配置和连接时使用的华为 PKI 根证书支持禁用（当验证华为新网元入网时，可配置重启该证书）。建议客户完成产品入网文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 3HUAWEI NetEngine40E配置指南 1 QoS配置并为产品配置客户 CA 签发的证书后，将该根证书禁用。对于客户未禁用华为 PKI 根证书而带来的安全风险和安全事件，由客户自行处置并承担后果。       - 产品生命周期政策华为公司对产品生命周期的规定以“产品生命周期终止政策”为准，该政策的详细内容请参见如下网址： **[https://support.huawei.com/ecolumnsweb/zh/](https://support.huawei.com/ecolumnsweb/zh/warranty-policy)****[warranty-policy](https://support.huawei.com/ecolumnsweb/zh/warranty-policy)** 。       - 漏洞处理流程华为公司对产品漏洞管理的规定以“漏洞处理流程”为准，该流程的详细内容请参见如下网址： **[https://www.huawei.com/cn/psirt/vul-response-process](https://www.huawei.com/cn/psirt/vul-response-process)**如企业客户须获取漏洞信息，请参见如下网址： **[https://](https://securitybulletin.huawei.com/enterprise/cn/security-advisory)****[securitybulletin.huawei.com/enterprise/cn/security-advisory](https://securitybulletin.huawei.com/enterprise/cn/security-advisory)**       - 华为企业业务最终用户许可协议 (EULA)本最终用户许可协议是最终用户（个人、公司或其他任何实体）与华为公司就华为软件的使用所缔结的协议。最终用户对华为软件的使用受本协议约束，该协议的详细内容请参见如下网址： **[https://e.huawei.com/cn/about/eula](https://e.huawei.com/cn/about/eula)** 。       - 产品资料生命周期策略华为公司针对随产品版本发布的售后客户资料（产品资料），发布了“产品资料生命周期策略”，该策略的详细内容请参见如下网址： **[https://](https://support.huawei.com/enterprise/zh/bulletins-website/ENEWS2000017760)****[support.huawei.com/enterprise/zh/bulletins-website/](https://support.huawei.com/enterprise/zh/bulletins-website/ENEWS2000017760)****[ENEWS2000017760](https://support.huawei.com/enterprise/zh/bulletins-website/ENEWS2000017760)** 。       - 华为预置证书权责说明华为公司对随设备出厂的预置数字证书，发布了“华为设备预置数字证书权责说明”，该说明的详细内容请参见如下网址： **[https://support.huawei.com/](https://support.huawei.com/enterprise/zh/bulletins-service/ENEWS2000015766)****[enterprise/zh/bulletins-service/ENEWS2000015766](https://support.huawei.com/enterprise/zh/bulletins-service/ENEWS2000015766)** 。       - 设备升级、打补丁的声明对设备进行升级或打补丁操作时，请使用软件数字签名（ OpenPGP ）验证工具验证软件。为避免软件被篡改或替换，防止给用户带来安全风险，建议用户进行此项操作。       - 特性声明– NetStream 功能，出于对网络流量的统计管理，可能涉及对最终用户的通信内容分析，建议您在所适用法律法规允许的目的和范围内方可启用相应的功能。在采集、存储用户通信内容的过程中，您应采取足够的措施以确保用户的通信内容受到严格保护。–镜像功能，可能基于运维目的需要对某些最终用户的通信内容进行分析，建议您在所适用法律法规允许的目的和范围内方可启用相应的功能。在采集、存储用户通信内容的过程中，您应采取足够的措施以确保用户的通信内容受到严格保护。–报文头获取功能，出于检测通信传输中的故障和错误的目的，可能涉及采集或存储个人用户某些通信内容。本公司无法单方采集或存储用户通信内容。建议您只有在所适用法律法规允许的目的和范围内方可启用相应的功能。在采集、存储用户通信内容的过程中，您应采取足够的措施以确保用户的通信内容受到严格保护。       - 可靠性设计声明对于网络规划和站点设计，必须严格遵守可靠性设计原则，具备设备级和方案级保护。设备级保护包括双网双平面，双机、跨板双链路的规划原则，避免出现单点，单链路故障。方案级指 FRR 、 VRRP 等快速收敛保护机制。在应用方案级保护文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 4HUAWEI NetEngine40E配置指南 1 QoS时，应避免保护方案的主备路径经过相同链路或者传输，以免方案级保护不生效。##### 特别声明       - 本文档中包含了 NE40E 支持的所有产品内容，如果需要了解在本国销售的设备或单板等硬件相关信息，请查阅硬件描述章节。       - 本手册仅作为使用指导，其内容依据实验室设备信息编写。手册提供的内容具有一般性的指导意义，并不确保涵盖所有使用场景。因版本升级、设备型号不同、板卡限制不同、配置文件不同等原因，可能造成手册中提供的内容与用户使用的设备界面不一致。请以用户设备界面的信息为准，本手册不再针对前述情况造成的差异一一说明。       - 本手册中提供的最大值是设备在实验室特定场景（例如被测试设备上只有某种类型的单板，或者只配置了某一种协议）达到的最大值。在现实网络中，由于设备硬件配置不同、承载的业务不同等原因可能会使设备测试出的最大值与手册中提供的数据不一致。       - 本手册中出现的接口编号仅作示例，并不代表设备上实际具有此编号的接口，实际使用中请以设备上存在的接口编号为准。       - 本手册中的硬件照片仅供参考，具体请以发货的硬件实物为准。       - 本手册中体现设备支持的相关硬件板卡，存在特定客户定制的需求，实际支持以售前销售界面为准。       - 出于特性介绍及配置示例的需要，产品资料中会使用公网 IP 地址，如无特殊说明，资料里出现的公网 IP 地址均为示意，不指代任何实际意义。       - 本手册中配置指南出现的“ XX 配置注意事项”，请结合产品的实际特性支持情况来使用。       - 本手册中的日志参考和告警参考，记录的是对应产品上注册的日志和告警信息。实际应用中可触发的日志和告警，取决于当前产品所支持的业务功能。       - 本文档中描述的所有设备尺寸数据均为设计尺寸，不包含尺寸公差。在部件制造过程中，由于加工或测量等因素的影响，实际尺寸存在一定的偏差。##### 符号约定在本文中可能出现下列标志，它们所代表的含义如下。|符号|说明||---|---|||表示如不避免则将会导致死亡或严重伤害的具有高等<br>级风险的危害。|||表示如不避免则可能导致死亡或严重伤害的具有中等<br>级风险的危害。|||表示如不避免则可能导致轻微或中度伤害的具有低等<br>级风险的危害。|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 5HUAWEI NetEngine40E配置指南 1 QoS##### 命令格式约定 修订记录|符号|说明||---|---|||用于传递设备或环境安全警示信息。如不避免则可能<br>会导致设备损坏、数据丢失、设备性能降低或其它不<br>可预知的结果。<br>“须知”不涉及人身伤害。|||对正文中重点信息的补充说明。<br>“说明”不是安全警示信息，不涉及人身、设备及环<br>境伤害信息。||格式|意义||---|---||粗体|命令行关键字（命令中保持不变、必须照输的部分）采用<br>加粗字体表示。||斜体|命令行参数（命令中必须由实际值进行替代的部分）采用<br>斜体表示。||[ ]|表示用“[ ]”括起来的部分在命令配置时是可选的。||{ x | y | ... }|表示从两个或多个选项中选取一个。||[ x | y | ... ]|表示从两个或多个选项中选取一个或者不选。||{ x | y | ... }*|表示从两个或多个选项中选取多个，最少选取一个，最多<br>选取所有选项。||[ x | y | ... ]*|表示从两个或多个选项中选取多个或者不选。||&<1-n>|表示符号&前面的参数可以重复1～n次。||#|由“#”开始的行表示为注释行。|修改记录累积了每次文档变更的说明。最新版本的文档包含以前所有文档版本的更新内容。|产品版本|文档版本|发布日期||---|---|---||V800R023C10SPC500|02|2024-06-30||V800R023C10SPC500|01|2024-03-30|### 1.2 QoS 简介介绍了 QoS 的基本概念和解决方案， DiffServ 模型和相关技术。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 6HUAWEI NetEngine40E配置指南 1 QoS##### 什么是 QoS服务质量 QoS （ Quality of Service ）是针对各种业务的不同需求，为其提供端到端的服务质量保证。 QoS 不会增加网络带宽，它是有效利用现有网络资源的工具，允许不同的流量不平等的竞争网络资源，语音、视频和重要的数据应用在网络设备中可以优先得到服务。随着网络技术的飞速发展，互联网中的业务越来越多样化。除了传统的 WWW 、 EMail 、 FTP 应用外，用户还尝试在 Internet 上拓展新业务，比如 IP 电话、电子商务、多媒体游戏、远程教学、远程医疗、可视电话、电视会议、视频点播、在线电影等。企业用户也希望通过 VPN 技术，将分布在各地的分支机构连接起来，开展一些事务性应用，比如访问公司的数据库或通过 Telnet 管理远程设备。图 **1-1** 互联网业务网络的普及，业务的多样化，使互联网流量激增，产生网络拥塞，转发时延增加，严重时还会产生丢包，导致业务质量下降甚至不可用。所以，要在 IP 网络上开展这些实时性业务，就必须解决网络拥塞问题。解决网络拥塞的最好的办法是增加网络的带宽。但从运营、维护的成本考虑，这是不现实的，最有效的解决方案就是应用一个“有保证”的策略对网络拥塞进行管理。QoS 技术就是在这种背景下发展起来的。 QoS 技术在当今的互联网中应用越来越多，其作用越来越重要。如果没有 QoS 技术，业务的服务质量就无法保证。##### 四大 QoS 组件       - 流分类和标记 （ Classification and Marking ）：要实现差分服务，需要首先将数据包分为不同的类别或者设置为不同的优先级。将数据包分为不同的类别，称为流分类，流分类并不修改原来的数据包。将数据包设置为不同的优先级称为标记，而标记会修改原来的数据包。说明这里的标记是“外部标记”，一般是在报文离开设备的时候在报文中进行设置，修改报文QoS 优先级字段，目的是为了将 QoS 信息传递给下一台设备；本文后面还有“内部标记”，用于设备内部处理报文，不修改报文。一般是在报文进入设备的时候，就通过流分类，给报文打上内部标记，这样，在报文从设备发出之前，都可以根据内部标记进行 QoS处理。       - 流量监管和整形 （ Policing and Shaping ）：是指将业务流量限制在特定的带宽，当业务流量超过额定带宽时，超过的流量将被丢弃或缓存。其中，将超过的流量丢弃的技术称为流量监管，将超过的流量缓存的技术称为流量整形。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 7
最终截取: ### 1.1 前 言

##### 概述


本文档介绍了 QoS 的基本概念、在不同应用场景中的配置过程和配置举例。

##### License 依赖


License 的详细信息，请查阅 License 使用指南。


       - 企业网用户：


– NE40E License 使用指南： **License** [使用指南](https://support.huawei.com/enterprise/zh/doc/EDOC1100194059)


– NE40E-X8AK License 使用指南： **License** [使用指南](https://support.huawei.com/enterprise/zh/doc/EDOC1100194062)

##### 产品版本


与本文档相对应的产品版本如下所示。

##### 读者对象

|产品名称|产品版本|
|---|---|
|HUAWEI NetEngine40E|V800R023C10SPC500|
|iMaster NCE-IP|V100R023C10SPC100|



本文档主要适用于以下工程师：


       - 数据配置工程师


       - 调测工程师


       - 网络监控工程师


       - 系统维护工程师

##### 安全声明


       - 受限公开声明


产品资料中主要介绍了您在使用华为设备时，在网络部署及维护时，需要使用的
命令。对用于生产、装备、返厂检测维修的接口、命令，不在资料中说明。


对于部分仅用于工程实施、定位故障的高级命令以及升级兼容命令，如使用不
当，将可能导致设备异常或者业务中断，建议较高权限的工程师使用。如您需
要，请向华为公司申请。


       - 加密算法声明


使用加密算法时， DES/3DES/RSA （ 3072 位以下 )/MD5 （数字签名场景和口令加
密） /SHA1 （数字签名场景）加密算法安全性低，存在安全风险，在协议支持的
加密算法选择范围内，建议使用更安全的加密算法，例如 AES/RSA （ 3072 位及以
上） /SHA2/HMAC-SHA2 。


出于安全性考虑，不建议使用不安全协议 Telnet 、 FTP 、 TFTP ；不建议使用特性
BGP 、 LDP 、 PCEP 、 MSDP 、 DCN 、 TCP-AO 、 MSTP 、 VRRP 、 E-trunk 、 AAA 、


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 2



HUAWEI NetEngine40E
配置指南 1 QoS


IPSEC 、 BFD 、 QX 、端口扩展、 SSH 、 SNMP 、 IS-IS 、 RIP 、 SSL 、 NTP 、 OSPF 、
Keychain 中的弱安全算法。如果确实需要使用，请执行 undo crypto weakalgorithm disable 命令使能弱安全算法功能。详细步骤请参见《配置指南》。


出于安全性考虑，不建议使用该特性中的弱安全算法，若当前系统已关闭弱安全
算法功能，配置弱安全算法会提示 Error 信息。如果确实需要使用弱安全算法，请
先执行 **undo crypto weak-algorithm disable** 命令使能弱安全算法功能。


       - 密码配置声明


– 当密码加密方式为 cipher 时，输入以 %^%#......%^%# 为起始和结束符的合法
密文（本设备可以解密的密文）时，在设备上查看配置文件时显示的是和配
置相同的密文，请不要采用该方式直接配置密码。


–
为保证设备安全，请定期修改密码。


       - MAC 地址、公网 IP 地址使用的声明


– 出于特性介绍及配置示例的需要，产品资料中会使用真实设备的 MAC 地址、
公网的 IP 地址，如无特殊说明，出现的真实设备的 MAC 地址、公网的 IP 地址
均为示意，不指代任何实际意义。


– 因开源及第三方软件中自带公网地址（包括公网 IP 地址、公网 URL 地址 / 域
名、邮箱地址），本产品没有使用这些公网地址，这遵循业界实践，也符合
开源软件使用规范。


–
出于功能特性实现的需要，设备会使用如下公网地址


表 **1-1** 公网地址列表

|公网地址|说明|
|---|---|
|http://www.huawei.com|华为官方网站地址|
|<EMAIL>|华为企业用户服务邮箱|



       - 个人数据声明


–
您购买的产品、服务或特性在业务运营或故障定位的过程中将可能获取或使
用用户的某些个人数据，因此您有义务根据所适用国家的法律制定必要的用
户隐私政策并采取足够的措施以确保用户的个人数据受到充分的保护。


–
废弃、回收或者再利用设备时，请注意根据需要备份或清除设备中的数据，
避免数据泄露的安全风险。如需支持，请联系售后技术支持人员。


       - 预置证书使用声明


在生产阶段预置于华为设备的华为证书是华为设备必备的出厂身份凭证，对其使
用声明如下：


–
华为预置证书仅用于部署阶段为设备接入客户网络建立初始安全通道以及设
备对接，华为不对预置证书的安全性做承诺与保证。


–
对于将华为预置证书作为业务证书使用而导致的安全风险和安全事件，由客
户自行处置并承担后果。


– 华为预置证书有效期自 2041 年起开始过期，可以通过 **display pki cert_list**
**domain default** 命令查看实际的有效期。


–
预置证书过期后，使用预置证书的业务会中断。


– 华为建议客户通过部署 PKI 系统对现网设备、软件签发证书并做好证书的生命
周期管理（为保证安全性推荐使用短有效期的证书）。


– 华为产品中用于产品入网初始化配置和连接时使用的华为 PKI 根证书支持禁用
（当验证华为新网元入网时，可配置重启该证书）。建议客户完成产品入网


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 3



HUAWEI NetEngine40E
配置指南 1 QoS


配置并为产品配置客户 CA 签发的证书后，将该根证书禁用。对于客户未禁用
华为 PKI 根证书而带来的安全风险和安全事件，由客户自行处置并承担后果。


       - 产品生命周期政策


华为公司对产品生命周期的规定以“产品生命周期终止政策”为准，该政策的详
细内容请参见如下网址： **[https://support.huawei.com/ecolumnsweb/zh/](https://support.huawei.com/ecolumnsweb/zh/warranty-policy)**
**[warranty-policy](https://support.huawei.com/ecolumnsweb/zh/warranty-policy)** 。


       - 漏洞处理流程


华为公司对产品漏洞管理的规定以“漏洞处理流程”为准，该流程的详细内容请
参见如下网址： **[https://www.huawei.com/cn/psirt/vul-response-process](https://www.huawei.com/cn/psirt/vul-response-process)**


如企业客户须获取漏洞信息，请参见如下网址： **[https://](https://securitybulletin.huawei.com/enterprise/cn/security-advisory)**
**[securitybulletin.huawei.com/enterprise/cn/security-advisory](https://securitybulletin.huawei.com/enterprise/cn/security-advisory)**


       - 华为企业业务最终用户许可协议 (EULA)


本最终用户许可协议是最终用户（个人、公司或其他任何实体）与华为公司就华
为软件的使用所缔结的协议。最终用户对华为软件的使用受本协议约束，该协议
的详细内容请参见如下网址： **[https://e.huawei.com/cn/about/eula](https://e.huawei.com/cn/about/eula)** 。


       - 产品资料生命周期策略


华为公司针对随产品版本发布的售后客户资料（产品资料），发布了“产品资料
生命周期策略”，该策略的详细内容请参见如下网址： **[https://](https://support.huawei.com/enterprise/zh/bulletins-website/ENEWS2000017760)**
**[support.huawei.com/enterprise/zh/bulletins-website/](https://support.huawei.com/enterprise/zh/bulletins-website/ENEWS2000017760)**
**[ENEWS2000017760](https://support.huawei.com/enterprise/zh/bulletins-website/ENEWS2000017760)** 。


       - 华为预置证书权责说明


华为公司对随设备出厂的预置数字证书，发布了“华为设备预置数字证书权责说
明”，该说明的详细内容请参见如下网址： **[https://support.huawei.com/](https://support.huawei.com/enterprise/zh/bulletins-service/ENEWS2000015766)**
**[enterprise/zh/bulletins-service/ENEWS2000015766](https://support.huawei.com/enterprise/zh/bulletins-service/ENEWS2000015766)** 。


       - 设备升级、打补丁的声明


对设备进行升级或打补丁操作时，请使用软件数字签名（ OpenPGP ）验证工具验
证软件。为避免软件被篡改或替换，防止给用户带来安全风险，建议用户进行此
项操作。


       - 特性声明


– NetStream 功能，出于对网络流量的统计管理，可能涉及对最终用户的通信
内容分析，建议您在所适用法律法规允许的目的和范围内方可启用相应的功
能。在采集、存储用户通信内容的过程中，您应采取足够的措施以确保用户
的通信内容受到严格保护。


–
镜像功能，可能基于运维目的需要对某些最终用户的通信内容进行分析，建
议您在所适用法律法规允许的目的和范围内方可启用相应的功能。在采集、
存储用户通信内容的过程中，您应采取足够的措施以确保用户的通信内容受
到严格保护。


–
报文头获取功能，出于检测通信传输中的故障和错误的目的，可能涉及采集
或存储个人用户某些通信内容。本公司无法单方采集或存储用户通信内容。
建议您只有在所适用法律法规允许的目的和范围内方可启用相应的功能。在
采集、存储用户通信内容的过程中，您应采取足够的措施以确保用户的通信
内容受到严格保护。


       - 可靠性设计声明


对于网络规划和站点设计，必须严格遵守可靠性设计原则，具备设备级和方案级
保护。设备级保护包括双网双平面，双机、跨板双链路的规划原则，避免出现单
点，单链路故障。方案级指 FRR 、 VRRP 等快速收敛保护机制。在应用方案级保护


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 4



HUAWEI NetEngine40E
配置指南 1 QoS


时，应避免保护方案的主备路径经过相同链路或者传输，以免方案级保护不生
效。

##### 特别声明


       - 本文档中包含了 NE40E 支持的所有产品内容，如果需要了解在本国销售的设备或
单板等硬件相关信息，请查阅硬件描述章节。


       - 本手册仅作为使用指导，其内容依据实验室设备信息编写。手册提供的内容具有
一般性的指导意义，并不确保涵盖所有使用场景。因版本升级、设备型号不同、
板卡限制不同、配置文件不同等原因，可能造成手册中提供的内容与用户使用的
设备界面不一致。请以用户设备界面的信息为准，本手册不再针对前述情况造成
的差异一一说明。


       - 本手册中提供的最大值是设备在实验室特定场景（例如被测试设备上只有某种类
型的单板，或者只配置了某一种协议）达到的最大值。在现实网络中，由于设备
硬件配置不同、承载的业务不同等原因可能会使设备测试出的最大值与手册中提
供的数据不一致。


       - 本手册中出现的接口编号仅作示例，并不代表设备上实际具有此编号的接口，实
际使用中请以设备上存在的接口编号为准。


       - 本手册中的硬件照片仅供参考，具体请以发货的硬件实物为准。


       - 本手册中体现设备支持的相关硬件板卡，存在特定客户定制的需求，实际支持以
售前销售界面为准。


       - 出于特性介绍及配置示例的需要，产品资料中会使用公网 IP 地址，如无特殊说
明，资料里出现的公网 IP 地址均为示意，不指代任何实际意义。


       - 本手册中配置指南出现的“ XX 配置注意事项”，请结合产品的实际特性支持情况
来使用。


       - 本手册中的日志参考和告警参考，记录的是对应产品上注册的日志和告警信息。
实际应用中可触发的日志和告警，取决于当前产品所支持的业务功能。


       - 本文档中描述的所有设备尺寸数据均为设计尺寸，不包含尺寸公差。在部件制造
过程中，由于加工或测量等因素的影响，实际尺寸存在一定的偏差。

##### 符号约定


在本文中可能出现下列标志，它们所代表的含义如下。

|符号|说明|
|---|---|
||表示如不避免则将会导致死亡或严重伤害的具有高等<br>级风险的危害。|
||表示如不避免则可能导致死亡或严重伤害的具有中等<br>级风险的危害。|
||表示如不避免则可能导致轻微或中度伤害的具有低等<br>级风险的危害。|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 5



HUAWEI NetEngine40E
配置指南 1 QoS

##### 命令格式约定 修订记录

|符号|说明|
|---|---|
||用于传递设备或环境安全警示信息。如不避免则可能<br>会导致设备损坏、数据丢失、设备性能降低或其它不<br>可预知的结果。<br>“须知”不涉及人身伤害。|
||对正文中重点信息的补充说明。<br>“说明”不是安全警示信息，不涉及人身、设备及环<br>境伤害信息。|


|格式|意义|
|---|---|
|粗体|命令行关键字（命令中保持不变、必须照输的部分）采用<br>加粗字体表示。|
|斜体|命令行参数（命令中必须由实际值进行替代的部分）采用<br>斜体表示。|
|[ ]|表示用“[ ]”括起来的部分在命令配置时是可选的。|
|{ x | y | ... }|表示从两个或多个选项中选取一个。|
|[ x | y | ... ]|表示从两个或多个选项中选取一个或者不选。|
|{ x | y | ... }*|表示从两个或多个选项中选取多个，最少选取一个，最多<br>选取所有选项。|
|[ x | y | ... ]*|表示从两个或多个选项中选取多个或者不选。|
|&<1-n>|表示符号&前面的参数可以重复1～n次。|
|#|由“#”开始的行表示为注释行。|



修改记录累积了每次文档变更的说明。最新版本的文档包含以前所有文档版本的更新
内容。

|产品版本|文档版本|发布日期|
|---|---|---|
|V800R023C10SPC500|02|2024-06-30|
|V800R023C10SPC500|01|2024-03-30|


