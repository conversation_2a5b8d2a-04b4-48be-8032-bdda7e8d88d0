

当连接到路由器时，会看到一段提示信息。这段信息可以修改为希望显示的内容。

##### 背景信息


登录警示信息是用户在连接到路由器之后，或者通过了登录验证之后、开始交互配置
之前系统显示的一段提示信息。使用此配置为用户登录提供明确的指示信息。


说明


设备重启后，如果用户在系统初始化过程中进行登录操作，由于文件系统还没有完成初始化，此
时会提示用户系统未获取到文件。

##### 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **header** **login** { **information** text | **file** file-name } ，设置登录时的警示信
息。


步骤 **3** 执行命令 **header** **shell** { **information** text | **file** file-name } ，设置登录成功后的警示
信息。


步骤 **4** 执行命令 **commit** ，提交配置。


**----**
结束
