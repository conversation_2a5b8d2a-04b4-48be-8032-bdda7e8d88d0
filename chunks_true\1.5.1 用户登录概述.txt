

用户只有成功登录到设备，才能实现对设备的管理与维护。用户登录设备的方式有：
Console 口、 Telnet 或 STelnet 。


配置了用户界面、用户管理和终端服务后，用户才能登录到设备，对本地或远端的设
备进行配置、监控和维护。


用户界面提供登录入口，用户管理确保登录安全，终端服务则提供登录协议支持。


用户可通过如 表 **1-10** 所示几种方式登录设备，对设备进行配置和管理。


表 **1-10** 用户登录方式








|登录方式|应用场景|说明|
|---|---|---|
|配置用户通过<br>**Console**口登录系<br>统|使用终端通过连接设备的<br>Console口登录设备，进行第<br>一次上电和配置。<br>●当用户无法进行远程访问<br>设备时，可通过Console进<br>行本地登录。<br>●当设备系统无法启动时，<br>可通过Console口进行诊断<br>或进入BootRom进行系统<br>升级。|缺省情况下，用户可以直接通<br>过Console口本地登录设备。<br>说明<br>如果用户登录的是备板Console<br>口，此时用户只有查询权限，无<br>配置管理权限。|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 54


HUAWEI NetEngine40E
配置指南 1 基础配置








|登录方式|应用场景|说明|
|---|---|---|
|配置用户通过<br>**Telnet**登录系统|通过终端连接到网络上，使用<br>Telnet方式登录设备，进行本<br>地或远程的配置，目标设备根<br>据配置的登录参数对用户进行<br>验证。<br>常用于本地或远程维护设备，<br>特别是远程维护方式，为用户<br>带来了极大的方便，但安全性<br>不高。|缺省情况下，用户不能通过<br>Telnet方式直接登录设备。如<br>果需要通过Telnet方式登录设<br>备，必须先通过Console口本<br>地登录设备，并完成以下配<br>置：<br>●配置设备管理网口的IP地<br>址，确保终端和登录的设备<br>之间路由可达（缺省情况<br>下，设备管理网口的IP地址<br>为***********/24）。<br>●配置VTY用户界面的用户认<br>证方式。<br>●配置VTY用户界面的用户级<br>别。<br>●使能Telnet服务器功能。|
|配置用户通过<br>**STelnet**登录系统|通过终端连接到网络上，如果<br>网络安全性不高，SSH<br>（Secure Shell）可提供安全<br>的信息保障和强大认证功能，<br>保护设备系统不受IP欺骗等攻<br>击。<br>常用于需要安全登录设备，实<br>现安全的本地维护或远程维<br>护。|缺省情况下，用户不能通过<br>STelnet方式直接登录设备。如<br>果需要通过STelnet方式登录设<br>备，必须先通过Console口本<br>地登录设备，并完成以下配<br>置：<br>●配置设备管理网口的IP地<br>址，确保终端和登录的设备<br>之间路由可达（缺省情况<br>下，设备上没有配置IP地<br>址）。<br>●配置VTY用户界面的用户认<br>证方式。<br>●配置VTY用户界面的用户级<br>别。<br>●配置VTY用户界面支持SSH<br>协议。<br>●配置SSH用户并指定服务方<br>式包含STelnet。<br>●使能STelnet服务器功能。|
|配置用户通过**AUX**<br>口登录系统|当用户终端和设备之间无可达<br>路由时，用户可以通过设备的<br>AUX口登录设备，实现远程配<br>置和管理，也可以通过AUX口<br>进行本地维护。|通过AUX口登录设备后无法直<br>接实现管理和维护设备，为了<br>实现远程管理和维护设备，建<br>议通过AUX口登录设备前，先<br>通过Console口本地登录设<br>备，更改AUX用户界面的用户<br>优先级。|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 55


HUAWEI NetEngine40E
配置指南 1 基础配置


当用户通过以上方式登录设备时，设备会自动执行一个系统中预设好的批处理文件，
并将此操作信息记录在系统日志文件中。

##### Console 口概述


Console 口概述请参见 首次登录系统概述 。

##### Telnet 概述


Telnet 协议在 TCP/IP 协议族中属于应用层协议，通过网络提供远程登录和虚拟终端功
能。 NE40E 提供的 Telnet 服务包括：


       - Telnet server ：用户在 PC 上运行 Telnet 客户端程序登录到设备，对设备进行配置管
理。此时，设备提供 Telnet server 服务。


       - Telnet client ：用户在 PC 上通过终端仿真程序或 Telnet 客户端程序建立与设备的连
接后，再执行 **telnet** 命令登录到其它设备，对其进行配置管理。如 图 **1-7** 所示， CE
此时既作为 Telnet server ，同时也提供 Telnet client 服务。


图 **1-7** Telnet server 提供 Telnet client 服务示意图


       - 中断 Telnet 服务


图 **1-8** Telnet 快捷键使用示意图


在 Telnet 连接过程中，可以使用两种快捷键来中断连接。如 图 **1-8** 所示， P1 通过
Telnet 登录到 P2 ，再 Telnet 连接到 P3 ，形成级联结构。 P1 是 P2 的 Telnet 客户端，
P2 是 P3 的 Telnet 客户端，以此结构说明两种快捷键的用法。


–
<Ctrl_]> 快捷键——通知服务器端断开连接


在网络畅通的情况下，键入 <Ctrl_]> 将通知 Telnet 服务器中断本次 Telnet 登
录，即， Telnet 服务器端主动断开连接。


例如，在 P3 上键入 <Ctrl_]> ，将退回到 P2 的提示符。


<P3> Select **Ctrl_]** to return to the prompt of P2
The connection was closed by the remote host.
<P2>


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 56


HUAWEI NetEngine40E
配置指南 1 基础配置


此时键入 <Ctrl_]> ，将退回到 P1 的提示符。


<P2> **Ctrl_]**
The connection was closed by the remote host.
<P1>


说明


如果由于某些原因网络连接断开，快捷键的指令无法到达 Telnet 服务器端，输入无
效。


–
<Ctrl_K> 快捷键——客户端主动断开连接


当服务器端故障且客户端无法感知时，客户端输入任何指令服务器均无响
应，这种情况下键入 <Ctrl_K> 快捷键，则 Telnet 客户端主动中断并退出 Telnet
连接。


例如，在 P3 上键入 <Ctrl_K> ，将直接中断并退出 Telnet 连接。


<P3> **Ctrl_K**
<P1>


须知


当远端用户登录数达到 VTY 类型用户界面的最大个数时，系统会提示所有的
用户接口都在使用，不允许 Telnet 。

##### STelnet 概述


说明


目前，设备作为 SSH 服务器时，支持 SSH2 和 SSH1 两个版本。设备作为 SSH 客户端时，只支持
SSH2 版本。 STelnet 基于 SSH2 协议，客户端和服务器之间经过协商，建立安全连接，客户端可
以像操作 Telnet 一样登录服务器。


Telnet 缺少安全的认证方式，而且传输过程采用 TCP 进行明文传输，存在很大的安全隐
患。单纯提供 Telnet 服务容易招致 DoS （ Denial of Service ）、主机 IP 地址欺骗、路由
欺骗等恶意攻击。


相对于 Telnet ， SSH （ Secure Shell ）通过以下措施实现在不安全网络上提供安全的远
程访问：


       - 支持密钥验证，根据非对称加密体系的加密原则，通过生成公钥和私钥，实现密
钥的安全交换，最终实现安全的会话全过程。


       - 支持对传输的数据进行加密。


       - SSH 客户端与服务器端通讯时，用户名及口令均进行加密，有效防止对口令的窃
听。


设备支持 SSH 服务器功能，可以接受多个 SSH 客户端的连接。同时，设备还支持 SSH 客
户端功能，允许用户与支持 SSH 服务器功能的设备建立 SSH 连接，从而实现从本地设备
通过 SSH 登录到远程设备。


       - 本地连接


如 图 **1-9** 所示，可以建立 SSH 通道进行本地连接。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 57


HUAWEI NetEngine40E
配置指南 1 基础配置


图 **1-9** 在局域网内建立 SSH 通道


       - 广域网连接


如 图 **1-10** 所示，可以建立 SSH 通道进行广域网连接。


图 **1-10** 通过广域网建立 SSH 通道


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 58


HUAWEI NetEngine40E
配置指南 1 基础配置
