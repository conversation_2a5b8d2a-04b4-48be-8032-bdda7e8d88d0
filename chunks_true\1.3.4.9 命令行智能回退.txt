

命令行具有智能回退功能，即在当前视图下可执行其他视图下的命令。

##### 背景信息


命令行支持智能回退，即在当前视图下执行某条命令，如果命令行匹配失败，会自动
退到上一级视图进行匹配，如果仍然失败则继续退到上一级视图匹配，直到退到系统
视图为止。如果能够匹配到此命令，则会在当前视图下执行此命令并进入相应视图。


用户在配置业务时，为完成所需配置，需进入待配置命令所在视图，才能配置成功，
这样就需要重复执行 quit 命令退出当前视图，进入所需视图，给用户配置带来很多重复
工作。智能回退功能，可以在当前视图下执行其他视图的命令，减少重复工作，为用
户配置提供便利。


说明


如果在当前视图下由于模糊匹配发生歧义导致匹配失败时，不进行智能回退。


命令行匹配失败时不进行智能回退。


**undo** 命令不支持智能回退。

##### 任务示例


1. 执行命令 **terminal command forward matched upper-view** 使能命令行智能回
退功能。


2. 执行命令 **system-view** ，进入系统视图。


3. 执行命令 **interface** GigabitEthernet1/0/0.1 ，创建子接口并进入子接口视图。


4. 无需退出当前视图，可直接执行命令 **interface** LoopBack 1 ，创建 LoopBack 接口
并进入 LoopBack 接口视图。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 27


HUAWEI NetEngine40E
配置指南 1 基础配置


须知


智能回退功能可能会出现命令行在非预期视图执行，可能会影响业务运行，配置命令
行前请仔细确认本视图下是否存在即将配置的命令行，如果不存在请在正确的视图执
行该命令行。


如果需要关闭命令行智能回退功能，可执行命令 **undo terminal command forward**
**matched upper-view** 关闭。
