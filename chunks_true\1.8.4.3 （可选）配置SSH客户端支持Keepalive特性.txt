

在 SSH 客户端配置 Keepalive 特性后，客户端将在设置的周期后发送 Keepalive 报文给
SSH 服务器，检测对端是否可达，以便尽早发现网络故障。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 240


HUAWEI NetEngine40E
配置指南 1 基础配置

##### 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **ssh client keepalive-interval** seconds ，配置 SSH 客户端发送 Keepalive 报文
的时间间隔。


如果在设置的周期内， SSH 客户端没有收到服务器的回应，客户端将在设置的周期后
发送一个 Keepalive 报文给服务器。如果服务器没有响应，客户端将断开连接。


步骤 **3** 执行命令 **ssh client keepalive-maxcount** count ，配置 SSH 客户端发送的 Keepalive 报
文的最大数目。


客户端发送 Keepalive 报文的时间间隔必须超过配置的发送 Keepalive 报文的最大数目。
例如发送报文的时间间隔是 0 秒（即不发送 keepalive 报文），那么配置的最大
Keepalive 报文数量将无效。


步骤 **4** 执行命令 **commit** ，提交配置。


**----**
结束
