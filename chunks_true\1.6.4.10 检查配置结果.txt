

通过 FTP 进行文件操作配置成功后，可以查看到 FTP 服务器的配置和状态信息、登录的
FTP 用户信息等内容。

##### 前提条件


已完成通过 FTP 进行文件操作的所有配置。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 157


HUAWEI NetEngine40E
配置指南 1 基础配置

##### 操作步骤


       - 使用 **display ftp-server** 命令查看 FTP 服务器的配置和状态信息。


       - 使用 **display ftp-users** 命令查看登录的 FTP 用户信息。


       - FTP 协议的白名单 Session-CAR 功能配置成功以后，可以按照如下指导检查配置结
果：


– 基于 IPv4 ：使用命令 **display cpu-defend whitelist session-car** **ftp**
**statistics** **slot** slot-id ，查看指定接口板上的 FTP 协议白名单 Session-CAR 的
统计信息。


如果需要查看某一段时间的统计信息，可以使用命令 **reset cpu-defend**
**whitelist session-car** **ftp** **statistics** **slot** slot-id 先清除指定接口板上的 FTP
协议白名单 Session-CAR 的统计信息，再使用命令 **display cpu-defend**
**whitelist session-car** **ftp** **statistics** **slot** slot-id 。


– 基于 IPv6 ：使用命令 **display cpu-defend whitelist-v6 session-car** **ftpv6**
**statistics** **slot** slot-id ，查看指定接口板上的 FTP 协议白名单 Session-CAR 的
统计信息。


如果需要查看某一段时间的统计信息，可以使用命令 **reset cpu-defend**
**whitelist-v6 session-car** **ftpv6** **statistics** **slot** slot-id 先清除指定接口板上
的 FTP 协议白名单 Session-CAR 的统计信息，再使用命令 **display cpu-defend**
**whitelist-v6 session-car** **ftpv6** **statistics** **slot** slot-id 。


**----**
结束
