

用户可以配置 SFTP 客户端的源地址信息，从指定的客户端源地址建立 SFTP 连接，保证
安全性。

##### 背景信息


用户可以在设备上指定某一接口，为此接口配置 IP 地址，然后使用该 IP 地址作为 SFTP
连接的源 IP 地址，从而达到进行安全校验的目的。


客户端源地址可以配置为源接口或源 IP 。


请在作为 SFTP 客户端的设备上进行如下的配置。

##### 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行以下命令配置 SFTP 的源地址或源接口信息。


       - IPv4 场景下：执行命令 **sftp client-source -a** source-ip-address { **public-net** | **-**
**vpn-instance** ipv6-vpn-instance-name } } 或 **sftp client-source -i** { interfacetype interface-number | interface-name }


       - IPv6 场景下：执行命令 **sftp ipv6 client-source** **-a** source-ipv6-address [ **-vpn-**
**instance** ipv6-vpn-instance-name ]


步骤 **3** 执行命令 **commit** ，提交配置。


**----**
结束


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 253


HUAWEI NetEngine40E
配置指南 1 基础配置
