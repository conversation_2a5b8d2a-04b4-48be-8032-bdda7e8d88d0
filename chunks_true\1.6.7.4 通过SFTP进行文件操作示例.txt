

在本示例中，通过在 SSH 服务器端生成本地密钥对，并在 SSH 服务器端配置 SSH 用户的
用户名和密码，使能 SFTP 服务，实现 SFTP 客户端连接 SSH 服务器后进行文件操作。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 181


HUAWEI NetEngine40E
配置指南 1 基础配置

##### 组网需求


随着设备的逐步稳定，应用范围在不断扩大，设备升级的范围也越来越大，设备的可
维护性要求也越来越高，特别是设备在线远程加载升级。这不仅可以丰富设备升级维
护手段，方便远程在线升级，还可以节约升级成本，减少客户等待升级的时间，在一
定程度上提高了客户的满意度。最通用的在线升级、数据传输就是 FTP 。但是 FTP 是明
文传输，甚至是明文传输用户名和密码，存在安全隐患。


而 SFTP 使得用户可以从远端安全的登入设备进行文件管理，增加了数据传输的安全
性。同时，由于提供了 Client 功能，可以在本设备上 SFTP 到远程设备，进行文件的安
全传输、在线升级。


如 图 **1-50** 所示，在作为 SSH 服务器的设备上使能 SFTP 服务器功能后， SFTP 客户端 PC 可
以通过 RSA 、 DSA 、 ECC 、 SM2 、 x509v3-ssh-rsa 、 password 、 password-rsa 、
password-ecc 、 password-dsa 、 password-sm2 、 password-x509v3-rsa 和 all 认证的方
式连接到 SSH 服务器端。


图 **1-50** 通过 SFTP 进行文件操作组网图


说明


本例中 interface1 代表 GigabitEthernet0/0/0 。

##### 配置注意事项


已经成功通过 Console 口登录作为 SFTP 服务器的设备，配置用户通过 SFTP 方式的登录
IP 地址，建议此 IP 地址配置在逻辑接口上。

##### 配置思路


采用如下的思路配置用户通过 SFTP 进行文件操作：


1. 创建本地用户并配置。


2. 在 SSH 服务器端使能 SFTP 服务器功能以及配置用户的服务类型。

##### 数据准备


为完成此配置举例，需准备如下的数据：


       - SSH 用户的认证方式为 password ，用户名为“ client001 ”。


       - client001 的用户级别为 3 。


       - SSH 服务器端的 IP 地址为 ************** 。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 182


HUAWEI NetEngine40E
配置指南 1 基础配置

##### 操作步骤


步骤 **1** 配置 SFTP 服务器的 IP 地址


<HUAWEI> **system-view**

[ ~ HUAWEI] **sysname SSH Server**

[*HUAWEI] **commit**

[ ~ SSH Server] **interface GigabitEthernet0/0/0**

[ ~ SSH Server-GigabitEthernet0/0/0] **undo shutdown**

[*SSH Server-GigabitEthernet0/0/0] **ip address ************** *************

[*SSH Server-GigabitEthernet0/0/0] **quit**

[*SSH Server] **commit**


步骤 **2** 在服务器端配置本地用户的用户名和密码


[*SSH Server] **aaa**

[*SSH Server-aaa] **local-user client001 password**
Please configure the password (8-128)
Enter Password:
Confirm Password:

[*SSH Server-aaa] **local-user client001 level 3**

[*SSH Server-aaa] **local-user client001 service-type ssh**

[*SSH Server-aaa] **quit**

[*SSH Server] **commit**


步骤 **3** 使能 SFTP 功能，并配置用户的服务类型为 SFTP


[ ~ SSH Server] **interface LoopBack 0**

[ ~ SSH Server-LoopBack0] **ip address ******** *****************

[*SSH Server-LoopBack0] **quit**

[*SSH Server] **sftp server enable**

[*SSH Server] **ssh server-source** **-i loopback 0**

[*SSH Server] **ssh user client001**

[*SSH Server] **ssh user client001 authentication-type password**

[*SSH Server] **commit**

[ ~ SSH Server] **ssh user client001 service-type sftp**

[*SSH Server] **commit**


步骤 **4** 配置 SSH 用户的授权目录


[ ~ SSH Server] **ssh user** **client001 sftp-directory cfcard:/**

[*SSH Server] **commit**


步骤 **5** 验证配置结果


打开客户端的 SFTP 软件，输入用户名、密码、端口号（默认为 22 ），访问 SSH 服务
器，并进行文件传输。


**----**
结束

##### SSH Server 的配置文件


#

sysname SSH Server
#

aaa
local-user client001 password cipher @%@%.OuC6Vo7Z,A'y ~ /KB&,vmd@%@%
local-user client001 service-type ssh
local-user client001 level 3

#
interface GigabitEthernet0/0/0
undo shutdown
ip address ************** ***********
#
interface LoopBack 0
ip address ******** ***************
sftp server enable
ssh server-source -i loopback 0
ssh user client001
ssh user client001 authentication-type password


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 183


HUAWEI NetEngine40E
配置指南 1 基础配置


ssh user client001 service-type sftp
ssh user client001 sftp-directory cfcard:/
#

return
