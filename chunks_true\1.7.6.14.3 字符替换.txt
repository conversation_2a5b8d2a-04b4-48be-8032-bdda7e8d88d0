

设备上的某个字符串需要替换成新的字符串时，可执行该功能进行批量替换。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 216


HUAWEI NetEngine40E
配置指南 1 基础配置

##### 应用环境


如果设备上的某个字符串或者某类字符串不满足用户需求，可以按如下步骤执行此功
能对这类字符进行批量替换。该功能仅替换当前视图下的字符串。


此功能只能在两阶段生效模式下生效。

##### 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **replace configuration pattern** src-string **with** target-string ，将源字符串
替换成目的字符串。


说明


源字符串 src-string 支持正则表达式。可使用正则表达式指定一类源字符串。


表 **1-36** 正则表达式语法意义描述












|特殊字符|功能|举例|
|---|---|---|
|\|转义字符。将下一个字符（特殊字<br>符或者普通字符）标记为普通字<br>符。|\*匹配*|
|^|匹配行首的位置。|^10匹配10.10.10.1，不匹配<br>2.2.2.2|
|$|匹配行尾的位置。|1$匹配10.10.10.1，不匹配<br>10.10.10.2|
|*|匹配前面的子正则表达式零次或多<br>次。|10*可以匹配1、10、100、1000、<br>……<br>(10)*可以匹配空、10、1010、<br>101010、……|
|+|匹配前面的子正则表达式一次或多<br>次。|10+可以匹配10、100、1000、<br>……<br>(10)+可以匹配10、1010、<br>101010、……|
|?|匹配前面的子正则表达式零次或一<br>次。<br>说明<br>当前，在华为公司数据通信设备上通<br>过命令行运用正则表达式输入？时，<br>系统显示为命令行帮助功能。华为公<br>司数据通信设备不支持正则表达式输<br>入？特殊字符。|10?可以匹配1或者10<br>(10)?可以匹配空或者10|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 217


HUAWEI NetEngine40E
配置指南 1 基础配置












|特殊字符|功能|举例|
|---|---|---|
|.|匹配任意单个字符。|a.b匹配任何一个以a开头，以b结<br>尾含有三个字符的字符串<br>0.0可以匹配0x0、020、……<br>.oo.可以匹配book、look、tool、<br>……|
|()|一对圆括号内的正则表达式作为一<br>个子正则表达式，匹配子表达式并<br>获取这一匹配。<br>如果圆括号中内容为空，则等价于<br>空串。<br>如果模式串只有()，则可匹配任意<br>字符串。<br>如果模式串中的右括号没有匹配的<br>左括号，则右括号就作为普通字<br>符。<br>如果模式串中的左括号没有匹配的<br>右括号，则为非法模式串。|100(200)+可以匹配100200、<br>100200200、……<br>(ab)匹配abcab<br>()匹配任意字符串<br>a()b匹配12ab12<br>a)b匹配za)bc<br>a(b为非法模式串|
|_|匹配一个符号，包括逗号、左大括<br>号、右大括号、左括号、右括号和<br>空格，在表达式的开头或结尾时还<br>可作起始符、结束符（同^ ，<br>$）。|_65001_可以匹配20 65001 30、<br>20 65001、65001 30、65001、<br>……|
|x|y|匹配x或y。|100|200匹配100或者200<br>1(2|3)4匹配124或者134，而不匹<br>配1234、14、1224、1334|
|[xyz]|匹配正则表达式中的任意一个字<br>符。不可同时匹配多个字符，也不<br>可匹配同一个字符多次。|[123]匹配255中的2<br>[abc]匹配字符“a”、“b”、<br>“c”|
|[^xyz]|匹配字符串中非“x”、“y”、<br>“z”的字符。只要字符串中有非<br>“x”、“y”、“z”的字符，就<br>能匹配到。|[^123]匹配除123之外的任何字符<br>[^abc]匹配除“a”、“b”、<br>“c”之外的任何字符|
|[a-z]|匹配正则表达式指定范围内的任意<br>一个字符。不可同时匹配多个字<br>符，也不可匹配同一个字符多次。|[0-9]匹配指定范围内的任意数字<br>[a-z]匹配指定范围内的任意字母<br>[z-a]为非法模式串|
|[^a-d]|匹配字符串中除“a”、“b”、<br>“c”、“d”以外的其他字符。只<br>要字符串中有a～d范围外的字符，<br>就能匹配到。|[^0-9]匹配所有非数字字符<br>[^a-z]匹配除字母以外的其他任意<br>字符<br>[^z-a]为非法模式串|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 218


HUAWEI NetEngine40E
配置指南 1 基础配置


说明


          - 除非特别说明，上表中涉及到的字符指的是可以打印的字符，包括字母、数字、空格及特殊
符号等。


步骤 **3** （可选）执行命令 **display configuration candidate** ，查看替换后的配置是否符合预
期：


       - 如果符合预期，请继续执行步骤 4 。

       - 如果不符合预期，请执行命令 **clear configuration candidate** 清除替换后的内
容，重新执行步骤 2 替换字符。


步骤 **4** 执行命令 **commit** ，提交配置。


**----**
结束
