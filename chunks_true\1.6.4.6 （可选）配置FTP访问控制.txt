

用户可以配置 FTP 访问控制列表，实现只允许指定的客户端登录到设备。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 152


HUAWEI NetEngine40E
配置指南 1 基础配置

##### 背景信息


当设备作为 FTP 服务器时，为提高安全性，可通过配置 ACL 实现只允许满足匹配条件的
客户端访问服务器。


请在作为 FTP 服务器的设备上进行如下的配置。

##### 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **acl** acl-number ，进入 ACL 视图。


步骤 **3** 执行命令 **rule** [ rule-id ] [ **name** rule-name ] { **deny** | **permit** } [ **fragment-type**
{ **fragment** | **non-fragment** | **non-subseq** | **fragment-subseq** | **fragment-spe-**
**first** } | **source** { source-ip-address { source-wildcard | **0** | src-netmask } | **any** } |
**time-range** time-name | **vpn-instance** vpn-instance-name ] [*] ，配置 ACL 规则。


步骤 **4** 执行命令 **quit** ，退回系统视图。


步骤 **5** 执行命令 **ftp** [ **ipv6** ] **acl** { acl-number | name } ，配置 FTP 访问控制列表。


步骤 **6** 执行命令 **commit** ，提交配置。


**----**
结束
