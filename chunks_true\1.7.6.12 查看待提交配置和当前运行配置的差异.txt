

用户完成一组配置，在提交之前，可以查看修改的配置和当前运行的配置之间的差
异，如果查看差异时有冲突，需先解决配置冲突后再查看。

##### 背景信息


用户完成一组配置，在提交之前，可以查看修改的配置和当前运行的配置之间的差
异。在用户修改配置到查看配置差异期间，如果设备上正在运行的配置发生变更，就
会发生配置冲突，用户执行查看配置差异命令时，会提示错误，此时需要先解决配置
冲突，才能查看到配置差异信息。


说明


该操作仅支持在两阶段生效模式下配置。

##### 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **display configuration candidate changes** ，查看当前用户未提交的配置和
当前运行配置之间的差异。


执行该步骤时，如果提示设备当前运行配置有变更，需执行 步骤 **3** 解决配置冲突，然后
再执行该步骤查看配置差异。


步骤 **3** （可选）执行命令 **refresh configuration candidate** ，更新当前用户未提交的配置，
解决配置冲突。


当发生配置冲突时，执行该步骤解决配置冲突。


**----**
结束


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 212


HUAWEI NetEngine40E
配置指南 1 基础配置
