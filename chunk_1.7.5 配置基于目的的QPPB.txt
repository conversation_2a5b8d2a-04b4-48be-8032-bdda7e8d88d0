头标题: 1.7.5 配置基于目的的QPPB
尾标题: ******* 配置BGP路由发送端的路由发布策略
内容: HUAWEI NetEngine40E配置指南 1 QoS表 **1-14** IP Precedence/MPLS EXP/802.1p 到服务等级的缺省映射表|IP Precedence/MPLS<br>EXP/802.1p|Service|Color||---|---|---||0|BE|Green||1|AF1|Green||2|AF2|Green||3|AF3|Green||4|AF4|Green||5|EF|Green||6|CS6|Green||7|CS7|Green|表 **1-15** 服务等级到 IP Precedence/MPLS EXP/802.1p 的缺省映射表|Service|Color|IP Precedence/MPLS EXP/802.1p||---|---|---||BE|Green、<br>Yellow、Red|0||AF1|Green、<br>Yellow、Red|1||AF2|Green、<br>Yellow、Red|2||AF3|Green、<br>Yellow、Red|3||AF4|Green、<br>Yellow、Red|4||EF|Green、<br>Yellow、Red|5||CS6|Green、<br>Yellow、Red|6||CS7|Green、<br>Yellow、Red|7|步骤 **4** （可选）在接口视图执行命令 **field dei enable vlan** { { vlan-id1 [ **to** vlanid2 ] }&<1-10> | **all** } ，使能接口指定 VLAN 范围内报文的 DEI 能力。使能后，简单流分类按照服务优先级进入队列，并且根据报文的 CFI 字段进行着色。说明DEI 功能与简单流分类同时配置且信任 802.1p 优先级时才能生效。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 96HUAWEI NetEngine40E配置指南 1 QoS步骤 **5** 执行命令 **commit** ，提交配置。步骤 **6** 执行命令 **quit** ，退回系统视图。步骤 **7** 请根据流量策略应用的接口选择下面的配置步骤。说明对于非 LPUF-50/LPUF-50-L/LPUI-21-L/LPUI-51-L/LPUF-51/LPUF-51-B/LPUI-51/LPUI-51-B/LPUS-51/LPUF-101/LPUF-101-B/LPUI-101/LPUI-101-B/LPUS-101 单板， L3VPN 差分模式为PIPE 的场景下，希望按照 8021P 值进行流量调度时，需要在系统视图下执行 **diffserv-mode pipe****mapping-8021p mpls-pop** 命令使能对 MPLS 出 PE 设备上接口出方向报文回填 8021P 值。       - 在三层接口上对 VLAN 报文应用流量策略a. 执行命令 **interface** **gigabitethernet** interface-number.subnumber ，进入子接口视图。b. 配置绑定 DS 域，根据使用场景选择如下配置：说明两种配置应用场景有所不同，配置 **qos phb enable** 只在下行生效，实现优先级映射；如果需要在上行和下行都实现优先级映射，则要配置 **trust upstream** 。#### ▪ 执行命令 trust upstream { 5p3d | ds-domain-name | default } ，在子接口上绑定 DS 域。#### ▪ 执行命令 qos phb enable { ds-domain-name | default } ，在下行子接口配置，用于绑定 DS 域。说明在接口下配置 **qos phb enable** 时，与该接口下配置的 **trust upstream** 命令、**qos phb disable** 命令互斥。c. 执行命令 **trust** { **8021p** | **inner-8021p** | **outer-8021p** } [ **inbound** |**outbound** ] ，使能根据 802.1p 的简单流分类。说明#### ▪ 在配置 trust 8021p 命令之前，必须先在接口上绑定 DS 域，即配置 trust**upstream** 命令，该配置才生效。#### ▪ 在接口加入到 Diff-Serv 域后， Diff-Serv 域所定义的流量策略将会自动对出入接口的流量起作用。       - 在二层接口上对 VLAN 报文应用流量策略a. 执行命令 **interface** **gigabitethernet** interface-number ，进入接口视图。b. 执行命令 **portswitch** ，进入二层接口视图。c. 执行命令 **port trunk allow-pass vlan** { { vlan-id1 [ **to** vlan-id2 ] }&<1-10> | **all** } ，配置二层端口以 tagged 方式加入到指定的 VLAN 中。d. 配置绑定 DS 域，根据使用场景选择如下配置：说明两种配置应用场景有所不同，配置 **qos phb enable vlan** 只在下行生效，实现优先级映射；如果需要在上行和下行都实现优先级映射，则要配置 **trust upstream vlan** 。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 97HUAWEI NetEngine40E配置指南 1 QoS#### ▪ 执行命令 trust upstream { 5p3d | ds-domain-name | default }[ **inbound** | **outbound** ] **vlan** { vlan-id1 [ **to** vlan-id2 ] } &<1-10> ，在下行接口配置，用于绑定 DS 域，实现优先级映射。#### ▪ 执行命令 qos phb enable { ds-domain-name | default | 5p3d } vlan{ vlan-id1 [ **to** vlan-id2 ] } &<1-10> ，在下行接口配置，用于绑定 DS域，实现优先级映射。说明在接口下配置 **qos phb enable** 时，与该接口下配置的 **trust upstream vlan** 命令、 **qos phb disable** 命令互斥。e. 执行命令 **trust 8021p** [ **inbound** | **outbound** ] **vlan** { vlan-id1 [ **to** vlanid2 ] } &<1-10> ，使能根据 802.1p 进行简单流分类。步骤 **8** 执行命令 **commit** ，提交配置。**----**结束##### 检查配置结果完成配置后，可以按以下指导来检查配置结果。       - 使用 **display diffserv domain** [ ds-domain-name ] [ **8021p** | **dscp** | **exp** ][ **inbound** | **outbound** ] 命令查看 DS 域的配置信息。       - 使用 **display diffserv domain application** ds-domain-name 命令查询指定 DS 域下应用的接口列表。#### 1.5.7 配置 MPLS 报文的优先级映射介绍如何配置 MPLS 报文的优先级映射和该功能的应用场景。##### 应用环境配置基于简单流分类的优先级映射可以将一种网络流量中的优先级映射到另外一种网络流量中，使流量在另外一种网络中按照原来的或用户配置的优先级传送。当 NE40E 作为不同网络之间的边缘路由器时，所有进入 NE40E 的 MPLS 报文，其原先的外部优先级标记即 EXP 将被映射为内部优先级（以 Diff-Serv 的服务等级和颜色表示）；NE40E 发出报文时，将内部优先级映射为某种外部业务优先级。MPLS 报文的优先级映射通常配置在网络的核心位置。##### 前置任务在配置 MPLS 报文的优先级映射之前，需要完成以下任务：       - 配置相关接口的物理参数       - 配置相关接口的链路层属性，保证接口的正常工作       - 配置相关接口的 IP 地址       - 使能路由协议，实现互通VS 模式下，该配置仅在 Admin VS 支持。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 98HUAWEI NetEngine40E配置指南 1 QoS##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **diffserv domain** ds-domain-name ，定义 DS 域并进入 DS 域视图。步骤 **3** 请根据实际情况对路由器的流量策略进行定义。       - 如果对入方向的 MPLS 流量定义流量策略，执行命令 **mpls-exp-inbound** exp **phb**service-class [ color ] 。       - 如果对出方向的 MPLS 流量定义流量策略，执行命令 **mpls-exp-outbound**service-class color **map** exp-value 。系统预先为用户定义了 default 域，如果用户在 DS 域中没有配置步骤 3 中的优先级映射，系统将采用缺省的映射关系。 default 域描述了缺省情况下 MPLS 报文 EXP 值和 QoS服务等级、颜色之间的映射关系，用户可以对 default 域中的映射关系进行修改，对于来自上游设备的报文，需要将报文的 EXP 值映射到 QoS 服务等级、颜色，具体映射关系如 表 **1-16** 所示；对于流向下游设备的报文，需要将报文的 QoS 服务等级、颜色映射到EXP 值，其映射关系如 表 **1-17** 所示。系统缺省的 MPLS 报文的 EXP 域值到服务类型映射关系如 表 **1-16** 所示。表 **1-16** EXP 与服务类型之间缺省的映射表|EXP|Service|Color|EXP|Service|Color||---|---|---|---|---|---||0|BE|Green|4|AF4|Green||1|AF1|Green|5|EF|Green||2|AF2|Green|6|CS6|Green||3|AF3|Green|7|CS7|Green|系统缺省的 MPLS 报文服务等级到 EXP 值的映射关系如 表 **1-17** 所示。表 **1-17** 服务等级与 EXP 之间缺省的映射表|Service|Color|MPLS EXP||---|---|---||BE|Green|0||AF1|Green、Yellow、Red|1||AF2|Green、Yellow、Red|2||AF3|Green、Yellow、Red|3||AF4|Green、Yellow、Red|4||EF|Green|5||CS6|Green|6||CS7|Green|7|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 99HUAWEI NetEngine40E配置指南 1 QoS步骤 **4** 执行命令 **commit** ，提交配置。步骤 **5** 执行命令 **quit** ，退回系统视图。步骤 **6** 执行命令 **interface** interface-type interface-number ，进入接口视图。步骤 **7** 执行命令 **trust upstream** { ds-domain-name | **default** } ，在接口上绑定 DS 域，使能简单流分类。步骤 **8** （可选）执行命令 **mpls l2vc diffserv domain** { **5p3d** | domain-name | **default** } ，在 VLL 接口上绑定 DS 域，指定获取私网标签优先级使用的 DS 域。步骤 **9** （可选）执行命令 **quit** ，退回系统视图。步骤 **10** （可选）执行命令 **slot** slot-id ，进入槽位视图。步骤 **11** （可选）执行命令 **mpls-inner-exp phb disable vll** ，去使能 VLL 场景下， PW 接口下行映射获取私网标签优先级的功能。步骤 **12** 执行命令 **commit** ，提交配置。**----**结束##### 检查配置结果完成配置后，可以按以下指导来检查配置结果。       - 使用 **display diffserv domain** [ ds-domain-name ] [ **8021p** | **dscp** | **exp** ][ **inbound** | **outbound** ] 命令查看 DS 域的配置信息。       - 使用 **display diffserv domain application** ds-domain-name 命令查询指定 DS 域下应用的接口列表。#### 1.5.8 配置 VXLAN 报文复杂流分类的流量策略介绍 VXLAN 报文复杂流分类的应用场景和配置过程。##### 应用环境基于 VXLAN 报文复杂流分类的流量策略支持根据报文的 DSCP 值、协议类型、 IP 地址、端口号等参数对不同分类的业务提供差别服务，以满足不同业务对网络带宽、时延等的应用需求。通常是在相对边界的路由器上配置复杂流分类的流量策略，在相对核心的路由器上配置简单流分类的流量策略。##### 前置任务在配置 VXLAN 报文复杂流分类的流量策略之前，需要完成以下任务：       - 配置 VXLAN 隧道       - BD/VPN 下绑定 VNI##### ******* 定义流分类要对网络中的流量进行基于类的 QoS 配置，就需要先定义流分类，流量的分类可以按照 ACL 规则、报文优先级等进行定义。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 100HUAWEI NetEngine40E配置指南 1 QoS##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** （可选）执行命令 **qos remark vxlan-qos-local-id enable** ，使能在 VXLAN 头中回填QoS 策略 ID 的功能。说明NetEngine 40E-X8AK 不支持此配置。步骤 **3** 执行命令 **traffic classifier** classifier-name [ **operator** { **and** | **or** } ] ，定义流分类并进入流分类视图。如果在一个流分类中配置了多个匹配规则，可以通过指定参数 operator 来设置这些规则之间的关系，其中：       - **and** ：指定类下的规则之间是逻辑“与”的关系，即数据包必须匹配全部规则才属于该类。       - **or** ：指定类下的规则之间是逻辑“或”的关系，即数据包只要匹配其中任何一个规则就属于该类。步骤 **4** 请根据实际情况对流分类的匹配规则进行定义。       - 如果定义 ACL 匹配规则，执行命令 **if-match** **acl** { acl-number | **name** acl-name }[ **precedence** precedence-value ] 。说明如果在高级 ACL 的 rule 规则中配置了虚匹配拟扩展局域网规则的命令 **rule** [ rule-id ] { **deny**| **permit** } { protocol | **udp** } **vxlan** **vni** vni 和匹配报文长度的命令 **rule** [ rule-id ] { **deny** |**permit** } protocol [ **packet-length** length-operation length-value ] ，会降低可配置的rule 规则的资源。ACL 匹配规则可以根据用户的不同需求定义不同的访问控制列表，包括协议类型、源地址、目的地址或则报文中的优先级字段等。 **if-match acl** 命令根据 **rule** 命令定义的 ACL 规则过滤报文，然后进行对应的流行为。       - 如果定义 DSCP 匹配规则，执行命令 **if-match** **dscp** { dscp-value | **af11** | **af12** |**af13** | **af21** | **af22** | **af23** | **af31** | **af32** | **af33** | **af41** | **af42** | **af43** | **cs1** | **cs2** |**cs3** | **cs4** | **cs5** | **cs6** | **cs7** | **ef** | **default** } 。       - 如果定义 IP 报文优先级的匹配规则，执行命令 **if-match** [ **ipv6** ] **ip-precedence**ip-precedence 。       - 如果定义匹配所有数据包的规则，执行命令 **if-match** **any** 。       - 如果定义基于 VXLAN 报文的 QoS 策略 ID 的匹配规则，执行命令 **if-match** [ **ipv6** ]**vxlan-qos-local-id** qos-local-id-value 。说明NetEngine 40E-X8AK 不支持定义基于 VXLAN 报文的 QoS 策略 ID 的匹配规则。**----**结束##### ******* 定义流行为并配置动作介绍设备支持的流行为及如何配置。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 101HUAWEI NetEngine40E配置指南 1 QoS##### 背景信息设备支持设置的流行为动作类型比较丰富，可以根据实际需要选择下面的一种或多种。##### 操作步骤       - 配置流量调度a. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **traffic behavior** behavior-name ，定义流行为并进入流行为视图。c. 执行命令 **car** { **cir** cir-value [ **pir** pir-value ] } [ **cbs** cbs-value [ **pbs** pbsvalue ] ] [ **adjust** adjust-value ] [ **green** { **discard** | **pass** [ **remark dscp**dscp | **service-class** class **color** color ] } | **yellow** { **discard** | **pass**[ **remark dscp** dscp | **service-class** class **color** color ] } | **red** { **discard** |**pass** [ **remark dscp** dscp | **service-class** class **color** color ] } ] [*][ **summary** ] [ **color-aware** ] [ **limit-type pps** ] ，配置流量监管动作。本命令为覆盖式命令，即在同一个策略的类上多次进行该配置后，按最后一次配置生效。说明报文被 remark 为 ef 、 be 、 cs6 和 cs7 服务等级后，报文颜色只能被 remark 为 green 。d. 执行命令 **commit** ，提交配置。       - 配置强制流分类a. 执行命令 **service-class** service-class **color** color ，配置指定服务等级的报文标记颜色。b. （可选）执行命令 **service-class** service-class **color** color **track** { **master** |**slave** } **bfd-session** **session-name** bfd-session-name ，配置依据指定的BFD 的会话状态来标记匹配流策略后的报文服务等级和颜色。c. 执行命令 **commit** ，提交配置。       - 设置报文的优先级a. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **traffic behavior** behavior-name ，定义流行为并进入流行为视图。c. 请根据实际情况进行如下配置。#### ▪ 如果重新设置 VXLAN 报文的 DSCP 值，执行命令 remark dscp dscpvalue 。#### ▪ 如果重新设置 VXLAN 报文的 QoS 策略 ID 值，执行命令 remark qos-local**id** qos-local-id-value 。说明NetEngine 40E-X8AK 不支持重新设置 VXLAN 报文的 QoS 策略 ID 值。d. 执行命令 **commit** ，提交配置。       - 配置级联流策略流行为中一般使用 ACL 规则进行重定向，但是 ACL 规则是有一定的规格限制的，复杂流分类所能定义的 ACL 规则无法满足实际场景的需求时，可以将流行为重定向到一个已经配置完成的流策略中，从而实现级联复杂流分类。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 102HUAWEI NetEngine40E配置指南 1 QoSa. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **traffic behavior** behavior-name ，定义流行为并进入流行为视图。c. 执行命令 **traffic-policy** policy-name ，配置级联下一个流策略。#### ▪ 配置级联流策略会造成设备转发性能下降。 ▪ 接口流量匹配级联的流策略时：           - 转发行为按照级联的流策略行为执行           - 非重复的流策略行为可以分别执行           - 重复的流策略行为按照级联的流策略行为执行#### ▪ 流策略接口上应用时所指定的 inbound 或 outbound 方向， link-layer 、mpls-layer 和 all-layer 等参数被级联的流策略继承。#### ▪ 两级 ACL 的流行为都是 service-class 时，以第一级 service-class 优先生效；但是如果第一级 service-class 带有 no-remark ，还是以第二级service-class 优先生效。d. （可选）执行命令 **hierarchical-car enable** ，在复杂流分类级联策略场景中使能层次化 CAR 动作。在流行为中嵌套流策略后，流量策略中可以配置 CAR 动作，实现对流量的层次化 CAR 限速。e. 执行命令 **commit** ，提交配置。**----**结束##### 1.5.8.3 （可选）使能匹配 VXLAN 报文的 QoS 策略 ID 模式定义流分类时如果定义了基于 VXLAN 报文的 QoS 策略 ID 的匹配规则，需要使能匹配VXLAN 报文的 QoS 策略 ID 的模式功能，匹配规则才能生效。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **traffic policy** policy-name ，进入已经定义的流策略视图。步骤 **3** 执行命令 **match-type vxlan-qos-local-id enable** ，使能匹配 VXLAN 报文的 QoS 策略ID 模式。说明NetEngine 40E-X8AK 不支持此配置。步骤 **4** 执行命令 **commit** ，提交配置。**----**结束##### 1.5.8.4 定义流量策略定义了流分类和动作后，需要配置流量策略，为定义的流关联动作。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 103HUAWEI NetEngine40E配置指南 1 QoS##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **traffic policy** policy-name ，定义流量策略并进入流策略视图。步骤 **3** 执行命令 **classifier** classifier-name **behavior** behavior-name [ **precedence**precedence-value ] ，在流量策略中为流分类指定采用的行为并设定策略匹配的优先级。步骤 **4** （可选）执行命令 **step** step-value ，配置策略间的步长。步骤 **5** （可选）执行命令 **statistics enable** ，使能流量策略的统计功能。为节省内存资源，系统缺省不使能流量策略的统计功能。当用户需要查看流量策略的统计数据时，可使能流量策略的统计功能。步骤 **6** 执行命令 **commit** ，提交配置。**----**结束##### 1.5.8.5 应用流量策略已配置的基于类的策略，需要应用到 BD/VPN 才能生效。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 请根据流量策略应用的实例类型选择下面的配置步骤。       - 在 BD 下应用复杂流分类策略，请执行如下步骤：a. 执行命令 **bridge-domain** bd-id ，进入 BD 视图。b. 执行命令 **traffic-policy** policy-name { **inbound** [ **link-layer** ] | **outbound** }**vxlan-mode** ，在 BD 下应用流量策略。c. （可选）执行命令 **traffic-policy-action car disable** [ **exclude** ] **slot** slot-id**vxlan-mode** ，去使能指定槽位号单板 VXLAN 模式的复杂流策略的 CAR 功能。说明为避免出现配置矛盾，相同视图下是否带 **exclude** 的配置不能同时存在。       - 在 VPN 下应用复杂流分类策略，请执行如下步骤：a. 执行命令 **ip** [ **dcn** ] **vpn-instance** vpn-instance-name ，进入 VPN 视图。b. 执行命令 **traffic-policy** policy-name { **inbound** [ **link-layer** ] | **outbound** }**vxlan-mode** ，在 VPN 下应用流量策略。c. （可选）执行命令 **traffic-policy-action car disable** [ **exclude** ] **slot** slot-id**vxlan-mode** ，去使能指定槽位号单板 VXLAN 模式的复杂流策略的 CAR 功能。说明为避免出现配置矛盾，相同视图下是否带 **exclude** 的配置不能同时存在。步骤 **3** 执行命令 **commit** ，提交配置。**----**结束文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 104HUAWEI NetEngine40E配置指南 1 QoS##### ******* 检查配置结果VXLAN 报文复杂流分类配置成功后，可以查看设备配置的类信息、流行为信息、指定策略中指定类及与类关联的行为的配置信息、策略的配置信息和运行情况、流量策略信息、队列配置信息和运行情况等内容。##### 操作步骤       - 使用 **display traffic behavior** { **system-defined** | **user-defined** } [ behaviorname ] 命令查看流行为的配置信息。       - 使用 **display traffic classifier** { **system-defined** | **user-defined** } [ classifiername ] 命令查看流分类的配置信息。       - 使用 **display traffic policy** { **system-defined** | **user-defined** } [ policy-name[ **classifier** classifier-name ] ] 命令查看流策略中所有流分类与流行为的关联信息或特定流分类与流行为的关联信息。       - 使用 **display traffic policy** **statistics** { **bridge-domain** bdid **vxlan-mode** |**vpn-instance** vpn-name **vxlan-mode** } { **inbound** | **outbound** } [ **verbose**{ **classifier-based** [ **class** class-name ] | **rule-based** [ **class** class-name ][ **filter** ] } ] 命令查看接口的流量策略统计信息。VS 模式下，该命令仅在 Admin VS 支持。**----**结束#### 1.5.9 （可选）设置协议报文的优先级配置主机报文的优先级，可以指定配置本机发送的管理协议报文或控制协议报文的DSCP （ Differentiated Services Code Point ）值，并根据这些协议报文的 DSCP 值让协议报文进入指定的内部优先级队列及获取对应的内部优先级颜色。##### 背景信息目前 NE40E 对协议报文进行内部调度时，默认指定为 CS6 队列，没有颜色，优先级是固定的。如果用户对 CS6 队列有特殊的用处，或者不用于业务转发，就会影响业务。同时，在下游设备中，指定的协议报文可能会因为进入低优先级的 QoS 队列而不满足调度需求，所以需要指定协议报文可以进入其他的队列进行灵活调度。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 请根据需要报文的类型，配置管理协议报文或控制协议报文的 DSCP 值。       - 执行命令 **host-packet type** { **management-protocol** | **control-protocol** } **dscp**dscp-value ，配置 IPv4 管理协议报文或控制协议报文的 DSCP 值。       - 执行命令 **host-packet ipv6 type** { **management-protocol** | **control-protocol** }**dscp** dscp-value ，配置 IPv6 管理协议报文或控制协议报文的 DSCP 值。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 105HUAWEI NetEngine40E配置指南 1 QoS说明一般情况下，各协议都有默认 DSCP 值，且部分协议的 DSCP 值可以通过 **host-packet type** 命令和各协议自己修改 DSCP 值的命令配置，这种情况下， DSCP 的配置生效规则如下：          - 当协议有自己的修改命令时，不管是否受 **host-packet type** 命令控制，都按协议自己的修改命令配置的 DSCP 值生效；          - 当协议没有自己的修改命令时，且受 **host-packet type** 命令控制，则按 **host-packet type** 命令配置的 DSCP 值生效；          - 当协议没有自己的修改命令时，且不受 **host-packet type** 命令控制，则按照默认 DSCP 值生效。如果需要了解各 PHB 对应的 DSCP 取值及含义请参考 DSCP 与 PHB 。IPv4 协议的 ToS/DSCP 值与 IPv6 协议的 Traffic Class/DSCP 值的修改方法如 表 **1-18** 和 表**1-19** 所示。表 **1-18** IPv4 协议的 ToS/DSCP 值及修改方法|协议|默认ToS/<br>DSCP值|是否受host-packet<br>type命令控制|各协议自己的修改命令||---|---|---|---||ICMP_ECHO|0|否|**ping** **-dscp** dscp-value||ICMP_ECHO<br>_REPLY|0|否|NA||ICMP差错|48|否|NA||DNS|0|否|NA||FTP|48|是，**host-packet type**<br>**management-protocol**|NA||TFTP|48|是，**host-packet type**<br>**management-protocol**|NA||SNMP|48|是，**host-packet type**<br>**management-protocol**|**snmp-agent packet-**<br>**priority** **snmp** priority-<br>level||SSH|48|是，**host-packet type**<br>**management-protocol**|**ssh server dscp** value||TELNET|48|是，**host-packet type**<br>**management-protocol**|**telnet server dscp**<br>value||SYSLOG<br>（UDP）|0|是，**host-packet type**<br>**management-protocol**|**info-center syslog**<br>**packet-priority**<br>priority-level<br>**info-center syslog**<br>**packet-priority**<br>priority-level命令优先级<br>高于**host-packet type**<br>**management-protocol**<br>命令。|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 106HUAWEI NetEngine40E配置指南 1 QoS|协议|默认ToS/<br>DSCP值|是否受host-packet<br>type命令控制|各协议自己的修改命令||---|---|---|---||SYSLOG<br>（TCP）|0|否|**info-center syslog**<br>**packet-priority**<br>priority-level||HWTACACS|48|是，**host-packet type**<br>**management-protocol**|NA||Radius|48|否|NA||NTP|48|是，**host-packet type**<br>**control-protocol**|NA||BFD|56|否|**tos-exp** tos-value<br>（BFD会话视图）<br>**tos-exp** tos-value<br>{ **dynamic** |**static** }<br>（BFD视图）||IGMP|48|否|NA||PIM|48|否|NA||CUSP|48|是，**host-packet type**<br>**control-protocol**|NA||BGP|48|是，**host-packet type**<br>**control-protocol**|NA||LDP|48|是，**host-packet type**<br>**control-protocol**|NA||OSPF|48|是，**host-packet type**<br>**control-protocol**|NA||DHCP<br>Server/<br>DHCP Relay|48|否|**dhcp dscp-outbound**<br>value||DHCP<br>Snooping|0|否|NA||GRE|内层IP ToS如<br>果有效则继承<br>内层IP报文<br>ToS/DSCP<br>值，否则设置<br>为48|否|NA||IKE|48|否|NA|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 107HUAWEI NetEngine40E配置指南 1 QoS|协议|默认ToS/<br>DSCP值|是否受host-packet<br>type命令控制|各协议自己的修改命令||---|---|---|---||VXLAN|内层IP ToS如<br>果有效则继承<br>内层IP报文<br>ToS/DSCP<br>值，否则设置<br>为48|否|NA||RSVP-TE|48|否|NA||MSDP|48|否|NA||PCEPv4|48|否|**dscp**dscp-val|表 **1-19** IPv6 协议的 Traffic Class/DSCP 值及修改方法|协议|默认Traffic<br>Class/DSCP值|是否受host-packet<br>type命令控制|各协议自己的修改命令||---|---|---|---||ICMP6_ECH<br>O|0|否|**ping ipv6** **-tc** traffic-<br>class-value||ICMP6_ECH<br>O_REPLY|复制<br>ICMP6_ECHO<br>报文TC/DSCP<br>值|否|NA||ICMP6差错|复制<br>ICMP6_ECHO<br>报文TC/DSCP<br>值|否|NA||ND<br>（NS/NA/RS<br>/RA）|48|否|NA||TNL6（IPv6<br>over IPv4）|0|否|NA||TNL6（IPv4<br>over IPv6）|0|否|**tunnel ipv4-ipv6**<br>**trafc-class** class-<br>value||DNSv6|0|否|NA||FTPv6|0|是，**host-packet ipv6**<br>**type management-**<br>**protocol**|NA||TFTPv6<br>SERVER|NA|否|NA|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 108HUAWEI NetEngine40E配置指南 1 QoS|协议|默认Traffic<br>Class/DSCP值|是否受host-packet<br>type命令控制|各协议自己的修改命令||---|---|---|---||TFTPv6<br>CLIENT|0|是，**host-packet ipv6**<br>**type management-**<br>**protocol**|NA||SNMPv6|48|否|**snmp-agent packet-**<br>**priority** **snmp** priority-<br>level||SSHv6|0|是，**host-packet ipv6**<br>**type management-**<br>**protocol**|NA||TELNETv6|0|是，**host-packet ipv6**<br>**type management-**<br>**protocol**|NA||SYSLOG<br>（UDP）|0|是，**host-packet ipv6**<br>**type management-**<br>**protocol**|**info-center syslog**<br>**packet-priority**<br>priority-level||SYSLOG<br>（TCP）|0|否|**info-center syslog**<br>**packet-priority**<br>priority-level||HWTACACS|48|否|NA||Radius|48|否|NA||NTPv6|0|是，**host-packet ipv6**<br>**type management-**<br>**protocol**|NA||BFDv6|56|否|**tos-exp** tos-value<br>（BFD会话视图）||BFDv6|56|否|**tos-exp** tos-value<br>{ **dynamic** |**static** }<br>（BFD视图）||MLD|48|否|NA||PIMv6|48|否|NA||BGP4+|48|是，**host-packet ipv6**<br>**type control-protocol**|NA||OSPFv3|48|是，**host-packet ipv6**<br>**type control-protocol**|NA||DHCPv6|48|否|NA|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 109HUAWEI NetEngine40E配置指南 1 QoS|协议|默认Traffic<br>Class/DSCP值|是否受host-packet<br>type命令控制|各协议自己的修改命令||---|---|---|---||GRE|内层IP TC如果<br>有效则继承内<br>层IP报文TC/<br>DSCP值，否<br>则设置为48|否|NA||VXLAN|内层IP TC如果<br>有效则继承内<br>层IP报文TC/<br>DSCP值，否<br>则设置为48|否|NA||PCEPv6|48|否|**dscp**dscp-val|步骤 **3** 执行命令 **host-packet dscp** dscp-value **map local-service** cos-value [ **color**color ] ，配置协议报文 DSCP 值和内部优先级及内部优先级颜色的对应关系。说明**host-packet type** 命令中的 DSCP 值只用来表示协议报文的优先级，二层协议的 802.1P 值是由该DSCP 值依据出接口配置的 DS 作用域（ DS-domain ）映射出来的， MPLS 报文的 EXP 值取的是该DSCP 值的高三位。如果不使用该命令，最终发送出去的协议报文优先级是设备预先固定设置的。步骤 **4** 执行命令 **commit** ，提交配置。**----**结束#### 1.5.10 维护基于类的 QoS介绍如何清空流量策略的统计信息。##### ******** 清空流量策略的统计信息介绍清空流量策略统计信息的命令。##### 背景信息须知清除统计信息后，以前的统计信息将无法恢复，务必仔细确认。##### 操作步骤       - 执行命令 **reset traffic** **policy** [ [ **name** ] policy-name ] **statistics** **interface**{ interface-name | interface-type interface-number } [ **vlan** vlan-id | **pe-vid**pe-vid **ce-vid** ce-vid | **vid** vid | **ce-vid** ce-vid | **vid** vid **ce-vid** ce-vid ] { **inbound**| **outbound** } ，清空指定接口下的流量策略统计数据。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 110HUAWEI NetEngine40E配置指南 1 QoS       - 执行命令 **reset traffic policy statistics** **bridge-domain** bdid { **inbound** |**outbound** } ，清空指定 BD 域下的流量策略统计数据。       - 执行命令 **reset traffic policy** [ **name** policy-name ] **statistics** { **vsi** vsiinstance-name [ **ac-mode** ] | **vpn-instance** vpn-instance-name } [ **slot** slotid ] { **inbound** | **outbound** } ，清空 VSI 实例绑定的 AC 接口的流量策略统计数据。       - 执行命令 **reset flow-car** [ **ipv6** ] [ { **source-ip** | **destination-ip** } ip-address ]**slot** slot-id ，清除指定单板的 flow-car 表。       - 执行命令 **reset flow-car** [ **ipv6** ] **all** ，清除整机的 flow-car 表。       - 执行命令 **reset flow-car** [ **ipv6** ] **statistics** { **source-ip** | **destination-ip** } [ ipaddress ] **slot** slot-id { **inbound** | **outbound** } ，根据源或目的 IP ，清除某槽位指定方向的 flow-car 统计计数。       - 执行命令 **reset user-queue statistics** **interface** { interface-name | interfacetype interface-number } { **pe-vid** pe-vid **ce-vid** ce-vid | **vlan** vlan-id }{ **inbound** | **outbound** } [ **policy** policy-name ] **classifier** classifier-name 或**reset user-queue statistics** **bridge-domain** bd-id { **inbound** | **outbound** }[ **policy** policy-name ] **classifier** classifier-name ，清空指定接口上用户队列的统计计数。**----**结束#### 1.5.11 配置举例介绍几种典型的流分类 QoS 策略的配置举例。##### ******** 配置 IP 报文复杂流分类示例以 IP 报文为例，介绍复杂流分类的配置。##### 组网需求如 图 **1-9** 所示，通过在 DeviceC 上配置复杂流分类，可实现对 DeviceA 与 DeviceB 之间的访问控制，并可以通过流量统计验证报文的收发情况。说明本例中 Interface1 ， Interface2 分别代表 GE1/0/0 ， GE2/0/0 。图 **1-9** 配置基于复杂流分类的流量策略示例图##### 配置思路采用如下的思路配置基于复杂流分类的流量策略：文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 111HUAWEI NetEngine40E配置指南 1 QoS1. 定义 ACL 规则。2. 定义流分类。3. 定义流行为。4. 定义流量策略。5. 应用流量策略。##### 数据准备为完成此配置举例，需准备如下的数据：       - ACL 编号。       - 流分类、流行为、流量策略的名字以及应用的接口号。##### 操作步骤步骤 **1** 定义 ACL 规则。<HUAWEI> **system-view**[ ~ HUAWEI] **sysname DeviceC**[*HUAWEI] **commit**[ ~ DeviceC] **acl number 3333**[*DeviceC-acl-advance-3333] **rule 5 permit ip source ******* 0 destination ******* 0**[*DeviceC-acl-advance-3333] **rule 10 permit ip source ******* 0 destination ******* 0**[*DeviceC-acl-advance-3333] **commit**[ ~ DeviceC-acl-advance-3333] **quit**步骤 **2** 定义流分类。[ ~ DeviceC] **traffic classifier c1**[*DeviceC-classifier-c1] **if-match acl 3333**[*DeviceC-classifier-c1] **commit**[ ~ DeviceC-classifier-c1] **quit**步骤 **3** 定义流行为。[ ~ DeviceC] **traffic behavior b1**[*DeviceC-behavior-b1] **permit**[*DeviceC-behavior-b1] **commit**[ ~ DeviceC-behavior-b1] **quit**步骤 **4** 定义流量策略。[ ~ DeviceC] **traffic policy p1**[*DeviceC-trafficpolicy-p1] **classifier c1 behavior b1**[*DeviceC-trafficpolicy-p1] **share-mode**[*DeviceC-trafficpolicy-p1] **statistics enable**[*DeviceC-trafficpolicy-p1] **commit**[ ~ DeviceC-trafficpolicy-p1] **quit**步骤 **5** 应用流量策略。[ ~ DeviceC] **interface gigabitethernet 1/0/0**[ ~ DeviceC-GigabitEthernet1/0/0] **traffic-policy p1 inbound**[*DeviceC-GigabitEthernet1/0/0] **traffic-policy p1 outbound**[*DeviceC-GigabitEthernet1/0/0] **commit**[ ~ DeviceC-GigabitEthernet1/0/0] **quit**[ ~ DeviceC] **interface gigabitethernet 2/0/0**[ ~ DeviceC-GigabitEthernet2/0/0] **traffic-policy p1 inbound**[*DeviceC-GigabitEthernet2/0/0] **traffic-policy p1 outbound**[*DeviceC-GigabitEthernet2/0/0] **commit**[ ~ DeviceC-GigabitEthernet2/0/0] **quit**步骤 **6** 检查配置结果。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 112HUAWEI NetEngine40E配置指南 1 QoS进行上述配置后，可以首先在 DeviceA 上执行 **ping ********* 命令，来对 DeviceB 进行 ping操作，再在 DeviceC 上通过 **display traffic policy statistics** 命令来查看 DeviceA 与DeviceB 之间的流量统计情况。[ ~ DeviceC] **display traffic policy statistics interface gigabitethernet 1/0/0 inbound**Info: The statistics is shared because the policy is shared.Interface: GigabitEthernet1/0/0Traffic policy inbound: p1Traffic policy applied at 2017-08-30 18:30:20Statistics enabled at 2017-08-30 18:30:20Statistics last cleared: NeverRule number: 1 IPv4, 0 IPv6Current status: OK!Item               Packets           Bytes------------------------------------------------------------------Matched                5            500+--Passed              4            400+--Dropped              1            100Missed                 0             0Last 30 seconds rateItem                 pps            bps------------------------------------------------------------------Matched                5            500+--Passed              4            400+--Dropped              1            100Missed                 0             0**----**结束##### 配置文件DeviceC 的配置文件#sysname DeviceC#acl number 3333rule 5 permit ip source ******* 0 destination ******* 0rule 10 permit ip source ******* 0 destination ******* 0#traffic classifier c1 operator orif-match acl 3333#traffic behavior b1#traffic policy p1share-modestatistics enableclassifier c1 behavior b1 precedence 1#interface GigabitEthernet1/0/0undo shutdowntraffic-policy p1 inboundtraffic-policy p1 outbound#interface GigabitEthernet2/0/0undo shutdowntraffic-policy p1 inboundtraffic-policy p1 outbound#return##### ******** 配置 MPLS 公网口基于 IP 的复杂流分类示例以 MPLS 公网口为例，介绍流分类、流行为的配置及其应用。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 113HUAWEI NetEngine40E配置指南 1 QoS##### 组网需求如 图 **1-10** 所示， PE1 、 P 、 PE2 为 MPLS 骨干网设备， CE1 、 CE2 、 CE3 、 CE4 为骨干网边缘接入设备。以 PE1 为例，通过在 PE1 的公网接口（即 interface3 ）上配置基于 IP 的复杂流分类策略，实现公网侧的流量控制，并可以通过流量统计验证报文的收发情况。说明本例中 Interface1 ， Interface2 ， Interface3 分别代表 GE1/0/0 、 GE2/0/0 、 GE1/0/3 。图 **1-10** 配置 MPLS 公网口基于 IP 的复杂流分类示例图##### 配置思路采用如下的思路配置基于复杂流分类的流量策略：1. 配置 MPLS 基本功能。2. 配置对出 / 入公网方向的报文，基于 IP 层信息做复杂流分类。3. 定义 ACL 规则。4. 定义流分类。5. 定义流行为。6. 定义流量策略。7. 应用流量策略。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 114HUAWEI NetEngine40E配置指南 1 QoS##### 数据准备为完成此配置举例，需准备如下的数据：       - ACL 编号。       - 流分类、流行为、流量策略的名字以及应用的接口号。##### 操作步骤步骤 **1** 配置 MPLS 基本功能（略）。MPLS 基本功能的配置，可以参见《 HUAWEI NetEngine40E 配置指南 -VPN-BGP/MPLS IP VPN 配置》中的配置 BGP/MPLS IP VPN 示例步骤 **2** 配置对出 / 入公网方向的报文，基于 IP 层信息做复杂流分类。<HUAWEI> **system-view**[ ~ HUAWEI] **sysname PE1**[*HUAWEI] **commit**[ ~ PE1] **slot 1**[ ~ PE1-slot-1] **traffic-policy match-ip-layer mpls-pop**[*PE1-slot-1] **traffic-policy match-ip-layer mpls-push**[*PE1-slot-1] **commit**[ ~ PE1-slot-1] **quit**步骤 **3** 定义 ACL 规则。[ ~ PE1] **acl number 3333**[*PE1-acl-advance-3333] **rule 5 permit ip source *********** 0 destination *********** 0**[*PE1-acl-advance-3333] **rule 10 permit ip source *********** 0 destination *********** 0**[*PE1-acl-advance-3333] **commit**[ ~ PE1-acl-advance-3333] **quit**步骤 **4** 定义流分类。[ ~ PE1] **traffic classifier c1**[*PE1-classifier-c1] **if-match acl 3333**[*PE1-classifier-c1] **commit**[ ~ PE1-classifier-c1] **quit**步骤 **5** 定义流行为。[ ~ PE1] **traffic behavior b1**[*PE1-behavior-b1] **permit**[*PE1-behavior-b1] **commit**[ ~ PE1-behavior-b1] **quit**步骤 **6** 定义流量策略。[ ~ PE1] **traffic policy p1**[*PE1-trafficpolicy-p1] **classifier c1 behavior b1**[*PE1-trafficpolicy-p1] **share-mode**[*PE1-trafficpolicy-p1] **statistic enable**[*PE1-trafficpolicy-p1] **commit**[ ~ PE1-trafficpolicy-p1] **quit**步骤 **7** 应用流量策略。[ ~ PE1] **interface gigabitethernet 1/0/3**[ ~ PE1-GigabitEthernet1/0/3] **traffic-policy p1 inbound**[*PE1-GigabitEthernet1/0/3] **traffic-policy p1 outbound**[*PE1-GigabitEthernet1/0/3] **commit**[ ~ PE1-GigabitEthernet1/0/3] **quit**步骤 **8** 检查配置结果。进行上述配置后，可以首先在 CE1 上执行 **ping ************* 命令，来对 CE3 进行 ping 操作，再在 PE1 上通过 **display traffic policy statistics** 命令来查看 CE3 与 CE1 之间的流量统计情况。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 115HUAWEI NetEngine40E配置指南 1 QoS[ ~ PE1] **display traffic policy statistics interface gigabitethernet 1/0/3 inbound**Info: The statistics is shared because the policy is shared.Interface: GigabitEthernet1/0/3Traffic policy inbound: p1Traffic policy applied at 2017-08-30 18:30:20Statistics enabled at 2017-08-30 18:30:20Statistics last cleared: NeverRule number: 1 IPv4, 0 IPv6Current status: OK!Item               Packets           Bytes------------------------------------------------------------------Matched                5            500+--Passed              4            400+--Dropped              1            100Missed                 0             0Last 30 seconds rateItem                 pps            bps------------------------------------------------------------------Matched                5            500+--Passed              4            400+--Dropped              1            100Missed                 0             0**----**结束##### 配置文件PE1 的配置文件#sysname PE1#slot 1traffic-policy match-ip-layer mpls-pop mpls-push#acl number 3333rule 5 permit ip source *********** 0 destination *********** 0rule 10 permit ip source *********** 0 destination *********** 0#traffic classifier c1 operator orif-match acl 3333#traffic behavior b1#traffic policy p1share-modestatistic enableclassifier c1 behavior b1 precedence 1#interface GigabitEthernet1/0/3undo shutdowntraffic-policy p1 inboundtraffic-policy p1 outbound#return##### ******** 配置 L3VPNv4 over SRv6 TE Policy 场景下应用级联策略匹配 SRv6 报文 内层信息的复杂流分类示例以 L3VPNv4 over SRv6 TE Policy 场景为例，介绍流分类、流行为的配置及其应用。##### 组网需求如图 1 所示：文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 116HUAWEI NetEngine40E配置指南 1 QoS       - 路由器 PE1 、 P 和 PE2 属于同一自治系统，要求它们之间通过 IS-IS 协议达到 IPv6 网络互连的目的。       - PE1 、 P 和 PE2 属于 IS-IS 进程 1 ，都是 Level-1 设备。要求在 PE1 和 PE2 之间建立双向 SRv6 TE Policy ，承载 L3VPNv4 业务。P 设备的 interface1 和 interface2 上分别应用级联流策略，对 CE1 与 CE2 之间的通信报文匹配 SRv6 报文内层信息进行复杂流分类。图 **1-11** 配置 L3VPNv4 over SRv6 TE Policy 组网图说明本例中 interface1 、 interface2 分别代表 GE1/0/0 、 GE2/0/0 。##### 配置思路1. 配置 L3VPNv4 over SRv6 TE Policy 。2. 配置匹配 SRv6 内层报文信息进行复杂流分类。a. 定义 ACL 规则。b. 定义流分类。c. 定义流行为。d. 定义流量策略。e. 应用流量策略##### 数据准备为完成此配置举例，需准备如下的数据：       - ACL 编号。       - 流分类、流行为、流量策略的名字以及应用的接口号。##### 操作步骤步骤 **1** 配置 L3VPNv4 over SRv6 TE Policy （略）。                                                         L3VPNv4 over SRv6 TE Policy 的配置可以参见《 HUAWEI NetEngine40E 配置指南Segment Routing IPv6 配置》中的配置 L3VPNv4 over SRv6 TE Policy 示例。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 117HUAWEI NetEngine40E配置指南 1 QoS步骤 **2** 配置匹配 SRv6 内层报文信息进行复杂流分类。1. 定义子流量策略匹配的 ACL 规则。[ ~ P] **acl number 3000**[*P-acl-advance-3000] **rule 5 permit ip dscp cs6**[*P-acl-advance-3000] **commit**[ ~ P-acl-advance-3000] **quit**2. 定义父流量策略和子流量策略绑定的流分类。# 定义父流量策略的流分类。[ ~ P] **traffic classifier outer**[*P-classifier-outer] **if-match ipv6 any**[*P-classifier-outer] **commit**[ ~ P-classifier-outer] **quit**# 定义子流量策略的流分类。[ ~ P] **traffic classifier inner**[*P-classifier-inner] **if-match acl 3000**[*P-classifier-inner] **commit**[ ~ P-classifier-inner] **quit**3. 定义子流量策略的流行为 **inner** ，创建子策略 **inner** 并绑定流分类和流行为。[ ~ P] **traffic behavior inner**[*P-behavior-inner] **permit**[*P-behavior-inner] **commit**[ ~ P-behavior-inner] **quit**[ ~ P] **traffic policy inner**[*P-trafficpolicy-inner] **classifier inner behavior inner**[*P-trafficpolicy-inner] **statistics enable**[*P-trafficpolicy-inner] **commit**[ ~ P-trafficpolicy-inner] **quit**4. 定义父策略的流行为 **outer** ，创建父流量策略 **outer** 并绑定流分类和流行为。# 流行为 **outer** 下配置匹配 SRv6 报文内层信息的级联流策略 **inner** 。[ ~ P] **traffic behavior outer**[*P-behavior-outer] **traffic-policy inner ip-layer srv6-inner**[*P-behavior-outer] **commit**[ ~ P-behavior-outer] **quit**[ ~ P] **traffic policy outer**[*P-trafficpolicy- **outer** ] **classifier outer behavior outer**[*P-trafficpolicy- **outer** ] **undo share-mode**[*P-trafficpolicy- **outer** ] **statistics enable**[*P-trafficpolicy- **outer** ] **commit**[ ~ P-trafficpolicy- **outer** ] **quit**5. 应用流量策略。在 P 连接 PE 设备的接口 GE1/0/0 和 GE2/0/0 的出 \ 入方向上应用父流量策略 **outer** 。[ ~ P] **interface gigabitethernet 1/0/0**[*P-GigabitEthernet1/0/0] **traffic-policy outer outbound**[*P-GigabitEthernet1/0/0] **traffic-policy outer inbound**[*P-GigabitEthernet1/0/0] **commit**[ ~ P-GigabitEthernet1/0/0] **quit**[ ~ P] **interface gigabitethernet 2/0/0**[*P-GigabitEthernet2/0/0] **traffic-policy outer outbound**[*P-GigabitEthernet2/0/0] **traffic-policy outer inbound**[*P-GigabitEthernet2/0/0] **commit**[ ~ P-GigabitEthernet2/0/0] **quit**步骤 **3** 检查配置结果完成上述配置后， CE1 和 CE2 间进行业务通信。可以在 P 设备上执行命令 **display traffic****policy statistics** 命令查看 CE1 与 CE2 之间的业务流量统计情况。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 118HUAWEI NetEngine40E配置指南 1 QoS       - 执行命令 **display traffic policy name inner statistics interface****gigabitethernet 1/0/0 inbound** ，查看 P 设备上匹配 SRv6 内层信息的接收报文统计信息。       - 执行命令 **display traffic policy name inner statistics interface****gigabitethernet 2/0/0 outbound** ，查看 P 设备上匹配 SRv6 内层信息的发送报文统计信息。**----**结束##### 配置文件P 的配置文件#sysname P##acl number 3000rule 5 permit ip dscp cs6#traffic classifier innerif-match acl 3000 precedence 1#traffic classifier outerif-match ipv6 any#traffic behavior innerpermit#traffic behavior outertraffic-policy inner ip-layer srv6-inner#traffic policy innerstatistics enableclassifier inner behavior inner precedence 1#traffic policy outerundo share-modestatistics enableclassifier outer behavior outer precedence 1#interface GigabitEthernet1/0/0undo shutdowntraffic-policy outer inboundtraffic-policy outer outbound#interface GigabitEthernet2/0/0undo shutdowntraffic-policy outer inboundtraffic-policy outer outbound#return##### ******** 配置双出口重定向示例以设备双出口重定向场景为例，介绍流分类、流行为的配置及其应用。##### 组网需求如 图 **1-12** 所示， DeviceA 通过 interface1 连接内网，通过 interface2 和 interface3 连接公网，默认情况下，来自内网的流量都通过 interface3 进入公网。当希望来自 Server 的流文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 119HUAWEI NetEngine40E配置指南 1 QoS量从 interface2 流入公网，而其他流量从 interface3 流入公网时，可通过在 DeviceA 上配置相关流量策略来实现。说明本例中 interface1 ， interface2 ， interface3 分别代表 GE1/0/0 ， GE2/0/0 和 GE3/0/0 。图 **1-12** 配置双出口重定向组网图##### 配置思路采用如下思路配置双出口重定向：1. 配置缺省路由。2. 定义 ACL 规则。3. 定义流分类。4. 定义流行为。5. 定义流量策略。6. 应用流量策略。##### 数据准备为完成此配置举例，需准备如下的数据：       - ACL 编号。       - 流分类、流行为、流量策略的名字以及应用的接口号。##### 操作步骤步骤 **1** 配置缺省路由，使内网流量默认从 interface3 进入公网， interface2 作为备份出口。<HUAWEI> **system-view**[ ~ HUAWEI] **ip route-static 0.0.0.0 0.0.0.0 ***********[*HUAWEI] **ip route-static 0.0.0.0 0.0.0.0 ********* preference 70**[*HUAWEI] **commit**步骤 **2** 定义 ACL 规则。# 定义规则 3001 ，匹配 Server 到内网中其他设备的流量[ ~ HUAWEI] **acl number 3001**文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 120HUAWEI NetEngine40E配置指南 1 QoS[*HUAWEI-acl-advance-3001] **rule 5 permit ip source ********* ********* destination ********* ***********[*HUAWEI-acl-advance-3001] **rule 10 permit ip source ********* ********* destination ********* ***********[*HUAWEI-acl-advance-3001] **rule 15 permit ip source ********* ********* destination ********* ***********[*HUAWEI-acl-advance-3001] **commit**[ ~ HUAWEI-acl-advance-3001] **quit**# 定义规则 3002 ，匹配源地址为 Server 的流量[ ~ HUAWEI] **acl number 3002**[*HUAWEI-acl-advance-3002] **rule 5 permit ip source ********* ***********[*HUAWEI-acl-advance-3002] **commit**[ ~ HUAWEI-acl-advance-3002] **quit**步骤 **3** 定义流分类。# 定义流分类 c1[ ~ HUAWEI] **traffic classifier c1**[*HUAWEI-classifier-c1] **if-match acl 3001**[*HUAWEI-classifier-c1] **commit**[ ~ HUAWEI-classifier-c1] **quit**# 定义流分类 c2[ ~ HUAWEI] **traffic classifier c2**[*HUAWEI-classifier-c2] **if-match acl 3002**[*HUAWEI-classifier-c2] **commit**[ ~ HUAWEI-classifier-c2] **quit**步骤 **4** 定义流行为。# 定义流行为 b1[ ~ HUAWEI] **traffic behavior b1**[*HUAWEI-behavior-b1] **permit**[*HUAWEI-behavior-b1] **commit**[ ~ HUAWEI-behavior-b1] **quit**# 定义流行为 b2[ ~ HUAWEI] **traffic behavior b2**[*HUAWEI-behavior-b2] **redirect ip-nexthop ***********[*HUAWEI-behavior-b2] **commit**[ ~ HUAWEI-behavior-b2] **quit**步骤 **5** 定义流量策略。[ ~ HUAWEI] **traffic policy p1**[*HUAWEI-trafficpolicy-p1] **classifier c1 behavior b1**[*HUAWEI-trafficpolicy-p1] **classifier c2 behavior b2**[*HUAWEI-trafficpolicy-p1] **commit**[ ~ HUAWEI-trafficpolicy-p1] **quit**步骤 **6** 应用流量策略。[ ~ HUAWEI] **interface gigabitethernet 1/0/0**[ ~ HUAWEI-GigabitEthernet1/0/0] **traffic-policy p1 inbound**[*HUAWEI-GigabitEthernet1/0/0] **commit**[ ~ HUAWEI-GigabitEthernet1/0/0] **quit**步骤 **7** 检查配置结果完成上述配置后，在 DeviceA 上执行 **display traffic policy** 命令可以查看流量策略、策略中定义的流分类以及与流分类相关的流行为的配置情况。[ ~ HUAWEI] **display traffic policy user-defined p1**User Defined Traffic Policy Information:Policy: p1Total: 5120 Used: 3 Free: 5117Description:文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 121HUAWEI NetEngine40E配置指南 1 QoSStep: 1Share-modeClassifier: c1 Precedence: 1Behavior: b1-noneClassifier: c2 Precedence: 2Behavior: b2Redirecting:redirect ip-nexthop *********Classifier: default-class Precedence: 65535Behavior: be-none**----**结束##### 配置文件HUAWEI 的配置文件#sysname HUAWEI#ip route-static 0.0.0.0 0.0.0.0 *********ip route-static 0.0.0.0 0.0.0.0 ********* preference 70#acl number 3001rule 5 permit ip source ********* ********* destination ********* *********rule 10 permit ip source ********* ********* destination ********* *********rule 15 permit ip source ********* ********* destination ********* *********acl number 3002rule 5 permit ip source ********* *********#traffic classifier c1 operator orif-match acl 3001traffic classifier c2 operator orif-match acl 3002#traffic behavior b1traffic behavior b2redirect ip-nexthop *********#traffic policy p1classifier c1 behavior b1 precedence 1classifier c2 behavior b2 precedence 1#interface gigabitethernet1/0/0undo shutdowntraffic-policy p1 inbound#return##### ******** 配置 QinQ 终结子接口复杂流分类示例以 QinQ 终结子接口复杂流分类为例，介绍复杂流分类的配置。##### 组网需求如 图 **1-13** 所示， SwitchA 、 SwitchB 通过 DeviceA 、 DeviceB 接入运营商网络。在DeviceA 的 QinQ 终结子接口 GE2/0/0.1 上配置复杂流分类，限制 SwitchA 的用户的接入速率为 10Mbit/s ，承诺突发流量尺寸为 150000 字节。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 122HUAWEI NetEngine40E配置指南 1 QoS说明                                                           QinQ 接口的详细介绍和配置请参见《 HUAWEI NetEngine40E 路由器 配置指南 局域网接入与城域网接入》中的“ QinQ 配置”部分。本例中 interface1 ， interface2 分别代表 GE1/0/0 ， GE2/0/0.1 。图 **1-13** QinQ 终结子接口配置复杂流分类举例组网图##### 配置思路配置 QinQ 终结子接口配置复杂流分类的思路如下：1. 配置 DeviceA 和 DeviceB 的 GE2/0/0.1 为 QinQ 终结子接口。2. 在 QinQ 终结子接口上配置基于复杂流分类的流量监管。##### 数据准备完成该举例，需要准备如下数据：       - 各接口的 IP 地址。       - QinQ 终结子接口终结的 VLAN 范围。       - SwitchA 的用户的接入速率 10Mbit/s 、承诺突发流量尺寸 150000 字节。       - 流分类、流行为、流量策略的名字以及应用的接口号。##### 操作步骤步骤 **1** 配置骨干网的 IGP ，本示例中使用 OSPF ，配置请参见《 HUAWEI NetEngine40E 路由器 配置指南 -IP 路由》中的“ OSPF 配置”部分。# 配置 DeviceA 。<HUAWEI> **system-view**[ ~ HUAWEI] **sysname DeviceA**[*HUAWEI] **commit**[ ~ DeviceA] **interface gigabitethernet 1/0/0**[ ~ DeviceA-GigabitEthernet1/0/0] **undo shutdown**[*DeviceA-GigabitEthernet1/0/0] **ip address ********* 24**[*DeviceA-GigabitEthernet1/0/0] **commit**文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 123HUAWEI NetEngine40E配置指南 1 QoS[ ~ DeviceA-GigabitEthernet1/0/0] **quit**[ ~ DeviceA] **ospf**[*DeviceA-ospf-1] **area 0**[*DeviceA-ospf-1-area-0.0.0.0] **network ********* ***********[*DeviceA-ospf-1-area-0.0.0.0] **network ******** ***********[*DeviceA-ospf-1-area-0.0.0.0] **commit**[ ~ DeviceA-ospf-1-area-0.0.0.0] **quit**[ ~ DeviceA-ospf-1] **quit**# 配置 DeviceB 。<HUAWEI> **system-view**[ ~ HUAWEI] **sysname DeviceB**[*HUAWEI] **commit**[ ~ DeviceB] **interface gigabitethernet 1/0/0**[ ~ DeviceB-GigabitEthernet1/0/0] **undo shutdown**[*DeviceB-GigabitEthernet1/0/0] **ip address ********* 24**[*DeviceB-GigabitEthernet1/0/0] **commit**[ ~ DeviceB-GigabitEthernet1/0/0] **quit**[ ~ DeviceB] **ospf**[*DeviceB-ospf-1] **area 0**[*DeviceB-ospf-1-area-0.0.0.0] **network ********* ***********[*DeviceB-ospf-1-area-0.0.0.0] **network ******** ***********[*DeviceB-ospf-1-area-0.0.0.0] **commit**[ ~ DeviceB-ospf-1-area-0.0.0.0] **quit**[ ~ DeviceB-ospf-1] **quit**步骤 **2** 配置 QinQ 终结子接口# 配置 DeviceA 。[ ~ DeviceA] **interface gigabitethernet 2/0/0.1**[*DeviceA-GigabitEthernet2/0/0.1] **control-vid 1 qinq-termination**[*DeviceA-GigabitEthernet2/0/0.1] **qinq termination pe-vid 100 ce-vid 10 to 20**[*DeviceA-GigabitEthernet2/0/0.1] **ip address ******** 24**[*DeviceA-GigabitEthernet2/0/0.1] **commit**[ ~ DeviceA-GigabitEthernet2/0/0.1] **quit**# 配置 DeviceB 。[ ~ DeviceB] **interface gigabitethernet 2/0/0.1**[*DeviceB-GigabitEthernet2/0/0.1] **control-vid 1 qinq-termination**[*DeviceB-GigabitEthernet2/0/0.1] **qinq termination pe-vid 100 ce-vid 10 to 20**[*DeviceB-GigabitEthernet2/0/0.1] **ip address ******** 24**[*DeviceB-GigabitEthernet2/0/0.1] **commit**[ ~ DeviceB-GigabitEthernet2/0/0.1] **quit**步骤 **3** 在 DeviceA 上配置 QinQ 终结子接口的复杂流分类功能# 配置流分类，定义匹配规则。[ ~ DeviceA] **traffic classifier c1**[*DeviceA-classifier-c1] **if-match any**[*DeviceA-classifier-c1] **commit**[ ~ DeviceA-classifier-c1] **quit**# 定义流行为。[ ~ DeviceA] **traffic behavior b1**[*DeviceA-behavior-b1] **car cir 10000 cbs 150000 pbs 0**[*DeviceA-behavior-b1] **commit**[ ~ DeviceA-behavior-b1] **quit**# 定义流量策略，将流分类与流行为关联。[ ~ DeviceA] **traffic policy p1**[*DeviceA-trafficpolicy-p1] **classifier c1 behavior b1**[*DeviceA-trafficpolicy-p1] **commit**[ ~ DeviceA-trafficpolicy-p1] **quit**文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 124HUAWEI NetEngine40E配置指南 1 QoS# 上述配置完成后，使用 **display** **traffic** **policy** 命令可以查看流量策略、策略中定义的流分类以及与流分类相关的流行为的配置情况。[ ~ DeviceA] **display traffic policy user-defined**User Defined Traffic Policy Information:Policy: p1Classifier: default-classBehavior: be-noneClassifier: c1Behavior: b1Committed Access Rate:CIR 10000 (Kbps), PIR 0 (Kbps), CBS 150000 (byte), PBS 0 (byte)Conform Action: passYellow Action: passExceed Action: discard# 将流量策略应用到接口上。[ ~ DeviceA] **interface gigabitethernet 2/0/0.1**[ ~ DeviceA-GigabitEthernet2/0/0.1] **traffic-policy p1 inbound**[*DeviceA-GigabitEthernet2/0/0.1] **commit**[ ~ DeviceA-GigabitEthernet2/0/0.1] **quit**步骤 **4** 检查配置结果接口绑定流量策略后， DeviceA 的 GE2/0/0.1 接口只接入 10M 的流量，超过的流量将被丢弃。**----**结束##### 配置文件       - DeviceA 的配置文件#sysname DeviceA#traffic classifier c1 operator orif-match any#traffic behavior b1car cir 10000 cbs 150000 green pass red discard#traffic policy p1classifier c1 behavior b1 precedence 1#interface GigabitEthernet1/0/0undo shutdownip address ********* *************#interface GigabitEthernet2/0/0.1encapsulation qinq-terminationqinq termination pe-vid 100 ce-vid 10 to 20ip address ******** *************traffic-policy p1 inbound#ospf 1area 0.0.0.0network ******** *********network ********* *********#return       - DeviceB 的配置文件#sysname DeviceB#文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 125HUAWEI NetEngine40E配置指南 1 QoSinterface GigabitEthernet1/0/0undo shutdownip address ********* *************#interface GigabitEthernet2/0/0.1encapsulation qinq-terminationqinq termination pe-vid 100 ce-vid 10 to 20ip address ******** *************#ospf 1area 0.0.0.0network ******** *********network ********* *********#return##### ******** 配置 MPLS 组网下基于复杂流分类的流量策略示例以 MPLS 组网为例，介绍流分类、流行为的配置及其应用。##### 组网需求如 图 **1-14** 所示， PE1 、 P 、 PE2 为 MPLS 骨干网设备， CE1 和 CE2 为骨干网边缘接入设备。三个本地网用户通过 CE1 访问 Internet 。       - 在 CE1 上限制网段 ******* 的用户的接入速率为 10Mbit/s ，承诺突发流量尺寸为150000 字节。       - 在 CE1 上限制网段 ******* 的用户的接入速率为 5Mbit/s ，承诺突发流量尺寸为100000 字节。       - 在 CE1 上限制网段 ******* 的用户的接入速率为 2Mbit/s ，承诺突发流量尺寸为100000 字节。       - 在 CE1 上分别标记三个网段用户业务流量的 DSCP 值为 40 、 26 和 0 。       - 在 PE1 上限制接入 MPLS 骨干网的接入速率为 15Mbit/s ，承诺突发流量尺寸为300000 字节，峰值接入速率为 20Mbit/s ，峰值突发尺寸为 500000 字节       - 在 CE1 上限制除 dns 、 snmp 、 snmptrap 和 syslog 类型的其它 UDP 协议报文的接入速率为 5Mbit/s ，承诺突发尺寸为 100000 字节，峰值接入速率为 15Mbit/s ，峰值突发尺寸为 200000 字节。说明本例中 interface1 ， interface2 ， interface3 ， interface4 分别代表 GE1/0/0 ， GE2/0/0 ，GE3/0/0 ， GE4/0/0 。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 126HUAWEI NetEngine40E配置指南 1 QoS图 **1-14** 配置基于复杂流分类的流量策略示例图##### 配置注意事项在配置过程中，需要注意一下事项：       - 当用户同时配置 **if-match any** 和 **deny** 时，复杂流分类将会禁止流经某个接口的所有流量通过，包括协议报文，因此请用户慎重进行上述流分类和流行为的组合配置。       - 当 **rule** 命令和流行为视图下同时配置了 **permit** 或 **deny** 动作时，只有 **rule** 命令允许通过的报文才会进行流行为的处理。只要 **rule** 命令或流行为视图中的任意一个配了 **deny** 动作，匹配规则的报文都会被丢弃。##### 配置思路采用如下的思路配置基于复杂流分类的流量策略：1. 配置 ACL 规则。2. 配置流分类。3. 配置流行为。4. 配置流量策略。5. 在接口上应用流量策略。##### 数据准备为完成此配置举例，需准备如下的数据：文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 127HUAWEI NetEngine40E配置指南 1 QoS       - ACL 编号 2001 、 2002 、 2003 、 3001 、 3002 。       - 三个网段用户的报文的 DSCP 值被分别重标记为 40 、 26 、 0 。       - 三个网段用户的流量保证带宽分别为 10Mbit/s 、 5Mbit/s 、 2Mbit/s ，允许最大突发流量分别为 150000 字节、 100000 字节、 100000 字节。       - CE1 上其它 UDP 协议报文的接入速率 5Mbit/s ，承诺突发尺寸 100000 字节，峰值接入速率为 15Mbit/s ，峰值突发尺寸为 200000 字节。       - PE1 的接入速率 15 Mbit/s ，承诺突发流量尺寸 300000 字节，峰值接入速率 20Mbit/s, 峰值突发尺寸为 500000 字节。       - 流分类、流行为、流量策略的名字以及应用的接口号。##### 操作步骤步骤 **1** 配置各接口 IP 地址、路由和 MPLS 基本功能（略）步骤 **2** 在 CE1 上配置复杂流分类，对三个本地网用户接入 CE1 的流量进行限制# 定义 ACL 规则。[ ~ CE1] **acl number 2001**[*CE1-acl4-basic-2001] **rule permit source ******* ***********[*CE1-acl4-basic-2001] **commit**[ ~ CE1-acl4-basic-2001] **quit**[ ~ CE1] **acl number 2002**[*CE1-acl4-basic-2002] **rule permit source ******* ***********[*CE1-acl4-basic-2002] **commit**[ ~ CE1-acl4-basic-2002] **quit**[ ~ CE1] **acl number 2003**[*CE1-acl4-basic-2003] **rule permit source ******* ***********[*CE1-acl4-basic-2003] **commit**[ ~ CE1-acl4-basic-2003] **quit**[ ~ CE1] **acl number 3001**[*CE1-acl4-advance-3001] **rule 0 permit udp destination-port eq dns**[*CE1-acl4-advance-3001] **rule 1 permit udp destination-port eq snmp**[*CE1-acl4-advance-3001] **rule 2 permit udp destination-port eq snmptrap**[*CE1-acl4-advance-3001] **rule 3 permit udp destination-port eq syslog**[*CE1-acl4-advance-3001] **commit**[ ~ CE1-acl4-advance-3001] **quit**[ ~ CE1] **acl number 3002**[*CE1-acl4-advance-3002] **rule 4 permit udp**[*CE1-acl4-advance-3002] **commit**[ ~ CE1-acl4-advance-3002] **quit**# 配置流分类，定义基于 ACL 的匹配规则。[ ~ CE1] **traffic classifier a**[*CE1-classifier-a] **if-match acl 2001**[*CE1-classifier-a] **commit**[ ~ CE1-classifier-a] **quit**[ ~ CE1] **traffic classifier b**[*CE1-classifier-b] **if-match acl 2002**[*CE1-classifier-b] **commit**[ ~ CE1-classifier-b] **quit**[ ~ CE1] **traffic classifier c**[*CE1-classifier-c] **if-match acl 2003**[*CE1-classifier-c] **commit**[ ~ CE1-classifier-c] **quit**[ ~ CE1] **traffic classifier udplimit**[*CE1-classifier-udplimit] **if-match acl 3001**[*CE1-classifier-udplimit] **commit**[ ~ CE1-classifier-udplimit] **quit**[ ~ CE1] **traffic classifier udplimit1**[*CE1-classifier-udplimit1] **if-match acl 3002**[*CE1-classifier-udplimit1] **commit**文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 128HUAWEI NetEngine40E配置指南 1 QoS[ ~ CE1-classifier-udplimit1] **quit**上述配置完成后，可以通过 **display traffic classifier** 命令查看流分类的配置信息。[ ~ CE1] **display traffic classifier user-defined**User Defined Classifier Information:Total: 65535 Used: 6 Free: 65529Classifier: aDescription:Operator: orRule(s):if-match acl 2001 precedence 1Classifier: bDescription:Operator: orRule(s):if-match acl 2002 precedence 2Classifier: cDescription:Operator: orRule(s):if-match acl 2003 precedence 3Classifier: udplimitDescription:Operator: orRule(s) :if-match acl 3001 precedence 4Classifier: udplimit1Description:Operator: orRule(s) :if-match acl 3002# 定义流行为，配置流量监管并重新设置 DSCP 。[ ~ CE1] **traffic behavior e**[*CE1-behavior-e] **car cir 10000 cbs 150000 pbs 0**[*CE1-behavior-e] **remark dscp 40**[*CE1-behavior-e] **commit**[ ~ CE1-behavior-e] **quit**[ ~ CE1] **traffic behavior f**[*CE1-behavior-f] **car cir 5000 cbs 100000 pbs 0**[*CE1-behavior-f] **remark dscp 26**[*CE1-behavior-f] **commit**[ ~ CE1-behavior-f] **quit**[ ~ CE1] **traffic behavior g**[*CE1-behavior-g] **car cir 2000 cbs 100000 pbs 0**[*CE1-behavior-g] **remark dscp 0**[*CE1-behavior-g] **commit**[ ~ CE1-behavior-g] **quit**[ ~ CE1] **traffic behavior udplimit**[*CE1-behavior-udplimit] **permit**[*CE1-behavior-udplimit] **commit**[ ~ CE1-behavior-udplimit] **quit**[ ~ CE1] **traffic behavior udplimit1**[*CE1-behavior-udplimit1] **car cir 5000 pir 15000 cbs 100000 pbs 200000 green pass yellow discard red****discard**[*CE1-behavior-udplimit1] **commit**[ ~ CE1-behavior-udplimit1] **quit**# 定义流量策略，将流分类与流行为关联。[ ~ CE1] **traffic policy 1**[*CE1-trafficpolicy-1] **classifier a behavior e**[*CE1-trafficpolicy-1] **commit**[ ~ CE1-trafficpolicy-1] **quit**[ ~ CE1] **traffic policy 2**[*CE1-trafficpolicy-2] **classifier b behavior f**[*CE1-trafficpolicy-2] **commit**[ ~ CE1-trafficpolicy-2] **quit**文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 129HUAWEI NetEngine40E配置指南 1 QoS[ ~ CE1] **traffic policy 3**[*CE1-trafficpolicy-3] **classifier c behavior g**[*CE1-trafficpolicy-3] **commit**[ ~ CE1-trafficpolicy-3] **quit**[ ~ CE1] **traffic policy udplimit**[*CE1-trafficpolicy-udplimit] **classifier udplimit behavior udplimit**[*CE1-trafficpolicy-udplimit] **classifier udplimit1 behavior udplimit1**[*CE1-trafficpolicy-udplimit] **commit**[ ~ CE1-trafficpolicy-udplimit] **quit**上述配置完成后，使用 **display** **traffic** **policy** 命令可以查看流量策略、策略中定义的流分类以及与流分类相关的流行为的配置情况。[ ~ CE1] **display traffic policy user-defined**User Defined Traffic Policy Information:Total: 10239 Used: 4 Free: 10235Policy: 1Total: 5120 Used: 2 Free: 5118Description:Step: 1Share-modeClassifier: a Precedence: 1Behavior: eCommitted Access Rate:CIR 10000 (Kbps), PIR 0 (Kbps), CBS 150000 (byte), PBS 0 (byte), ADJUST 0Conform Action: pass Yellow Action: pass Exceed Action: discard Color-aware: noMarking:remark dscp cs5Classifier: default-class Precedence: 65535Behavior:  be-nonePolicy: 2Total: 5120 Used: 2 Free: 5118Description:Step: 1Share-modeClassifier: b Precedence: 1Behavior: fCommitted Access Rate:CIR 5000 (Kbps), PIR 0 (Kbps), CBS 100000 (byte), PBS 0 (byte), ADJUST 0Conform Action: pass Yellow Action: pass Exceed Action: discard Color-aware: noMarking:remark dscp af31Classifier: default-class Precedence: 65535Behavior:  be-nonePolicy: 3Total: 5120 Used: 2 Free: 5118Description:Step: 1Share-modeClassifier: c Precedence: 1Behavior: gCommitted Access Rate:CIR 2000 (Kbps), PIR 0 (Kbps), CBS 100000 (byte), PBS 0 (byte), ADJUST 0Conform Action: pass Yellow Action: pass Exceed Action: discard Color-aware: noMarking:remark dscp defaultClassifier: default-class Precedence: 65535Behavior:  be-nonePolicy: udplimitTotal: 5120 Used: 2 Free: 5118Description:Step: 1Share-modeClassifier: udplimit Precedence: 1Behavior: udplimit-noneClassifier: udplimit1 Precedence: 10文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 130HUAWEI NetEngine40E配置指南 1 QoSBehavior: udplimit1Committed Access Rate:CIR 5000 (Kbps), PIR 15000 (Kbps), CBS 100000 (byte), PBS 200000 (byte), ADJUST 0Conform Action: pass Yellow Action: pass Exceed Action: discard Color-aware: noClassifier: default-class Precedence: 65535Behavior:  be-none# 将流量策略应用到入接口上。[ ~ CE1] **interface gigabitethernet 1/0/0**[ ~ CE1-GigabitEthernet1/0/0] **undo shutdown**[ ~ CE1-GigabitEthernet1/0/0] **traffic-policy 1 inbound**[*CE1-GigabitEthernet1/0/0] **commit**[ ~ CE1-GigabitEthernet1/0/0] **quit**[ ~ CE1] **interface gigabitethernet 3/0/0**[ ~ CE1-GigabitEthernet3/0/0] **undo shutdown**[*CE1-GigabitEthernet3/0/0] **traffic-policy 2 inbound**[*CE1-GigabitEthernet3/0/0] **commit**[ ~ CE1-GigabitEthernet3/0/0] **quit**[ ~ CE1] **interface gigabitethernet 4/0/0**[ ~ CE1-GigabitEthernet4/0/0] **undo shutdown**[*CE1-GigabitEthernet4/0/0] **traffic-policy 3 inbound**[*CE1-GigabitEthernet4/0/0] **commit**[ ~ CE1-GigabitEthernet4/0/0] **quit**[ ~ CE1] **interface gigabitethernet 2/0/0**[ ~ CE1-GigabitEthernet2/0/0] **undo shutdown**[*CE1-GigabitEthernet2/0/0] **traffic-policy udplimit outbound**[*CE1-GigabitEthernet2/0/0] **commit**[ ~ CE1-GigabitEthernet2/0/0] **quit**步骤 **3** 在 PE1 上配置复杂流分类，对接入 MPLS 骨干网的流量进行限制# 配置流分类，定义匹配规则。[ ~ PE1] **traffic classifier pe**[*PE1-classifier-pe] **if-match any**[*PE1-classifier-pe] **commit**[ ~ PE1-classifier-pe] **quit**上述配置完成后，可以通过 **display traffic classifier** 命令查看流分类的配置信息。[ ~ PE1] **display traffic classifier user-defined**User Defined Classifier Information:Total: 65535 Used: 6 Free: 65529Classifier: peDescription:Operator: orRule(s):if-match any# 定义流行为，配置流量监管。[ ~ PE1] **traffic behavior pe**[*PE1-behavior-pe] **car cir 15000 pir 20000 cbs 300000 pbs 500000**[*PE1-behavior-pe] **commit**[ ~ PE1-behavior-pe] **quit**# 定义流量策略，将流分类与流行为关联。[ ~ PE1] **traffic policy pe**[*PE1-trafficpolicy-pe] **classifier pe behavior pe**[*PE1-trafficpolicy-pe] **commit**[ ~ PE1-trafficpolicy-pe] **quit**上述配置完成后，使用 **display** **traffic** **policy** 命令可以查看流量策略、策略中定义的流分类以及与流分类相关的流行为的配置情况。[ ~ PE1] **display traffic policy user-defined**文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 131HUAWEI NetEngine40E配置指南 1 QoSUser Defined Traffic Policy Information:Total: 10239 Used: 1   Free: 10238Policy: peTotal: 5120 Used: 2 Free: 5118Description:Step: 1Share-modeClassifier: pe Precedence: 1Behavior: peCommitted Access Rate:CIR 15000 (Kbps), PIR 2000 (Kbps), CBS 300000 (byte), PBS 500000 (byte), ADJUST0Conform Action: pass Yellow Action: pass Exceed Action: discard Color-aware: noClassifier: default-class Precedence: 65535Behavior:  be-none# 将流量策略应用到入接口上。[ ~ PE1] **interface gigabitethernet 1/0/0**[ ~ PE1-GigabitEthernet1/0/0] **undo shutdown**[ ~ PE1-GigabitEthernet1/0/0] **traffic-policy pe inbound**[*PE1-GigabitEthernet1/0/0] **commit**[ ~ PE1-GigabitEthernet1/0/0] **quit**步骤 **4** 检查配置结果正确进行上述配置后，当有流量通过，在 CE1 和 PE1 上可以通过 **display interface** 命令看到接口流量根据流量策略进行了带宽保证。**----**结束##### 配置文件       - CE1 的配置文件#sysname CE1#acl number 2001rule 5 permit source ******* *********acl number 2002rule 5 permit source ******* *********acl number 2003rule 5 permit source ******* *********acl number 3001rule 0 permit udp destination-port eq dnsrule 1 permit udp destination-port eq snmprule 2 permit udp destination-port eq snmptraprule 3 permit udp destination-port eq syslogacl number 3002rule 4 permit udp#traffic classifier a operator orif-match acl 2001#traffic classifier b operator orif-match acl 2002#traffic classifier c operator orif-match acl 2003#traffic classifier udplimit operator orif-match acl 3001#traffic classifier udplimit1 operator orif-match acl 3002#traffic behavior e文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 132HUAWEI NetEngine40E配置指南 1 QoScar cir 10000 cbs 150000 green pass red discardremark dscp cs5#traffic behavior fcar cir 5000 cbs 100000 green pass red discardremark dscp af31#traffic behavior gcar cir 2000 cbs 100000 green pass red discardremark dscp default#traffic behavior udplimit#traffic behavior udplimit1car cir 5000 pir 15000 cbs 100000 pbs 200000 green pass yellow discard red discard#traffic policy 1classifier a behavior e precedence 1#traffic policy 2classifier b behavior f precedence 1#traffic policy 3classifier c behavior g precedence 1#traffic policy udplimitclassifier udplimit behavior udplimit precedence 1classifier udplimit1 behavior udplimit1 precedence 2#interface GigabitEthernet1/0/0undo shutdownip address ******* *************traffic-policy 1 inbound#interface GigabitEthernet2/0/0undo shutdownip address ******** *************traffic-policy udplimit outbound#interface GigabitEthernet3/0/0undo shutdownip address ******* *************traffic-policy 2 inbound#interface GigabitEthernet4/0/0undo shutdownip address ******* *************traffic-policy 3 inbound#ospf 1area 0.0.0.0network ******* *********network ******* *********network ******* *********network ******** *********#return       - PE1 的配置文件#sysname PE1#mpls lsr-id ***********#mpls#mpls ldp#traffic classifier pe operator orif-match any文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 133HUAWEI NetEngine40E配置指南 1 QoS#traffic behavior pecar cir 15000 pir 20000 cbs 300000 pbs 500000 green pass yellow pass red discard#traffic policy peclassifier pe behavior pe precedence 1#interface GigabitEthernet1/0/0undo shutdownip address ******** *************traffic-policy pe inbound#interface GigabitEthernet2/0/0undo shutdownip address ********* *************mplsmpls ldp#interface LoopBack0ip address *********** ***************#ospf 1area 0.0.0.0network ******** *********network ********* *********network *********** 0.0.0.0#return       - P 的配置文件#sysname P#mpls lsr-id ***********#mpls#mpls ldp#interface GigabitEthernet 1/0/0undo shutdownip address ********* *************mplsmpls ldp#interface GigabitEthernet 2/0/0undo shutdownip address ********* *************mplsmpls ldp#interface LoopBack0ip address *********** ***************#ospf 1area 0.0.0.0network ********* *********network ********* *********network *********** 0.0.0.0#return       - PE2 的配置文件#sysname PE2#mpls lsr-id ***********#mpls#文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 134HUAWEI NetEngine40E配置指南 1 QoSmpls ldp#interface GigabitEthernet1/0/0undo shutdownip address ********* *************#interface GigabitEthernet2/0/0undo shutdownip address ********* *************mplsmpls ldp#interface LoopBack0ip address *********** ***************#ospf 1area 0.0.0.0network ********* *********network ********* *********network *********** 0.0.0.0#return       - CE2 的配置文件#sysname CE2#interface GigabitEthernet2/0/0undo shutdownip address ********* *************#ospf 1area 0.0.0.0network ********* *********#return##### ******** 配置 VLAN 报文复杂流分类的流量策略示例以 VLAN QoS 场景为例，介绍复杂流分类流量策略的配置及其应用。##### 组网需求如 图 **1-15** 所示， DeviceA 和 DeviceB 通过 VLAN 网络互联，当有 IP 数据流从 DeviceA 进入VLAN 网络时，默认将 IP 报文中的优先级拷贝到 VLAN 帧的优先级，当从 VLAN 网络进入到 DeviceB 时，通过在 DeviceB 上配置按照 VLAN 帧优先级进行匹配的规则，实现设置从DeviceB 转发出去的 IP 数据包的 IP 优先级。说明本例中 interface1 ， interface2 ， interface3 分别代表 GE1/0/0 ， GE2/0/0.1 ， GE3/0/0 。图 **1-15** 配置 VLAN QoS 组网图文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 135HUAWEI NetEngine40E配置指南 1 QoS##### 配置注意事项在配置过程中，需注意以下事项：       - 系统缺省不使能流量策略的统计功能。当用户需要显示流量策略的统计数据时，可配置 **statistics enable** 命令使能流量策略的统计功能。##### 配置思路采用如下思路配置 VLAN QoS ：1. 首先在 DeviceA 、 DeviceB 上进行 VLAN 的配置和路由的配置。2. 然后是在 DeviceB 上进行 QoS 策略的配置。##### 数据准备为完成此配置举例，需要准备以下数据：       - 流分类名称、流行为名称、流量策略名称       - 重新标记的优先级##### 操作步骤步骤 **1** 定义一个类，匹配 VLAN 帧的 8021P 域为 2 的数据包。<HUAWEI> **system-view**[ ~ HUAWEI] **sysname DeviceB**[*HUAWEI] **commit**[ ~ DeviceB] **traffic classifier test**[*DeviceB-classifier-test] **if-match 8021p 2**[*DeviceB-classifier-test] **commit**[ ~ DeviceB-classifier-test] **quit**步骤 **2** 定义流行为，设置 IP 的优先级为 4 。[ ~ DeviceB] **traffic behavior test**[*DeviceB-behavior-test] **remark ip-precedence 4**[*DeviceB-behavior-test] **commit**[ ~ DeviceB-behavior-test] **quit**步骤 **3** 定义 QoS 策略，将已配置的流行为指定给已经定义的类。[ ~ DeviceB] **traffic policy test**[*DeviceB-trafficpolicy-test] **classifier test behavior test**[*DeviceB-trafficpolicy-test] **commit**[ ~ DeviceB-trafficpolicy-test] **quit**步骤 **4** 将已定义的 QoS 策略应用在路由器 DeviceB 的 GE2/0/0.1 入方向。[ ~ DeviceB] **interface Gigabitethernet 2/0/0.1**[ ~ DeviceB-Gigabitethernet2/0/0.1] **traffic-policy test inbound link-layer**[*DeviceB-Gigabitethernet2/0/0.1] **commit**[ ~ DeviceB-Gigabitethernet2/0/0.1] **quit**步骤 **5** 检查配置结果配置完成后， DeviceA 上通过 GE1/0/0.1 转发 IP 优先级为 2 的数据包时，可以观察到进入到 VLAN 网络的 VLAN 帧的优先级为 2 ，当该 VLAN 帧到达 DeviceB ，并转发至********/24 网段时，可以观察到此时 IP 数据包的 IP 优先级为 4 。**----**结束文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 136HUAWEI NetEngine40E配置指南 1 QoS##### 配置文件DeviceB 的配置文件#sysname DeviceB#traffic classifier test operator orif-match 8021p 2#traffic behavior testremark ip-precedence 4#traffic policy testclassifier test behavior test precedence 1#interface GigabitEthernet2/0/0.1traffic-policy test inbound link-layer#return##### ******** 配置二层接口基于简单流分类的优先级映射示例（ VLAN ）以 VLAN 报文的优先级映射场景为例，介绍简单流分类的配置。##### 组网需求如 图 **1-16** 所示， VLAN10 的用户报文，通过 SwitchA 连接 DeviceA ，从 VLAN 网络进入 IP网络，在 DeviceA 上配置 DS 域的优先级映射，设置从 DeviceA 转发出去的 IP 数据包的 IP优先级。说明本例中 interface1 ， interface2 分别代表 GE1/0/0 、 GE2/0/0 。图 **1-16** 配置 VLAN QoS 组网图##### 配置注意事项在配置过程中，需注意以下事项：       - 在配置 **trust 8021p vlan** 命令之前，必须先在接口上绑定 DS 域，即配置 **trust****upstream vlan** 命令，该配置才生效。##### 配置思路采用如下思路配置 VLAN QoS ：文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 137HUAWEI NetEngine40E配置指南 1 QoS1. 在 DeviceA 上进行 VLAN 的配置。2. 在 DeviceA 的入接口配置信任上游业务优先级。3. 在 DeviceA 的入接口配置基于简单流分类的业务优先级映射。4. 在 DeviceA 的出接口配置使能报文的优先级字段映射。##### 数据准备为完成此配置举例，需要准备以下数据：       - VLAN ID 。       - 需要映射的 802.1p 优先级、路由器内部的服务等级和颜色、 IP 网络的 DSCP 值。##### 操作步骤步骤 **1** 在 DeviceA 创建和关联 VLAN# 创建 VLAN<DeviceA> **system-view**[ ~ DeviceA] **vlan batch 10**[*DeviceA] **commit**[ ~ DeviceA] **interface gigabitethernet 1/0/0**[ ~ DeviceA-GigabitEthernet1/0/0] **portswitch**[*DeviceA-GigabitEthernet1/0/0] **port trunk allow-pass vlan 10**[*DeviceA-GigabitEthernet1/0/0] **commit**[ ~ DeviceA-GigabitEthernet1/0/0] **quit**步骤 **2** 在 DeviceA 的入接口 GE1/0/0 使能简单流分类，将 VLAN 报文中的优先级按照缺省映射关系映射 IP 报文的优先级中[ ~ DeviceA-GigabitEthernet1/0/0] **trust upstream default vlan 10**[*DeviceA-GigabitEthernet1/0/0] **trust 8021p vlan 10**[*DeviceA-GigabitEthernet1/0/0] **commit**[ ~ DeviceA-GigabitEthernet1/0/0] **quit**步骤 **3** 在 DeviceA 的出接口 GE2/0/0 配置 VLAN 帧到 IP 报文的优先级映射[ ~ DeviceA] **diffserv domain default**[*DeviceA-dsdomain-default] **8021p-inbound 2 phb ef green**[*DeviceA-dsdomain-default] **ip-dscp-outbound** **ef green map** **34**[*DeviceA-dsdomain-default] **commit**[ ~ DeviceA-dsdomain-default] **quit**[ ~ DeviceA] **interface gigabitethernet 2/0/0**[*DeviceA-GigabitEthernet2/0/0] **portswitch**[*DeviceA-GigabitEthernet2/0/0] **port trunk allow-pass vlan 10**[*DeviceA-GigabitEthernet2/0/0] **trust upstream default vlan 10**[*DeviceA-GigabitEthernet2/0/0] **trust 8021p vlan 10**[*DeviceA-GigabitEthernet2/0/0] **commit**[ ~ DeviceA-GigabitEthernet2/0/0] **quit**完成上述配置后，上游过来的 802.1p 优先级为 2 的 VLAN 帧在 DeviceA 处映射转换成业务类型为 EF green 的 IP 报文。其它 802.1p 优先级的 VLAN 帧在 DeviceA 处按照系统缺省的映射关系将 VLAN 帧中的 802.1p 优先级映射成对应的 IP 报文的 DSCP 值。步骤 **4** 检查配置结果在 DeviceA 上执行 **display port-queue statistics interface gigabitethernet 2/0/0****outbound** 命令，由于在入口配置了 8021p 优先级 2 到 IP 报文业务优先级 EF 的映射，因此在出接口会没有 AF2 流量通过的报文统计计数。<DeviceA> display port-queue statistics interface gigabitethernet 2/0/0 outboundGigabitEthernet2/0/0 outbound traffic statistics:[be]文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 138HUAWEI NetEngine40E配置指南 1 QoSCurrent usage percentage of queue: 0Total pass:1,003,905,621 packets,       90,351,505,260 bytesTotal discard:0 packets,             0 bytesDrop tail discard:0 packets,             0 bytesWred discard:0 packets,             0 bytesLast 30 seconds pass rate:0 pps,               0 bpsLast 30 seconds discard rate:0 pps,               0 bpsDrop tail discard rate:0 pps,               0 bpsWred discard rate:0 pps,               0 bpsbuffer size:          10 kbytesused buffer size:        0 kbytesPeak rate:0000-00-00 00:00:00             0 bps[af1]Current usage percentage of queue: 0Total pass:0 packets,             0 bytesTotal discard:0 packets,             0 bytesDrop tail discard:0 packets,             0 bytesWred discard:0 packets,             0 bytesLast 30 seconds pass rate:0 pps,               0 bpsLast 30 seconds discard rate:0 pps,               0 bpsDrop tail discard rate:0 pps,               0 bpsWred discard rate:0 pps,               0 bpsbuffer size:          10 kbytesused buffer size:        0 kbytesPeak rate:0000-00-00 00:00:00             0 bps[af2]Current usage percentage of queue: 0Total pass:0 packets,             0 bytesTotal discard:0 packets,             0 bytesDrop tail discard:0 packets,             0 bytesWred discard:0 packets,             0 bytesLast 30 seconds pass rate:0 pps,               0 bpsLast 30 seconds discard rate:0 pps,               0 bpsDrop tail discard rate:0 pps,               0 bpsWred discard rate:0 pps,               0 bpsbuffer size:          10 kbytesused buffer size:        0 kbytesPeak rate:0000-00-00 00:00:00             0 bps[af3]Current usage percentage of queue: 0Total pass:0 packets,             0 bytes文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 139HUAWEI NetEngine40E配置指南 1 QoSTotal discard:0 packets,             0 bytesDrop tail discard:0 packets,             0 bytesWred discard:0 packets,             0 bytesLast 30 seconds pass rate:0 pps,               0 bpsLast 30 seconds discard rate:0 pps,               0 bpsDrop tail discard rate:0 pps,               0 bpsWred discard rate:0 pps,               0 bpsbuffer size:          10 kbytesused buffer size:        0 kbytesPeak rate:0000-00-00 00:00:00             0 bps[af4]Current usage percentage of queue: 0Total pass:0 packets,             0 bytesTotal discard:0 packets,             0 bytesDrop tail discard:0 packets,             0 bytesWred discard:0 packets,             0 bytesLast 30 seconds pass rate:0 pps,               0 bpsLast 30 seconds discard rate:0 pps,               0 bpsDrop tail discard rate:0 pps,               0 bpsWred discard rate:0 pps,               0 bpsbuffer size:          10 kbytesused buffer size:        0 kbytesPeak rate:0000-00-00 00:00:00             0 bps[ef]Current usage percentage of queue: 0Total pass:27,167,382 packets,       3,477,424,896 bytesTotal discard:0 packets,             0 bytesDrop tail discard:0 packets,             0 bytesWred discard:0 packets,             0 bytesLast 30 seconds pass rate:844,397 pps,          864,661,792 bpsLast 30 seconds discard rate:0 pps,               0 bpsDrop tail discard rate:0 pps,               0 bpsWred discard rate:0 pps,               0 bpsbuffer size:          10 kbytesused buffer size:        0 kbytesPeak rate:0000-00-00 00:00:00             0 bps[cs6]Current usage percentage of queue: 0Total pass:335 packets,           25,502 bytesTotal discard:0 packets,             0 bytesDrop tail discard:文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 140HUAWEI NetEngine40E配置指南 1 QoS0 packets,             0 bytesWred discard:0 packets,             0 bytesLast 30 seconds pass rate:0 pps,               0 bpsLast 30 seconds discard rate:0 pps,               0 bpsDrop tail discard rate:0 pps,               0 bpsWred discard rate:0 pps,               0 bpsbuffer size:          10 kbytesused buffer size:        0 kbytesPeak rate:0000-00-00 00:00:00             0 bps[cs7]Current usage percentage of queue: 0Total pass:0 packets,             0 bytesTotal discard:0 packets,             0 bytesDrop tail discard:0 packets,             0 bytesWred discard:0 packets,             0 bytesLast 30 seconds pass rate:0 pps,               0 bpsLast 30 seconds discard rate:0 pps,               0 bpsDrop tail discard rate:0 pps,               0 bpsWred discard rate:0 pps,               0 bpsbuffer size:          10 kbytesused buffer size:        0 kbytesPeak rate:0000-00-00 00:00:00             0 bps**----**结束##### 配置文件DeviceA 的配置文件#sysname DeviceA#diffserv domain default8021p-inbound 2 phb ef greenip-dscp-outbound ef green map 34#vlan 10#interface GigabitEthernet 1/0/0portswitchundo shutdownport trunk allow-pass vlan 10trust upstream default vlan 10trust 8021p vlan 10#interface GigabitEthernet2/0/0portswitchundo shutdownport trunk allow-pass vlan 10trust upstream default vlan 10trust 8021p vlan 10#return文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 141HUAWEI NetEngine40E配置指南 1 QoS##### ******** 配置基于简单流分类的优先级映射示例（ MPLS ）以 MPLS 报文的优先级映射场景为例，介绍简单流分类的配置。##### 配置需求三台路由器之间建立 MPLS 邻居，从 DeviceA 上进入的 IP 流量，在 DeviceA 至 DeviceC 之间走 MPLS 转发。从 DeviceC 流出后，又恢复成 IP 流。要求流量在 DeviceA 上，能任意修改成为 MPLS 流量之后的优先级，在 DeviceC 上能任意修改恢复 IP 流之后的优先级。说明          - 在本配置例中，假定已经建立好了三台路由器上的 MPLS 配置， IP 流量从 DeviceA 到 DeviceC能进行 MPLS 转发， MPLS 流量出 DeviceC 时，能转换成 IP 流。          - 以下的配置，只列出了与 QoS 配置相关的命令。          - 本例中 interface1 ， interface2 分别代表 GE1/0/0 ， GE2/0/0 。图 **1-17** 配置基于简单流分类的优先级映射组网图##### 配置思路采用如下思路配置 MPLS QoS ：1. 在 DeviceA 的入接口 GigabitEthernet1/0/0 配置 IP DSCP 到 MPLS EXP 的映射，并使能简单流分类。2. 在 DeviceC 的入接口 GigabitEthernet1/0/0 配置 MPLS EXP 到 IP DSCP 的映射，并使能简单流分类。##### 数据准备为完成此配置举例，需要准备以下数据：需要映射的 MPLS EXP 优先级、路由器内部的服务等级和颜色、 IP 网络的 DSCP 值。##### 操作步骤步骤 **1** 配置 MPLS 基本能力和路由（略）详细配置请参见《 HUAWEI NetEngine40E 路由器 配置指南 -MPLS 》。步骤 **2** 在 DeviceA 的 GigabitEthernet1/0/0 接口上配置 DSCP 到 MPLS 的映射[ ~ DeviceA] **diffserv domain default**[ ~ DeviceA-dsdomain-default] **ip-dscp-inbound 18 phb af4 green**[*DeviceA-dsdomain-default] **mpls-exp-outbound af4 green map 5**[*DeviceA-dsdomain-default] **commit**[ ~ DeviceA-dsdomain-default] **quit**[ ~ DeviceA] **interface GigabitEthernet 1/0/0**[ ~ DeviceA-GigabitEthernet1/0/0] **undo shutdown**文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 142HUAWEI NetEngine40E配置指南 1 QoS[ ~ DeviceA-GigabitEthernet1/0/0] **trust upstream default**[*DeviceA-GigabitEthernet1/0/0] **commit**[ ~ DeviceA-GigabitEthernet1/0/0] **quit**[ ~ DeviceA] **interface GigabitEthernet 2/0/0**[ ~ DeviceA-GigabitEthernet2/0/0] **undo shutdown**[ ~ DeviceA-GigabitEthernet2/0/0] **trust upstream default**[*DeviceA-GigabitEthernet2/0/0] **commit**[ ~ DeviceA-GigabitEthernet2/0/0] **quit**在 DeviceA 的入接口上配置服务等级 AF2 green （ DSCP 值 18 ）到路由器内部业务等级AF4 的映射，在出接口上配置路由器内部业务等级 AF4 到 MPLS 服务等级 EF （优先级 5 ）的映射，从 DeviceA 出来的是 EF 流量。步骤 **3** 在 DeviceC 的 GigabitEthernet1/0/0 接口上配置 MPLS 到 DSCP 的映射[ ~ DeviceC] **diffserv domain default**[ ~ DeviceC-dsdomain-default] **mpls-exp-inbound 5 phb af3 green**[*DeviceC-dsdomain-default] **ip-dscp-outbound af3 green map 32**[*DeviceC-dsdomain-default] **commit**[ ~ DeviceC-dsdomain-default] **quit**[ ~ DeviceC] **interface GigabitEthernet 1/0/0**[ ~ DeviceC-GigabitEthernet1/0/0] **undo shutdown**[ ~ DeviceC-GigabitEthernet1/0/0] **trust upstream default**[*DeviceC-GigabitEthernet1/0/0] **commit**[ ~ DeviceC-GigabitEthernet1/0/0] **quit**[ ~ DeviceC] **interface GigabitEthernet 2/0/0**[ ~ DeviceC-GigabitEthernet2/0/0] **undo shutdown**[ ~ DeviceC-GigabitEthernet2/0/0] **trust upstream default**[*DeviceC-GigabitEthernet2/0/0] **commit**[ ~ DeviceC-GigabitEthernet2/0/0] **quit**在 DeviceC 的入接口上配置 MPLS 优先级 5 到路由器内部业务等级为 AF3 green 的映射，在出接口配置路由器内部业务等级 AF3 green 到 DSCP 值 32 的转换， DeviceC 出来的是AF4 流量。步骤 **4** 检查配置结果配置完成后，从 DeviceA 的 GigabitEthernet1/0/0 发送 DSCP 值为 18 的流量，大小为100Mbit/s ， DeviceC 出来的是 DSCP 值为 32 的流量，流量为 100Mbit/s 。**----**结束##### 配置文件       - DeviceA 的配置文件#sysname DeviceA#diffserv domain defaultip-dscp-inbound 18 phb af4 greenmpls-exp-outbound af4 green map 5#interface GigabitEthernet1/0/0undo shutdownip address ******* *************trust upstream default#interface GigabitEthernet2/0/0undo shutdownip address ******* *************trust upstream default#return       - DeviceC 的配置文件#sysname DeviceC文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 143HUAWEI NetEngine40E配置指南 1 QoS#diffserv domain defaultip-dscp-outbound af3 green map 32mpls-exp-inbound 5 phb af3 green#interface GigabitEthernet1/0/0undo shutdownip address ******* *************trust upstream default#interface GigabitEthernet2/0/0undo shutdownip address ******* *************trust upstream default#return##### ********* 配置 EVC 子接口基于简单流分类的优先级映射示例（ VLAN ）以 VLAN 报文的优先级映射场景为例，介绍 EVC 子接口基于简单流分类的优先级映射的配置。##### 组网需求不同的小区拥有相同的业务，如上网、 IPTV 、 VoIP 等业务。为了便于管理，各个小区的网络管理者将业务划分到同一个 VLAN 中。同时，为了保证不同业务的质量，需要在路由器 PE1 和 PE2 上使能简单流分类功能。如 图 **1-18** 所示，小区 1 和小区 2 中拥有相同的业务，属于相同 VLAN 。现需要以低廉的成本实现小区 1 和小区 2 中的业务互通。同时在 PE1 和 PE2 的 DS 域上配置优先级映射。要求在 PE1 上通过 EVC 子接口 GE1/0/1.1 的下行流量不修改 VLAN 报文的 outer-8021p 和dscp 字段；在 PE2 上通过 EVC 子接口 GE1/0/1.1 的下行流量不修改 VLAN 报文的outer-8021p 和 mpls-exp 字段。说明本例中 interface1 ， interface2 ， Subinterface1.1 分别代表 GE1/0/1 ， GE1/0/2 ， GE1/0/1.1 。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 144HUAWEI NetEngine40E配置指南 1 QoS图 **1-18** 配置 EVC QoS 组网图##### 配置注意事项所有 VLAN 内的业务在同一个网段。##### 配置思路搭建 EVC 模型：1. 将连接小区 1 和小区 2 的设备端口划分到 VLAN10 中。2. 在 PE 设备上搭建 EVC 模型：– 配置广播域 BD ，用来转发业务。– 创建二层子接口，并加入 BD ，同时在下行口配置流封装，以实现小区 1 和小区 2 中的业务互通。使能简单流分类：1. 在 PE1 的 EVC 子接口 GE1/0/1.1 上配置 802.1p 优先级和路由器内部的服务等级和颜色的映射。2. 在 PE2 的 EVC 子接口 GE1/0/1.1 上配置路由器内部的服务等级和颜色和 802.1p 优先级的映射。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 145HUAWEI NetEngine40E配置指南 1 QoS##### 数据准备为完成此配置举例，需要准备以下数据：       - 设备连接用户的接口编号。       - 设备之间连接的接口编号。       - 业务所属的 VLAN ID 。       - 广播域 BD ID 。       - 需要映射的 802.1p 优先级、路由器内部的服务等级和颜色。##### 操作步骤步骤 **1** 将 CE 的下行口划分到指定 VLAN# 配置 CE1 。<HUAWEI> **system-view**[ ~ HUAWEI] **sysname CE1**[*HUAWEI] **commit**[ ~ CE1] **vlan 10**[*CE1-vlan10] **quit**[*CE1] **interface gigabitethernet 1/0/1**[*CE1-GigabitEthernet1/0/1] **undo shutdown**[*CE1-GigabitEthernet1/0/1] **portswitch**[*CE1-GigabitEthernet1/0/1] **port link-type access**[*CE1-GigabitEthernet1/0/1] **port default vlan 10**[*CE1-GigabitEthernet1/0/1] **quit**[*CE1] **interface gigabitethernet 1/0/2**[*CE1-GigabitEthernet1/0/2] **undo shutdown**[*CE1-GigabitEthernet1/0/2] **portswitch**[*CE1-GigabitEthernet1/0/2] **port link-type trunk**[*CE1-GigabitEthernet1/0/2] **port trunk allow-pass vlan 10**[*CE1-GigabitEthernet1/0/2] **quit**[*CE1] **commit**# 配置 CE2 。<HUAWEI> **system-view**[ ~ HUAWEI] **sysname CE2**[*HUAWEI] **commit**[ ~ CE2] **vlan 10**[*CE2] **interface gigabitethernet 1/0/1**[*CE2-GigabitEthernet1/0/1] **undo shutdown**[*CE2-GigabitEthernet1/0/1] **portswitch**[*CE2-GigabitEthernet1/0/1] **port link-type access**[*CE2-GigabitEthernet1/0/1] **port default vlan 10**[*CE2-GigabitEthernet1/0/1] **quit**[*CE2] **interface gigabitethernet 1/0/2**[*CE2-GigabitEthernet1/0/2] **undo shutdown**[*CE2-GigabitEthernet1/0/2] **portswitch**[*CE2-GigabitEthernet1/0/2] **port link-type trunk**[*CE2-GigabitEthernet1/0/2] **port trunk allow-pass vlan 10**[*CE2-GigabitEthernet1/0/2] **quit**[*CE2] **commit**步骤 **2** 搭建 EVC 模型1. 在分别在 PE 上创建 BD# 配置 PE1 。<HUAWEI> **system-view**[ ~ HUAWEI] **sysname PE1**[*HUAWEI] **commit**[ ~ PE1] **bridge-domain 10**文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 146HUAWEI NetEngine40E配置指南 1 QoS[*PE1-bd10] **quit**# 配置 PE2 。<HUAWEI> **system-view**[ ~ HUAWEI] **sysname PE2**[*HUAWEI] **commit**[ ~ PE2] **bridge-domain 10**[*PE2-bd10] **quit**2. 创建二层子接口，将二层子接口加入 BD ，同时配置流封装和流动作。# 配置 PE1 。[*PE1] **interface gigabitethernet 1/0/1**[*PE1-GigabitEthernet1/0/1] **undo shutdown**[*PE1-GigabitEthernet1/0/1] **quit**[*PE1] **interface gigabitethernet 1/0/1.1 mode l2**[*PE1-GigabitEthernet1/0/1.1] **encapsulation dot1q vid 10**[*PE1-GigabitEthernet1/0/1.1] **bridge-domain 10**[*PE1-GigabitEthernet1/0/1.1] **commit**[ ~ PE1-GigabitEthernet1/0/1.1] **quit**# 配置 PE2 。[ ~ PE2] **interface gigabitethernet 1/0/1**[*PE2-GigabitEthernet1/0/1] **undo shutdown**[*PE2-GigabitEthernet1/0/1] **quit**[*PE2] **interface gigabitethernet 1/0/1.1 mode l2**[*PE2-GigabitEthernet1/0/1.1] **encapsulation dot1q vid 10**[*PE2-GigabitEthernet1/0/1.1] **bridge-domain 10**[*PE2-GigabitEthernet1/0/1.1] **commit**[ ~ PE2-GigabitEthernet1/0/1.1] **quit**步骤 **3** 在 EVC 子接口使能简单流分类。# 配置 PE1 。[ ~ PE1] **diffserv domain gina**[*PE1-dsdomain-gina] **8021p-outbound cs6 red map 5**[*PE1-dsdomain-gina] **quit**[*PE1] **interface gigabitethernet 1/0/1.1 mode l2**[*PE1-GigabitEthernet1/0/1.1] **trust upstream gina**[*PE1-GigabitEthernet1/0/1.1] **qos phb outer-8021p disable**[*PE1-GigabitEthernet1/0/1.1] **qos phb dscp disable**[*PE1-GigabitEthernet1/0/1.1] **commit**[ ~ PE1-GigabitEthernet1/0/1.1] **quit**# 配置 PE2 。[ ~ PE2] **diffserv domain gina**[*PE2-dsdomain-gina] **8021p-inbound 2 phb af1 green**[*PE2-dsdomain-gina] **quit**[*PE2] **interface gigabitethernet 1/0/1.1 mode l2**[*PE2-GigabitEthernet1/0/1.1] **trust upstream gina**[*PE2-GigabitEthernet1/0/1.1] **qos phb outer-8021p disable**[*PE2-GigabitEthernet1/0/1.1] **qos phb mpls-exp disable**[*P-GigabitEthernet1/0/1.1] **commit**[ ~ PE2-GigabitEthernet1/0/1] **quit**步骤 **4** 验证配置结果上述配置完成后，执行命令 **display bridge-domain** 命令，可以查看到二层子接口加入的 BD 、 BD 的状态等信息。以 PE1 为例：[ ~ PE1] **display bridge-domain**The total number of bridge-domains is : 1-------------------------------------------------------------------------------MAC_LRN: MAC learning;     STAT: Statistics;     SPLIT: Split-horizon;BC: Broadcast;         MC: Unknown multicast;  UC: Unknown unicast;*down: Administratively down; FWD: Forward;       DSD: Discard;-------------------------------------------------------------------------------文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 147HUAWEI NetEngine40E配置指南 1 QoSBDID State MAC-LRN STAT  BC MC UC SPLIT  Description-------------------------------------------------------------------------------10  up  enable disable FWD FWD FWD disable上述配置完成后，执行命令 **display diffserv domain** [ ds-domain-name ] [ **8021p** |**dscp** | **exp** ] [ **inbound** | **outbound** ] 命令查看 DS 域的配置信息。以 PE1 为例：[ ~ PE1] **display diffserv domain gina**Diffserv domain name:gina8021p-inbound 0 phb be green8021p-inbound 1 phb af1 green8021p-inbound 2 phb af2 green8021p-inbound 3 phb af3 green8021p-inbound 4 phb af4 green8021p-inbound 5 phb ef green8021p-inbound 6 phb cs6 green8021p-inbound 7 phb cs7 green8021p-outbound be green map 08021p-outbound be yellow map 08021p-outbound be red map 08021p-outbound af1 green map 18021p-outbound af1 yellow map 18021p-outbound af1 red map 18021p-outbound af2 green map 28021p-outbound af2 yellow map 28021p-outbound af2 red map 28021p-outbound af3 green map 38021p-outbound af3 yellow map 38021p-outbound af3 red map 38021p-outbound af4 green map 48021p-outbound af4 yellow map 48021p-outbound af4 red map 48021p-outbound ef green map 58021p-outbound ef yellow map 58021p-outbound ef red map 58021p-outbound cs6 green map 68021p-outbound cs6 yellow map 68021p-outbound cs6 red map 58021p-outbound cs7 green map 78021p-outbound cs7 yellow map 78021p-outbound cs7 red map 7ip-dscp-inbound 0 phb be greenip-dscp-inbound 1 phb be greenip-dscp-inbound 2 phb be greenip-dscp-inbound 3 phb be greenip-dscp-inbound 4 phb be greenip-dscp-inbound 5 phb be greenip-dscp-inbound 6 phb be greenip-dscp-inbound 7 phb be greenip-dscp-inbound 8 phb af1 greenip-dscp-inbound 9 phb be greenip-dscp-inbound 10 phb af1 greenip-dscp-inbound 11 phb be greenip-dscp-inbound 12 phb af1 yellowip-dscp-inbound 13 phb be greenip-dscp-inbound 14 phb af1 redip-dscp-inbound 15 phb be greenip-dscp-inbound 16 phb af2 greenip-dscp-inbound 17 phb be greenip-dscp-inbound 18 phb af2 greenip-dscp-inbound 19 phb be greenip-dscp-inbound 20 phb af2 yellowip-dscp-inbound 21 phb be greenip-dscp-inbound 22 phb af2 redip-dscp-inbound 23 phb be greenip-dscp-inbound 24 phb af3 greenip-dscp-inbound 25 phb be greenip-dscp-inbound 26 phb af3 greenip-dscp-inbound 27 phb be green文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 148HUAWEI NetEngine40E配置指南 1 QoSip-dscp-inbound 28 phb af3 yellowip-dscp-inbound 29 phb be greenip-dscp-inbound 30 phb af3 redip-dscp-inbound 31 phb be greenip-dscp-inbound 32 phb af4 greenip-dscp-inbound 33 phb be greenip-dscp-inbound 34 phb af4 greenip-dscp-inbound 35 phb be greenip-dscp-inbound 36 phb af4 yellowip-dscp-inbound 37 phb be greenip-dscp-inbound 38 phb af4 redip-dscp-inbound 39 phb be greenip-dscp-inbound 40 phb ef greenip-dscp-inbound 41 phb be greenip-dscp-inbound 42 phb be greenip-dscp-inbound 43 phb be greenip-dscp-inbound 44 phb be greenip-dscp-inbound 45 phb be greenip-dscp-inbound 46 phb ef greenip-dscp-inbound 47 phb be greenip-dscp-inbound 48 phb cs6 greenip-dscp-inbound 49 phb be greenip-dscp-inbound 50 phb be greenip-dscp-inbound 51 phb be greenip-dscp-inbound 52 phb be greenip-dscp-inbound 53 phb be greenip-dscp-inbound 54 phb be greenip-dscp-inbound 55 phb be greenip-dscp-inbound 56 phb cs7 greenip-dscp-inbound 57 phb be greenip-dscp-inbound 58 phb be greenip-dscp-inbound 59 phb be greenip-dscp-inbound 60 phb be greenip-dscp-inbound 61 phb be greenip-dscp-inbound 62 phb be greenip-dscp-inbound 63 phb be greenip-dscp-outbound be green map 0ip-dscp-outbound be yellow map 0ip-dscp-outbound be red map 0ip-dscp-outbound af1 green map 10ip-dscp-outbound af1 yellow map 12ip-dscp-outbound af1 red map 14ip-dscp-outbound af2 green map 18ip-dscp-outbound af2 yellow map 20ip-dscp-outbound af2 red map 22ip-dscp-outbound af3 green map 26ip-dscp-outbound af3 yellow map 28ip-dscp-outbound af3 red map 30ip-dscp-outbound af4 green map 34ip-dscp-outbound af4 yellow map 36ip-dscp-outbound af4 red map 38ip-dscp-outbound ef green map 46ip-dscp-outbound ef yellow map 46ip-dscp-outbound ef red map 46ip-dscp-outbound cs6 green map 48ip-dscp-outbound cs6 yellow map 48ip-dscp-outbound cs6 red map 48ip-dscp-outbound cs7 green map 56ip-dscp-outbound cs7 yellow map 56ip-dscp-outbound cs7 red map 56user-priority 0 phb be greenuser-priority 1 phb af1 greenuser-priority 2 phb af2 greenuser-priority 3 phb af3 greenuser-priority 4 phb af4 greenuser-priority 5 phb ef greenuser-priority 6 phb cs6 greenuser-priority 7 phb cs7 greenmpls-exp-inbound 0 phb be green文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 149HUAWEI NetEngine40E配置指南 1 QoSmpls-exp-inbound 1 phb af1 greenmpls-exp-inbound 2 phb af2 greenmpls-exp-inbound 3 phb af3 greenmpls-exp-inbound 4 phb af4 greenmpls-exp-inbound 5 phb ef greenmpls-exp-inbound 6 phb cs6 greenmpls-exp-inbound 7 phb cs7 greenmpls-exp-outbound be green map 0mpls-exp-outbound be yellow map 0mpls-exp-outbound be red map 0mpls-exp-outbound af1 green map 1mpls-exp-outbound af1 yellow map 1mpls-exp-outbound af1 red map 1mpls-exp-outbound af2 green map 2mpls-exp-outbound af2 yellow map 2mpls-exp-outbound af2 red map 2mpls-exp-outbound af3 green map 3mpls-exp-outbound af3 yellow map 3mpls-exp-outbound af3 red map 3mpls-exp-outbound af4 green map 4mpls-exp-outbound af4 yellow map 4mpls-exp-outbound af4 red map 4mpls-exp-outbound ef green map 5mpls-exp-outbound ef yellow map 5mpls-exp-outbound ef red map 5mpls-exp-outbound cs6 green map 6mpls-exp-outbound cs6 yellow map 6mpls-exp-outbound cs6 red map 6mpls-exp-outbound cs7 green map 7mpls-exp-outbound cs7 yellow map 7mpls-exp-outbound cs7 red map 7**----**结束##### 配置文件       - PE1 的配置文件#sysname PE1#diffserv domain gina8021p-outbound cs6 red map 5#bridge-domain 10#interface GigabitEthernet1/0/1undo shutdown#interface GigabitEthernet1/0/1.1 mode l2encapsulation dot1q vid 10bridge-domain 10trust upstream ginaqos phb outer-8021p disableqos phb dscp disable#return       - PE2 的配置文件#sysname PE2#diffserv domain gina8021p-inbound 2 phb af1 green#bridge-domain 10#interface GigabitEthernet1/0/1undo shutdown文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 150HUAWEI NetEngine40E配置指南 1 QoS#interface GigabitEthernet1/0/1.1 mode l2encapsulation dot1q vid 10bridge-domain 10trust upstream ginaqos phb outer-8021p disableqos phb mpls-exp disable#return       - CE1 的配置文件#sysname CE1#vlan 10#interface GigabitEthernet1/0/1portswitchundo shutdownport link-type accessport default vlan 10dcn#interface GigabitEthernet1/0/2portswitchundo shutdownport link-type trunkport trunk allow-pass vlan 10#return       - CE2 的配置文件#sysname CE2#vlan 10#interface GigabitEthernet1/0/1portswitchundo shutdownport link-type accessport default vlan 10dcn#interface GigabitEthernet1/0/2portswitchundo shutdownport link-type trunkport trunk allow-pass vlan 10#return### 1.6 策略路由配置#### 1.6.1 策略路由概述策略路由是基于复杂流分类的 QoS 策略框架，专指流行为是重定向到报文出接口的一种 QoS 策略。内部网络通过一台路由器与外部网络连接，路由器有多个到外部网络的出口，为了控制某些报文通过指定的出口转发，就需要配置接口的策略路由。报文到达路由器后，路由器首先根据策略路由转发，若没有配置策略路由或配置了策略路由但找不到匹配的表项时，再根据路由表来转发报文。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 151HUAWEI NetEngine40E配置指南 1 QoS#### 1.6.2 策略路由配置注意事项##### 特性限制无#### 1.6.3 基于重定向到 MPLS-TE P2P 隧道的策略路由配置介绍基于重定向到 MPLS-TE P2P 隧道的策略路由应用场景和配置过程。##### 应用环境随着网络的普及，网络中的业务越来越多样化，多种业务流共享同一网络资源。如果需要对进入网络的流量或网络中的流量进行分类管理和限制（例如对语音、视频、数据等业务分别对待，分配不同的带宽、保证不同的时延；对来自不同用户的流量分别对待，保证不同的带宽和优先级），简单流分类的流量策略就难以满足需求。此时，与复杂流分类类似，当指定出接口为 MPLS-TE P2P 隧道时，可以配置策略路由实现 IP报文重定向。##### 前置任务在配置基于重定向到 MPLS-TE P2P 隧道的策略路由之前，需要完成以下任务：       - 配置相关接口的物理参数，保证物理链路正常工作       - 配置相关接口的链路层属性，保证接口的正常工作       - 配置相关接口的 IP 地址       - 配置使能路由协议，实现网络层互通##### ******* 配置策略路由匹配规则 背景信息策略路由的匹配规则是基于高级 ACL 特性的，在配置策略路由之前，需要创建高级 ACL列表并配置规则。具体步骤，请参考配置高级 ACL 。##### ******* 配置策略路由的动作 背景信息策略路由的每一个策略下每个 **node** 配置有规则与动作，需要先完成 *********** 配置策略路由匹配规则 ，动作就是对匹配的 IP 报文重定向到出接口。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **policy-based-route** policy-name **permit** **node** node-id **map-instance**map-instance-id ，创建策略或一个策略点，并进入 Policy-Based-Route 视图。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 152HUAWEI NetEngine40E配置指南 1 QoS说明policy-name 最多配置 100 个。策略点 **node** node-id 最多配置 200 个。map-instance-id 配置的必须与 node-id 一致。步骤 **3** 执行命令 **if-match acl** **name** acl-name ，设置 IP 地址匹配条件。步骤 **4** 执行命令 **apply output-interface** { interface-name | interface-type interfacenumber } ，设置报文重定向出接口。说明仅支持 P2P TE 隧道，且存在联动删除，即隧道类型发生变化时，配置从 policy 下删除。tunnel-name 的格式是：          - **Tunnel** interface-number ， interface-number 格式是 x/y/z 。          - **Tunnel** tunnel-number ， tunnel-number 是整数形式，取值范围是 0 ～ 4294967295 。步骤 **5** 执行命令 **commit** ，提交配置。**----**结束##### ******* 应用策略路由 背景信息策略路由需要应用在目标报文到达路由器的入接口上。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **interface** interface-type interface-number ，进入接口视图。步骤 **3** 执行命令 **ip policy-based-route** policy-name ，在接口上使能策略路由。步骤 **4** 执行命令 **commit** ，提交配置。**----**结束##### ******* 检查配置结果可以查看策略路由的配置信息。##### 前提条件已经完成策略路由的所有配置。##### 操作步骤步骤 **1** 使用 **display ip policy-based-route** 命令查看已使能的策略。步骤 **2** 使用 **display policy-based-route** [ policy-name ] 命令查看已创建的策略内容。**----**结束文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 153HUAWEI NetEngine40E配置指南 1 QoS#### 1.6.4 配置举例##### ******* 配置基于重定向到 MPLS-TE P2P 隧道的策略路由示例本举例介绍配置基于重定向到 MPLS-TE P2P 隧道的策略路由示例。##### 组网需求如 图 **1-19** 所示，在 DeviceA 上定义一条名为 aaa 的策略路由，所有从 GE1/0/0 接收的源地址为 ***********/24 的报文通过 Tunnel30 发送，从 GE2/0/0 接收的源地址为***********/24 的报文通过 Tunnel40 发送。其中， DeviceA 分别和 DeviceB 、 DeviceC 直连。说明本例中 Interface1 ， Interface2 分别代表 GE1/0/0 ， GE2/0/0 。图 **1-19** 配置基于重定向到 MPLS-TE P2P 隧道的策略路由组网图##### 配置思路基于源地址的策略路由配置思路如下：1. 配置 DeviceA 、 DeviceB 、 DeviceC 、 DeviceD 接口的 IP 地址。2. 分别配置 DeviceB 和 DeviceC 到 DeviceD 的路由。3. 定义 ACL 。4. 定义策略路由的规则和动作，在接口上使能策略路由。##### 数据准备为完成此配置举例，需准备如下的数据：文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 154HUAWEI NetEngine40E配置指南 1 QoS       - ACL 编号及规则       - 策略路由的名称       - 策略路由执行动作所使用的出接口或下一跳地址##### 操作步骤步骤 **1** 配置 DeviceA 、 DeviceB 、 DeviceC 、 DeviceD 接口的 IP 地址。（略）步骤 **2** 分别配置 DeviceB 和 DeviceC 到 DeviceD 的路由。（略）步骤 **3** 配置 DeviceA ，定义 ACL# 定义访问控制列表， ACL 3001 匹配源地址为 *************/24** 的报文， ACL 3002 匹配源地址为 *************/24** 的报文。[ ~ DeviceA] **acl number 3001**[*DeviceA-acl4-advance-3001] **rule 5 permit ip source *********** ***********[*DeviceA-acl4-advance-3001] **commit**[ ~ DeviceA-acl4-advance-3001] **quit**[ ~ DeviceA] **acl number 3002**[*DeviceA-acl4-advance-3002] **rule 5 permit ip source *********** ***********[*DeviceA-acl4-advance-3002] **commit**[ ~ DeviceA-acl4-advance-3002] **quit**步骤 **4** 配置 DeviceA ，定义策略路由的规则和动作，在接口上使能策略路由# 定义 5 号节点，使源地址为 *************/24** 的报文被发往 **Tunnel30** 。[ ~ DeviceA] **interface Tunnel30**[*DeviceA] **quit**[*DeviceA] **policy-based-route aaa permit node 5**[*DeviceA-policy-based-route-aaa-5] **if-match acl name a3001**[*DeviceA-policy-based-route-aaa-5] **apply output-interface Tunnel30**[*DeviceA-policy-based-route-aaa-5] **commit**[ ~ DeviceA-policy-based-route-aaa-5] **quit**# 定义 10 号节点，使源地址为 *************/24** 的报文被发往 **Tunnel40** 。[ ~ DeviceA] **interface Tunnel40**[*DeviceA] **quit**[*DeviceA] **policy-based-route aaa permit node 10**[*DeviceA-policy-based-route-aaa-10] **if-match acl name a3002**[*DeviceA-policy-based-route-aaa-10] **apply output-interface Tunnel40**[*DeviceA-policy-based-route-aaa-10] **commit**[ ~ DeviceA-policy-based-route-aaa-10] **quit**# 在 GE1/0/0 上应用定义的策略 aaa ，处理此接口接收的报文。[ ~ DeviceA] **interface gigabitethernet 1/0/0**[ ~ DeviceA-GigabitEthernet1/0/0] **ip address *********** ***************[*DeviceA-GigabitEthernet1/0/0] **ip policy-based-route aaa**[*DeviceA-GigabitEthernet1/0/0] **undo shutdown**[*DeviceA-GigabitEthernet1/0/0] **commit**[ ~ DeviceA-GigabitEthernet1/0/0] **quit**# 在 GE2/0/0 上应用定义的策略 aaa ，处理此接口接收的报文。[ ~ DeviceA] **interface gigabitethernet 2/0/0**[ ~ DeviceA-GigabitEthernet2/0/0] **ip address *********** ***************[*DeviceA-GigabitEthernet2/0/0] **ip policy-based-route aaa**[*DeviceA-GigabitEthernet2/0/0] **undo shutdown**[*DeviceA-GigabitEthernet2/0/0] **commit**[ ~ DeviceA-GigabitEthernet2/0/0] **quit**[ ~ DeviceA] **quit**文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 155HUAWEI NetEngine40E配置指南 1 QoS步骤 **5** 验证配置结果执行命令 **display ip policy-based-route** 命令查看已使能的策略。<DeviceA> **display ip policy-based-route**policy Name           Interfaceaaa             GigabitEthernet1/0/0aaa             GigabitEthernet2/0/0执行命令 **display policy-based-route** 命令查看已创建的策略内容。<DeviceA> **display policy-based-route**----------------------------------------------------User Defined policy-based-route Policy Information:----------------------------------------------------Total: 100 Used: 1 Free: 99Policy: aaaNode: 5   MapInstance: 5if-match acl name a3001apply output-interface Tunnel30Node: 10  MapInstance: 10if-match acl name a3002apply output-interface Tunnel40**----**结束##### 配置文件       - DeviceA 的配置文件#sysname DeviceA#acl number 3001rule 5 permit ip source *********** *********acl number 3002rule 5 permit ip source *********** *********#interface Tunnel30#interface Tunnel40#policy-based-route aaa permit node 5 map-instance 5if-match acl name a3001apply output-interface Tunnel30policy-based-route aaa permit node 10 map-instance 10if-match acl name a3002apply output-interface Tunnel40#interface GigabitEthernet1/0/0undo shutdownip address *********** *************ip policy-based-route aaa#interface GigabitEthernet2/0/0undo shutdownip address *********** *************ip policy-based-route aaa#return       - DeviceB 的配置文件#sysname DeviceB#interface GigabitEthernet1/0/0undo shutdownip address *********** *************#文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 156HUAWEI NetEngine40E配置指南 1 QoSip route-static ******** ************* ***********#return       - DeviceC 的配置文件#sysname DeviceC#interface GigabitEthernet1/0/0undo shutdownip address *********** *************#ip route-static ******** ************* ***********#return### 1.7 QPPB 配置介绍了 QPPB 的基本原理、配置过程和配置举例。#### 1.7.1 QPPB 概述应用 QPPB 技术可以由 BGP 路由发送者通过设置 BGP 属性预先对路由进行分类。在部署大型复杂组网环境中，需要执行大量的复杂流分类，而且无法按照团体属性、ACL 、 Prefix 或 AS-Path 对报文进行分类。在网络结构不稳定，需经常变化网络结构时，配置修改路由分类策略的工作量非常大甚至难以实施。在这种情况下，可以通过部署 QPPB 减少配置修改的工作量，只需要修改 BGP 路由发送者上的路由策略就可以满足需求。QPPB （ Qos Policy Propagation Through the Border Gateway Protocol ）是通过 BGP传播 QoS 策略的简称，优势是通过 BGP 路由发送者设置 BGP 属性，预先对路由进行分类； BGP 路由接收者可以依据 BGP 路由发送者设置属性对 BGP 路由应用不同的本地 QoS策略。QPPB 实现机制如下：       - 在 BGP 路由发送端，首先通过匹配路由策略，为发送到路由接收端的不同路由信息设置不同的 BGP 路由属性，包括 AS_PATH 、团体属性、扩展团体属性等。       - 在 BGP 路由接收端，主要有以下流程：a. 根据接收到的 BGP 路由属性信息，包括 AS_PATH 、团体属性、扩展团体属性等，通过匹配路由接收策略，对匹配的 BGP 路由设置关联的 QoS 策略 ID （ QoSLocal-ID ）或 IP 优先级。b. 报文转发时，根据关联的 QoS 策略 ID 使用不同的流行为。c. 创建 QPPB 本地策略，为 BGP 路由设置关联的 QoS 策略。d. 在接口应用，对于所有符合匹配规则的报文实施 QPPB 本地策略。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 157HUAWEI NetEngine40E配置指南 1 QoS#### 1.7.2 QPPB 配置注意事项##### 特性限制表 **1-20** 本特性的使用限制|特性限制|系列|涉及产品||---|---|---||全局复杂流支持源和目的qos-local-id重定向不支持<br>LPUF-50/LPUF-50-L/LPUI-21-L/LPUI-51-L/LPUF-51/<br>LPUF-51-B/LPUI-51/LPUI-51-B/LPUS-51/LPUF-101/<br>LPUF-101-B/LPUI-101/LPUI-101-B/LPUS-101单板。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||接口上复杂流支持源和目的qos-local-id不支持<br>LPUF-50/LPUF-50-L/LPUI-21-L/LPUI-51-L/LPUF-51/<br>LPUF-51-B/LPUI-51/LPUI-51-B/LPUS-51/LPUF-101/<br>LPUF-101-B/LPUI-101/LPUI-101-B/LPUS-101单板。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||接口复杂流不支持outbound方向同时匹配源和目的<br>qos-local-id。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 158HUAWEI NetEngine40E配置指南 1 QoS|特性限制|系列|涉及产品||---|---|---||全局复杂流不支持outbound方向同时匹配源和目的<br>qos-local-id。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||当QPPB流动作配置为remark dscp时，仅修改报文<br>DSCP值但是不按照修改后的DSCP值入队列。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK|#### 1.7.3 配置全局 QPPB##### 应用环境配置全局 QPPB 可以对不同接口接收的同一类型报文实现相同的行为策略。如 图 **1-20** 所示， Device A 可以从多个接口接收需要转发给 Device D 的具有某一相同属性的报文。客户希望对拥有此相同属性的报文应用相同的流策略，对其进行 QoS 调整。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 159HUAWEI NetEngine40E配置指南 1 QoS图 **1-20** 配置全局 QPPB 应用组网图##### 前置任务在配置全局 QPPB 之前，需要完成以下任务：       - 配置 BGP 的基本功能       - 配置 BGP 发布的本地网络路由       - 配置建立 BGP 连接所使用的接口##### 1.7.3.1 配置 BGP 路由发送端的路由发布策略配置发送端的路由策略信息。##### 背景信息在 BGP 路由的发送端进行如下配置。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** （可选）配置基本 ACL 规则。1. 执行命令 **acl** { **name** basic-acl-name { **basic** | [ **basic** ] **number** basic-aclnumber } | [ **number** ] basic-acl-number } [ **match-order** { **config** | **auto** } ] ，创建基本 ACL ，并进入基本 ACL 视图。2. 执行命令 **rule** [ rule-id ] { **deny** | **permit** } [ **fragment-type** { **fragment** | **non-****fragment** | **non-subseq** | **fragment-subseq** | **fragment-spe-first** } | **source**{ source-ip-address { source-wildcard | **0** } | **any** } | **time-range** time-name |**vpn-instance** vpn-instance-name ] [*] ，配置基本 ACL 规则。对于报文是否匹配 ACL 规则，采用如下策略：       - 如果匹配上 ACL 规则，且 ACL 规则的动作是 permit ，则报文会被命中。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 160HUAWEI NetEngine40E配置指南 1 QoS       - 如果匹配上 ACL 规则，且 ACL 规则的动作是 deny ，则报文会被丢弃。       - 如果没有匹配上 ACL 规则，则报文不会被命中，只进行正常转发。       - 如果引用的 ACL 规则不存在，或者 ACL 存在但是 ACL 中没有定义规则，则报文不会被命中，只进行正常转发。步骤 **3** 执行命令 **quit** ，退回到系统视图。步骤 **4** 执行命令 **route-policy** route-policy-name matchMode **node** node ，创建路由策略的节点，并进入路由策略视图。步骤 **5** 选择执行下列命令，配置路由策略中的匹配原则。       - 如果匹配 ACL ，执行命令 **if-match acl** { acl-number | acl-name } 。说明路由策略只支持匹配 2000 ～ 2999 的基本 acl 。       - 如果匹配 BGP 路由信息的 AS 路径列表，执行命令 **if-match as-path-filter** { aspath-filter-number | as-path-filter-name } &<1-16> 。       - 如果匹配 BGP 路由信息的团体属性列表，执行命令 **if-match community-filter**{ basic-comm-filter-num [ **whole-match** ] | adv-comm-filter-num }* &<1-16>或 **if-match community-filter** comm-filter-name [ **whole-match** ] 。       - 如果匹配路由信息的路由权值，执行命令 **if-match cost** cost 。       - 如果匹配地址前缀列表，执行命令 **if-match ip-prefix** ip-prefix 。步骤 **6** 选择执行下列命令，通过匹配路由策略为 BGP 路由设置路由属性。       - 如果设置 AS_Path 属性，执行命令 **apply as-path** as-number &<1-128>[ **additive** ] 。       - 如果设置团体属性，执行命令 **apply community** { [ community-number |aa:nn ] &<1-32> | **internet** | **no-advertise** | **no-export** | **no-export-****subconfed** } * [ **additive** ] 。       - 如果设置路由信息的路由权值，执行命令 **apply cost** { [ **+** | **-** ] cost | **inherit** } 。AS_Path 、团体属性、扩展团体属性是为匹配上路由策略的路由设置不同的 BGP 路由属性，配置其中一种既可。步骤 **7** 执行命令 **quit** ，退回到系统视图。步骤 **8** 执行命令 **bgp** as-number ，进入 BGP 视图。步骤 **9** 执行命令 **peer** { ipv4-address | group-name } **route-policy** route-policy-name**export** ，配置向对等体发布的路由应用路由策略。说明在应用路由策略前请确认 BGP 的对等体已经建立。步骤 **10** 执行命令 **peer** peerIpv4Addr **advertise-community** ，将团体属性发布给对等体。步骤 **11** 执行命令 **commit** ，提交配置。**----**结束##### ******* 配置接收端应用路由策略配置接收端的路由策略，定义规则并应用规则。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 161HUAWEI NetEngine40E配置指南 1 QoS##### 背景信息在 BGP 路由的接收端进行如下配置。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **route-policy** route-policy-name matchMode **node** node ，创建路由策略的节点，并进入路由策略视图。步骤 **3** 选择执行下列命令，配置接收端路由接收策略匹配规则。       - 如果匹配 BGP 路由信息的 AS 路径列表，执行命令 **if-match as-path-filter** aspath-acl-number &<1-16> 。       - 如果匹配 BGP 路由信息的团体属性列表，执行命令 **if-match community-filter**{ basic-comm-filter-num [ **whole-match** ] | ext-comm-filter-num }&<1-16> 。       - 如果匹配路由信息的路由权值，执行命令 **if-match cost** value 。说明BGP 路由接收的路由属性必须为 BGP 路由发送端通告的路由属性。步骤 **4** 选择执行下列命令，配置匹配路由策略的关联属性：       - 执行命令 **apply qos-local-id** qos-local-id ，对匹配的路由策略，设置关联的 QoS策略 ID 。说明在路由策略中 **apply qos-local-id** qos-local-id 关联 QoS 策略 ID 被应用于 QPPB 时，所配置的ID 不应该超出 QPPB 策略下 **qos-local-id** qos-local-id **behavior** behavior-name 配置的 QoS策略 ID 范围。       - 执行命令 **apply ip-precedence** ip-precedence ，对匹配的路由策略，设置关联的IP 优先级。一个路由策略（ route-policy ）由多个节点（ node ）构成。一个节点包括多个 **if-****match** 和 **apply** 子句。 **if-match** 子句用来定义该节点的匹配条件， **apply** 子句用来定义通过过滤的路由行为。在一个节点中，可以同时配置多个 **if-match** 匹配规则，这些过滤规则之间的关系是逻辑“与”，即所有 **if-match** 子句都必须匹配，才能通过该路由策略。路由策略节点间的过滤关系是逻辑“或”，即只要通过了一个节点的过滤，就可通过该路由策略。如果没有通过任何一个节点的过滤，路由信息将无法通过该路由策略。步骤 **5** 执行命令 **quit** ，退回到系统视图。步骤 **6** 执行命令 **bgp** as-number ，启动 BGP ，进入 BGP 视图。步骤 **7** 执行命令 **peer** ipv4-address **route-policy** route-policy-name **import** ，对从对等体（路由发送端）来的路由应用接收端路由策略。说明在应用路由策略前请确认 BGP 的对等体已经建立。步骤 **8** 执行命令 **ipv6-family** **unicast** ，进入 BGP-IPv6 单播地址族视图。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 162HUAWEI NetEngine40E配置指南 1 QoS步骤 **9** 执行命令 **ipv6 qppb** ，使能 BGP 的邻居入口策略支持 IPv6 QPPB 属性功能。说明配置本步骤可以使下发给 RM 的 BGP IPv6 路由携带 Qos 属性， BGP IPv6 路由支持流量统计等功能。步骤 **10** 执行命令 **commit** ，提交配置。**----**结束##### ******* 配置全局应用流策略并使能全局 QPPB为实现全局 QPPB 功能，必须先配置全局应用流策略。##### 背景信息为实现全局 QPPB 功能，必须先配置全局应用流策略，且该流策略定义的流分类中需要配置基于 QoS 策略 ID 的匹配规则。流策略相关配置可参见 **1.5** 基于类的 **QoS** 配置 章节。VS 模式下，该配置仅在 Admin VS 支持。##### 操作步骤步骤 **1** 定义基于三层 / 四层信息的流分类1. 执行命令 **system-view** ，进入系统视图。2. 执行命令 **traffic classifier** classifier-name [ **operator** { **and** | **or** } ] ，定义流分类并进入流分类视图。3. 执行命令 **if-match qos-local-id** qos-local-id ，定义基于源或目的 QoS 策略 ID 的匹配规则。4. 执行命令 **if-match qos-local-id** **source** source-local-id **destination**destination-local-id ，定义基于源和目的 QoS 策略 ID 的匹配规则。5. 执行命令 **commit** ，提交配置。步骤 **2** 定义流行为并配置流动作具体配置请参考 **1.5.3.2** 定义流行为并配置动作 。步骤 **3** 定义流量策略并在策略中为类指定行为具体配置请参考 **1.5.3.3** 定义流量策略 。步骤 **4** 执行命令 **quit** ，退出到系统视图。步骤 **5** 执行命令 **traffic-policy** policy-name { **inbound** | **outbound** } **global-acl** ，配置全局应用流策略。步骤 **6** 执行命令 **qppb qos-local-id** { **source** | **destination** | **both inbound** } ，使能全局QPPB 。步骤 **7** 执行命令 **commit** ，提交配置。**----**结束文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 163HUAWEI NetEngine40E配置指南 1 QoS#### 1.7.4 配置基于源的 QPPB配置基于源的 QPPB 策略对来自不同发送端的路由进行区别对待，实现差异的行为策略。##### 应用环境用配置 QPPB 特性的方式通过 BGP 传播 QoS 策略同时适用于 IBGP 和 EBGP ，可以在同一个自治系统内部或者不同的自治系统之间进行配置。如 图 **1-21** 所示，运营商 B 的网络 (AS200) 和运营商 C 的网络 (AS300) 到达运营商 D 的网络(AS400) 的流量需要经过运营商 A 的网络 (AS100) ，运营商 B 和运营商 C 是 BGP 路由的发送方，运营商 A 是 BGP 路由的接收方，根据运营商 A 和运营商 B 、运营商 C 签署的流量控制策略，运营商 A 需要对来自运营商 B 和运营商 C 流经的流量进行限速。运营商 B 和运营商 C 向运营商 A 通告带有团体属性的 BGP 路由，运营商 A 收到后，通过匹配 BGP 团体列表、 ACL 、 BGP AS path list ，为 BGP 路由设置关联的流行为和 QoS 策略ID ，在流量的接口上使能，所有流经运营商 A 的流量就会应用相应的 QPPB 本地策略。设备支持在流量的上行或下行部署基于源的 QPPB 特性。图 **1-21** 配置基于源的 QPPB 应用组网图##### 前置任务在配置 QPPB 之前，需要完成以下任务：       - 配置 BGP 的基本功能       - 配置 BGP 发布的本地网络路由       - 配置建立 BGP 连接所使用的接口##### 1.7.4.1 配置路由发送端的路由策略配置发送端的路由策略信息。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 164HUAWEI NetEngine40E配置指南 1 QoS##### 背景信息在 BGP 路由的发送端进行如下配置。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** （可选）配置基本 ACL 规则。1. 执行命令 **acl** { **name** basic-acl-name { **basic** | [ **basic** ] **number** basic-aclnumber } | [ **number** ] basic-acl-number } [ **match-order** { **config** | **auto** } ] ，创建基本 ACL ，并进入基本 ACL 视图。2. 执行命令 **rule** [ rule-id ] { **deny** | **permit** } [ **fragment-type** { **fragment** | **non-****fragment** | **non-subseq** | **fragment-subseq** | **fragment-spe-first** } | **source**{ source-ip-address { source-wildcard | **0** } | **any** } | **time-range** time-name |**vpn-instance** vpn-instance-name ] [*] ，配置基本 ACL 规则。对于报文是否匹配 ACL 规则，采用如下策略：       - 如果匹配上 ACL 规则，且 ACL 规则的动作是 permit ，则报文会被命中。       - 如果匹配上 ACL 规则，且 ACL 规则的动作是 deny ，则报文会被丢弃。       - 如果没有匹配上 ACL 规则，则报文不会被命中，只进行正常转发。       - 如果引用的 ACL 规则不存在，或者 ACL 存在但是 ACL 中没有定义规则，则报文不会被命中，只进行正常转发。步骤 **3** 执行命令 **quit** ，退回到系统视图。步骤 **4** 执行命令 **route-policy** route-policy-name matchMode **node** node ，创建路由策略的节点，并进入路由策略视图。步骤 **5** 选择执行下列命令，配置路由策略中的匹配原则。       - 如果匹配 ACL ，执行命令 **if-match acl** { acl-number | acl-name } 。说明路由策略只支持匹配 2000 ～ 2999 的基本 acl 。       - 如果匹配 BGP 路由信息的 AS 路径列表，执行命令 **if-match as-path-filter** { aspath-filter-number | as-path-filter-name } &<1-16> 。       - 如果匹配 BGP 路由信息的团体属性列表，执行命令 **if-match community-filter**{ basic-comm-filter-num [ **whole-match** ] | adv-comm-filter-num }* &<1-16>或 **if-match community-filter** comm-filter-name [ **whole-match** ] 。       - 如果匹配路由信息的路由权值，执行命令 **if-match cost** cost 。       - 如果匹配地址前缀列表，执行命令 **if-match ip-prefix** ip-prefix 。步骤 **6** 选择执行下列命令，通过匹配路由策略为 BGP 路由设置路由属性。       - 如果设置 AS_Path 属性，执行命令 **apply as-path** as-number &<1-128>[ **additive** ] 。       - 如果设置团体属性，执行命令 **apply community** { [ community-number |aa:nn ] &<1-32> | **internet** | **no-advertise** | **no-export** | **no-export-****subconfed** } * [ **additive** ] 。       - 如果设置路由信息的路由权值，执行命令 **apply cost** { [ **+** | **-** ] cost | **inherit** } 。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 165HUAWEI NetEngine40E配置指南 1 QoSAS_Path 、团体属性、扩展团体属性是为匹配上路由策略的路由设置不同的 BGP 路由属性，配置其中一种既可。步骤 **7** 执行命令 **quit** ，退回到系统视图。步骤 **8** 执行命令 **bgp** as-number ，进入 BGP 视图。步骤 **9** 执行命令 **peer** { ipv4-address | group-name } **route-policy** route-policy-name**export** ，配置向对等体发布的路由应用路由策略。说明在应用路由策略前请确认 BGP 的对等体已经建立。步骤 **10** 执行命令 **peer** peerIpv4Addr **advertise-community** ，将团体属性发布给对等体。步骤 **11** 执行命令 **commit** ，提交配置。**----**结束##### ******* 配置接收端应用路由策略配置接收端的路由策略，定义规则并应用规则。##### 背景信息在 BGP 路由的接收端进行如下配置。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **route-policy** route-policy-name matchMode **node** node ，创建路由策略的节点，并进入路由策略视图。步骤 **3** 选择执行下列命令，配置接收端路由接收策略匹配规则。       - 如果匹配 BGP 路由信息的 AS 路径列表，执行命令 **if-match as-path-filter** aspath-acl-number &<1-16> 。       - 如果匹配 BGP 路由信息的团体属性列表，执行命令 **if-match community-filter**{ basic-comm-filter-num [ **whole-match** ] | ext-comm-filter-num }&<1-16> 。       - 如果匹配路由信息的路由权值，执行命令 **if-match cost** value 。说明BGP 路由接收的路由属性必须为 BGP 路由发送端通告的路由属性。步骤 **4** 选择执行下列命令，配置匹配路由策略的关联属性：       - 执行命令 **apply qos-local-id** qos-local-id ，对匹配的路由策略，设置关联的 QoS策略 ID 。说明在路由策略中 **apply qos-local-id** qos-local-id 关联 QoS 策略 ID 被应用于 QPPB 时，所配置的ID 不应该超出 QPPB 策略下 **qos-local-id** qos-local-id **behavior** behavior-name 配置的 QoS策略 ID 范围。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 166HUAWEI NetEngine40E配置指南 1 QoS       - 执行命令 **apply ip-precedence** ip-precedence ，对匹配的路由策略，设置关联的IP 优先级。一个路由策略（ route-policy ）由多个节点（ node ）构成。一个节点包括多个 **if-****match** 和 **apply** 子句。 **if-match** 子句用来定义该节点的匹配条件， **apply** 子句用来定义通过过滤的路由行为。在一个节点中，可以同时配置多个 **if-match** 匹配规则，这些过滤规则之间的关系是逻辑“与”，即所有 **if-match** 子句都必须匹配，才能通过该路由策略。路由策略节点间的过滤关系是逻辑“或”，即只要通过了一个节点的过滤，就可通过该路由策略。如果没有通过任何一个节点的过滤，路由信息将无法通过该路由策略。步骤 **5** 执行命令 **quit** ，退回到系统视图。步骤 **6** 执行命令 **bgp** as-number ，启动 BGP ，进入 BGP 视图。步骤 **7** 执行命令 **peer** ipv4-address **route-policy** route-policy-name **import** ，对从对等体（路由发送端）来的路由应用接收端路由策略。说明在应用路由策略前请确认 BGP 的对等体已经建立。步骤 **8** 执行命令 **ipv6-family** **unicast** ，进入 BGP-IPv6 单播地址族视图。步骤 **9** 执行命令 **ipv6 qppb** ，使能 BGP 的邻居入口策略支持 IPv6 QPPB 属性功能。说明配置本步骤可以使下发给 RM 的 BGP IPv6 路由携带 Qos 属性， BGP IPv6 路由支持流量统计等功能。步骤 **10** 执行命令 **commit** ，提交配置。**----**结束##### ******* 配置接收端流行为对接收端匹配上流分类的流量，提供有区别的流行为服务。##### 背景信息在 BGP 路由的接收端进行如下配置。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **traffic behavior** behavior-name ，定义行为进入流行为视图。步骤 **3** 请根据实际情况进行如下配置。       - 如果配置流量监管动作，执行命令 **car** { **cir** cir-value [ **pir** pir-value ] } [ **cbs** cbsvalue [ **pbs** pbs-value ] ] [ **adjust** adjust-value ] [ **green** { **discard** | **pass**[ **remark dscp** dscp-value | **service-class** class **color** color ] } | **yellow**{ **discard** | **pass** [ **remark dscp** dscp-value | **service-class** class **color** color ] } |**red** { **discard** | **pass** [ **remark dscp** dscp-value | **service-class** class **color**color ] } ] [*] [ **summary** ] [ **color-aware** ] [ **limit-type pps** ] 。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 167HUAWEI NetEngine40E配置指南 1 QoS       - 如果重新标记 IP 报文的 DSCP 值，执行命令 **remark** [ **ipv6** ] **dscp** { dscp-value |**af11** | **af12** | **af13** | **af21** | **af22** | **af23** | **af31** | **af32** | **af33** | **af41** | **af42** | **af43** |**cs1** | **cs2** | **cs3** | **cs4** | **cs5** | **cs6** | **cs7** | **default** | **ef** } 。       - 如果重新设置 IP 报文的优先级，执行命令 **remark ip-precedence** ipprecedence 。       - 如果设置满足规则的所有报文通过，执行命令 **permit** 。       - 如果设置满足规则的所有报文禁止通过，执行命令 **deny** 。       - 如果设置对指定服务等级的报文标记颜色，执行命令 **service-class** service-class**color** color 。       - 如果配置用户队列的调度参数对用户业务进行 HQoS 调度，执行命令 **user-queue****cir** cir-value [ [ **pir** pir-value ] | [ **flow-queue** flow-queue-name ] | [ **flow-****mapping** mapping-name ] | [ **user-group-queue** group-name ] | [ **service-****template** service-template-name ] ]*       - 如果将报文重定向到指定的 VPN 组，执行命令 **redirect vpn-group** vpn-groupname 。       - 如果设置级联下一个流策略，执行命令 **traffic-policy** policy-name 。说明在流行为视图中同时执行命令 **hierarchical-car enable** ，可以在复杂流分类级联策略场景中使能层次化 CAR 动作。–配置级联流策略会造成设备转发性能下降。–接口流量匹配级联的流策略时：#### ▪ 转发行为按照级联的流策略行为执行 ▪ 非重复的流策略行为可以分别执行 ▪ 重复的流策略行为按照级联的流策略行为执行–一个接口的一个方向上只能应用一个流量策略。如果该流策略级联了另外一个流策略，那么这个接口该方向上相当于同时应用了多个流量策略。– 流策略接口上应用时所指定的 inbound 或 outbound 方向， link-layer 、 mplslayer 和 all-layer 等参数被级联的流策略继承。– 两级 ACL 的流行为都是 service-class 时，以第一级 service-class 优先生效；但是如果第一级 service-class 带有 no-remark ，还是以第二级 service-class 优先生效。       - 如果配置提升流行为动作的优先级，执行命令 **increase-priority** 。设备上同时配置了 BGP Flow Specification 和 QPPB 的流动作，如果需要 QPPB 的流动作优先生效，可以执行该命令实现。步骤 **4** 执行命令 **commit** ，提交配置。**----**结束##### 1.7.4.4 配置 QPPB 本地策略通过匹配 BGP 团体列表、 ACL 、 BGP AS path list ，为 BGP 路由设置关联的 QoS 策略，在流量的入接口和出接口上使能 QPPB 后，流量就会应用相应的 QoS 策略。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 168HUAWEI NetEngine40E配置指南 1 QoS##### 背景信息在 BGP 路由的接收端进行如下配置。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **qppb local-policy** policy-name ，创建 QPPB 策略，并进入 QPPB 策略视图。步骤 **3** ( 可选 ) 执行命令 **statistics enable** ，使能 QPPB 的统计功能。步骤 **4** （可选）执行命令 **service-class outbound enable** ，使能 QPPB 本地策略下行 serviceclass 功能步骤 **5** 执行命令 **qos-local-id** qos-local-id **behavior** behavior-name ，将 QoS 策略 ID 和behavior 绑定。步骤 **6** 执行命令 **commit** ，提交配置。**----**结束##### 1.7.4.5 在接口下应用 QPPB在接口应用 QPPB 策略，对于所有符合匹配规则的报文实施流行为。##### 背景信息可配置基于流量的上行或者下行的 QPPB ，达到相同的实现效果。QPPB 中的 BGP 路由只针对公网 BGP 路由（私网下的 QPPB 扩展为 L3VPN 中应用QPPB ）。在 BGP 路由的接收端进行如下配置。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** （可选）执行命令 **qos match-type qos-local-id enhance** ，使能 qos-local-id 增强模式。报文上行方向应用复杂流分类对其执行 remark qos-local-id 动作或者在 Route-Policy 视图下为报文设置 qos-local-id ，叠加 CGN 或 IPsec 业务后，报文被引流至增值业务板进行处理。这种场景下，如果需要在接口下行方向匹配 qos-local-id 信息对报文应用 QPPB策略，需要使能 qos-local-id 增强模式，否则无法匹配 qos-local-id 信息。步骤 **3** 执行命令 **interface** interface-type interface-number ，进入接口视图。步骤 **4** 应用 QPPB 策略，请根据实际情况选择如下命令行。       - 配置基于上行 QoS 策略的 QPPB ，执行命令 **qppb-policy** policy-name **source****inbound** ，在接口入方向应用 QPPB 策略。       - 配置基于下行 QoS 策略的 QPPB ，执行命令 **qppb-policy** **qos-local-id** **source****inbound** 和 **qppb-policy** policy-name **outbound** ，分别在入接口和出接口应用QPPB 策略。       - 配置基于 IP 优先级的 QPPB ，执行命令 **qppb-policy ip-precedence** **source** ，根据路由策略指定流行为，并对源地址实施 QPPB 动作。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 169HUAWEI NetEngine40E配置指南 1 QoS说明**source** 关键字表示所发布的携带路由属性的路由方向作为流量的源地址方向。**----**结束##### ******* 检查配置结果QPPB 功能配置成功后，可以查看 QPPB 信息等内容。##### 背景信息在所有视图下执行下面的 **display** 命令，可以查看 QPPB 的运行信息，检查配置的效果。运行信息的详细解释请参见《 HUAWEI NetEngine40E 路由器 命令参考》的“ QoS 命令”。##### 操作步骤步骤 **1** 使用 **display qppb local-policy configuration** policy-name 命令查看 qppb 本地策略的配置。步骤 **2** 使用 **display qppb local-policy statistics** **interface** interface-type interfacenumber [ **qos-local-id** qos-local-id ] { **inbound** | **outbound** } 命令查看指定 qppb 本地策略的统计信息。**----**结束#### 1.7.5 配置基于目的的 QPPB配置基于目的的 QPPB 策略对去往不同接收端的路由进行区别对待，实现差异的行为策略。##### 应用环境用配置 QPPB 特性的方式通过 BGP 传播 QoS 策略同时适用于 IBGP 和 EBGP ，可以在同一个自治系统内部或者不同的自治系统之间进行配置。如 图 **1-22** 所示，运营商 B 的网络 (AS200) 和运营商 C 的网络 (AS300) 到达运营商 D 的网络（ AS400 ）的流量需要经过运营商 A 的网络 (AS100) ，运营商 B 和运营商 C 是 BGP 路由的发送方，运营商 A 是 BGP 路由的接收方，根据运营商 A 和运营商 D 签署的流量控制策略，运营商 A 需要对所有流入运营 D 的流量都进行限速。运营商 B 和运营商 C 向运营商 A 通告带有团体属性的 BGP 路由，运营商 A 收到后，通过匹配 BGP 团体列表、 ACL 、 BGP AS path list ，为 BGP 路由设置关联的流行为和 QoS 策略ID ，在流量的接口上使能，所有流经运营商 A 的流量就会应用相应的 QPPB 本地策略。设备支持在流量的上行或下行部署基于目的的 QPPB 特性。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 170HUAWEI NetEngine40E配置指南 1 QoS图 **1-22** 配置基于目的的 QPPB 应用组网图##### 前置任务在配置 QPPB 之前，需要完成以下任务：       - 配置 BGP 的基本功能       - 配置 BGP 发布的本地网络路由       - 配置建立 BGP 连接所使用的接口##### ******* 配置 BGP 路由发送端的路由发布策略配置发送端的路由策略信息。##### 背景信息在 BGP 路由的发送端进行如下配置。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** （可选）配置基本 ACL 规则。1. 执行命令 **acl** { **name** basic-acl-name { **basic** | [ **basic** ] **number** basic-aclnumber } | [ **number** ] basic-acl-number } [ **match-order** { **config** | **auto** } ] ，创建基本 ACL ，并进入基本 ACL 视图。2. 执行命令 **rule** [ rule-id ] { **deny** | **permit** } [ **fragment-type** { **fragment** | **non-****fragment** | **non-subseq** | **fragment-subseq** | **fragment-spe-first** } | **source**{ source-ip-address { source-wildcard | **0** } | **any** } | **time-range** time-name |**vpn-instance** vpn-instance-name ] [*] ，配置基本 ACL 规则。对于报文是否匹配 ACL 规则，采用如下策略：       - 如果匹配上 ACL 规则，且 ACL 规则的动作是 permit ，则报文会被命中。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 171HUAWEI NetEngine40E配置指南 1 QoS       - 如果匹配上 ACL 规则，且 ACL 规则的动作是 deny ，则报文会被丢弃。       - 如果没有匹配上 ACL 规则，则报文不会被命中，只进行正常转发。       - 如果引用的 ACL 规则不存在，或者 ACL 存在但是 ACL 中没有定义规则，则报文不会被命中，只进行正常转发。步骤 **3** 执行命令 **quit** ，退回到系统视图。步骤 **4** 执行命令 **route-policy** route-policy-name matchMode **node** node ，创建路由策略的节点，并进入路由策略视图。步骤 **5** 选择执行下列命令，配置路由策略中的匹配原则。       - 如果匹配 ACL ，执行命令 **if-match acl** { acl-number | acl-name } 。说明路由策略只支持匹配 2000 ～ 2999 的基本 acl 。       - 如果匹配 BGP 路由信息的 AS 路径列表，执行命令 **if-match as-path-filter** { aspath-filter-number | as-path-filter-name } &<1-16> 。       - 如果匹配 BGP 路由信息的团体属性列表，执行命令 **if-match community-filter**{ basic-comm-filter-num [ **whole-match** ] | adv-comm-filter-num }* &<1-16>或 **if-match community-filter** comm-filter-name [ **whole-match** ] 。       - 如果匹配路由信息的路由权值，执行命令 **if-match cost** cost 。       - 如果匹配地址前缀列表，执行命令 **if-match ip-prefix** ip-prefix 。步骤 **6** 选择执行下列命令，通过匹配路由策略为 BGP 路由设置路由属性。       - 如果设置 AS_Path 属性，执行命令 **apply as-path** as-number &<1-128>[ **additive** ] 。       - 如果设置团体属性，执行命令 **apply community** { [ community-number |aa:nn ] &<1-32> | **internet** | **no-advertise** | **no-export** | **no-export-****subconfed** } * [ **additive** ] 。       - 如果设置路由信息的路由权值，执行命令 **apply cost** { [ **+** | **-** ] cost | **inherit** } 。AS_Path 、团体属性、扩展团体属性是为匹配上路由策略的路由设置不同的 BGP 路由属性，配置其中一种既可。步骤 **7** 执行命令 **quit** ，退回到系统视图。步骤 **8** 执行命令 **bgp** as-number ，进入 BGP 视图。步骤 **9** 执行命令 **peer** { ipv4-address | group-name } **route-policy** route-policy-name**export** ，配置向对等体发布的路由应用路由策略。说明在应用路由策略前请确认 BGP 的对等体已经建立。步骤 **10** 执行命令 **peer** peerIpv4Addr **advertise-community** ，将团体属性发布给对等体。步骤 **11** 执行命令 **commit** ，提交配置。**----**结束##### ******* 配置接收端应用路由策略配置接收端的路由策略，定义规则并应用规则。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 172
最终截取: #### 1.7.5 配置基于目的的 QPPB


配置基于目的的 QPPB 策略对去往不同接收端的路由进行区别对待，实现差异的行为策
略。

##### 应用环境


用配置 QPPB 特性的方式通过 BGP 传播 QoS 策略同时适用于 IBGP 和 EBGP ，可以在同一
个自治系统内部或者不同的自治系统之间进行配置。


如 图 **1-22** 所示，运营商 B 的网络 (AS200) 和运营商 C 的网络 (AS300) 到达运营商 D 的网络
（ AS400 ）的流量需要经过运营商 A 的网络 (AS100) ，运营商 B 和运营商 C 是 BGP 路由的
发送方，运营商 A 是 BGP 路由的接收方，根据运营商 A 和运营商 D 签署的流量控制策
略，运营商 A 需要对所有流入运营 D 的流量都进行限速。


运营商 B 和运营商 C 向运营商 A 通告带有团体属性的 BGP 路由，运营商 A 收到后，通过匹
配 BGP 团体列表、 ACL 、 BGP AS path list ，为 BGP 路由设置关联的流行为和 QoS 策略
ID ，在流量的接口上使能，所有流经运营商 A 的流量就会应用相应的 QPPB 本地策略。


设备支持在流量的上行或下行部署基于目的的 QPPB 特性。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 170



HUAWEI NetEngine40E
配置指南 1 QoS


图 **1-22** 配置基于目的的 QPPB 应用组网图

##### 前置任务


在配置 QPPB 之前，需要完成以下任务：


       - 配置 BGP 的基本功能


       - 配置 BGP 发布的本地网络路由


       - 配置建立 BGP 连接所使用的接口

