import pymupdf  # PyMuPDF
import pymupdf4llm
import io

def replace_sharp_in_pdf(pdf_path: str) -> bytes:
    """
    读取 pdf_path，把所有文本里的 # 替换为 /#，返回修改后的 PDF 字节流。
    """
    doc = pymupdf.open(pdf_path)

    for page in doc:
        # 1. 读取页面对象的文本字典
        d = page.get_text("dict")

        # 2. 遍历所有文本片段（span）
        for block in d["blocks"]:
            if block["type"] != 0:        # 只处理文本块
                continue
            for line in block["lines"]:
                for span in line["spans"]:
                    txt: str = span["text"]
                    if "#" in txt:
                        new_txt = txt.replace("#", "/#")
                        span["text"] = new_txt

        # 3. 用新文本覆盖原页面
        page.clean_contents()            # 先清理旧的指令流
        page.insert_text(
            (0, 0),                      # 这里只是占位，后面会被覆盖
            "",                          # 空文本
            render_mode=3                # 不可见文字
        )
        # 4. 重新把 dict 写回页面（PyMuPDF 官方推荐方式）
        page.apply_redactions()          # 清空旧内容
        page.insert_textbox(
            rect=page.rect,
            buffer="",
            render_mode=3
        )
        # 5. 重新插入文本
        for block in d["blocks"]:
            if block["type"] != 0:
                continue
            for line in block["lines"]:
                for span in line["spans"]:
                    page.insert_text(
                        point=span["origin"],
                        text=span["text"],
                        fontname="helv",
                        fontsize=span["size"],
                        rotate=0,
                        render_mode=0
                    )

    # 6. 写入内存字节流
    buffer = io.BytesIO()
    doc.save(buffer)
    doc.close()
    buffer.seek(0)
    return buffer.read()

def pdf_bytes_to_markdown(pdf_bytes: bytes) -> str:
    """
    把 PDF 字节流转成 markdown。
    """
    doc = pymupdf.open(stream=pdf_bytes)
    md_text = pymupdf4llm.to_markdown(doc)
    doc.close()
    return md_text


import re

def escape_markdown_hashes_correct(text: str) -> str:
    """
    智能转义 Markdown 文本中的非标题 # 符号。
    
    此函数会正确处理以下情况：
    1. 保留 Markdown 标题（e.g., "# 这是一个标题"）
    2. 保留代码块中的 # （e.g., ```python # 这是代码注释 ```）
    3. 转义所有其他位置的 # （e.g., "A#123", "更多信息请参考#145章节"）
    
    Args:
        text: 待处理的 Markdown 格式字符串。
    
    Returns:
        处理后的 Markdown 字符串。
    """
    
    # 步骤 1: 识别并保护代码块
    # 使用一个独特的占位符来临时替换代码块，避免内部的 # 被处理
    code_block_pattern = re.compile(r'```.*?```', re.DOTALL)
    code_blocks = code_block_pattern.findall(text)
    placeholder_template = "---CODEBLOCK-PLACEHOLDER-{}---"
    
    for i, block in enumerate(code_blocks):
        text = text.replace(block, placeholder_template.format(i), 1)
    
    # 步骤 2: 转义所有非标题的 #
    # Markdown 标题要求 # 后面必须紧跟一个空格，且位于行首。
    # 所以我们只需要转义那些不满足此条件的 #。
    
    # 匹配不在行首，或行首但后面没有空格的 #
    # `(?m)`: 多行模式，使 ^ 匹配每行的开头
    # `(?<!^)`: 负向后行断言，匹配前面不是行首的 # （即行中间的 #）
    # `^#(?!\s)`: 匹配行首的 #，但后面没有空格
    # |: 或
    # 这种组合可以准确地捕获所有非标题的 #
    
    # 修正后的正则表达式，更准确地匹配所有非标题的 #
    # 匹配行中和行尾的 #，或行首但后面没有空格的 #
    non_heading_hash_pattern = re.compile(r'(?m)(?<!^)#|(?m)^#(?!\s)')
    text = non_heading_hash_pattern.sub(r'\#', text)
    
    # 步骤 3: 恢复代码块
    for i, block in enumerate(code_blocks):
        text = text.replace(placeholder_template.format(i), block, 1)
        
    return text


if __name__ == "__main__":
    import fitz
    filename = "2.pdf"
    # doc = pymupdf.open(filename)  # use a Document for subsequent processing
    doc = fitz.open(filename)
    my_headers = doc.get_toc()
    # my_headers = pymupdf4llm.TocHeaders(doc)  # use the table of contents for determining headers
    print(my_headers)
    # this will *NOT* scan the document for font sizes!
    md_text = escape_markdown_hashes_correct(pymupdf4llm.to_markdown(doc, hdr_info=my_headers))
    import pathlib
    pathlib.Path("2.5.md").write_bytes(md_text.encode())
    
    