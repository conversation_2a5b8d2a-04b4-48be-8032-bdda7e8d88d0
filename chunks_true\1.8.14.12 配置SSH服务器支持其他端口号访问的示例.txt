

在本示例中，通过设定 SSH 服务器端的侦听端口号为其他端口号，实现只有合法的用
户才能建立 SSH 连接。

##### 组网需求


SSH 协议的标准侦听端口号为 22 ，如果攻击者不断访问标准端口，将会使带宽和服务
器性能不断下降，从而导致其他正常用户无法访问。


设定 SSH 服务器端的侦听端口号为其他端口号，攻击者并不知道 SSH 侦听端口号的更
改，仍然发送标准端口号 22 的 socket 连接， SSH 服务器检测发现请求连接端口号不是
侦听的端口号，就不建立 socket 连接。


这样只有合法的用户采用 SSH 服务器设定的非标准侦听端口才能建立 socket 连接，进行
SSH 协议的版本号协商、算法协商及会话密钥生成、认证、会话请求、会话阶段等过
程。


图 **1-76** 配置 SSH 服务器支持其他端口号访问组网图


说明


本例中的 Interface1 代表接口 GigabitEthernet0/0/0 。

##### 配置思路


采用如下的思路配置 SSH 服务器支持其他端口号访问的示例：


1. 在 SSH 服务器上配置用户 client001 和 client002 ，分别使用不同的认证方式登录
SSH 服务器。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 330


HUAWEI NetEngine40E
配置指南 1 基础配置


2. 分别在 STelnet 客户端 Client002 和 SSH 服务器端生成本地密钥对，并为用户
client002 绑定 SSH 客户端的 RSA 公钥，实现客户端登录服务器端时，对客户端进行
验证。


3. SSH 服务器端 STelnet 和 SFTP 服务使能。


4. 配置 SSH 用户的服务方式和授权目录。


5. 配置 SSH 服务器侦听端口号，实现客户端以其他端口号访问服务器。


6. 用户 client001 和 client002 分别以 STelnet 和 SFTP 方式登录 SSH 服务器。

##### 数据准备


为完成此配置例，需准备如下的数据：


       - 用户 client001 ，登录验证方式为 password ，以 STelnet 方式登录 SSH 服务器。


       - 用户 client002 ，验证方式为 RSA ，并为其分配公钥 RsaKey001 ，以 SFTP 方式登录
SSH 服务器。


       - SSH 服务器的 IP 地址为 ******** 。


       - SSH 服务器端侦听端口号为 1025 。

##### 操作步骤


步骤 **1** 在服务器端生成本地密钥对


<HUAWEI> **system-view**

[ ~ HUAWEI] **sysname SSH Server**

[*HUAWEI] **commit**

[ ~ SSH Server] **rsa local-key-pair create**
The key name will be: CE1_Host
The range of public key size is (2048, 3072).
NOTE: Key pair generation will take a short while.
Please input the modulus [default = 3072]:3072

[*SSH Server] **commit**


步骤 **2** 配置服务器端 RSA 公钥


说明


SSH 用户主要有 Password 、 RSA 、 password-rsa 、 ECC 、 password-ecc 、 DSA 、 password-dsa 、
SM2 、 password-sm2 或 all 这几种认证方式：


          - 如果 SSH 用户的认证方式为 password 、 password-rsa 、 password-dsa 、 password-sm2 和
password-ecc 时，必须配置同名的 local-user 用户。


          - 如果 SSH 用户的认证方式为 RSA 、 password-rsa 、 DSA 、 password-dsa 、 SM2 、 passwordsm2 、 ECC 、 password-ecc 和 all ，服务器端应保存 SSH 客户端的 RSA 、 DSA 、 SM2 或 ECC 公
钥。


# 客户端生成客户端的本地密钥对。


<HUAWEI> **system-view**

[ ~ HUAWEI] **sysname client002**

[*HUAWEI] **commit**

[ ~ client002] **rsa local-key-pair create**

[*client002] **commit**


# 查看客户端上生成 RSA 公钥。


[ ~ client002] **display rsa local-key-pair public**

=====================================================

Time of Key pair created: 16:38:51 2007/5/25
Key name: client002_Host
Key type: RSA encryption Key


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 331


HUAWEI NetEngine40E
配置指南 1 基础配置


=====================================================

Key code:
3047

0240

BFF35E4B C61BD786 F907B5DE 7D6770C3 E5FD17AB

203C8FCB BBC8FDF2 F7CB674E 519E8419 0F6B97A8

EA91FC4B B9E18836 5E74BFD5 4C687767 A89C6B43

1D7E3E1B

0203

010001
Host public key for PEM format code:
---- BEGIN SSH2 PUBLIC KEY ---AAAAB3NzaC1yc2EAAAADAQABAAAAQQC/815LxhvXhvkHtd59Z3DD5f0XqyA8j8u7
yP3y98tnTlGehBkPa5eo6pH8S7nhiDZedL/VTGh3Z6ica0Mdfj4b
---- END SSH2 PUBLIC KEY ---Public key code for pasting into OpenSSH authorized_keys file :
ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAAAQQC/815LxhvXhvkHtd59Z3DD5f0XqyA8j8u7yP3y98tn
TlGehBkPa5eo6pH8S7nhiDZedL/VTGh3Z6ica0Mdfj4b rsa-key

=====================================================

Time of Key pair created: 16:38:51 2007/5/25
Key name: client002_Server
Key type: RSA encryption Key

=====================================================

Key code:
3067

0260

BCFAC085 49A2E70E 1284F901 937D7B63 D7A077AB

******** 4BCA86C0 4CD18B70 5DFAC9D3 9A3F3E74

9B2AF4CB 69FA6483 E87DA590 7B47721A 16391E27

1C76ABAB 743C568B 1B35EC7A 8572A096 BCA9DF0E

BC89D3DB 5A83698C 9063DB39 A279DD89

0203

010001


# 将客户端上产生的 RSA 公钥传送到服务器端。


[ ~ SSH Server] **rsa peer-public-key RsaKey001**
Enter "RSA public key" view, return system view with "peer-public-key end".

[*SSH Server-rsa-public-key] **public-key-code begin**
Enter "RSA key code" view, return last view with "public-key-code end".

[*SSH Server-rsa-key-code] **3047**

[*SSH Server-rsa-key-code] **0240**

[*SSH Server-rsa-key-code] **BFF35E4B C61BD786 F907B5DE 7D6770C3 E5FD17AB**

[*SSH Server-rsa-key-code] **203C8FCB BBC8FDF2 F7CB674E 519E8419 0F6B97A8**

[*SSH Server-rsa-key-code] **EA91FC4B B9E18836 5E74BFD5 4C687767 A89C6B43**

[*SSH Server-rsa-key-code] **1D7E3E1B**

[*SSH Server-rsa-key-code] **0203**

[*SSH Server-rsa-key-code] **010001**

[*SSH Server-rsa-key-code] **public-key-code end**

[*SSH Server-rsa-public-key] **peer-public-key end**

[*SSH Server-rsa-public-key] **commit**


步骤 **3** 在服务器端创建 SSH 用户


# 配置 VTY 用户界面。


[ ~ SSH Server] **user-interface vty 0 4**

[ ~ SSH Server-ui-vty0-4] **authentication-mode aaa**

[*SSH Server-ui-vty0-4] **protocol inbound ssh**

[*SSH Server-ui-vty0-4] **commit**

[ ~ SSH Server-ui-vty0-4] **quit**


       - 创建 SSH 用户 Client001 。


# 新建用户名为 Client001 的 SSH 用户，且认证方式为 password 。


[ ~ SSH Server] **ssh user client001**

[*SSH Server] **ssh user client001 authentication-type password**

[*SSH Server] **commit**

# 为 SSH 用户 Client001 配置密码。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 332


HUAWEI NetEngine40E
配置指南 1 基础配置


[ ~ SSH Server] **aaa**

[*SSH Server-aaa] **local-user client001 password**
Please configure the password (8-128)
Enter Password:
Confirm Password:


说明


设置的密码必须满足以下要求：


–
密码采取交互式输入，系统不回显输入的密码。


– 输入的密码为字符串形式，区分大小写，长度范围是 8 ～ 16 。输入的密码至少包含两种
类型字符，包括大写字母、小写字母、数字及特殊字符。


–
特殊字符不包括“？”和空格。但是，当输入的密码两端使用双引号时，可在密码中间
输入空格。

#### ▪ 如果使用双引号设置带空格密码，双引号之间不能再使用双引号。 ▪ 如果使用双引号没有设置带空格密码，双引号之间可以再使用双引号。


例如， "Aa 123"45"" 为不合法密码， "Aa123"45"" 为合法密码。


配置文件中将以密文形式体现设置的密码。


[*SSH Server-aaa] **local-user client001 service-type ssh**

[*SSH Server-aaa] **commit**

[ ~ SSH Server-aaa] **quit**


# 配置 Client001 的服务方式为 STelnet 。


[*SSH Server] **ssh user client001 service-type stelnet**


       - 创建 SSH 用户 Client002 。


# 新建用户名为 Client002 的 SSH 用户，且认证方式为 RSA ，并绑定 SSH 客户端 RSA
公钥。


[ ~ SSH Server] **ssh user client002**

[*SSH Server] **ssh user client002 authentication-type rsa**

[*SSH Server] **ssh user client002 assign rsa-key RsaKey001**

[*SSH Server] **commit**


# 配置 Client002 的服务方式为 SFTP ，并为其配置授权目录。


[ ~ SSH Server] **ssh user client002 service-type sftp**

[*SSH Server] **ssh user client002 sftp-directory cfcard:**

[*SSH Server] **commit**


步骤 **4** SSH 服务器端 STelnet 和 SFTP 服务使能


[ ~ SSH Server] **interface LoopBack 0**

[ ~ SSH Server-LoopBack0] **ip address ******** *****************

[*SSH Server-LoopBack0] **quit**

[*SSH Server] **stelnet server enable**

[*SSH Server] **sftp server enable**

[*SSH Server] **ssh server-source** **-i loopback 0**


[*SSH Server] **commit**


步骤 **5** 配置 SSH 服务端新的侦听端口号


[*SSH Server] **ssh server port 1025**


步骤 **6** SSH 客户端连接 SSH 服务器


# 第一次登录，则需要使能 SSH 客户端首次认证功能。


使能客户端 Client001 首次认证功能。


<HUAWEI> **system-view**

[ ~ HUAWEI] **sysname client001**

[*HUAWEI] **commit**

[ ~ client001] **ssh client first-time enable**


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 333


HUAWEI NetEngine40E
配置指南 1 基础配置


[*client001] **commit**


使能客户端 Client002 首次认证功能


[*client002] **ssh client first-time enable**

[*client002] **commit**


# STelnet 客户端用新端口号连接 SSH 服务器。


[ ~ client001] **stelnet ******** 1025**
Please input the username:client001
Trying ******** ...
Press CTRL+K to abort

Connected to ******** ...
The server is not authenticated. Continue to access it?(Y/N):y
Save the server's public key?(Y/N):y
The server's public key will be saved with the name ********. Please wait...
Enter password:


显示登录成功信息如下：


Info: The max number of VTY users is 10, and the number
of current VTY users on line is 1.

<SSH Server>


# SFTP 客户端用新端口号连接 SSH 服务器。


[ ~ client002] **sftp ******** 1025**
Please input the username:client002
Trying ******** ...
Press CTRL+K to abort
The server is not authenticated. Continue to access it?(Y/N):y
Save the server's public key?(Y/N):y
The server's public key will be saved with the name ********. Please wait.

..
sftp-client>


步骤 **7** 检查配置结果


# 攻击者使用原端口号 22 访问 SSH 服务器，不能成功。


[ ~ client002] **sftp **********
Please input the username:client002
Trying ******** ...
Press CTRL+K to abort

Error: Failed to connect to the server.


# 查看 SSH 状态信息。


[ ~ SSH Server] **display ssh server status**
SSH Version                : 2.0
SSH authentication timeout (Seconds)    : 60
SSH authentication retries (Times)     : 3
SSH server key generating interval (Hours) : 0
SSH version 1.x compatibility       : Enable
SSH server keepalive            : Disable
SFTP IPv4 server              : Enable

SFTP IPv6 server              : Enable

STELNET IPv4 server            : Enable

STELNET IPv6 server            : Enable

SNETCONF IPv4 server            : Enable

SNETCONF IPv6 server            : Enable
SNETCONF IPv4 server port(830)       : Disable
SNETCONF IPv6 server port(830)       : Disable
SCP IPv4 server              : Enable

SCP IPv6 server              : Enable
SSH port forwarding            : Disable
SSH IPv4 server port            : 22
SSH IPv6 server port            : 22
ACL name                  :


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 334


HUAWEI NetEngine40E
配置指南 1 基础配置


ACL number                 :

ACL6 name                 :

ACL6 number                :
SSH server ip-block            : Enable


**----**
结束

##### 配置文件


       - SSH 服务器上的配置文件


#

sysname SSH Server
#
rsa peer-public-key rsakey001
public-key-code begin
308188

028180

B21315DD 859AD7E4 A6D0D9B8 121F23F0 006BB1BB A443130F 7CDB95D8 4A4AE2F3

D94A73D7 36FDFD5F 411B8B73 3CDD494A 236F35AB 9BBFE19A 7336150B 40A35DE6

2C6A82D7 5C5F2C36 67FBC275 2DF7E4C5 1987178B 8C364D57 DD0AA24A A0C2F87F

474C7931 A9F7E8FE E0D5A1B5 092F7112 660BD153 7FB7D5B2 171896FB 1FFC38CD

0203

010001
public-key-code end
peer-public-key end
#
interface loopback 0
ip address ******** ***************
stelnet server enable
sftp server enable
ssh server-source -i loopback 0
ssh server port 1025
ssh user client001
ssh user client001 authentication-type password
ssh user client001 service-type stelnet
ssh user client002
ssh user client002 authentication-type rsa
ssh user client002 assign rsa-key rsakey001
ssh user client002 sftp-directory cfcard:
ssh user client002 service-type sftp
#

aaa
local-user client001 password cipher @%@%UyQs4,KTtSwJo(4QmW#K,LC:@%@%
local-user client001 service-type ssh
#
interface GigabitEthernet0/0/0
undo shutdown
ip address ******** ***********
#
user-interface vty 0 4
authentication-mode aaa
protocol inbound ssh
#

return


       - SSH 客户端 Client001 的配置文件


#
sysname client001
#
interface GigabitEthernet0/0/0
undo shutdown
ip address ******** ***********
#
ssh client first-time enable
#

return


       - SSH 客户端 Client002 的配置文件


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 335


HUAWEI NetEngine40E
配置指南 1 基础配置


#
sysname client002
#
interface GigabitEthernet0/0/0
undo shutdown
ip address ******** ***********
#
ssh client first-time enable
#

return
