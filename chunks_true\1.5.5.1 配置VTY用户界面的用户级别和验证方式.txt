

VTY 用户界面的其他属性在设备上都有缺省值，用户一般不需要另外配置。但是可以根
据用户使用需求，配置相关属性。具体配置请参见 配置 **VTY** 用户界面 。

##### 操作步骤


       - 配置 VTY 用户界面的用户级别


a. 执行命令 **system-view** ，进入系统视图。

b. 执行命令 **user-interface** **vty** first-ui-number [ last-ui-number ] ，进入 VTY
用户界面视图。


c. 执行命令 **user privilege** **level** level ，设置用户级别， VTY 用户界面的用户级
别和命令级别对应关系如 表 **1-13** 所示。


说明


当存在 **command-privilege level rearrange** 配置时， level 取值范围是 0 ～ 15 ；


当不存在 **command-privilege level rearrange** 配置时， level 取值范围是 0 ～ 3 。


表 **1-13** 用户级别与命令级别对应关系














|用户<br>级别<br>（0<br>～<br>3）|用户<br>级别<br>（0～<br>15）|命令<br>级别|级别<br>名称|说明|
|---|---|---|---|---|
|0|0|0|参观<br>级|网络诊断工具命令（ping、tracert）、从<br>本设备出发访问外部设备的命令（Telnet<br>客户端）等。|
|1|1～9|0、1|监控<br>级|用于系统维护，包括display等命令。<br>说明<br>并不是所有display命令都是监控级，比如管<br>理配置文件中的**display current-**<br>**confguration**命令是3级管理级。各命令的<br>级别请参见《HUAWEI NetEngine40E-命令参<br>考》手册。|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 73


HUAWEI NetEngine40E
配置指南 1 基础配置















|用户<br>级别<br>（0<br>～<br>3）|用户<br>级别<br>（0～<br>15）|命令<br>级别|级别<br>名称|说明|
|---|---|---|---|---|
|2|10～<br>14|0、<br>1、2|配置<br>级|业务配置命令。|
|3|15|0、<br>1、<br>2、3|管理<br>级|用于系统基本运行的命令，对业务提供支<br>撑作用，包括文件系统、FTP、TFTP、配<br>置文件切换命令、备板控制命令、用户管<br>理命令、命令级别设置命令，设备重启<br>reboot命令、用于业务故障诊断的<br>debugging命令等。|


说明




              - 用户的级别与命令级别对应，不同级别的用户登录后，只能使用等于或低于自己
级别的命令，从而保证了设备的安全性。


              - 如果用户界面下配置的命令级别访问权限与用户名本身对应的操作权限冲突，以
用户名本身对应的级别为准。


d. 执行命令 **commit** ，提交配置。


       - 配置 VTY 用户界面 AAA 验证方式


用户界面验证方式配置为 AAA 验证时，必须指定本地用户的接入类型。


a. 执行命令 **system-view** ，进入系统视图。


b. （可选）执行命令 **crypto password irreversible-algorithm hmac-**
**sha256** ，设置用户密文口令加密算法为 hmac-sha256 。


c. 执行命令 **aaa** ，进入 AAA 视图。


d. 执行命令 **local-user** user-name **password** [ **cipher** password |
**irreversible-cipher** irreversible-cipher-password ] ，配置本地用户名和密
码。

#### ▪ 不选择 cipher 或 irreversible-cipher 关键字时，密码以交互式输入，系统

不回显密码。


输入的密码为字符串形式，区分大小写，开启用户账户安全策略时，取
值范围是 8 ～ 128 。关闭用户账户安全策略时，长度范围是 1 ～ 128 。开启
用户账户安全策略时，密码不能与用户名及其用户名反向字符串相同，
且密码必须包括大写字母、小写字母、数字及特殊字符。


说明


特殊字符不包括“？”和空格。但是，当输入的密码两端使用双引号时，可在
密码中间输入空格。


                - 如果使用双引号设置带空格密码，双引号之间不能再使用双引号。


                - 如果使用双引号没有设置带空格密码，双引号之间可以再使用双引号。


例如， "Aa 123"45"" 为不合法密码， "Aa123"45"" 为合法密码。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 74


HUAWEI NetEngine40E
配置指南 1 基础配置

#### ▪ 选择 cipher 关键字时，密码可以以简单形式输入，也可以以密文形式输

入。


密码以简单形式输入，要求与不选择 **cipher** 关键字时一样。密码以简单
形式输入，系统会回显简单形式的密码，存在安全风险，因此建议使用
交互式输入密码。


无论是简单输入还是密文输入，配置文件中都以密文形式体现。

#### ▪ 选择 irreversible-cipher 关键字时，密码可以以简单形式输入，也可以

以不可逆密文形式输入。


密码以简单形式输入，要求与不选择 **irreversible-cipher** 关键字时一
样。


无论是简单输入还是不可逆密文输入，配置文件中都以密文形式体现。


e. 执行命令 **local-user** user-name **service-type** **ssh** ，配置本地用户的接入类
型为 SSH 。


f. 执行命令 **local-user** user-name **user-group** user-group-name ，配置本地用
户加入指定用户组。


g. 执行命令 **quit** ，退出 AAA 视图。

h. 执行命令 **user-interface** **vty** first-index [ last-index ] ，进入 VTY 用户界面视
图。


i. 执行命令 **authentication-mode** **aaa** ，设置用户验证方式为 AAA 验证。


j. 执行命令 **commit** ，提交配置。


**----**
结束
