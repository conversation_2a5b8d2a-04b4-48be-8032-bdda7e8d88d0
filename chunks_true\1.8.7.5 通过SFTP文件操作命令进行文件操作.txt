

用户可以通过 SFTP 客户端管理 SSH 服务器上的目录和文件，以及查看 SFTP 客户端命令
帮助。

##### 背景信息


当 SFTP 客户端登录到 SSH 服务器之后，用户可以在 SFTP 客户端进行如下操作：


       - 创建并删除 SSH 服务器上的目录，以及显示当前的工作目录和指定目录下的文件
或目录信息


       - 改变文件名、删除文件、显示文件列表、上传下载文件


       - 查看 SFTP 客户端命令帮助


请在作为 SSH 客户端的路由器上进行如下的配置。

##### 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行基于网络协议的如下操作步骤：


       - 基于 IPv4 协议的配置情况：


执行命令 **sftp** [ **-a** source-ip-address ] [ **-force-receive-pubkey** ] host-ipaddress [ port-number ] [ [ **prefer_kex** prefer_kex ] | [ **prefer_ctos_cipher**
prefer_ctos_cipher ] | [ **prefer_stoc_cipher** prefer_stoc_cipher ] |

[ **prefer_ctos_hmac** prefer_ctos_hmac ] | [ **prefer_stoc_hmac**
prefer_stoc_hmac ] | [ **prefer_ctos_compress zlib** ] | [ **prefer_stoc_compress**
**zlib** ] | [ **public-net** | **-vpn-instance** vpn-instance-name ] | [ **-ki** interval ] | [ **-**
**kc** count ] | [ **identity-key** identity-key-type ] | [ **user-identity-key** userkey ] ] [*] ，以 SFTP 方式通过使用 IPv4 的地址登录到 SSH 服务器上，并进入 SFTP 客户
端视图。


       - 基于 IPv6 协议的配置情况：


执行命令 **sftp ipv6** [ **-force-receive-pubkey** ] [ **-a** source-ipv6-address ] hostipv6-address [ [ [ **-vpn-instance** vpn-instance-name ] | **public-net** ] | [ **-oi**
{ interface-name | interface-type interface-number } ] [ port-number ] |

[ **prefer_kex** { prefer_kex } ] | [ **prefer_ctos_cipher** prefer_ctos_cipher ] |

[ **prefer_stoc_cipher** prefer_stoc_cipher ] | [ **prefer_ctos_hmac**
prefer_ctos_hmac ] | [ **prefer_stoc_hmac** prefer_stoc_hmac ] |

[ **prefer_ctos_compress** **zlib** ] | [ **prefer_stoc_compress** **zlib** ] | [ **-ki** interval ]
| [ **-kc** count ] | [ **identity-key** identity-key-type ] | [ **user-identity-key** userkey ] ] [*] ，以 SFTP 方式通过使用 IPv6 的地址登录到 SSH 服务器上，并进入 SFTP 客户
端视图。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 257


HUAWEI NetEngine40E
配置指南 1 基础配置


步骤 **3** 可根据需要，执行如 表 **1-41** 中的一个或多个操作。


表 **1-41** 文件操作















|文件操作|Col2|操作|
|---|---|---|
|管理目录<br>操作|改变用户的当前工作<br>目录|执行命令**cd** [ path ]|
|管理目录<br>操作|改变用户的工作目录<br>为当前工作目录的上<br>一级目录|执行命令**cdup**|
|管理目录<br>操作|显示用户的当前工作<br>目录|执行命令**pwd**|
|管理目录<br>操作|显示目录中的文件和<br>子目录的列表|执行命令**dir** [ remote-filename [ local-<br>filename ] ]|
|管理目录<br>操作|删除服务器上目录|执行命令**rmdir** directory-name|
|管理目录<br>操作|在服务器上创建新目<br>录|执行命令**mkdir** path|
|管理文件<br>操作|改变服务器上指定的<br>文件的名字|执行命令**rename** old-name new-name|
|管理文件<br>操作|下载远程服务器上的<br>文件|执行命令**get** remote-filename [ local-<br>filename ]|
|管理文件<br>操作|上传本地文件到远程<br>服务器|执行命令**put** local-filename [ remote-<br>filename ]|
|管理文件<br>操作|删除服务器上文件|执行命令**remove** path或**delete** file|
|显示SFTP客户端命令帮助|显示SFTP客户端命令帮助|执行命令**help** [ command-name ]|


步骤 **4** 执行命令 **commit** ，提交配置。


**----**
结束

##### 后续处理


由于 SFTP 服务器上有最大客户端数限制，及时断开 SFTP 客户端与服务器的会话连接，
可便于其他合法用户及时登录 SFTP 服务器。


当用户登录 SFTP 服务器完成一系列操作后，可在 SFTP 客户端视图下执行命令 **bye** 、
**exit** 或 **quit** ，断开 SFTP 客户端与服务器的会话连接。
