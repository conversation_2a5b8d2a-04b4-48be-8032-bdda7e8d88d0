

使能 SSH 客户端首次认证功能后，当 STelnet 客户端第一次登录 SSH 服务器时，不对 SSH
服务器的 RSA 、 DSA 、 ECC 、 SM2 公钥进行有效性检查。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 238


HUAWEI NetEngine40E
配置指南 1 基础配置

##### 背景信息


使能 SSH 客户端首次认证功能后，在 STelnet 客户端第一次登录 SSH 服务器时，不对 SSH
服务器的 RSA 、 DSA 、 SM2 或 ECC 公钥进行有效性检查。登录后，客户端将自动保存
RSA 、 DSA 、 SM2 或 ECC 公钥，为下次登录时认证。


如果没有使能 SSH 客户端首次登录功能，当 STelnet 客户端第一次登录 SSH 服务器时，
由于对 SSH 服务器的 RSA 、 DSA 、 SM2 或 ECC 公钥有效性检查失败，会导致登录服务器
失败。若 STelnet 客户端需要第一次就成功登录 SSH 服务器，除了使能 SSH 客户端首次
登录功能之外，还可以通过 配置用户首次登录其他设备（ **SSH** 客户端为 **SSH** 服务器分配
公钥方式） 事先在客户端为 SSH 服务器分配 RSA 、 DSA 、 SM2 或 ECC 公钥来实现。


请在作为 SSH 客户端的路由器上进行如下的配置。

##### 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **ssh client first-time enable** ，使能 SSH 客户端首次登录功能。


步骤 **3** 执行命令 **commit** ，提交配置。


**----**
结束
