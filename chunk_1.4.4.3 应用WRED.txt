头标题: 1.4.4.3 应用WRED
尾标题: 1.4.4.4 检查配置结果
内容: HUAWEI NetEngine40E配置指南 1 QoS##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **traffic behavior** behavior-name ，定义流行为并进入行为视图。步骤 **3** 执行命令 **car** { **cir** cir-value [ **pir** pir-value ] } [ **cbs** cbs-value [ **pbs** pbs-value ] ][ **adjust** adjust-value ] [ **green** { **discard** | **pass** [ **remark dscp** dscp | **service-class**class **color** color ] } | **yellow** { **discard** | **pass** [ **remark dscp** dscp | **service-class**class **color** color ] } | **red** { **discard** | **pass** [ **remark dscp** dscp | **service-class** class**color** color ] } ] * [ **summary** ] [ **color-aware** ] 或命令 **car** { **cir** cir-value [ **pir** pirvalue ] } [ **cbs** cbs-value [ **pbs** pbs-value ] ] [ **green** { **discard** | **pass** [ **remark****dscp** dscp | **service-class** class **color** color ] } | **yellow** { **discard** | **pass** [ **remark****dscp** dscp | **service-class** class **color** color ] } | **red** { **discard** | **pass** [ **remark dscp**dscp | **service-class** class **color** color ] } ] * [ **summary** ] [ **color-aware** ] [ **limit-****type pps** ] 或命令 **car** { **cir** **cir-percentage** cir-percentage-value [ **pir** **pir-****percentage** pir-percentage-value ] } [ **cbs** cbs-value [ **pbs** pbs-value ] ] [ **adjust**adjust-value ] [ **green** { **discard** | **pass** [ **remark dscp** dscp | **service-class** class**color** color ] } | **yellow** { **discard** | **pass** [ **remark dscp** dscp | **service-class** class**color** color ] } | **red** { **discard** | **pass** [ **remark dscp** dscp | **service-class** class **color**color ] } ] * [ **color-aware** ] ，配置流量监管动作。可以根据实际需要，选择不同的参数：       - 如果需要配置单令牌桶监管，请选择参数 **cir** 和 **cbs** ，并配置 **pbs** 为 0 。       - 如果需要配置单速率双令牌桶监管，请选择参数 **cir** 、 **cbs** 和 **pbs** 。       - 如果需要配置双速率双令牌桶监管，请选择参数 **cir** 、 **pir** 、 **cbs** 和 **pbs** 。       - **cir** 和 **pir** 的单位是 kbit/s ， **cbs** 和 **pbs** 的单位是 byte 。说明配置并应用 CAR 之后，可以使用 **display traffic policy statistics interface** 命令来查看 CAR 的统计信息。接口上应用配置了流量监管的流量策略将影响原有的 **qos car** 命令。步骤 **4** 执行命令 **user-queue** **cir** cir-value [ [ **pir** pir-value ] | [ **flow-queue** flow-queuename ] | [ **flow-mapping** mapping-name ] | [ **user-group-queue** group-name ] |[ **service-template** service-template-name ] ] * ，指定流行为的动作为基于类的HQoS 调度。步骤 **5** （可选）执行命令 **flow-car** { **ipv6** | **ipv4-ipv6** } **cir** cir-value [ **pir** pir-value ] [ **cbs**cbs-value [ **pbs** pbs-value ] ] **identifier** { **source-ip** | **destination-ip** } ，针对命中的ACL 网段所包含的所有 IP 地址，基于源或目的 IP 做独立限速。步骤 **6** 执行命令 **commit** ，提交配置。**----**结束##### 后续处理NE40E 支持流量监管后 remark 报文的业务优先级和报文颜色。报文被 remark 为 ef 、be 、 cs6 和 cs7 服务等级后，报文颜色只能被 remark 为 green 。##### ******* 定义流量策略定义了流分类和动作后，需要配置流量策略，为定义的流关联动作。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 19HUAWEI NetEngine40E配置指南 1 QoS##### 背景信息在待配置路由器上进行以下配置。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **traffic policy** policy-name ，定义流量策略并进入策略视图。步骤 **3** 执行命令 **classifier** classifier-name **behavior** behavior-name [ **precedence**precedence-value ] ，在流量策略中为类指定采用的流行为。步骤 **4** 执行命令 **commit** ，提交配置。**----**结束##### ******* 应用流量策略配置好的基于类的策略，需要应用到接口才能生效。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** （可选）在单板上配置应用流量策略时匹配的报文信息。1. 执行命令 **slot** slot-id ，进入槽位视图。2. 请根据实际需求选择如下一种配置。–执行命令 **traffic-policy match-ip-layer** { **mpls-pop** | **mpls-push** } [*] ，配置入 / 出公网方向的报文，只匹配 IP 层（三层）信息做复杂流分类，应用流量策略。– 配置入 / 出公网方向的报文，匹配 IP 信息、 MPLS 信息做复杂流分类。请根据实际需求选择如下一种或两种配置。#### ▪ 执行命令 traffic-policy match-mpls-layer { mpls-push | mpls-pop }                           - ，配置入 / 出公网方向的报文同时匹配 IP 信息和 MPLS 信息做复杂流分类，应用流量策略。#### ▪ 执行命令 traffic-policy match-mpls-layer l2-inbound ，配置出公网方向的报文在二层转发场景匹配 MPLS 信息做复杂流分类，应用流量策略。3. 执行命令 **quit** ，退出槽位视图。步骤 **3** 执行命令 **interface** interface-type interface-number ，进入接口视图。步骤 **4** （可选）执行命令 **qos traffic-car member-link-scheduler distribute** ，配置在将复杂流分类的 CAR 动作应用到 Trunk 口时，将限速总带宽以成员口为单位分布限速。说明该命令仅在 Ethernet Trunk 接口下支持。步骤 **5** 请根据流量策略应用的接口选择下面的配置步骤。       - 在三层接口应用复杂流分类策略，请执行如下步骤。a. 执行命令 **interface** interface-type interface-number ，进入接口视图。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 20HUAWEI NetEngine40E配置指南 1 QoSb. 执行命令 **traffic-policy** policy-name { **inbound** | **outbound** } [ **all-layer** |**link-layer** | **mpls-layer** ] ，在三层接口应用流量策略。说明如果指定 **link-layer** 参数，设备将根据报文的二层信息进行规则匹配并执行相应的动作。如果指定 **mpls-layer** 参数，设备将根据指定 MPLS 报文的标签头信息进行复杂流分类。如果指定 **all-layer** 参数，设备将根据报文的二层信息进行规则匹配并执行相应的动作；如果报文的二层信息没有匹配上流分类规则，则继续根据报文的三层信息进行规则匹配并执行相应的动作。       - 在二层接口应用复杂流分类策略的时候，可以指定 VLAN 范围来应用，请执行如下步骤。a. 执行命令 **interface** interface-type interface-number ，进入接口视图。b. 执行命令 **portswitch** ，切换到二层端口视图。c. 执行命令 **port trunk allow-pass vlan** { { vlan-id1 [ **to** vlan-id2 ] }&<1-10> | **all** } ，配置二层端口以 tagged 方式加入到指定的 VLAN 中。d. 执行命令 **traffic-policy** policy-name { **inbound** | **outbound** } **vlan** { vlanid1 [ **to** vlan-id2 ] | **all** } [ **all-layer** | **link-layer** | **mpls-layer** ] ，在二层接口应用流量策略。       - 在 EVC 二层子接口应用复杂流分类策略的时候，可以指定带宽分配方式，请执行如下步骤。a. 执行命令 **interface** interface-type interface-number.subnum **mode l2** ，进入 EVC 二层子接口视图。b. 执行命令 **traffic-policy** policy-name { **inbound** | **outbound** } **identifier**{ **none** | **vid** | **ce-vid** | **vid-ce-vid** } [ **all-layer** | **link-layer** ] ，在 EVC 二层子接口应用流量策略。说明**identifier** 指定的分配方式必须与 EVC 二层子接口配置的封装方式一致。       - 在 QinQ 接口应用复杂流分类策略的时候，可以指定 PVLAN 和 CVLAN 来应用，请执行如下步骤。a. 执行命令 **interface** interface-type interface-number.subinterfacenumber ，进入子接口视图。b. 执行命令 **encapsulation** **qinq-termination** [ **local-switch** | **rt-****protocol** ] ，设定终结子接口的 VLAN ID 和对带两层 Tag 的用户报文进行终结。c. 执行命令 **qinq termination pe-vid** pe-vid [ **to** high-pe-vid ] **ce-vid** ce-vid[ **to** high-ce-vid ] [ **vlan-group** group-id ] ，配置 QinQ 子接口终结功能。d. 执行命令 **traffic-policy** policy-name { **inbound** | **outbound** } **pe-vid** pe-vid**ce-vid** ce-vid1 [ **to** ce-vid2 ] [ **all-layer** | **link-layer** | **mpls-layer** ] ，在QINQ 接口应用流量策略。说明在 QinQ 接口应用复杂流分类策略的时候，也可以不指定 PVLAN 和 CVLAN ，直接通过**traffic-policy** policy-name { **inbound** | **outbound** } [ **all-layer** | **link-layer** |**mpls-layer** ] 命令配置即可。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 21HUAWEI NetEngine40E配置指南 1 QoS步骤 **6** 执行命令 **commit** ，提交配置。**----**结束##### 1.3.5.5 检查配置结果基于复杂流分类的流量监管的配置成功后，可以查看设备配置的类信息、流行为信息、指定策略中指定类及与类关联的行为的配置信息、策略的配置信息和运行情况、流量策略信息、队列配置信息和运行情况等内容。##### 操作步骤       - 使用 **display interface** [ interface-type [ interface-number ] ] 命令查看接口的流量信息。       - 使用 **display traffic behavior** { **system-defined** | **user-defined** } [ behaviorname ] 命令查看流行为的配置信息。       - 使用 **display traffic classifier** { **system-defined** | **user-defined** } [ classifiername ] 命令查看流分类的配置信息。       - 使用 **display traffic policy** { **system-defined** | **user-defined** } [ policy-name[ **classifier** classifier-name ] ] 命令查看流策略中所有流分类与流行为的关联信息或特定流分类与流行为的关联信息。       - 使用 **display car resource** **slot** slot-id 命令查看 CAR 资源的分配情况。       - 使用 **display qos resource rule** { **aclv4** | **aclv6** | **l2acl** | **mpls** } **slot** slot-id 命令查看每单板 ACL Rule 的使用情况。       - 使用 **display qos resource traffic-policy application** [ **slot** slot-id ] 命令查看有多少接口已经绑定了 traffic-policy ，还有多少接口可以再绑定 traffic-policy 。       - 使用 **display traffic policy** [ [ **name** ] policy-name ] **statistics interface**{ interface-name | interface-type interface-number } [ **vlan** vlan-id | **pe-vid**pe-vid **ce-vid** ce-vid | **vid** vid | **ce-vid** ce-vid | **vid** vid **ce-vid** ce-vid ] { **inbound**| **outbound** } [ **verbose** { **classifier-based** [ **class** class-name ] | **rule-based**[ **class** class-name ] [ **filter** ] } ] 命令查看流量策略统计信息。说明如果需要查看 CAR 的统计信息，必须配置参数： **classifier-based** 。**----**结束#### 1.3.6 配置流量整形与流量监管的作用一样，流量整形主要是对流量监管中需要丢弃的报文进行缓存，通常是采用缓冲区和令牌桶来完成的。##### 应用环境在网络流量很大的时候，超出规格的报文将被直接丢弃。如果不希望下游网络因为上游发送数据流量过大而造成拥塞或大量报文的直接丢弃，可以通过在上游路由器的出接口配置流量整形，限制流出某一网络的某一连接的流量与突发，使这类报文以比较均匀的速度向外发送，以利于网络上下游之间的带宽匹配。流量整形通常使用缓冲区和令牌桶来完成，因此当报文的发送速度过快时，超出规格的报文不会被直接丢弃，而是在缓冲区进行缓存，即进入缓存队列。在令牌桶的控制文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 22HUAWEI NetEngine40E配置指南 1 QoS下，网络空闲时再按照队列调度优先级均匀地发送这些被缓冲的报文，避免了报文因直接丢弃而导致的大量重传。说明Diff-Serv 主要是为 BA （ Behavior Aggregation ）数据流提供带宽上的保证， NE40E 采用预先定义好的队列调度机制为 EF 、 AF 等不同类型的业务分配资源，用户无须进行队列管理方面的设置。目前， NE40E 仅支持接口出方向报文队列的流量整形。##### 前置任务在配置流量整形之前，需要完成以下任务：       - 配置相关接口的物理参数       - 配置相关接口的链路层属性，保证接口的正常工作       - 配置相关接口的 IP 地址       - 使能路由协议，实现互通##### 操作步骤       - 配置基于接口的流量整形。a. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **interface** interface-type interface-number ，进入接口视图。c. 执行命令 **port shaping** { shaping-value | **shaping-percentage** shapingpercentage-value } [ **network-header-length** network-header-lengthvalue ] [ **pbs** pbs-value ] [ **weight-mode** ] ，在接口出方向配置流量整形，可以对接口出方向上的报文流量进行整形。说明对于 Trunk 接口，若需要按照成员口权重分配 shaping 值，可执行命令 **port shaping**shaping-value [ **weight-mode** ] 。d. （可选）执行命令 **qos port-shaping member-link-scheduler****distribute** ，配置 Trunk 成员口在不同网络处理器（ NP ）上时按照成员口权重分配整形带宽。缺省情况下，如果 Trunk 成员口在相同网络处理器（ NP ）上，成员口间会分担 Trunk 口上配置的整形带宽。但是，如果 Trunk 成员口属于不同的网络处理器（ NP ），每个成员口的整形带宽均为 Trunk 口上配置的带宽值，此时会造成 Trunk 口的流量翻倍，达不到预期的整形效果。这种场景下，建议执行本配置。e. 执行命令 **port-queue** cos-value { { **pq** | **wfq** **weight** weight-value | **lpq** } |**shaping** { shaping-value | **shaping-percentage** shaping-percentagevalue } [ **pbs** pbs-value ] | **port-wred** wred-name | **low-latency** } [*]**outbound** 或命令 **port-queue** cos-value **cir** { { cir-value [ **cbs** cbs-value ]**cir-schedule** **pq** **pir** pir-value } | { **cir-percentage** cir-percentage-value[ **cbs** cbs-value ] **cir-schedule** **pq** **pir** **pir-percentage** pir-percentagevalue } } [ **pbs** pbs-value ] **pir-schedule** { **pq** | **wfq weight** weight-value |**lpq** } [ **port-wred** wred-name ] **outbound** ，设置不同等级端口队列的调度策略。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 23HUAWEI NetEngine40E配置指南 1 QoSf. 执行命令 **port-queue-alarm** cos-value { **discard-packet** discard-packetnumber | **discard-byte** discard-byte-number | **discard-packet-ratio**discard-packet-coefficient discard-packet-exponent } [ **interval** intervaltime ] ，配置端口队列丢弃告警功能。g. 执行命令 **commit** ，提交配置。       - 配置基于子端口队列的流量整形。a. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **sub-port-queue** sub-port-queue-name ，进入子端口队列模板视图。c. 执行命令 **shaping** shaping-value [ **pbs** pbs-value ] **outbound** ，配置子端口队列整形速率。d. 执行命令 **weight** weight-value **outbound** ，配置子端口队列权重值e. 执行命令 **quit** ，退回系统视图。f. 执行命令 **interface** interface-type interface-number ，进入接口视图。g. 执行命令 **sub-port-queue** sub-port-queue-name **outbound** [ **group**group-name ] ，在接口下应用子端口队列模板。h. （可选）执行命令 **qos default sub-port-queue** **shaping** shaping-value[ **pbs** pbs-value ] **outbound** ，修改默认子端口队列的整形参数。i. （可选）执行命令 **bas-load-balance exclude sub-port-queue****outbound** ，配置下行方向的流量不依据子端口队列进行选路。若流量转发接口为 Eth-trunk 接口，同一个子端口队列的下行方向流量默认依据子端口队列进行选路，即通过同一个 Trunk 成员口转发该子端口队列的下行流量，可能会出现 Trunk 各成员口间流量负载不均的问题。如果想要下行方向流量负载分担到 Trunk 接口的不同成员口，可配置本命令实现。j. （可选）执行命令 **qos sub-port-queue member-link-scheduler****distribute outbound** ，配置 Trunk 成员口 sub-port-queue 限速带宽按照成员口权重进行分配。Trunk 接口下应用了 sub-port-queue 后，如果 Trunk 接口的成员口分布在不同网络处理器上，则每个成员口的限速带宽均为 Trunk 接口上配置的限速带宽。此时会造成 Trunk 接口的流量翻倍，达不到预期的限速效果。这种情况下，可以在 Trunk 接口下配置 **qos sub-port-queue member-link-scheduler****distribute** 命令使得 Trunk 成员口 sub-port-queue 限速带宽按照成员口权重进行分配。说明Bras 场景建议同时配置命令 bas-load-balance exclude sub-port-queue outbound ，否则 Trunk 接口的 sub-port-queue 限速可能达不到配置的限速带宽。k. 执行命令 **commit** ，提交配置。l. 执行命令 **quit** ，退回系统视图。       - 配置基于端口队列的流量整形a. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **port-queue-template** port-queue-name ，创建端口队列模板并进入端口队列模板视图。c. 执行命令 **queue** cos-value { { **pq** | **wfq** **weight** weight-value | **lpq** } |**shaping** { shaping-value | **shaping-percentage** shaping-percentage文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 24HUAWEI NetEngine40E配置指南 1 QoSvalue } [ **pbs** pbs-value ] | **port-wred** wred-name | **low-latency** } [*] ，配置端口队列模板的端口队列调度参数。d. 执行命令 **share-shaping** { **af1** | **af2** | **af3** | **af4** | **be** | **cs6** | **cs7** | **ef** } [*] [ **pq** |**wfq** **weight** weight-value | **lpq** ] { shaping-value | **shaping-percentage**shaping-percentage-value } [ **pbs** pbs-value ] ，配置端口队列的联合流量整形。说明同一接口下命令 **port share-shaping** 与命令 **share-shaping** （端口队列视图） 互斥，不能同时配置。e. 执行命令 **commit** ，提交配置。f. 执行命令 **quit** ，返回系统视图。g. 执行命令 **interface** interface-type interface-number ，进入接口视图。h. 执行命令 **port-queue-template** port-queue-name **outbound** ，在接口上应用已配置好的端口队列模板。说明同一接口下的 **port-queue** 命令与 **port-queue-template** 模板互斥。仅当 Tunnel 接口的协议配置为 GRE 或 GRE IPv6 ，且已配置了 **port-shaping** 命令时，该Tunnel 接口才支持应用端口队列模板。i. 执行命令 **commit** ，提交配置。       - 配置基于槽位视图的流量整形。a. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **slot** slot-id ，进入槽位视图。c. 执行命令 **port shaping** shaping-value **bind** **mtunnel** ，限制分布式组播 VPN单板上接口类型为 MTunnel 的所有向外发送数据的速率。d. 执行命令 **commit** ，提交配置。       - 配置基于业务模板的精度补偿值。a. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **service-template** service-template-name ，进入业务模板视图。c. 执行命令 **network-header-length** network-header-length { **inbound** |**outbound** } ，设置业务模板的精度补偿长度。d. 执行命令 **commit** ，提交配置。e. 执行命令 **quit** ，返回系统视图。f. 执行命令 **interface** interface-type interface-number ，进入接口视图。g. 执行命令 **shaping service-template** service-template-name ，在接口上应用已定义的业务模板。h. 执行命令 **commit** ，提交配置。**----**结束##### 检查配置结果完成配置后，可以按以下指导来检查配置结果。       - 使用 **display interface** [ interface-type [ interface-number ] ] 命令，查看接口的流量信息。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 25HUAWEI NetEngine40E配置指南 1 QoS       - 使用 **display service-template configuration** [ **verbose** [ service-templatename ] ] 命令，查看用户自定义的业务模板的信息。       - 使用 **display port-wred configuration** [ **verbose** [ port-wred-name ] ] 命令，查看端口队列 WRED 对象的配置参数。       - 使用 **display port-queue configuration** **interface** interface-type interfacenumber **outbound** 命令，查看端口队列详细配置信息。       - 使用 **display port-queue statistics** [ **slot** slot-id | **interface** interface-typeinterface-number ] [ cos-value ] **outbound** 命令，查看端口队列的统计计数信息。       - 使用 **display sub-port-queue configuration** [ **verbose** [ sub-port-queuename ] ] 命令，查看子接口队列信息。#### 1.3.7 维护流量监管、流量整形清除流量监管、流量整形的统计信息。##### 1.3.7.1 清空 CAR 的统计信息介绍如何清空 CAR 的统计信息。##### 背景信息须知清除统计信息后，以前的统计信息将无法恢复，务必在执行该操作之前仔细确认。在确认需要清除指定接口指定方向的 CAR 统计数据后，请在用户视图下执行下面的**reset** 命令。##### 操作步骤       - 执行 **reset car statistics interface** { interface-type interface-number |interface-name } [ **vlan** vlan-id | **vid** vid | **ce-vid** ce-vid | **vid** vid **ce-vid** ce-vid |**pe-vid** pe-vid **ce-vid** ce-vid ] { **inbound** | **outbound** } 命令清空指定接口指定方向的 CAR 统计数据。**----**结束#### 1.3.8 配置举例从具体应用场景、配置命令等方面对流量监管、流量整形、接口限速进行了详细的描述。##### 1.3.8.1 配置流量监管示例以特性的流量控制场景为例，介绍如何配置流量监管。通过在接口上配置流量监管，实现收发报文的总流量限制、报文流限制。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 26HUAWEI NetEngine40E配置指南 1 QoS##### 组网需求DeviceA 通过接口 GE3/0/0 与 DeviceB 的接口 GE1/0/0 互连， Server 、 PC1 、 PC2 可经由DeviceA 和 DeviceB 访问 Internet 。Server 、 PC1 与 DeviceA 的 GE1/0/0 接口在同一网段， PC2 与 DeviceA 的 GE2/0/0 接口在同一网段。在 DeviceA 上对接口 GE1/0/0 接收到的源自 Server 和 PC1 的报文流分别实施流量控制如下：       - 来自 Server 的报文流量进行带宽保证，固定为 5Mbit/s ，最高不超过 6Mbit/s ，流量超过 5Mbit/s 且不超过 6Mbit/s 时，报文正常发送，超过 6Mbit/s 时，超过部分的流量降级为 BE 流进行发送。       - 来自 PC1 的报文流量约束为 2Mbit/s ，流量超过 2Mbit/s 时则丢弃超标流量。说明本例中 interface1 ， interface2 ， interface3 分别代表 GE1/0/0 ， GE2/0/0 ， GE3/0/0 。图 **1-6** 流量监管配置组网图##### 配置注意事项在配置中需注意以下事项：       - 报文被 remark 为 ef 、 be 、 cs6 和 cs7 服务等级后，报文颜色只能被 remark 为Green 。       - 当用户需要显示流量策略的统计数据时，可配置 **statistics enable** 使能流量策略的统计功能。##### 配置思路采用如下思路配置流量监管。1. 配置各接口的 IP 地址2. 在 DeviceA 的入接口 GE1/0/0 上通过复杂流分类对来自 Server 和 PC1 的流量进行监管。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 27HUAWEI NetEngine40E配置指南 1 QoS##### 数据准备完成此例配置，需准备以下数据：       - Server 和 PC1 流量的 ACL 列表号、流分类名称、流行为名称、流量策略名称和流量策略应用的接口       - 流量监管的承诺信息速率、峰值信息速率、承诺突发尺寸、最大突发尺寸##### 操作步骤步骤 **1** 配置各接口的 IP 地址（略）步骤 **2** 配置 DeviceA# 配置 ACL 规则列表，分别匹配来源于 Server 和 PC1 的报文流。<HUAWEI> **system-view**[ ~ HUAWEI] **sysname DeviceA**[*HUAWEI] **commit**[ ~ DeviceA] **acl number 2001**[*DeviceA-acl-basic-2001] **rule permit source ******* 0.0.0.0**[*DeviceA-acl-basic-2001] **commit**[ ~ DeviceA-acl-basic-2001] **quit**[ ~ DeviceA] **acl number 2002**[*DeviceA-acl-basic-2002] **rule permit source ******* 0.0.0.0**[*DeviceA-acl-basic-2002] **commit**[ ~ DeviceA-acl-basic-2002] **quit**# 配置流分类，并定义基于 ACL 的流分类匹配规则。[ ~ DeviceA] **traffic classifier class1**[*DeviceA-classifier-class1] **if-match acl 2001**[*DeviceA-classifier-class1] **commit**[ ~ DeviceA-classifier-class1] **quit**[ ~ DeviceA] **traffic classifier class2**[*DeviceA-classifier-class2] **if-match acl 2002**[*DeviceA-classifier-class2] **commit**[ ~ DeviceA-classifier-class2] **quit**# 定义流行为，对来自 Server 的报文流量带宽设定为 5Mbit/s ，最高不超过 6Mbit/s ，流量超过 5Mbit/s 且不超过 6Mbit/s 时，报文正常通过，超过 6Mbit/s 时，超过部分的流量降级为 BE 流进行发送。[ ~ DeviceA] **traffic behavior behavior1**[*DeviceA-behavior-behavior1] **car cir 5000 pir 6000 green pass yellow pass red pass service-class be****color green**[*DeviceA-behavior-behavior1] **commit**[ ~ DeviceA-behavior-behavior1] **quit**# 定义流行为，对来自 PC1 的报文流量约束为 2Mbit/s ，流量超过 2Mbit/s 时则丢弃超标流量。[ ~ DeviceA] **traffic behavior behavior2**[*DeviceA-behavior-behavior2] **car cir 2000 green pass red discard**[*DeviceA-behavior-behavior2] **commit**[ ~ DeviceA-behavior-behavior2] **quit**# 定义策略，将类与行为关联。[ ~ DeviceA] **traffic policy policy1**[*DeviceA-trafficpolicy-policy1] **classifier class1 behavior behavior1**[*DeviceA-trafficpolicy-policy1] **classifier class2 behavior behavior2**[*DeviceA-trafficpolicy-policy1] **commit**[ ~ DeviceA-trafficpolicy-policy1] **quit**# 将策略应用到 GE1/0/0 接口上。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 28HUAWEI NetEngine40E配置指南 1 QoS[ ~ DeviceA] **interface gigabitethernet 1/0/0**[ ~ DeviceA-GigabitEthernet1/0/0] **undo shutdown**[*DeviceA-GigabitEthernet1/0/0] **traffic-policy policy1 inbound**[*DeviceA-GigabitEthernet1/0/0] **commit**步骤 **3** 检查配置结果在 DeviceB 上执行 **display interface** 命令，可以查看到接口上的流量信息。**----**结束##### 配置文件       - DeviceA 的配置文件#sysname DeviceA#acl number 2001rule 5 permit source ******* 0acl number 2002rule 5 permit source ******* 0#traffic classifier class1 operator orif-match acl 2001traffic classifier class2 operator orif-match acl 2002#traffic behavior behavior1car cir 5000 pir 6000 green pass yellow pass red pass service-class be color greentraffic behavior behavior2car cir 2000 green pass red discard#traffic policy policy1classifier class1 behavior behavior1 precedence 1classifier class2 behavior behavior2 precedence 2#interface GigabitEthernet1/0/0undo shutdownip address ******* 255.255.255.0traffic-policy policy1 inbound#return### 1.4 拥塞管理和拥塞避免配置通过调整网络的流量来解除网络拥塞并介绍当网络发生拥塞时的几种不同丢包策略。#### 1.4.1 拥塞管理和拥塞避免简介拥塞管理就是在网络间歇性出现拥塞，时延敏感业务要求得到比其他业务更高质量的QoS 服务时，通过调整报文的调度次序来满足时延敏感业务高 QoS 服务的一种拥塞控制机制。拥塞避免是指通过监控网络资源（如队列或内存缓冲区）的使用情况，在拥塞发生或有加剧趋势时主动丢弃报文，通过调整网络的流量来解除网络过载的一种拥塞控制机制。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 29HUAWEI NetEngine40E配置指南 1 QoS#### 1.4.2 拥塞管理和拥塞避免配置注意事项##### 特性限制无#### 1.4.3 配置拥塞管理介绍如何通过配置队列调度参数实现拥塞管理。##### 背景信息拥塞管理指网络在发生拥塞时，如何进行管理和控制。处理的方法是使用队列技术，将从一个接口发出的所有报文放入多个队列，按照各个队列的优先级进行处理。不同的队列调度算法用来解决不同的问题，并产生不同的效果。队列调度参数的配置，可通过端口队列模板和接口下直接配置两种方式实现。       - 基于端口队列模板配置队列调度参数，该方式适用于需要为多个接口配置队列调度参数时，可以减少配置工作量。       - 接口下直接配置队列调度参数，该方式适用于需要配置的接口数量较少，且各接口下调度参数相差较大时。##### 操作步骤       - 在接口上直接配置队列调度参数a. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **interface** interface-type interface-number ，进入接口视图。c. 执行命令 **port-queue** cos-value { { **pq** | **wfq weight** weight-value | **lpq** } |**shaping** { shaping-value | **shaping-percentage** shaping-percentagevalue } [ **pbs** pbs-value ] | **port-wred** wred-name | **low-latency** } ***outbound** 或命令 **port-queue** cos-value **cir** { { cir-value [ **cbs** cbs-value ]**cir-schedule pq pir** pir-value } | { **cir-percentage** cir-percentage-value[ **cbs** cbs-value ] **cir-schedule pq pir pir-percentage** pir-percentagevalue } } [ **pbs** pbs-value ] **pir-schedule** { **pq** | **wfq weight** weight-value |**lpq** } [ **port-wred** wred-name ] **outbound** ，在接口视图下配置队列调度参数。d. （可选）执行命令 **port-queue-alarm** cos-value **buffer percentage**percentage-value ，配置端口队列使用率的告警门限。说明如果需要配置端口队列使用率告警门限的接口多，可以在槽位视图下执行命令 **port-****queue-alarm** cos-value **buffer percentage** percentage-value 基于槽位进行配置。在槽位视图下的配置对其所有接口都生效；槽位视图和接口视图同时配置此功能时，接口视图配置的参数优先生效。e. （可选）执行命令 **port-queue-alarm** cos-value { **discard-packet** discardpacket-number | **discard-byte** discard-byte-number | **discard-packet-****ratio** discard-packet-coefficient discard-packet-exponent } [ **interval**interval-time ] ，配置端口队列丢弃告警门限值。f. 执行命令 **commit** ，提交配置。       - 基于端口队列模板配置队列调度参数。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 30HUAWEI NetEngine40E配置指南 1 QoSa. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **port-queue-template** port-queue-name ，创建端口队列模板并进入端口队列模板视图。c. 执行命令 **queue** cos-value { { **pq** | **wfq weight** weight-value | **lpq** } |**shaping** { shaping-value | **shaping-percentage** shaping-percentagevalue } [ **pbs** pbs-value ] | **port-wred** wred-name | **low-latency** } * 或命令**queue** cos-value **cir** { { cir-value [ **cbs** cbs-value ] **cir-schedule pq pir** pirvalue } | { **cir-percentage** cir-percentage-value [ **cbs** cbs-value ] **cir-****schedule pq pir pir-percentage** pir-percentage-value } } [ **pbs** pbsvalue ] **pir-schedule** { **pq** | **wfq weight** weight-value | **lpq** } [ **port-wred**wred-name ] ，配置端口队列模板中各队列的调度参数。d. 执行命令 **quit** ，返回系统视图。e. 执行命令 **interface** interface-type interface-number ，进入接口视图。f. 执行命令 **port-queue-template** port-queue-name **outbound** ，在接口视图下应用端口队列模板。g. （可选）执行命令 **port-queue-alarm** cos-value **buffer percentage**percentage-value ，配置端口队列使用率的告警门限。说明如果需要配置端口队列使用率告警门限的接口多，可以在槽位视图下执行命令 **port-****queue-alarm** cos-value **buffer percentage** percentage-value 基于槽位进行配置。在槽位视图下的配置对其所有接口都生效；槽位视图和接口视图同时配置此功能时，接口视图配置的参数优先生效。h. （可选）执行命令 **port-queue-alarm** cos-value { **discard-packet** discardpacket-number | **discard-byte** discard-byte-number | **discard-packet-****ratio** discard-packet-coefficient discard-packet-exponent } [ **interval**interval-time ] ，配置端口队列丢弃告警门限值。i. 执行命令 **commit** ，提交配置。       - a. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **port-queue-template** port-queue-name ，创建端口队列模板并进入端口队列模板视图。c. 执行命令 **queue** cos-value { { **pq** | **wfq weight** weight-value | **lpq** } |**shaping** { shaping-value | **shaping-percentage** shaping-percentagevalue } [ **pbs** pbs-value ] | **port-wred** wred-name **low-latency** }* ，配置端口队列模板中各队列的调度参数。d. 执行命令 **quit** ，返回系统视图。e. 执行命令 **interface** interface-type interface-number ，进入接口视图。f. 执行命令 **port-queue-template** port-queue-name **outbound** ，在接口视图下应用端口队列模板。g. （可选）执行命令 **port-queue-alarm** cos-value **buffer percentage**percentage-value ，配置端口队列使用率的告警门限。h. （可选）执行命令 **port-queue-alarm** cos-value { **discard-packet** discardpacket-number | **discard-byte** discard-byte-number | **discard-packet-****ratio** discard-packet-coefficient discard-packet-exponent } [ **interval**interval-time ] ，配置端口队列丢弃告警门限值。i. 执行命令 **commit** ，提交配置。**----**结束文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 31HUAWEI NetEngine40E配置指南 1 QoS#### 1.4.4 配置拥塞避免WRED （ Weighted Random Early Detection ），可以通过设定阈值决定随机丢弃报文的条件，避免使多个 TCP 连接同时降低发送速度，从而避免了 TCP 的全局同步现象。##### 应用环境由于内存资源的有限，当发生网络拥塞时，传统的处理方法是采用尾丢弃，即将超出规格的所有报文都丢弃。对于 TCP 报文，由于大量的报文被丢弃，将造成 TCP 超时，从而引发 TCP 的慢启动和拥塞避免机制，使 TCP 减少报文的发送。当队列同时丢弃多个TCP 连接的报文时，将造成多个 TCP 连接同时进入慢启动和拥塞避免，称之为 TCP 全局同步。这样多个 TCP 连接发向队列的报文将同时减少，使得发向队列的报文的量低于链路发送的速度，减少了带宽的利用率。为了避免 TCP 全局同步这种情况，队列可以采用 WRED 丢弃策略。由于 WRED 随机地丢弃报文，将避免使多个 TCP 连接同时降低发送速度，从而避免了 TCP 的全局同步现象，提高了线路带宽的利用率。说明WRED 丢弃策略通常与 WFQ 队列配合使用。##### 前置配置在配置 WRED 之前，需完成以下任务：       - 配置相关接口的物理参数       - 配置相关接口的链路层属性，保证接口的正常工作       - 配置相关接口的 IP 地址       - 使能路由协议，实现互通##### ******* 配置 WRED 模板配置 WRED 模板中不同颜色报文的高低门限百分比和丢弃概率。##### 背景信息每个 WRED 模板最多支持红、黄、绿三种颜色报文的处理。一般绿色报文设置的丢弃概率比较小，高、低门限值比较大；黄色报文次之；红色报文设置的丢弃概率最大，高、低门限值最小。通过配置 WRED 模板，用户可以为队列设定高低门限百分比和丢弃概率。       - 当报文队列的实际长度占端口队列的长度百分比小于低门限百分比时，不丢弃报文。       - 当报文队列的实际长度占端口队列的长度百分比在低门限百分比和高门限百分比之间时， WRED 开始随机丢弃报文（队列的长度越长，报文被丢弃的概率越高）。       - 当报文队列的实际长度占端口队列的长度百分比大于高门限百分比时，丢弃所有的报文。每种颜色报文的门限值百分比和丢弃概率都是可配置的。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 32HUAWEI NetEngine40E配置指南 1 QoS说明          - 如果用户不配置端口队列的 WRED 模板，系统采用缺省的尾丢弃策略。          - 红色丢弃优先级队列的高低门限百分比可以配置得最小，黄色丢弃优先级队列的高低门限百分比可以配置得稍大些，绿色丢弃优先级队列的高低门限百分比可以配置得最大。          - 用户在实际配置时， WRED 低门限百分比建议从 50% 开始取值，根据不同颜色的丢弃优先级逐级调整。丢弃概率建议取值为 100% 。在待配置路由器上进行以下配置。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **port-wred** port-wred-name ，创建 WRED 模板并进入 WRED 视图。步骤 **3** 执行命令 **color** { **green** | **yellow** | **red** } **low-limit** low-limit-percentage **high-limit**high-limit-percentage **discard-percentage** discard-percentage ，配置不同颜色的报文的高低门限百分比和丢弃概率。步骤 **4** （可选）执行命令 **queue-depth** { queue-depth-value | **buffer-time** queue-depthtime } ，配置调整端口队列的队列深度。步骤 **5** 执行命令 **commit** ，提交配置。**----**结束##### 1.4.4.2 （可选）配置端口队列的联合流量整形当多个优先级的端口队列出接口为同一个主接口时，可以针对同样调度方式的端口队列进行联合流量整形。##### 背景信息请在主接口上进行如下配置。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **interface** interface-type interface-number ，进入接口视图。步骤 **3** 执行命令 **port share-shaping** { **af1** | **af2** | **af3** | **af4** | **be** | **cs6** | **cs7** | **ef** } [*] [ **pq** | **wfq****weight** weight-value | **lpq** ] { shaping-value | **shaping-percentage** shapingpercentage-value } [ **pbs** pbs-value ] ，配置端口队列的联合流量整形。本命令为覆盖式命令，即在同一接口多次配置流量整形参数后，按最后一次配置生效。同一接口下命令 **port share-shaping** 与命令 **share-shaping** （端口队列视图） 互斥，不能同时配置。本命令不支持在 Trunk 成员口下配置，配置了本命令的接口也不能加入 Trunk 口。本命令仅在 LPUI-52-E/LPUI-120-E/LPUF-480-E/LPUF-480/LPUF-480-B/LPUI-480/LPUI-480-B/LPUI-480-L/LPUI-1T/LPUI-1T-B/LPUI-1T-L/LPUI-2T/LPUI-2T-B/LPUF-241/LPUF-241-R/LPUI-241/LPUI-241-B/LPUF-481/LPUF-402-E 单板支持，或在文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 33HUAWEI NetEngine40E配置指南 1 QoSLPUF-120 系列母板 /LPUF-240 系列母板配合 P52-E/P120-E/P120-H/P240-E/P240-H 子卡使用时支持。步骤 **4** 执行命令 **commit** ，提交配置。**----**结束##### 1.4.4.3 应用 WRED基于业务类型应用配置好的 WRED 模板。##### 背景信息在配置了 WRED 模板的路由器上进行以下配置。##### 操作步骤       - 在接口视图上应用 WREDa. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **interface** interface-type interface-number ，进入接口视图。c. 执行命令 **port-queue** cos-value { { **pq** | **wfq** **weight** weight-value | **lpq** } |**shaping** { shaping-value | **shaping-percentage** shaping-percentagevalue } [ **pbs** pbs-value ] | **port-wred** wred-name | **low-latency** } [*]**outbound** ，配置接口出方向的 QoS 服务等级、承诺信息速率和峰值速率。d. （可选）执行命令 **qos buffer-monitor default-queue enable** ，使能默认SQ 队列的缓存监控功能。接口使能默认 SQ 队列的缓存监控功能后，设备会采集接口默认 SQ 队列的缓存值，通过网管或执行命令 **display port-queue statistics interface****outbound default** 观察缓存值变化趋势，可以辅助预判是否可能会出现流量拥塞。e. （可选）执行命令 **port-queue-alarm** cos-value **buffer** **percentage**percentage-value ，配置接口出方向的 QoS 队列的使用率告警门限。f. （可选）执行命令 **port-queue-alarm** cos-value { **discard-packet** discardpacket-number | **discard-byte** discard-byte-number | **discard-packet-****ratio** discard-packet-coefficient discard-packet-exponent } **interval**interval-time ，配置端口队列丢弃告警功能。g. 执行命令 **commit** ，提交配置。h. 执行命令 **quit** ，退回到系统视图。       - 在槽位视图配置的 MTunnel 上应用 WRED 。a. 执行命令 **slot** slot-id ，进入槽位视图。b. 执行命令 **port-queue** cos-value { { **pq** | **wfq** **weight** weight-value | **lpq** } |**shaping** { shaping-value | **shaping-percentage** shaping-percentagevalue } | **port-wred** wred-name } * **outbound** **bind** **mtunnel** ，在分布式组播 VPN 绑定的 MTunnel 上为不同等级的组播报文端口队列配置调度策略，并在调度策略中应用配置好的 WRED 模板。c. 执行命令 **commit** ，提交配置。**----**结束文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 34HUAWEI NetEngine40E配置指南 1 QoS##### 1.4.4.4 检查配置结果WRED 配置成功后，可以查看 WRED 配置情况。##### 操作步骤       - 使用 **display port-wred configuration** [ **verbose** [ port-wred-name ] ] 命令查看 WRED 模板配置的参数。       - 使用 **display port-queue configuration interface** interface-type interfacenumber **outbound** 命令用来显示端口队列配置详细信息。       - 使用 **display port-queue statistics** [ **slot** slot-id | **interface** interface-typeinterface-number ] [ cos-value ] **outbound** [ **default** ] 命令查看端口队列的统计信息。       - 使用 **display port-queue statistics slot** slot-id [ cos-value ] **outbound** **bind****mtunnel** 命令查看分布式组播 VPN 的虚环回 Mtunnel 接口的端口队列的统计信息。**----**结束#### 1.4.5 配置低速链路的队列调度低速链路上，由于网络资源有限，容易产生拥塞。为了避免网络拥塞时报文被统一丢弃，需要将不同业务流量分类到不同的流队列进行 PQ 和 WFQ 队列调度。##### 应用环境低速接口建立的链路上，由于网络资源有限，容易产生拥塞。为了避免网络拥塞时报文被统一丢弃，需要将不同业务流量分类到不同的流队列进行 PQ 和 WFQ 队列调度，优先保证高优先级的报文通过。说明低速链路的队列调度只支持在 Serial 接口、 Trunk-Serial 接口、 Global-MP-Group 接口和 MPgroup 接口下配置。##### 前置任务在配置低速链路的队列调度之前，需完成以下任务：       - 配置相关接口的物理参数       - 配置相关接口的链路层属性，保证接口的正常工作       - 配置相关接口的 IP 地址       - 使能路由协议，实现互通##### ******* 配置 PQ 调度在低速接口的链路上，将不同业务流量分类到不同的队列进行绝对优先级队列调度。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 35HUAWEI NetEngine40E配置指南 1 QoS##### 背景信息说明af2 、 af3 、 af4 、 ef 、 cs6 、 cs7 队列的报文支持配置 PQ 调度。配置 PQ 队列调度的队列优先级高于 WFQ 队列调度。对一个队列如果同时配置 PQ 和 WFQ 的队列调度，后配置的生效。在待配置路由器上进行以下配置。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **interface** interface-type interface-number ，进入接口视图。说明低速链路的队列调度只支持在 Serial 接口、 Trunk-Serial 接口、 Global-MP-Group 接口和 MPgroup 接口下配置。步骤 **3** 执行命令 **port-queue** { **af2** | **af3** | **af4** | **ef** | **cs6** | **cs7** } **pq shaping pir** { pir-value |**percentage** pir-percent } ，配置 PQ 队列调度参数。步骤 **4** 执行命令 **commit** ，提交配置。**----**结束##### 1.4.5.2 配置 WFQ 调度在低速接口的链路上，将不同业务流量分类到不同的队列进行加权公平队列调度。##### 背景信息在待配置路由器上进行以下配置。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **interface** interface-type interface-number ，进入接口视图。说明低速链路的队列调度只支持在 Serial 接口、 Trunk-Serial 接口、 Global-MP-Group 接口和 MPgroup 接口下配置。步骤 **3** 根据想要选择的 WFQ 队列调度模式分别执行以下步骤：       - 选择 Weight 调度模式时，进行以下配置：a. 执行命令 **set port-queue weight-mode** 命令，将接口的 WFQ 队列调度模式配置为 Weight 模式。b. 执行命令 **port-queue** { **cs7** | **cs6** | **ef** | **af4** | **af3** | **af2** | **af1** | **be** } { **wfq****weight** weight-value | **shaping** { shaping-value | **shaping-percentage**shaping-percentage-value } } [*] **outbound** ，配置接口出方向上端口队列的WFQ 调度参数，包括权重和整形速率。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 36
最终截取: ##### 1.4.4.3 应用 WRED


基于业务类型应用配置好的 WRED 模板。

##### 背景信息


在配置了 WRED 模板的路由器上进行以下配置。

##### 操作步骤


       - 在接口视图上应用 WRED


a. 执行命令 **system-view** ，进入系统视图。


b. 执行命令 **interface** interface-type interface-number ，进入接口视图。


c. 执行命令 **port-queue** cos-value { { **pq** | **wfq** **weight** weight-value | **lpq** } |
**shaping** { shaping-value | **shaping-percentage** shaping-percentagevalue } [ **pbs** pbs-value ] | **port-wred** wred-name | **low-latency** } [*]

**outbound** ，配置接口出方向的 QoS 服务等级、承诺信息速率和峰值速率。

d. （可选）执行命令 **qos buffer-monitor default-queue enable** ，使能默认
SQ 队列的缓存监控功能。


接口使能默认 SQ 队列的缓存监控功能后，设备会采集接口默认 SQ 队列的缓存
值，通过网管或执行命令 **display port-queue statistics interface**
**outbound default** 观察缓存值变化趋势，可以辅助预判是否可能会出现流量
拥塞。

e. （可选）执行命令 **port-queue-alarm** cos-value **buffer** **percentage**
percentage-value ，配置接口出方向的 QoS 队列的使用率告警门限。


f. （可选）执行命令 **port-queue-alarm** cos-value { **discard-packet** discardpacket-number | **discard-byte** discard-byte-number | **discard-packet-**
**ratio** discard-packet-coefficient discard-packet-exponent } **interval**
interval-time ，配置端口队列丢弃告警功能。


g. 执行命令 **commit** ，提交配置。


h. 执行命令 **quit** ，退回到系统视图。


       - 在槽位视图配置的 MTunnel 上应用 WRED 。


a. 执行命令 **slot** slot-id ，进入槽位视图。


b. 执行命令 **port-queue** cos-value { { **pq** | **wfq** **weight** weight-value | **lpq** } |
**shaping** { shaping-value | **shaping-percentage** shaping-percentagevalue } | **port-wred** wred-name } * **outbound** **bind** **mtunnel** ，在分布式组
播 VPN 绑定的 MTunnel 上为不同等级的组播报文端口队列配置调度策略，并
在调度策略中应用配置好的 WRED 模板。


c. 执行命令 **commit** ，提交配置。


**----**
结束


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 34



HUAWEI NetEngine40E
配置指南 1 QoS

