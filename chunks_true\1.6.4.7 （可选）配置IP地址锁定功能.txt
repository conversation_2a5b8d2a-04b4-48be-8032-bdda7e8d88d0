

为了提高安全性， FTP 支持 IP 地址锁定功能，防止恶意用户攻击，破坏用户密码。

##### 背景信息


用户 FTP 登录失败后，根据 IP 地址记录 FTP 登录失败的次数，当一定时间内失败次数达
到阈值后，将 IP 地址锁定，所有通过该 IP 地址登录的用户均不能正常连接。

##### 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **undo ftp server ip-block disable** ，使能 FTP 服务器上的客户端 IP 地址锁定
功能。


步骤 **3** 执行命令 **ftp server ip-block failed-times** failed-times **period** period ，配置锁定用
户前的连续认证失败次数和连续失败时间周期。


步骤 **4** 执行命令 **ftp server ip-block reactive** reactive-period ，配置锁定用户前的连续认证
失败次数和连续失败时间周期。


步骤 **5** 执行命令 **commit** ，提交配置。


步骤 **6** 执行命令 **quit** ，进入用户视图。


步骤 **7** 执行命令 **activate ftp server ip-block ip-address** ip-address [ **vpn-instance** vpnname ] ，解除对认证失败的 IP 地址的阻止。


**----**
结束


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 153


HUAWEI NetEngine40E
配置指南 1 基础配置
