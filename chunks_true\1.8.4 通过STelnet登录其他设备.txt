

STelnet 是一种安全的 Telnet 服务，用户可以通过 STelnet 方式从当前设备登录到另一台
设备，对其进行远程管理。

##### 应用环境


网络中有大量设备需要管理与维护，用户不可能为每台设备连接用户终端，特别是终
端与需要管理的设备之间无可达路由时，用户可以使用 Telnet 方式从当前设备登录到网
络上另一台设备，从而实现对远程设备的管理与维护。但是 Telnet 缺少安全的认证方
式，而且传输过程采用 TCP 进行简单方式传输，存在很大的安全隐患。


而 STelnet 是一种安全的 Telnet 服务，建立在 SSH 连接的基础之上。 SSH 可以利用加密和
强大的认证功能提供安全保障，保护设备不受诸如 IP 地址欺诈等攻击。如 图 **1-63** 所
示，设备支持 SSH 功能，用户可以通过 SSH 方式登录到远端设备上，对设备进行远程管
理和维护。此时，当前的设备是客户端，待登录的设备是 SSH 服务器。


图 **1-63** 通过 STelnet 登录其他设备示意图

##### 前置任务


在通过 STelnet 登录其他设备之前，需要完成以下任务：


       - 成功 配置用户通过 **STelnet** 登录系统 。
