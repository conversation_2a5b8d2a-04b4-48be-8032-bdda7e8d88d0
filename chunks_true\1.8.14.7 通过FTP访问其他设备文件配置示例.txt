

在本示例中，通过从 FTP 客户端登录 FTP 服务器，实现从 FTP 服务器中下载系统软件和
配置文件到客户端。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 300


HUAWEI NetEngine40E
配置指南 1 基础配置

##### 背景信息


说明


使用 FTP 协议存在安全风险，建议使用 SFTP 方式访问其他设备文件。

##### 组网需求


当用户需要与远程 FTP 服务器进行文件传输、目录管理等操作时，可以配置当前设备为
FTP 客户端，通过 FTP 方式访问远程 FTP 服务器，以实现远程管理和维护。


如 图 **1-71** 所示，作为 FTP 客户端的设备和 FTP 服务器之间路由可达，用户可通过从 FTP
客户端登录 FTP 服务器，实现从 FTP 服务器中下载系统软件和配置文件到客户端。


图 **1-71** 配置通过 FTP 访问其他设备文件组网图


说明


本例中的 Interface1 代表接口 1/0/1 。

##### 配置思路


采用如下的思路配置通过 FTP 访问其他设备文件：


1. 配置用户登录 FTP 服务器的用户名和密码及访问目录。


2. 使能 FTP 服务器的 FTP 功能。


3. 使用 FTP 客户端登录命令实现登录 FTP 服务器。


4. 配置客户端文件传输方式和工作目录，实现从服务器下载用户所需文件。

##### 数据准备


为完成此配置示例，需准备如下的数据：


       - FTP 客户端用于登录的用户名和密码。


       - FTP 服务器的 IP 地址为 ******* 。


       - 目标文件及在 FTP 客户端中的位置。

##### 注意事项


当网络所处环境不足够安全时，我们建议选择安全的协议。安全协议举例参见：
************* 通过 **SFTP** 访问其他设备文件配置示例（ **ECC** 认证方式） 。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 301


HUAWEI NetEngine40E
配置指南 1 基础配置

##### 操作步骤


步骤 **1** 在 FTP 服务器上配置 FTP 用户。


<HUAWEI> **system-view**

[ ~ HUAWEI] **aaa**

[*HUAWEI-aaa] **local-user huawei password**
Please configure the password (8-128)
Enter Password:
Confirm Password:


说明


设置的密码必须满足以下要求：


          - 密码采取交互式输入，系统不回显输入的密码。


          - 输入的密码为字符串形式，区分大小写，长度范围是 8 ～ 16 。输入的密码至少包含两种类型
字符，包括大写字母、小写字母、数字及特殊字符。


          - 特殊字符不包括“？”和空格。但是，当输入的密码两端使用双引号时，可在密码中间输入
空格。


–
如果使用双引号设置带空格密码，双引号之间不能再使用双引号。


–
如果使用双引号没有设置带空格密码，双引号之间可以再使用双引号。


例如， "Aa 123"45"" 为不合法密码， "Aa123"45"" 为合法密码。


配置文件中将以密文形式体现设置的密码。


[*HUAWEI-aaa] **local-user huawei service-type ftp**

[*HUAWEI-aaa] **local-user huawei ftp-directory cfcard:/**

[*HUAWEI-aaa] **local-user huawei level 3**

[*HUAWEI-aaa] **commit**

[ ~ HUAWEI-aaa] **quit**


步骤 **2** 使能 FTP 服务功能


[ ~ HUAWEI] **interface LoopBack 0**

[ ~ HUAWEI-LoopBack0] **ip address ******** *****************

[*HUAWEI-LoopBack0] **quit**

[*HUAWEI] **ftp server enable**

[*HUAWEI] **ftp server-source** **-i loopback 0**

[*HUAWEI] **commit**

[ ~ HUAWEI] **quit**


步骤 **3** 从 FTP 客户端登录到 FTP 服务器。


<HUAWEI> **ftp **********
Trying ******** ...
Press CTRL+K to abort

Connected to *******.
220 FTP service ready.
User(********:(none)):huawei
331 Password required for huawei.
Enter password:
230 User logged in.

[ftp]


步骤 **4** 在 FTP 客户端上，配置二进制传输格式和 Flash 工作目录。


[ ftp] **binary**
200 Type set to I.

[ftp] **lcd new_dir:/**
The current local directory is new_dir:.

[ftp] **commit**


步骤 **5** 在 FTP 客户端上，从远端 FTP 服务器下载最新系统软件。


[ftp] **get V800R023C10SPC500B020D0123.cc**
200 Port command okay.
150 Opening BINARY mode data connection for V800R023C10SPC500B020D0123.cc.
226 Transfer complete.
FTP: 1127 byte(s) received in 0.156 second(s) 7.22Kbyte(s)/sec.


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 302


HUAWEI NetEngine40E
配置指南 1 基础配置


[ftp] **quit**


可以使用 **dir** 命令查看是否将所需的文件下载到客户端。


**----**
结束

##### 配置文件


       - FTP 服务器上的配置文件


#

aaa
local-user huawei password cipher @%@%UyQs4,KTtSwJo(4QmW#K,LC:@%@%
local-user huawei ftp-directory cfcard:/
local-user huawei level 3
local-user huawei service-type ftp
#
interface GigabitEthernet1/0/1
undo shutdown
ip address ******* *************
#
interface loopback 0
ip address ******** ***************
ftp server enable
ftp server-source -i loopback 0
#

return


       - FTP 客户端上的配置文件


#
interface GigabitEthernet1/0/1
undo shutdown
ip address ******* *************
#

return
