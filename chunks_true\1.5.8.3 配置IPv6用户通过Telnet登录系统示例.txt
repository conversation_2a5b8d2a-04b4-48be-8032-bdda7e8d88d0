

在本示例中，通过配置 VTY 用户界面以及 IPv6 用户登录参数，实现从 IPv6 客户端登录设
备。

##### 组网需求


IPv6 用户可以通过终端登录到其它网段上的设备，进行远程维护。


图 **1-29** 配置通过 Telnet 登录组网图


说明


本例中的 Interface1 代表接口 GigabitEthernet0/0/0 。

##### 配置思路


1. 建立物理连接。


2. 配置 Device 管理网口的 IPv6 地址。


3. 配置 VTY 用户界面的相关参数，包括呼入呼出限制。


4. 配置登录用户的相关参数。

##### 数据准备


Telnet 客户端已安装 PuTTY 最新版本（ 0.70 latest release 或者 0.71 ），且需保证
Telnet 客户端和设备 Interface1 之间 IPv6 路由通。


为完成此配置举例，需准备如下的数据：


       - Device 管理网口的 IPv6 地址。


       - VTY 用户界面的最大个数为 15 。


       - 禁止登录的用户登录其他路由器的 ACL6 号为 3001 。


       - VTY 用户界面断开连接的时间 20 分钟。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 111


HUAWEI NetEngine40E
配置指南 1 基础配置


       - VTY 用户界面的终端屏幕每屏显示的行数为 30 。


       - VTY 用户界面的历史命令缓冲区大小为 20 。


       - 通过 Telnet 协议访问的 IPv6 用户的用户名（ huawei123 ）、密码
（ YsHsjx_202206 ）、验证方式（ AAA ）。

##### 配置注意事项


当网络所处环境不足够安全时，我们建议选择安全的协议。安全协议举例参见：
*********** 配置 **IPv6** 用户通过 **STelnet** 登录系统示例 。

##### 操作步骤


步骤 **1** 在 PC 端和路由器端分别和网络连接


步骤 **2** 配置登录 IPv6 地址


<HUAWEI> **system-view**

[ ~ HUAWEI] **sysname Device**

[*HUAWEI] **commit**

[ ~ Device] **interface GigabitEthernet0/0/0**

[ ~ Device-GigabitEthernet0/0/0] **undo shutdown**

[ ~ Device-GigabitEthernet0/0/0] **ipv6 enable**

[*Device-GigabitEthernet0/0/0] **ipv6 address 2001:db8::1 32**

[*Device-GigabitEthernet0/0/0] **commit**

[ ~ Device-GigabitEthernet0/0/0] **quit**


步骤 **3** 使能 Telnet 服务


[ ~ Device] **telnet ipv6 server enable**

[*Device] **telnet ipv6 server-source** **-a 2001:db8::1**

[*Device] **commit**


步骤 **4** 在路由器端配置 VTY 用户界面


# 配置 VTY 用户界面的最大个数。


[ ~ Device] **user-interface maximum-vty 15**

[*Device] **commit**


# 配置登录的用户禁止登录其他路由器。


[ ~ Device] **acl ipv6 3001**

[*Device-acl6-advance-3001] **rule deny tcp source any destination-port eq telnet**

[*Device-acl6-advance-3001] **quit**

[*Device] **user-interface vty 0 14**

[*Device-ui-vty0-14] **acl ipv6 3001 outbound**


# 配置 VTY 用户界面的终端属性。


[*Device-ui-vty0-14] **shell**

[*Device-ui-vty0-14] **idle-timeout 20**

[*Device-ui-vty0-14] **screen-length 30**

[*Device-ui-vty0-14] **history-command max-size 20**


# 配置 VTY 用户界面的用户验证方式。


[*Device-ui-vty0-14] **authentication-mode aaa**

[*Device-ui-vty0-14] **commit**

[ ~ Device-ui-vty0-14] **quit**


步骤 **5** 在路由器端配置登录用户参数


# 配置登录验证方式。


[ ~ Device] **aaa**


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 112


HUAWEI NetEngine40E
配置指南 1 基础配置


[*Device-aaa] **local-user huawei123 password**
Please configure the password (8-128)
Enter Password:
Confirm Password:


说明


          - 不选择 **cipher** 或 **irreversible-cipher** 关键字时，密码以交互式输入，系统不回显密码。


输入的密码为字符串形式，区分大小写，开启用户账户安全策略时，取值范围是 8 ～ 128 。关
闭用户账户安全策略时，长度范围是 1 ～ 128 。开启用户账户安全策略时，密码不能与用户名
及其用户名反向字符串相同，且密码必须包括大写字母、小写字母、数字及特殊字符。


特殊字符不包括“？”和空格。但是，当输入的密码两端使用双引号时，可在密码中间输入
空格。


–
如果使用双引号设置带空格密码，双引号之间不能再使用双引号。


–
如果使用双引号没有设置带空格密码，双引号之间可以再使用双引号。


例如， "Aa 123"45"" 为不合法密码， "Aa123"45"" 为合法密码。


          - 选择 **cipher** 关键字时，密码可以以简单形式输入，也可以以密文形式输入。


密码以简单形式输入，要求与不选择 **cipher** 关键字时一样。密码以简单形式输入，系统会回
显简单形式的密码，存在安全风险，因此建议使用交互式输入密码。


无论是简单输入还是密文输入，配置文件中都以密文形式体现。


          - 选择 **irreversible-cipher** 关键字时，密码可以以简单形式输入，也可以以不可逆密文形式输
入。


密码以简单形式输入，要求与不选择 **irreversible-cipher** 关键字时一样。


无论是简单输入还是不可逆密文输入，配置文件中都以密文形式体现。


[*Device-aaa] **local-user huawei123 service-type telnet**

[*Device-aaa] **local-user huawei123 level 3**

[*Device-aaa] **commit**

[ ~ Device-aaa] **quit**


步骤 **6** 配置 Telnet 客户端登录


双击 PuTTY.exe 打开 PuTTY 配置界面，选择“ Session ”，在“ Host Name (or IP
address) ”输入框中输入欲访问的服务器 IPv6 地址，端口号使用默认的 23 号，如 图
**1-30** 所示。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 113


HUAWEI NetEngine40E
配置指南 1 基础配置


图 **1-30** PuTTY 的 Telnet 登录界面


单击“ Open ”，会提示用户输入用户名和密码，如 图 **1-31** 所示（本例中用户名为
huawei123 ，密码为 YsHsjx_202206 ）。


图 **1-31** 登录设备后的界面


**----**
结束

##### Device 的配置文件


#
sysname Device
#

acl number 3001
rule 5 deny tcp destination-port eq telnet


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 114


HUAWEI NetEngine40E
配置指南 1 基础配置


#

aaa
local-user huawei123 password irreversible-cipher $1c$]zV2B\j!z:$hRujV[%/IE|
0MwBQ}5sAX(RdE[oj#5otqG6=@>KK$
local-user huawei123 service-type telnet
local-user huawei123 level 3

local-user huawei123 state block fail-times 3 interval 5

#
interface GigabitEthernet0/0/0
undo shutdown
ipv6 enable
ipv6 address 2001:DB8::1/32
#
telnet ipv6 server enable
telnet ipv6 server-source -a 2001:db8::1
#
user-interface maximum-vty 15
#
user-interface vty 0 14
acl ipv6 3001 outbound
authentication-mode aaa
history-command max-size 20
idle-timeout 20 0
screen-length 30
#

return
