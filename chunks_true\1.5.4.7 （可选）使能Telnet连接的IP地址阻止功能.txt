

Telnet 连接的 IP 地址阻止功能可以阻止认证失败的 IP 地址通过 Telnet 登录设备，去使能
状态下，设备容易在安全方面遭受网络攻击。

##### 背景信息


当设备作为 Telnet 服务器时，为提高安全性，可通过配置 IP 地址阻止功能防止设备遭受
网络攻击。


请在作为 Telnet 服务器的设备上进行如下的配置。

##### 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图


步骤 **2** 执行命令 **undo telnet server ip-block disable** ，使能 Telnet 连接的 IP 地址阻止功能。


步骤 **3** 执行命令 **commit** ，提交配置。


说明


只有当 IP 地址阻止功能处于使能状态时，认证失败的 IP 地址才能被阻止，从而提升设备安全性。


步骤 **4** 认证失败的 IP 地址将会被锁定 5 分钟，如果需要对被锁定的 IP 地址提前解除阻止，可执
行如下步骤：


1. 执行命令 **quit** ，退出系统视图，进入用户视图。


2. 执行命令 **activate vty ip-block ip-address** ip-address [ **vpnname** vpnname ] ，解除对 Telnet 连接中认证失败的 IP 地址的阻止。


**----**
结束


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 70


HUAWEI NetEngine40E
配置指南 1 基础配置
