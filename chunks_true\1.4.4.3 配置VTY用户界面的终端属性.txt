

VTY 用户界面的终端属性包括用户超时断连功能、终端屏幕的显示行数以及历史命令缓
冲区的大小。

##### 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **user-interface** **vty** first-ui-number [ last-ui-number ] ，进入 VTY 用户界面
视图。


步骤 **3** 执行命令 **shell** ，启用 VTY 终端服务。


步骤 **4** 执行命令 **idle-timeout** minutes [ seconds ] ，设置用户超时断连功能。


在设定的时间内，如果连接始终处于空闲状态，系统将自动断开该连接。


步骤 **5** 执行命令 **screen-length** screen-length ，设置终端屏幕每屏显示的行数。


步骤 **6** 执行命令 **history-command max-size** size-value ，设置历史命令缓冲区的大小。


步骤 **7** 执行命令 **commit** ，提交配置。


**----**
结束


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 45


HUAWEI NetEngine40E
配置指南 1 基础配置
