

配置公网 SSH 客户端访问私网 SSH 服务器的示例。在本示例中，通过配置公网用户的
SSH 相关属性信息，实现公网用户分别以 STelnet 方式和 SFTP 方式访问私网设备。

##### 组网需求


如 图 **1-77** 所示，作为 SSH 客户端的设备 PE1 位于 MPLS 骨干网中，作为 SSH 服务器的 CE1
位于 AS 号为 65410 的私网中。公网用户可以通过 PE1 安全地访问和管理私网设备 CE1 。


图 **1-77** 配置公网 SSH 客户端访问私网 SSH 服务器组网图


说明


本例中的 Interface1 ， Interface2 ， Interface3 分别代表接口 GE1/0/1 、 GE2/0/1 和 GE1/0/2 。

##### 配置思路


采用如下的思路配置 SSH 支持私网访问：


1. 在作为 SSH 客户端的 PE 设备上配置 VPN 实例，实现将 CE 接入 PE 。


2. 在 PE 与 CE 之间建立 EBGP 对等体关系，引入 VPN 路由。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 336


HUAWEI NetEngine40E
配置指南 1 基础配置


3. 分别在 STelnet 客户端 Client002 和 SSH 服务器端生成本地密钥对，并为用户
client002 绑定 SSH 客户端的 RSA 公钥，实现客户端登录服务器端时，对客户端进行
验证。


4. SSH 服务器端 STelnet 和 SFTP 服务使能。


5. 公网用户 client001 、 client002 分别以 STelnet 方式和 SFTP 方式访问私网设备。

##### 数据准备


为完成此配置例，需准备如下的数据：


       - PE 设备上的 vpn-instance 名称为 vpn1 。


       - PE 设备上的 vpn-target 为 111:1 。


       - PE1 的 IP 地址为 ******** ， PE2 的 IP 地址为 ******** 。


       - 用户 client001 ，登录验证方式为 password 。


       - 用户 client002 ，验证方式为 RSA ，并为其分配公钥 RsaKey001 。


       - 私网 SSH 服务器 CE1 的 IP 地址为 ******** 。

##### 操作步骤


步骤 **1** 配置 MPLS 骨干网


配置 MPLS 骨干网上配置 IGP 协议，实现骨干网 PE 和 P 的互通。在 MPLS 骨干网上配置
MPLS 基本能力和 MPLS LDP ，建立 LDP LSP 。


具体配置过程略，可参见本示例的配置文件。


步骤 **2** 在 PE 上配置 VPN 实例，将 CE 接入 PE


# 配置 PE1 。


[*PE1] **ip vpn-instance vpn1**

[*PE1-vpn-instance-vpn1] **route-distinguisher 100:1**

[*PE1-vpn-instance-vpn1-af-ipv4] **vpn-target 111:1 both**

[*PE1-vpn-instance-vpn1-af-ipv4] **quit**

[*PE1-vpn-instance-vpn1] **quit**

[*PE1] **interface gigabitethernet 2/0/1**

[*PE1-GigabitEthernet2/0/1] **ip binding vpn-instance vpn1**

[*PE1-GigabitEthernet2/0/1] **undo shutdown**

[*PE1-GigabitEthernet2/0/1] **ip address ******** 24**

[*PE1-GigabitEthernet2/0/1] **quit**

[*PE1] **commit**


# 配置 PE2 。


[*PE2] **ip vpn-instance vpn1**

[*PE2-vpn-instance-vpn1] **route-distinguisher 200:1**

[*PE2-vpn-instance-vpn1-af-ipv4] **vpn-target 111:1 both**

[*PE2-vpn-instance-vpn1-af-ipv4] **quit**

[*PE2-vpn-instance-vpn1] **quit**

[*PE2] **interface gigabitethernet 2/0/1**

[*PE2-GigabitEthernet2/0/1] **ip binding vpn-instance vpn1**

[*PE2-GigabitEthernet2/0/1] **undo shutdown**

[*PE2-GigabitEthernet2/0/1] **ip address ******** 24**

[*PE2-GigabitEthernet2/0/1] **quit**

[*PE2] **commit**


# 按 图 **1-77** 配置各 CE 的接口 IP 地址，配置过程略。


配置完成后，在 PE 上执行 **display ip vpn-instance verbose** 命令可以看到 VPN 实例的
配置情况。各 PE 能 ping 通自己接入的 CE 。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 337


HUAWEI NetEngine40E
配置指南 1 基础配置


说明


当 PE 上有多个绑定了同一个 VPN 的接口，则使用 **ping -vpn-instance** 命令 ping 对端 PE 接入的 CE
时，要指定源 IP 地址，即要指定 **ping -vpn-instance** vpn-instance-name **-a** source-ip-address
dest-ip-address 命令中的参数 **-a** source-ip-address ，否则可能 ping 不通。


[ ~ PE1] **ping -vpn-instance vpn1 **********
PING ********: 56 data bytes, press CTRL_C to break
Reply from ********: bytes=56 Sequence=1 ttl=255 time=260 ms
Reply from ********: bytes=56 Sequence=2 ttl=255 time=70 ms
Reply from ********: bytes=56 Sequence=3 ttl=255 time=60 ms
Reply from ********: bytes=56 Sequence=4 ttl=255 time=60 ms
Reply from ********: bytes=56 Sequence=5 ttl=255 time=90 ms
--- ******** ping statistics --5 packet(s) transmitted
5 packet(s) received
0.00% packet loss
round-trip min/avg/max = 60/108/260 ms


步骤 **3** 在 PE 与 CE 之间建立 EBGP 对等体关系，引入 VPN 路由


# 配置 CE1 。


[*CE1] **bgp 65410**

[*CE1-bgp] **peer ******** as-number 100**

[*CE1-bgp] **import-route direct**

[*CE1-bgp] **quit**

[*CE1] **commit**


# 配置 PE1 。


[*PE1] **bgp 100**

[*PE1-bgp] **ipv4-family vpn-instance vpn1**

[*PE1-bgp-vpn1] **peer ******** as-number 65410**

[*PE1-bgp-vpn1] **import-route direct**

[*PE1-bgp-vpn1] **quit**

[*PE1-bgp] **quit**

[*PE1] **commit**


# 配置 CE2 。


[*CE2] **bgp 65420**

[*CE2-bgp] **peer ******** as-number 100**

[*CE2-bgp] **import-route direct**

[*CE2-bgp] **quit**

[*CE2-bgp] **commit**


# 配置 PE2 。


[*PE2] **bgp 100**

[*PE2-bgp] **ipv4-family vpn-instance vpn1**

[*PE2-bgp-vpn1] **peer ******** as-number 65420**

[*PE2-bgp-vpn1] **import-route direct**

[*PE2-bgp-vpn1] **quit**

[*PE2-bgp] **quit**

[*PE2-bgp] **commit**


配置完成后，在 PE 上执行 **display bgp vpnv4 vpn-instance peer** 命令，可以看到 PE
与 CE 之间的 BGP 对等体关系已建立，并达到 Established 状态。


以 PE1 与 CE1 的对等体关系为例：


[ ~ PE1] **display bgp vpnv4 vpn-instance vpn1 peer**
BGP local router ID : *******

Local AS number : 100
Total number of peers : 1         Peers in established state : 1
Peer     V   AS MsgRcvd MsgSent OutQ  Up/Down    State  PrefRcv
********   4  65410    3    3   0 00:00:37 **Established** 1


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 338


HUAWEI NetEngine40E
配置指南 1 基础配置


# 在 PE 之间建立 MP-IBGP 对等体关系


具体配置过程略，可参考本示例的配置文件。


步骤 **4** 在服务器端生成本地密钥对


[*CE1] **rsa local-key-pair create**
The key name will be: CE1_Host
The range of public key size is (2048, 3072).
NOTE: Key pair generation will take a short while.
Please input the modulus [default = 3072]:3072
Generating keys...

[*CE1] **commit**


步骤 **5** 配置服务器端 RSA 公钥


# 客户端生成客户端的本地密钥对


[*PE1] **rsa local-key-pair create**
The key name will be: PE1_Host
The range of public key size is (2048, 3072).
NOTE: Key pair generation will take a short while.
Please input the modulus [default = 3072]:3072
Generating keys...

[*PE1] **commit**


# 查看客户端上生成 RSA 公钥。


[ ~ PE1] **display rsa local-key-pair public**

=====================================================

Time of Key pair created: 12:02:09 2007/6/8
Key name: PE1_Host
Key type: RSA encryption Key

=====================================================

Key code:
3047

0240

BC011055 8BCCB887 384E5A14 1EF982A8 CA44A376

87787138 3BDB1FF0 D21F05D8 41BECF56 B2FA0695

8F76F1B2 5D3E2F35 A8051CE1 E0234274 9D8BB20D

E2EE8EB5

0203

010001
Host public key for PEM format code:
---- BEGIN SSH2 PUBLIC KEY ---AAAAB3NzaC1yc2EAAAADAQABAAAAQQC8ARBVi8y4hzhOWhQe+YKoykSjdod4cTg7
2x/w0h8F2EG+z1ay+gaVj3bxsl0+LzWoBRzh4CNCdJ2Lsg3i7o61
---- END SSH2 PUBLIC KEY ---Public key code for pasting into OpenSSH authorized_keys file :
ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAAAQQC8ARBVi8y4hzhOWhQe+YKoykSjdod4cTg72x/w0h8F
2EG+z1ay+gaVj3bxsl0+LzWoBRzh4CNCdJ2Lsg3i7o61 rsa-key

=====================================================

Time of Key pair created: 12:02:09 2007/6/8
Key name: PE1_Server
Key type: RSA encryption Key

=====================================================

Key code:
3067

0260

9E6EDDE7 AEFF3F9F 5090ECA5 11DE117E 6660707F

23AC8DE2 BDB58E1E D46856B5 419CAEDF 3A33DD40

278C6403 24ADC2E6 B110A8ED B6CC644F 055C5437

D720D3D8 9A3F9DE5 4FE062DF F2DC443E 9092A0F4

970B8CC9 C8684678 CF0682F3 6301F5F3

0203

010001


# 将客户端上产生的 RSA 公钥传送到服务器端。


[*CE1] **rsa peer-public-key rsakey001**


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 339


HUAWEI NetEngine40E
配置指南 1 基础配置


Enter "RSA public key" view, return system view with "peer-public-key end".

[*CE1-rsa-public-key] **public-key-code begin**
Enter "RSA key code" view, return last view with "public-key-code end".

[*CE1-rsa-key-code] **3067**

[*CE1-rsa-key-code] **0240**

[*CE1-rsa-key-code] **BC011055 8BCCB887 384E5A14 1EF982A8 CA44A376**

[*CE1-rsa-key-code] **87787138 3BDB1FF0 D21F05D8 41BECF56 B2FA0695**

[*CE1-rsa-key-code] **8F76F1B2 5D3E2F35 A8051CE1 E0234274 9D8BB20D**

[*CE1-rsa-key-code] **E2EE8EB5**

[*CE1-rsa-key-code] **0203**

[*CE1-rsa-key-code] **010001**

[*CE1-rsa-key-code] **public-key-code end**

[*CE1-rsa-public-key] **peer-public-key end**

[*CE1-rsa-public-key] **quit**

[*CE1] **commit**


步骤 **6** 在服务器端创建 SSH 用户


说明


SSH 用户主要有 Password 、 RSA 、 password-rsa 、 ECC 、 password-ecc 、 DSA 、 password-dsa 、
SM2 、 password-sm2 或 all 这几种认证方式：


          - 如果 SSH 用户的认证方式为 password 、 password-rsa 、 password-dsa 、 password-sm2 和
password-ecc 时，必须配置同名的 local-user 用户。


          - 如果 SSH 用户的认证方式为 RSA 、 password-rsa 、 DSA 、 password-dsa 、 SM2 、 passwordsm2 、 ECC 、 password-ecc 和 all ，服务器端应保存 SSH 客户端的 RSA 、 DSA 、 SM2 或 ECC 公
钥。


# 配置 VTY 用户界面。


[ ~ CE1] **user-interface vty 0 4**

[ ~ CE1-ui-vty0-4] **authentication-mode aaa**

[*CE1-ui-vty0-4] **protocol inbound ssh**

[*CE1-ui-vty0-4] **commit**

[ ~ CE1-ui-vty0-4] **quit**


       - 创建 SSH 用户 Client001 。


# 新建用户名为 Client001 的 SSH 用户，且认证方式为 password 。


[ ~ CE1] **ssh user client001**

[*CE1] **ssh user client001 authentication-type password**

[*SSH Server] **ssh server cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr**

[*SSH Server] **ssh server hmac sha2_512 sha2_256**

[*SSH Server] **ssh server key-exchange dh_group_exchange_sha256**

[*SSH Server] **ssh server publickey rsa_sha2_256 rsa_sha2_512**

[*SSH Server] **ssh server dh-exchange min-len 3072**

[*SSH Server] **ssh client publickey rsa_sha2_256 rsa_sha2_512**

[*SSH Server] **ssh client cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr**

[*SSH Server] **ssh client hmac sha2_512 sha2_256**

[*SSH Server] **ssh client key-exchange dh_group_exchange_sha256**


# 为 SSH 用户 Client001 配置密码。


[*CE1] **aaa**

[*CE1-aaa] **local-user client001 password**
Please configure the password (8-128)
Enter Password:
Confirm Password:


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 340


HUAWEI NetEngine40E
配置指南 1 基础配置


说明


设置的密码必须满足以下要求：


–
密码采取交互式输入，系统不回显输入的密码。


– 输入的密码为字符串形式，区分大小写，长度范围是 8 ～ 16 。输入的密码至少包含两种
类型字符，包括大写字母、小写字母、数字及特殊字符。


–
特殊字符不包括“？”和空格。但是，当输入的密码两端使用双引号时，可在密码中间
输入空格。

#### ▪ 如果使用双引号设置带空格密码，双引号之间不能再使用双引号。 ▪ 如果使用双引号没有设置带空格密码，双引号之间可以再使用双引号。


例如， "Aa 123"45"" 为不合法密码， "Aa123"45"" 为合法密码。


配置文件中将以密文形式体现设置的密码。


[*CE1-aaa] **local-user client001 service-type ssh**

[*CE1-aaa] **quit**


# 配置 Client001 的服务方式为 STelnet 。


[*CE1] **ssh user client001 service-type stelnet**


       - 新建用户名为 Client002 的 SSH 用户，且认证方式为 RSA ，并绑定 SSH 客户端 RSA 公
钥。


[*CE1] **ssh user client002**

[*CE1] **ssh user client002 authentication-type rsa**

[*CE1] **ssh user client002 assign rsa-key rsakey001**

[*SSH Server] **ssh server cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr**

[*SSH Server] **ssh server hmac sha2_512 sha2_256**

[*SSH Server] **ssh server key-exchange dh_group_exchange_sha256**

[*SSH Server] **ssh server publickey rsa_sha2_256 rsa_sha2_512**

[*SSH Server] **ssh server dh-exchange min-len 3072**

[*SSH Server] **ssh client publickey rsa_sha2_256 rsa_sha2_512**

[*SSH Server] **ssh client cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr**

[*SSH Server] **ssh client hmac sha2_512 sha2_256**

[*SSH Server] **ssh client key-exchange dh_group_exchange_sha256**


# 配置 Client002 的服务方式为 SFTP ，并为其配置授权目录。


[*CE1] **ssh user client002 service-type sftp**

[*CE1] **ssh user client002 sftp-directory cfcard:**

[*CE1] **commit**


步骤 **7** SSH 服务器端 STelnet 和 SFTP 服务使能


[ ~ CE1] **interface LoopBack 0**

[ ~ CE1-LoopBack0] **ip address ******** *****************

[*CE1-LoopBack0] **quit**

[*CE1] **stelnet server enable**

[*CE1] **sftp server enable**

[*CE1] **ssh server-source** **-i loopback 0**


[*CE1] **commit**


步骤 **8** PE 设备作为 SSH 客户端登录 CE 设备


# 第一次登录，则需要使能 SSH 客户端首次认证功能。


[ ~ PE1] **ssh client first-time enable**

[*PE1] **commit**


# 以 STelnet 方式登录 SSH 服务器。


[ ~ PE1] **stelnet ******** -vpn-instance vpn1**
Please input the username:client001
Trying ******** ...
Press CTRL+K to abort

Connected to ******** ...


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 341


HUAWEI NetEngine40E
配置指南 1 基础配置


The server is not authenticated. Do you continue to access it?(Y/N):y
Do you want to save the server's public key?(Y/N):y
The server's public key will be saved with the name:********. Please wait...
Enter password:


显示登录成功信息如下：


Info: The max number of VTY users is 10, and the current number
of VTY users on line is 1.

<CE1>


# 以 SFTP 方式登录 SSH 服务器。


[ ~ PE1] **sftp ******** -vpn-instance vpn1**
Please input the username:client002
Trying ******** ...
Press CTRL+K to abort
The server is not authenticated. Do you continue to access it?(Y/N):y
Do you want to save the server's public key?(Y/N):y
The server's public key will be saved with the name:********. Please wait...


登录成功后，显示如下信息。可继续进行 FTP 相关操作。


<sftp-client>


步骤 **9** 检查配置结果


# 查看 SSH 状态信息。


[ ~ CE1] **display ssh server status**
SSH Version                : 2.0
SSH authentication timeout (Seconds)    : 60
SSH authentication retries (Times)     : 3
SSH server key generating interval (Hours) : 0
SSH version 1.x compatibility       : Enable
SSH server keepalive            : Disable
SFTP IPv4 server              : Enable

SFTP IPv6 server              : Enable

STELNET IPv4 server            : Enable

STELNET IPv6 server            : Enable

SNETCONF IPv4 server            : Enable

SNETCONF IPv6 server            : Enable
SNETCONF IPv4 server port(830)       : Disable
SNETCONF IPv6 server port(830)       : Disable
SCP IPv4 server              : Enable

SCP IPv6 server              : Enable
SSH port forwarding            : Disable
SSH IPv4 server port            : 22
SSH IPv6 server port            : 22
ACL name                  :

ACL number                 :

ACL6 name                 :

ACL6 number                :
SSH server ip-block            : Enable


**----**
结束

##### 配置文件


       - CE1 的配置文件


#

sysname CE1
#
rsa peer-public-key rsakey001
public-key-code begin
3067

0240

BC011055 8BCCB887 384E5A14 1EF982A8 CA44A376


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 342


HUAWEI NetEngine40E
配置指南 1 基础配置


87787138 3BDB1FF0 D21F05D8 41BECF56 B2FA0695

8F76F1B2 5D3E2F35 A8051CE1 E0234274 9D8BB20D

E2EE8EB5

0203

010001
public-key-code end
peer-public-key end
#
interface loopback 0
ip address ******** ***************
stelnet server enable
sftp server enable
ssh server-source -i loopback 0
ssh user client001
ssh user client001 authentication-type password
ssh user client001 service-type stelnet
ssh user client002
ssh user client002 assign rsa-key rsakey001
ssh user client002 authentication-type rsa
ssh user client002 sftp-directory cfcard:
ssh user client002 service-type sftp
#
ssh server cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr
ssh server hmac sha2_512 sha2_256
ssh server key-exchange dh_group_exchange_sha256
#
ssh server publickey rsa_sha2_256 rsa_sha2_512
#
ssh server dh-exchange min-len 3072
#
ssh client publickey dsa ecc rsa rsa_sha2_256 rsa_sha2_512
#
ssh client cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr
ssh client hmac sha2_512 sha2_256
ssh client key-exchange dh_group_exchange_sha256
#

aaa
local-user client001 password cipher @%@%UyQs4,KTtSwJo(4QmW#K,LC:@%@%
local-user client001 service-type ssh
#
interface GigabitEthernet1/0/1
undo shutdown
ip address ******** *************
#
bgp 65410
peer ******** as-number 100
#
ipv4-family unicast
undo synchronization
import-route direct
peer ******** enable
#
user-interface vty 0 4
authentication-mode aaa
protocol inbound ssh
#

return

       - PE1 的配置文件


#

sysname PE1
#
ip vpn-instance vpn1
ipv4-family
route-distinguisher 100:1
apply-label per-instance
vpn-target 111:1 export-extcommunity
vpn-target 111:1 import-extcommunity
#
mpls lsr-id *******


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 343


HUAWEI NetEngine40E
配置指南 1 基础配置


#
mpls
#
mpls ldp
#
interface GigabitEthernet1/0/1
undo shutdown
ip address ******** *************
mpls
mpls ldp
#
interface GigabitEthernet2/0/1
undo shutdown
ip binding vpn-instance vpn1
ip address ******** *************
#
interface LoopBack1
ip address ******* ***************
#
bgp 100
peer ******* as-number 100
peer ******* connect-interface LoopBack1
#
ipv4-family unicast
undo synchronization
peer ******* enable
#
ipv4-family vpnv4
policy vpn-target
peer ******* enable
#
ipv4-family vpn-instance vpn1
import-route direct
peer ******** as-number 65410
#
ssh client first-time enable
#

return

       - P 的配置文件


#

sysname P
#
mpls lsr-id *******
#
mpls
#
mpls ldp
#
interface GigabitEthernet1/0/1
undo shutdown
ip address ******** *************
mpls
mpls ldp
#
interface GigabitEthernet1/0/2
undo shutdown
ip address ******** *************
mpls
mpls ldp
#
interface LoopBack1
ip address ******* ***************
#

return

       - PE2 的配置文件


#

sysname PE2
#


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 344


HUAWEI NetEngine40E
配置指南 1 基础配置


ip vpn-instance vpn1
ipv4-family
route-distinguisher 200:1
apply-label per-instance
vpn-target 111:1 export-extcommunity
vpn-target 111:1 import-extcommunity
#
mpls lsr-id *******
#
mpls
#
mpls ldp
#
interface GigabitEthernet1/0/1
undo shutdown
ip address ******** ***************
mpls
mpls ldp
#
interface GigabitEthernet2/0/1
undo shutdown
ip binding vpn-instance vpn1
ip address ******** *************
#
interface LoopBack1
ip address ******* ***************
#
bgp 100
peer ******* as-number 100
peer ******* connect-interface LoopBack1
#
ipv4-family unicast
undo synchronization
peer ******* enable
#
ipv4-family vpnv4
policy vpn-target
peer ******* enable
#
ipv4-family vpn-instance vpn1
import-route direct
peer ******** as-number 65420
#

return


       - CE2 的配置文件


#

sysname CE2
#
interface GigabitEthernet1/0/1
undo shutdown
ip address ******** *************
#
bgp 65420
peer ******** as-number 100
#
ipv4-family unicast
undo synchronization
import-route direct
peer ******** enable
#

return
