



##### 背景信息 操作步骤



更改登录用户主要用于不同权限用户之间的切换。


当设备作为 FTP 客户端，与 FTP 服务器连接建立成功后，可以更改当前登录的用户，以
实现不同权限用户之间的切换。不同用户成功切换后，不会影响当前的 FTP 连接，即
FTP 控制连接、数据连接及连接状态都不会变。


更改登录用户时，输入的用户名 / 密码错误，则会断开当前连接，用户必须重新登录才
能继续访问设备。


说明


HUAWEI NetEngine40E 可以在不退出 FTP 客户端视图的情况下，以其他的用户名登录到 FTP 服务
器。所建立的 FTP 连接，与执行 **ftp** 命令建立的 FTP 连接完全相同。



步骤 **1** 根据服务器端 IP 地址类型不同，进行如下操作：


       - 执行命令 **ftp** [ [ **-a** source-ip-address | **-i** { interface-type interface-number |
interface-name } ] host-ip [ port-number ] [ **vpn-instance** vpn-instancename ] | **public-net** ] ，设备使用 IPv4 地址与 FTP 服务器建立连接，进入 FTP 客户
端视图。


       - 执行命令 **ftp** **ipv6** [ **-a** source-ip6 ] host-ipv6-address [ [ **vpn-instance** ipv6vpn-instance-name ] | **public-net** ] [ **-oi** { interface-type interface-number |
interface-name } ] [ port-number ] ，设备使用 IPv6 地址与 FTP 服务器建立连接，
进入 FTP 客户端视图。


步骤 **2** 执行命令 **user** username ，更改当前的登录用户，重新登录 FTP 服务器。


更改当前的登录用户后，原用户与 FTP 服务器的连接将断开。


说明


只有 3 级及 3 级以上的 FTP 用户才能通过本命令更改用户身份，登录 FTP 服务器。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 251


HUAWEI NetEngine40E
配置指南 1 基础配置


步骤 **3** 执行命令 **commit** ，提交配置。


**----**
结束
