

命令行接口分为若干个命令视图，所有命令都注册在某个（或某些）命令视图下。通
常情况下，必须先进入命令所在的视图才能执行该命令。


说明


两阶段生效模式下，当系统中有未提交的配置时，视图中以       - 标识，如果系统中没有未提交的配

~
置时，视图中以 标识。


# 与路由器建立连接，如果此路由器是缺省配置，则进入用户视图，在屏幕上显示：


<HUAWEI>


# 键入 **system-view** 后回车，进入系统视图。


<HUAWEI> **system-view**

[ ~ HUAWEI]


# 在系统视图下键入 **aaa** ，则可进入 AAA 视图。

[ ~ HUAWEI] **aaa**

[ ~ HUAWEI-aaa]


说明


命令行提示符“ HUAWEI ”是缺省的主机名，此名称也可以用 **sysname** 指定。通过提示符可以
判断当前所处的视图，例如：“ <> ”表示用户视图，“ [] ”表示除用户视图以外的其它视图。


多级视图中，用户可以直接执行 # ，即可直接回到系统视图。


用户可以在任意视图中，执行！或 # 加字符串，此时的用户输入将全部（包括！和 # 在内）作为
系统的注释行内容，不会产生对应的配置信息。


执行命令 **quit** ，可以从当前视图退回到上一级别的视图。如果在用户视图下执行该命
令，则退出系统。


执行命令 **return** ，可以从当前视图退回到用户视图。如果在用户视图下执行该命令，
仍然处于用户视图。


有些在系统视图下实现的命令，在其它视图下也可以实现，但实现的功能与命令视图
密切相关。比如启动 MPLS 协议的命令 **mpls** ，在系统视图下执行表示启动全局 MPLS 能
力，在接口视图下执行表示启动当前接口的 MPLS 能力。
