

SFTP 使得用户可以从远端安全的登录设备进行文件管理，增加了数据传输的安全性。

##### 应用环境


随着设备的逐步稳定，应用范围在不断扩大，设备升级的范围也越来越大，设备的可
维护性要求也越来越高，特别是设备在线远程加载升级。这不仅可以丰富设备升级维
护手段，方便远程在线升级，还可以节约升级成本，减少客户等待升级的时间，在一
定程度上提高了客户的满意度。最通用的在线升级、数据传输就是 FTP 。但是 FTP 是明
文传输，甚至是明文传输用户名和密码，存在安全隐患。


而 SFTP 使得用户可以从远端安全的登入设备进行文件管理，增加了数据传输的安全
性。同时，由于提供了 Client 功能，可以在本设备上 SFTP 到远程设备，进行文件的安
全传输、在线升级。

##### 前置任务


在通过 SFTP 进行文件操作之前，需要完成以下任务：


       - 终端与设备之间三层路由可达。
