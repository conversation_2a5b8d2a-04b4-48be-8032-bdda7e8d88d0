#!/usr/bin/env python3
"""
快速测试修复效果
"""

from main2 import convert_pdf_to_markdown

def quick_test():
    """快速测试"""
    print("=== 快速测试修复效果 ===")
    
    try:
        # 转换PDF
        markdown_content = convert_pdf_to_markdown("2.pdf", "fixed_output.md")
        
        # 检查前几个标题的内容
        lines = markdown_content.split('\n')
        
        print("前20行内容:")
        for i, line in enumerate(lines[:20]):
            print(f"{i+1:2d}: {line}")
        
        # 查找1.1前言的内容
        print("\n=== 查找1.1前言的内容 ===")
        in_section = False
        section_lines = []
        
        for line in lines:
            if line.strip() == "## 1.1 前 言":
                in_section = True
                continue
            elif line.strip().startswith("##") and in_section:
                break
            elif in_section:
                section_lines.append(line)
        
        print("1.1前言的内容:")
        for i, line in enumerate(section_lines[:10]):  # 只显示前10行
            print(f"{i+1:2d}: {line}")
        
        print(f"\n转换完成，生成文件: fixed_output.md")
        print(f"总长度: {len(markdown_content)} 字符")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    quick_test()
