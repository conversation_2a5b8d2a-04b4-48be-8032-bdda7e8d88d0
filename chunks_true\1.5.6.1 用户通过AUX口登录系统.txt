

用户可以通过 AUX 口配置终端与设备的连接。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 100


HUAWEI NetEngine40E
配置指南 1 基础配置

##### 背景信息


说明


通过 AUX 口登录设备后无法直接实现管理和维护设备，为了实现远程管理和维护设备，建议通过
AUX 口登录设备前，先通过 Console 口本地登录设备，更改 AUX 用户界面的用户优先级。

##### 操作步骤


步骤 **1** 通过 Console 口登录设备后，执行如下步骤：


1. 执行命令 **system-view** ，进入系统视图。


2. 执行命令 **user-interface aux** ui-number ，进入 AUX 类型用户界面视图。


3. 执行命令 **user privilege level** level ，设置 AUX 用户优先级。


4. 执行命令 **authentication-mode** { **aaa** | **password** } ，设置用户验证方式。


5. 执行命令 **set authentication password** [ **cipher** password ] ，设置用户登录密
码。


说明


用户输入的密码必须符合以下要求：


–
不选择 **cipher** 关键字时，密码以交互式输入，系统不回显密码。


输入的密码为字符串形式，区分大小写，长度范围是 8 ～ 16 。输入的密码至少包含两种
类型字符，包括大写字母、小写字母、数字及特殊字符。当 **user-password min-len**
min-length 命令配置的最小密码长度大于 8 时，最小长度为 **user-password min-len** 命
令的配置值。


特殊字符不包括“？”和空格。但是，当输入的密码两端使用双引号时，可在密码中间
输入空格。

#### ▪ 如果使用双引号设置带空格密码，双引号之间不能再使用双引号。 ▪ 如果使用双引号没有设置带空格密码，双引号之间可以再使用双引号。


例如， "a 123"45"" 为不合法密码， "a123"45"" 为合法密码。


–
选择 **cipher** 关键字时，密码可以以明文形式输入，也可以以密文形式输入。

#### ▪ 密码以明文形式输入，要求与不选择 cipher 关键字时一样。密码以明文形式输

入，系统会回显明文形式的密码，存在安全风险，因此建议使用交互式输入密
码。

#### ▪ 密码以密文形式输入，密码的长度必须是 48 ～ 128 个连续字符串。


无论是明文输入还是密文输入，配置文件中都以密文形式体现。


步骤 **2** 在 PC 上打开终端仿真程序，这里以 PuTTY.exe 程序为例，出现如 图 **1-22** 所示客户端配
置界面。选择 Serial 选项。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 101


HUAWEI NetEngine40E
配置指南 1 基础配置


图 **1-22** 客户端配置界面


步骤 **3** 选择客户端配置界面左侧目录树（ Category ）中的连接协议 Serial ，出现如 图 **1-23** 所示
界面。设置端口通信参数，与设备的缺省值保持一致。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 102


HUAWEI NetEngine40E
配置指南 1 基础配置


图 **1-23** 设置连接端口示意图


步骤 **4** 单击“ Open ”，会提示用户配置验证密码，系统会自动保存此密码配置。直到出现用
户视图的命令行提示符，如 <HUAWEI> ，此时用户进入了用户视图配置环境。


**----**
结束
