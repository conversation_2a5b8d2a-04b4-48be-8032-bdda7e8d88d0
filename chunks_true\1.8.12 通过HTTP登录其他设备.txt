

HTTP （ Hypertext Transfer Protocol ）即超文本传输协议，是用于从 WWW 服务器传
输超文本到本地浏览器的传送协议。 HTTP 是一个应用层协议，由请求和响应构成，是
一个标准的客户端 / 服务器模型。

##### 背景信息


当用户需要进行从 HTTP 服务器端下载证书，可以使用 HTTP 协议。 HTTP 是 Hypertext
Transfer Protocol 的简称，它用来在 Internet 上传递 Web 页面信息。


说明


使用 HTTP 协议存在安全风险。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 266


HUAWEI NetEngine40E
配置指南 1 基础配置

##### 前置任务


在配置通过 HTTP 方式登录设备之前，需要完成以下任务：


       - HTTP 服务器配置 SSL 策略。


       - 终端与设备之间路由可达。

##### 操作步骤


       - 配置 HTTP 客户端的 SSL 策略：


a. 执行命令 **system-view** ，进入系统视图。


b. 执行命令 **ssl policy** policy-name ，配置 SSL 策略并进入 SSL 策略视图。

c. 执行命令 **certificate load** ，为 SSL 策略加载证书。 HTTP 客户端需要根据
HTTP 服务器加载的证书格式为 SSL 策略加载证书或证书链：

#### ▪ 执行命令 certificate load pem-cert certFile key-pair { dsa | rsa }

**key-file** keyFile **auth-code** [ **cipher** authCode ] ，为 SSL 策略加载 PEM
格式的证书。

#### ▪ 执行命令 certificate load pfx-cert certFile key-pair { dsa | rsa } mac

或 **certificate load** **pfx-cert** certFile **key-pair** { **dsa** | **rsa** } { **mac**
**cipher** mac-code | **key-file** keyFile } **auth-code** **cipher** authCode ，为
SSL 策略加载 PFX 格式的证书。

#### ▪ 执行命令 certificate load pem-chain certFile key-pair { dsa | rsa }

**key-file** keyFile **auth-code** [ **cipher** authCode ] ，为 SSL 策略加载 PEM
格式的证书链。


d. 执行命令 **trusted-ca load** ，加载信任证书机构文件。 HTTP 客户端需要根据
HTTP 服务器加载的信任证书机构文件为 SSL 策略加载信任证书机构文件：

#### ▪ 执行命令 trusted-ca load pem-ca caFile ，为 SSL 策略加载 PEM 格式信

任证书机构文件。

#### ▪ 执行命令 trusted-ca load asn1-ca caFile ，为 SSL 策略加载 ASN1 格式信

任证书机构文件。

#### ▪ 执行命令 trusted-ca load pfx-ca caFile auth-code [ cipher

authCode ] ，为 SSL 策略加载 PFX 格式信任证书机构文件。


e. 执行命令 **commit** ，提交配置。


f. 执行命令 **quit** ，返回系统视图。


       - 配置通过 HTTP 登录其他设备：


a. 执行命令 **http** ，使能 HTTP 功能，并进入 HTTP 视图。


b. （可选）执行命令 **client source-interface** { interface-name | interfacetype interface-number } ，为 HTTP 客户端绑定源接口。


c. （可选）执行命令 **client ipv6 source-address** ipv6-address [ **vpn-instance**
ipv6-vpn-instance-name ] ，设置 HTTP 客户端源 IPv6 地址和 VPN 。


d. 执行命令 **client ssl-policy** policy-name ，为 HTTP 客户端配置 SSL （ Secure
Sockets Layer ）策略。


e. 执行命令 **client ssl-verify peer** ，配置 HTTP 客户端对服务器端进行 SSL
（ Secure Sockets Layer ）校验。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 267


HUAWEI NetEngine40E
配置指南 1 基础配置


f. 执行命令 **commit** ，提交配置。


**----**
结束
