

系统支持的用户界面有 Console 用户界面和 VTY 用户界面。


在配置了用户界面、用户管理和终端服务后，用户才能登录到设备对本地或远端的网
络设备进行配置、监控和维护。用户界面提供登录场所，用户管理确保登录安全，终
端服务则提供登录协议支持。


每个用户界面有对应的用户界面视图（ User-interface view ），在用户界面视图下网络
管理员可以配置一系列参数，如用户登录时是否需要认证、用户登录后的级别等。当
用户使用该用户界面登录时，将受到这些参数限制，从而达到统一管理各种用户会话
连接的目的。


目前系统支持的用户界面：


       - Console 用户界面：用来管理和监控通过 Console 口登录的用户。


Console 口端口类型为 EIA/TIA-232 DCE 。


       - VTY （ Virtual Type Terminal ，虚拟类型终端）用户界面：用来管理和监控通过
VTY 方式登录的用户。


VTY 口用于对设备进行 Telnet 或 SSH 访问，最多支持 21 个用户同时通过 VTY 方式访
问。


说明


同一用户登录的方式不同，分配的用户界面不同；同一用户登录的时间不同，分配的用户界面也
可能不同。

##### 用户界面的编号


用户登录设备时，系统会根据此用户的登录方式自动分配一个当前空闲且编号最小的
相应类型的用户界面，整个登录过程将受到该用户界面视图下的配置约束。


用户界面的编号有两种方式：相对编号方式和绝对编号方式。


       - 相对编号方式


相对编号方式只能唯一指定某种类型的用户界面中的一个或一组，而不能跨类型
操作。


相对编号方式的形式是：用户界面类型＋编号，遵守的规则如下：


– Console 口的编号： CON 0 。


– VTY 的编号：第一个为 VTY 0 ，第二个为 VTY 1 ，依此类推。


       - 绝对编号方式


绝对编号方式可以唯一的指定一个用户界面或一组用户界面。绝对编号的起始编
号是 0 ，每次增长 1 ，并按照 Console 、 VTY 的顺序依次分配。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 35


HUAWEI NetEngine40E
配置指南 1 基础配置


Console 口有 1 个， VTY 类型的用户界面有 21 个，可以在系统视图下使用命令 **user-**
**interface maximum-vty** 设置最大用户界面个数。


表 **1-8** 用户界面的相对、绝对编号说明





|用户界面|说明|绝对编号|相对编号|
|---|---|---|---|
|Console用<br>户界面|用来管理和监控通过<br>Console口登录的用户。<br>该用户界面仅在Admin-<br>VS支持。|0|0|
|TTY（True<br>Type<br>Terminal）<br>用户界面<br>说明<br>当前未支持<br>TTY用户界<br>面。|用来管理和监控通过异<br>步串口登录的用户。|1～32|第一个为TTY 0，第二<br>个为TTY 1，依此类<br>推。<br>绝对编号1～32对应相<br>对编号TTY 0～TTY<br>31。|
|AUX用户界<br>面|用来管理和监控通过<br>AUX口登录的用户。<br>通常用于通过Modem进<br>行拨号访问。|33|0|
|VTY<br>（Virtual<br>Type<br>Terminal）<br>用户界面|用来管理和监控通过<br>Telnet或SSH方式登录的<br>用户。|34～54|第一个为VTY 0，第二<br>个为VTY 1，依此类<br>推。<br>绝对编号34～54对应<br>相对编号VTY 0～VTY<br>20。|

##### 用户界面的用户验证





配置用户界面的用户验证方式后，用户登录设备时，系统对用户的身份进行验证。对
用户的验证方式如下：


       - Password 验证：需要进行密码验证，只有密码验证成功，用户才能成功登录。


       - AAA 验证：需要进行用户名和密码验证，用户名或密码错误，均会导致登录失
败。对 Telnet 用户一般采用 AAA 验证。

##### 用户界面的用户级别


系统支持对登录用户进行分级管理。用户所能访问命令的级别由用户的级别决定。


       - 如果对用户采用 password 验证，登录到设备的用户所能访问的命令级别由登录时
的用户界面级别决定。


       - 如果对用户采用 AAA 验证，登录到设备的用户所能访问的命令级别由 AAA 配置信
息中本地用户的级别决定。


用户级别与命令级别的对应关系如 表 **1-9** 所示。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 36


HUAWEI NetEngine40E
配置指南 1 基础配置


表 **1-9** 用户级别与命令级别对应关系



















|用户<br>级别<br>（0～<br>3）|用户级<br>别（0<br>～<br>15）|命令级<br>别|级别名<br>称|说明|
|---|---|---|---|---|
|0|0|0|参观级|网络诊断工具命令（ping、tracert）、从本设<br>备出发访问外部设备的命令（Telnet客户端）<br>等。|
|1|1～9|0、1|监控级|用于系统维护，包括display等命令。<br>说明<br>并不是所有display命令都是监控级，比如管理配置<br>文件中的**display current-confguration**命令是3级<br>管理级。各命令的级别请参见《HUAWEI<br>NetEngine40E-命令参考》手册。|
|2|10～<br>14|0、<br>1、2|配置级|业务配置命令。|
|3|15|0、<br>1、<br>2、3|管理级|用于系统基本运行的命令，对业务提供支撑作<br>用，包括文件系统、FTP、TFTP、配置文件切<br>换命令、备板控制命令、用户管理命令、命令<br>级别设置命令，设备重启reboot命令、用于业<br>务故障诊断的debugging命令等。|
