

STelnet 基于 SSH2 协议，客户端和服务器之间经过协商，建立安全连接，客户端可以像
操作 Telnet 一样登录服务器。

##### 背景信息


请在作为 SSH 服务器的设备上进行如下的配置。

##### 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **user-interface** **vty** first-ui-number [ last-ui-number ] ，进入 VTY 用户界面
视图。


步骤 **3** 执行命令 **authentication-mode** **aaa** ，设置验证方式为 AAA 验证。


步骤 **4** 执行命令 **protocol inbound** { **ssh** | **all** } ，配置 VTY 支持 SSH 协议。


说明


如果配置用户界面支持的协议是 SSH ，必须设置 VTY 用户界面验证方式为 AAA 验证，否则
**protocol inbound** { **ssh** | **all** } 将不能配置成功。


步骤 **5** 执行命令 **commit** ，提交配置。


**----**
结束


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 75


HUAWEI NetEngine40E
配置指南 1 基础配置
