

在本示例中，通过登录 SCP 服务器，实现从 SCP 服务器中下载文件至客户端。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 345


HUAWEI NetEngine40E
配置指南 1 基础配置

##### 组网需求


如 图 **1-78** 所示，作为 SCP 客户端的设备和服务器路由可达，并从 SCP 服务器中下载文件
至客户端。


图 **1-78** 配置通过 SCP 访问其他设备文件配置示例组网图

##### 配置思路


采用如下的思路配置通过 SCP 访问其他设备文件：


1. 在 SSH 服务器端生成本地密钥对。


2. 在 SSH 服务器端创建 SSH 用户。


3. 在 SSH 服务器端使能 SCP 功能。


4. 使能客户端首次认证功能。


5. 在 SCP 客户端指定源接口 IP 地址。


6. 从 SSH 服务器下载文件至本地。

##### 数据准备


为完成此配置例，需准备如下的数据：


       - SSH 用户名、认证方式和认证密码。


       - SCP 客户端源接口 IP 地址。


       - 源文件名及路径和目标文件名及路径。

##### 操作步骤


步骤 **1** 在服务器端生成本地密钥对


<HUAWEI> **system-view**

[ ~ HUAWEI] **sysname SSH Server**

[*HUAWEI] **commit**

[ ~ SSH Server] **rsa local-key-pair create**
The key name will be: SSH Server_Host
The range of public key size is (2048, 3072).
NOTE: Key pair generation will take a short while.
Please input the modulus [default = 3072]:3072


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 346


HUAWEI NetEngine40E
配置指南 1 基础配置


步骤 **2** 在 SSH 服务器端创建 SSH 用户


# 配置 VTY 用户界面。


[*SSH Server] **user-interface vty 0 4**

[*SSH Server-ui-vty0-4] **authentication-mode aaa**

[*SSH Server-ui-vty0-4] **protocol inbound ssh**

[*SSH Server-ui-vty0-4] **quit**


# 新建用户名为 client001 的 SSH 用户，且认证方式为 password 。


[*SSH Server] **ssh user client001**
Info: Succeeded in adding a new SSH user.

[*SSH Server] **ssh user client001 authentication-type password**

[*SSH Server] **ssh server cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr**

[*SSH Server] **ssh server hmac sha2_512 sha2_256**

[*SSH Server] **ssh server key-exchange dh_group_exchange_sha256**

[*SSH Server] **ssh server publickey rsa_sha2_256 rsa_sha2_512**

[*SSH Server] **ssh server dh-exchange min-len 3072**

[*SSH Server] **ssh client publickey rsa_sha2_256 rsa_sha2_512**

[*SSH Server] **ssh client cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr**

[*SSH Server] **ssh client hmac sha2_512 sha2_256**

[*SSH Server] **ssh client key-exchange dh_group_exchange_sha256**


# 为 SSH 用户 client001 配置密码为 %TGB6yhn7ujm 。


[*SSH Server] **aaa**

[*SSH Server-aaa] **local-user client001 password**
Please configure the password (8-128)
Enter Password:
Confirm Password:
Info: A new user is added.

[*SSH Server-aaa] **local-user client001 service-type ssh**

[*SSH Server-aaa] **local-user client001 level 3**

[*SSH Server-aaa] **quit**


# 配置 SSH 用户 client001 的服务方式为 all 。


[*SSH Server] **ssh user client001 service-type all**


步骤 **3** 在服务器端使能 SCP 服务


[*SSH Server] **scp server enable**

[*SSH Server] **commit**


步骤 **4** 从 SCP 客户端下载服务器上的文件


# 第一次登录，需要使能 SSH 客户端首次认证功能。


<HUAWEI> **system-view**

[ ~ HUAWEI] **sysname SCP Client**

[*SCP Client] **ssh client first-time enable**


# 设置 SCP 客户端的源地址为 LoopBack 接口 IP 地址 ******* 。


[*SCP Client] **scp client-source -a *********
Info: Succeeded in setting the source address of the SCP client to *******.


# 使用 aes128 加密算法将文件 license.txt 从 IP 地址为 ************** 的远端 SCP 服务器
下载至本地用户目录下。


[*SCP Client] **scp -a ******* -cipher aes128 client001@**************:license.txt license.txt**

[*SCP Client] **commit**


步骤 **5** 验证配置结果


在 SCP 客户端执行 **display scp-client** 命令，查看结果如下：


<HUAWEI> **display scp-client**


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 347


HUAWEI NetEngine40E
配置指南 1 基础配置


The source address of SCP client is *******.


**----**
结束

##### 配置文件


       - SCP 服务器上的配置文件


#

sysname SSH Server
#

aaa
local-user client001 password irreversible-cipher @%@%1-w$!gvBa#6W,ZUm2EN*BYqNWwI3BV\uV`
%_oauS;RQB%>> ~ GV#QzO ~ k/8;U6;@%@%
local-user client001 service-type ssh
local-user client001 level 3

#
scp server enable
ssh user client001
ssh user client001 authentication-type password
ssh user client001 service-type all
#
ssh server cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr
ssh server hmac sha2_512 sha2_256
ssh server key-exchange dh_group_exchange_sha256
#
ssh server publickey rsa_sha2_256 rsa_sha2_512
#
ssh server dh-exchange min-len 3072
#
ssh client publickey dsa ecc rsa rsa_sha2_256 rsa_sha2_512
#
ssh client cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr
ssh client hmac sha2_512 sha2_256
ssh client key-exchange dh_group_exchange_sha256
#
user-interface vty 0 4
authentication-mode aaa
protocol inbound ssh
#

return


       - SCP 客户端的配置文件


#
sysname SCP Client
#
ssh client first-time enable
scp client-source *******
#

return
