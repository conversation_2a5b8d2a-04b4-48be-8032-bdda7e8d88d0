

设备第一次上电可以使用 Console 口或通过管理网口登录，使用 Console 口进行本地登
录是登录设备的最基本的方式，也是配置通过其他方式登录设备的基础。


Console 口是一种通信串行端口，由设备的主控板提供。一块主控板提供一个 Console
口，端口类型为 EIA/TIA-232 DCE 。用户终端的串行端口可以与设备的 Console 口直接
连接，实现对设备的本地配置。


Console 口状态如下：


       - Connected ： Console 口处于试连接状态。


       - Disconnected ：与 Console 口连接断开。


说明


          - 首次登录，系统自动提示用户设置密码。


          - 通过按 CTRL+R 恢复出厂设置，从而清除用户设置的密码。


在构建大型网络时，为了实现对设备的远程统一管理和控制，以提高部署效率和降低
运维成本，设备首次上电时还可以基于即插即用功能通过 DCN 或 DHCP 方式批量部署设
备。
