

用户终端与设备建立 Telnet 连接之前，建议通过 Console 口登录设备，开启设备的
Telnet 服务器功能，以便用户可通过 Telnet 方式远程登录设备。

##### 背景信息


请在作为 Telnet 服务器的设备上，基于不同的网络协议，以下步骤请任选其一执行：

##### 操作步骤


       - 基于 IPv4


a. 执行命令 **system-view** ，进入系统视图。


b. 执行命令 **telnet server enable** ，启动 Telnet 服务器。


c. 执行命令 **commit** ，提交配置。


       - 基于 IPv6


a. 执行命令 **system-view** ，进入系统视图。

b. 执行命令 **telnet** **ipv6 server enable** ，启动 Telnet 服务器。


c. 执行命令 **commit** ，提交配置。


说明


当关闭 Telnet 服务器时，现有的 Telnet 连接不会中断，但是新的连接将不被允许，只能通过
SSH 或 Console 口等其他方式登录设备。


**----**
结束


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 66


HUAWEI NetEngine40E
配置指南 1 基础配置
