#!/usr/bin/env python3
"""
对比相同标题下的正文内容差异
"""

import re
from typing import List, Dict, Tu<PERSON>

def extract_content_by_title_pattern(markdown_content: str, target_pattern: str) -> str:
    """根据标题模式提取对应的内容"""
    lines = markdown_content.split('\n')
    content_lines = []
    capturing = False
    
    for line in lines:
        # 检查是否匹配目标标题模式
        if re.search(target_pattern, line, re.IGNORECASE):
            capturing = True
            continue
        
        # 如果遇到其他标题，停止捕获
        if capturing and re.match(r'^#{1,6}\s+', line):
            break
        
        # 如果正在捕获，添加内容
        if capturing:
            content_lines.append(line)
    
    return '\n'.join(content_lines).strip()

def compare_specific_sections():
    """对比特定章节的内容"""
    print("=== 对比相同标题下的正文内容 ===")
    
    # 读取两个文档
    try:
        with open('baseline_pymupdf4llm.md', 'r', encoding='utf-8') as f:
            baseline_content = f.read()
        
        with open('test_debug.md', 'r', encoding='utf-8') as f:
            our_content = f.read()
    except FileNotFoundError as e:
        print(f"文件未找到: {e}")
        return
    
    # 定义要对比的标题模式
    title_patterns = [
        (r'1\.1\s*前\s*言', '1.1 前言'),
        (r'1\.2\s*首次登录配置', '1.2 首次登录配置'),
        (r'1\.2\.1\s*首次登录概述', '1.2.1 首次登录概述'),
        (r'1\.2\.2\s*首次登录设备配置注意事项', '1.2.2 首次登录设备配置注意事项'),
        (r'1\.2\.3\s*通过Console口登录设备', '1.2.3 通过Console口登录设备'),
        (r'1\.3\s*命令行接口配置', '1.3 命令行接口配置'),
    ]
    
    for pattern, title_name in title_patterns:
        print(f"\n{'='*60}")
        print(f"对比章节: {title_name}")
        print('='*60)
        
        # 提取基准内容
        baseline_section = extract_content_by_title_pattern(baseline_content, pattern)
        our_section = extract_content_by_title_pattern(our_content, pattern)
        
        print(f"基准内容长度: {len(baseline_section)} 字符")
        print(f"我们的内容长度: {len(our_section)} 字符")
        
        if len(baseline_section) > 0 and len(our_section) > 0:
            ratio = len(our_section) / len(baseline_section)
            print(f"长度比例: {ratio:.2f}")
        
        # 显示内容预览
        print(f"\n--- 基准内容预览 (前500字符) ---")
        print(baseline_section[:500])
        if len(baseline_section) > 500:
            print("...")
        
        print(f"\n--- 我们的内容预览 (前500字符) ---")
        print(our_section[:500])
        if len(our_section) > 500:
            print("...")
        
        # 分析内容差异
        print(f"\n--- 内容差异分析 ---")
        
        # 检查是否包含相似的关键词
        baseline_words = set(baseline_section.lower().split())
        our_words = set(our_section.lower().split())
        
        if baseline_words and our_words:
            common_words = baseline_words & our_words
            similarity = len(common_words) / len(baseline_words | our_words)
            print(f"词汇相似度: {similarity:.2f}")
        
        # 检查我们的内容是否包含其他章节的内容
        other_chapter_indicators = [
            '1.2', '1.3', '1.4', '1.5', '配置', '登录', '管理', '接口', 
            '用户', '认证', '授权', '服务器', '客户端'
        ]
        
        found_indicators = []
        for indicator in other_chapter_indicators:
            if indicator.lower() in our_section.lower() and indicator.lower() not in baseline_section.lower():
                found_indicators.append(indicator)
        
        if found_indicators:
            print(f"我们的内容包含额外关键词: {found_indicators}")
        
        # 检查基准内容的特征
        baseline_lines = baseline_section.split('\n')
        our_lines = our_section.split('\n')
        
        print(f"基准内容行数: {len([l for l in baseline_lines if l.strip()])}")
        print(f"我们的内容行数: {len([l for l in our_lines if l.strip()])}")

def analyze_1_1_content_in_detail():
    """详细分析1.1前言的内容差异"""
    print(f"\n{'='*80}")
    print("详细分析 1.1 前言 的内容差异")
    print('='*80)
    
    try:
        with open('baseline_pymupdf4llm.md', 'r', encoding='utf-8') as f:
            baseline_content = f.read()
        
        with open('test_debug.md', 'r', encoding='utf-8') as f:
            our_content = f.read()
    except FileNotFoundError as e:
        print(f"文件未找到: {e}")
        return
    
    # 提取1.1前言的内容
    baseline_1_1 = extract_content_by_title_pattern(baseline_content, r'1\.1\s*前\s*言')
    our_1_1 = extract_content_by_title_pattern(our_content, r'1\.1\s*前\s*言')
    
    print(f"基准1.1前言内容:")
    print("-" * 40)
    print(baseline_1_1)
    
    print(f"\n我们的1.1前言内容:")
    print("-" * 40)
    print(our_1_1)
    
    # 分析差异
    print(f"\n差异分析:")
    print(f"基准内容长度: {len(baseline_1_1)} 字符")
    print(f"我们的内容长度: {len(our_1_1)} 字符")
    
    # 检查我们的内容是否包含了后续章节的概述
    if "1.2" in our_1_1 or "首次登录" in our_1_1:
        print("❌ 我们的内容包含了1.2章节的信息")
    
    if "1.3" in our_1_1 or "命令行接口" in our_1_1:
        print("❌ 我们的内容包含了1.3章节的信息")
    
    if "1.4" in our_1_1:
        print("❌ 我们的内容包含了1.4章节的信息")
    
    # 检查基准内容的关键特征
    baseline_keywords = ['概述', 'license', '产品版本', '读者对象', '前言']
    our_keywords_found = []
    baseline_keywords_found = []
    
    for keyword in baseline_keywords:
        if keyword.lower() in baseline_1_1.lower():
            baseline_keywords_found.append(keyword)
        if keyword.lower() in our_1_1.lower():
            our_keywords_found.append(keyword)
    
    print(f"基准内容关键词: {baseline_keywords_found}")
    print(f"我们的内容关键词: {our_keywords_found}")

def find_content_extraction_pattern():
    """找出内容提取的模式问题"""
    print(f"\n{'='*80}")
    print("分析内容提取模式问题")
    print('='*80)
    
    # 分析多个章节，找出共同的问题模式
    try:
        with open('baseline_pymupdf4llm.md', 'r', encoding='utf-8') as f:
            baseline_content = f.read()
        
        with open('test_debug.md', 'r', encoding='utf-8') as f:
            our_content = f.read()
    except FileNotFoundError as e:
        print(f"文件未找到: {e}")
        return
    
    # 分析多个章节的长度比例
    test_patterns = [
        (r'1\.1\s*前\s*言', '1.1 前言'),
        (r'1\.2\.1\s*首次登录概述', '1.2.1 首次登录概述'),
        (r'1\.2\.2\s*首次登录设备配置注意事项', '1.2.2 首次登录设备配置注意事项'),
        (r'1\.2\.3\s*通过Console口登录设备', '1.2.3 通过Console口登录设备'),
        (r'1\.3\.1\s*命令行接口概述', '1.3.1 命令行接口概述'),
    ]
    
    length_ratios = []
    
    for pattern, title_name in test_patterns:
        baseline_section = extract_content_by_title_pattern(baseline_content, pattern)
        our_section = extract_content_by_title_pattern(our_content, pattern)
        
        if len(baseline_section) > 0 and len(our_section) > 0:
            ratio = len(our_section) / len(baseline_section)
            length_ratios.append((title_name, ratio, len(baseline_section), len(our_section)))
            print(f"{title_name}: 比例={ratio:.2f}, 基准={len(baseline_section)}, 我们={len(our_section)}")
    
    # 分析比例模式
    avg_ratio = sum(r[1] for r in length_ratios) / len(length_ratios) if length_ratios else 0
    print(f"\n平均长度比例: {avg_ratio:.2f}")
    
    # 找出异常的章节
    for title, ratio, baseline_len, our_len in length_ratios:
        if ratio > 2:  # 我们的内容比基准长2倍以上
            print(f"⚠️  {title} 内容过长 (比例: {ratio:.2f})")
        elif ratio < 0.5:  # 我们的内容比基准短一半以上
            print(f"⚠️  {title} 内容过短 (比例: {ratio:.2f})")

if __name__ == "__main__":
    compare_specific_sections()
    analyze_1_1_content_in_detail()
    find_content_extraction_pattern()
