

用户可以使用 TFTP 命令从当前设备下载服务器上的文件到本地。

##### 背景信息


VPN 是一个私有网络，它利用公有网络（通常为 Internet ）连接远端设备或用户。当打
开一个 TFTP 会话时， TFTP 客户端支持在命令中指定 vpn-instance-name ， TFTP 客户端
使用指定的 VPN 连接远端 TFTP 服务器。


下载文件时， TFTP 客户端向 TFTP 服务器发送 read 请求报文。当接收到数据报文时，
TFTP 客户端再向服务器发送 acknowledgement 报文。


根据服务器的 IP 地址类型不同，选择下列步骤中的一种进行操作。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 244


HUAWEI NetEngine40E
配置指南 1 基础配置

##### 操作步骤


       - 执行命令 **tftp** [ **-a** source-ip-address | **-i** interface-type interface-number ]
host-ip-address [ **vpn-instance** vpn-instance-name | **public-net** ] **get** sourcefilename [ destination-filename ] ，使用 TFTP 下载文件。


参数 interface-type 必须是 Loopback 接口。


       - 执行命令 **tftp ipv6** [ **-a** source-ipv6-address ] tftp-server-ipv6 [ **vpn-instance**
vpn-instance-name | public-net ] [ **-oi** interface-type interface-number ] **get**
source-filename [ destination-filename ] ，使用 TFTP 下载文件。


**----**
结束
