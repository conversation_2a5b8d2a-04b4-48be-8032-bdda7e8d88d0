
用户可以通过管理目录在逻辑上将文件分级存放。

##### 背景信息


对目录的管理包括：改变当前目录、显示当前目录、显示目录中的文件和子目录列表
以及创建和删除目录。

##### 操作步骤


       - 执行命令 **cd** directory ，改变当前所处的目录。


       - 执行命令 **pwd** ，查看当前所处的目录。

       - 执行命令 **dir** [ **/all** ] [ filename ] ，显示目录中的文件和子目录的列表。


所有路径都支持绝对路径或相对于当前工作路径的相对路径。


该命令显示包含的文件信息详见下表：


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 144


HUAWEI NetEngine40E
配置指南 1 基础配置


说明


显示的文件和系统的软件版本和业务配置有关，此处仅列出常见文件。


支持使用通配符“        - ”匹配。


表 **1-22** 文件信息描述表

|文件名|文件含义|
|---|---|
|$_checkpoint|保存配置回退点信息的文件。|
|**.cc|软件版本文件。|
|$_install_hpg|存放随版补丁的目录。|
|$_install_mod|存放MOD（动态模块包）的目录。|
|$_license|用户激活过的license文件备份的目<br>录。|
|$_security_info|保存AAA用户历史数据的目录。|
|$_system|Linux系统自带的目录，存放系统使用<br>的脚本。|
|backup_bkp_elb.txt|背板电子标签备份文件。|
|backupelb.txt|备份的某个单板的电子标签的文件，<br>最近一次自动读某个单板的电子标签<br>生成该文件。|
|backupelable.txt|备份的某个单板的电子标签的文件，<br>最近一次通过命令行读取某个单板的<br>电子标签后生成该文件。|
|dbupgrade.log|DB升级过程日志文件。|
|device.sys|系统硬件配置文件。|
|lcsbox|保存当前激活的GTL license的文件<br>名、文件内容等的文件。|
|lpustat|接口板定时采集接口板芯片丢弃计<br>数，上送主控板后保存在该目录下。|
|pmdata|保存业务性能生成的数据文件的目<br>录。|
|security|保存SSL证书的目录。|
|logfle|日志信息文件，占用独立的存储空<br>间。|
|KPISTAT|存放KPI指标采集数据。|
|said|存放SAID节点进行故障检测、诊断和<br>恢复过程中产生的信息。|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 145


HUAWEI NetEngine40E
配置指南 1 基础配置

|文件名|文件含义|
|---|---|
|lost+found|异常重启时，系统恢复文件管理模块<br>遭破坏的信息文件。|
|**.zip/**.cfg/**.dat|系统配置文件。具体说明请参见<br>**save**。<br>压缩后的日志文件也以.zip为后缀，其<br>中：<br>●log_槽位号_时间.log.zip：达到设<br>定大小的普通日志文件。<br>●diaglog_槽位号_时间.log.zip：达到<br>设定大小的诊断日志文件。<br>日志文件大小通过**info-center logfle**<br>**size**命令来设置。|
|*.pat|补丁文件。|
|lpustat.dat|转发引擎故障丢包采集日志文件。<br>转发引擎故障丢包数据会自动记录，<br>数据以结构化的16进制文件存储，保<br>存在主控板CF卡lpustat目录下，文件<br>名是lpustat.dat。因为lpustat.dat文件<br>采用16进制存储，因此无法直接打开<br>阅读，可以联系华为工程师将此文件<br>解析成lpustat.csv文档，基于时间、槽<br>号、模块、芯片、故障ID、故障信息<br>进行显示。<br>当设备CF卡剩余空间小于等于110M的<br>时候，设备不记录丢包采集的日志。|
|insegdroplog.log|MPLS转发丢包采集日志文件。公网标<br>签转发会自动记录因标签表查不中而<br>丢包的相关信息，该信息保存在主控<br>板CF卡insegdroplog目录下，文件名<br>是insegdroplog.log。当日志文件<br>insegdroplog.log大于8M时，自动压<br>缩为insegdroplog_时间戳.rar，并删除<br>insegdroplog.log。新的丢包产生时，<br>重新生成insegdroplog.log文件，继续<br>记录。insegdroplog整个目录下所有<br>文件最多占用10M。当日志文件查过<br>10M时，会删除最老日志文件，记录<br>最新日志信息。|



       - 执行命令 **mkdir** directory ，创建目录。

       - 执行命令 **rename** source-filename destination-filename ，重新命名目录。


       - 执行命令 **rmdir** directory ，删除目录。


**----**
结束


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 146


HUAWEI NetEngine40E
配置指南 1 基础配置
