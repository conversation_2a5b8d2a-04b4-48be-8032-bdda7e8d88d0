

用户如果希望配置完成后再生效，可以配置两阶段生效模式。

##### 背景信息


两阶段生效模式，可以提高配置的安全性和可靠性，将用户配置对业务的异常影响降
到最低。对于已经生效的业务，如果用户发现配置不符合预期，可以回退之前提交的
配置。

##### 操作步骤


步骤 **1** （可选）在用户视图下执行命令 **configuration exclusive** ，锁定配置。


当用户希望锁定配置，禁止其他用户同一时间进行配置和提交时，可通过锁定配置来
达到独占当前运行数据集的目的。如果配置被其他用户锁定，则首先需要联系该用户
解除配置锁定。


须知


用户锁定配置后，其他用户可以对当前运行数据库进行配置，但是不能提交配置。


如果其他用户需要对运行数据库进行配置，则首先需要由锁定配置的用户进行解锁。


步骤 **2** 执行命令 **system-view** ，进入两阶段生效模式，并且编辑配置。


说明


两阶段生效模式下，用户的提示符如下：


<HUAWEI> **system-view**

[ ~ HUAWEI]


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 192


HUAWEI NetEngine40E
配置指南 1 基础配置


步骤 **3** （可选）执行命令 **display configuration candidate merge** ，预览当前配置事务中候
选数据集里面的配置，包括用户未提交的配置和原系统的配置。


如果发现配置错误或者不需要所编辑的配置时，可以使用 **clear configuration**
**candidate** 命令，清除未提交的所有配置。


步骤 **4** （可选）执行命令 **commit** **trial** [ time ] [ **persist** persistId ] ，配置试运行功能。


试运行过程中，用户可以通过 **abort trial** [ { **session** session-id | **persist** persistId } ]
命令取消配置试运行，系统配置回退至试运行前的配置状态。用户还可以通过命令
**display configuration trial status** 查看系统中配置试运行的状态。


步骤 **5** 完成配置后，执行命令 **commit** ，提交配置。


说明


为了保护某些业务，在业务进程刚开始启动时，会进行配置锁定，所以在此期间用户提交会出现
提交失败。到启动结束时会自动解除锁定，在此期间无法提交配置，但可以执行查询操作。


如果提交配置失败，建议等待 30 秒后再提交配置，如果再次失败，说明配置被某个用户锁定。


步骤 **6** （可选）如果用户前面已经锁定配置，则执行命令 **quit** 退回用户视图后，执行命令
**undo configuration exclusive** ，解除锁定配置。


须知


用户锁定配置然后配置完成必须解除锁定，否则其他用户的任何配置都不会生效。


**----**
结束
