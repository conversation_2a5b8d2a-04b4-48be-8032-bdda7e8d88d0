

设备第一次上电时，可以通过管理网口使用 SSH 方式登录。

##### 背景信息


通过终端连接到网络上，如果网络安全性不高， SSH （ Secure Shell ）可提供安全的信
息保障和强大认证功能，保护设备系统不受 IP 欺骗等攻击。缺省情况下，用户可以通
过管理网口直接登录设备。


客户端已安装 PuTTY.exe 软件。


说明


设备上电后会自动将管理网口（ GigabitEthernet0/0/0 ）绑定到保留 VPN （保留 VPN 为
__LOCAL_OAM_VPN__ ），并为其配置固定 IP 地址 ***********/24 。


用户可以为终端配置 ***********/24 网段的其他 IP ，通过 SSH 方式登录设备，实现对设备的现场
维护。


如果设备在启动前已经连接 DHCP 服务器，管理网口的默认地址可能被覆盖或者丢失。


在设备上进行业务配置后，需要及时修改用户名和密码。管理网口的 IP 可以修改和删除，并且根
据需要关闭该接口。

##### 操作步骤


步骤 **1** 配置 SSH 客户端。


打开 PuTTY.exe 程序，出现如 图 **1-4** 所示客户端配置界面。在“ Host Name (or IP
address) ”文本框中输入 SSH 服务器的 IP 地址。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 11


HUAWEI NetEngine40E
配置指南 1 基础配置


图 **1-4** SSH 客户端配置界面


步骤 **2** 单击“ Open ”，如果连接正常则会提示用户输入用户名和密码，如 图 **1-5** 所示。


图 **1-5** SSH 客户端登录认证界面


说明


此处的用户名和密码需输入设备的缺省账号及密码，详见设备定制及缺省帐号与密码清单。


通过配置 Console 口进而使用 STelnet 方式登录设备的详细过程可参考 **1.5.5** 配置用户通过 **STelnet**
登录系统 。


步骤 **3** 设备空配置启动时，如果 .defcfg 文件中没有预置账号， STelnet 登录时会进入 first-login

流程，此时会提示新创建用户并设置密码，如 图 **1-6** 所示。


通过 **user-security-policy first-login-linkage enable** 命令，可以在首次登录设备
时，限制 Console 和 STelnet 只能有一个流程创建用户。该命令仅支持在配置文件中生
效，对于已经登录的用户无法执行。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 12


HUAWEI NetEngine40E
配置指南 1 基础配置


图 **1-6** 设备进入 first-login 流程界面


创建成功后连接会被关闭，用户需要重新用新创建的用户登录。


**----**
结束
