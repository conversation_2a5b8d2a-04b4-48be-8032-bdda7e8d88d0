头标题: ******* 配置拥塞管理及拥塞避免示例
尾标题: ******* 配置低速链路队列调度示例
内容: HUAWEI NetEngine40E配置指南 1 QoSa. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **port-queue-template** port-queue-name ，创建端口队列模板并进入端口队列模板视图。c. 执行命令 **queue** cos-value { { **pq** | **wfq weight** weight-value | **lpq** } |**shaping** { shaping-value | **shaping-percentage** shaping-percentagevalue } [ **pbs** pbs-value ] | **port-wred** wred-name | **low-latency** } * 或命令**queue** cos-value **cir** { { cir-value [ **cbs** cbs-value ] **cir-schedule pq pir** pirvalue } | { **cir-percentage** cir-percentage-value [ **cbs** cbs-value ] **cir-****schedule pq pir pir-percentage** pir-percentage-value } } [ **pbs** pbsvalue ] **pir-schedule** { **pq** | **wfq weight** weight-value | **lpq** } [ **port-wred**wred-name ] ，配置端口队列模板中各队列的调度参数。d. 执行命令 **quit** ，返回系统视图。e. 执行命令 **interface** interface-type interface-number ，进入接口视图。f. 执行命令 **port-queue-template** port-queue-name **outbound** ，在接口视图下应用端口队列模板。g. （可选）执行命令 **port-queue-alarm** cos-value **buffer percentage**percentage-value ，配置端口队列使用率的告警门限。说明如果需要配置端口队列使用率告警门限的接口多，可以在槽位视图下执行命令 **port-****queue-alarm** cos-value **buffer percentage** percentage-value 基于槽位进行配置。在槽位视图下的配置对其所有接口都生效；槽位视图和接口视图同时配置此功能时，接口视图配置的参数优先生效。h. （可选）执行命令 **port-queue-alarm** cos-value { **discard-packet** discardpacket-number | **discard-byte** discard-byte-number | **discard-packet-****ratio** discard-packet-coefficient discard-packet-exponent } [ **interval**interval-time ] ，配置端口队列丢弃告警门限值。i. 执行命令 **commit** ，提交配置。       - a. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **port-queue-template** port-queue-name ，创建端口队列模板并进入端口队列模板视图。c. 执行命令 **queue** cos-value { { **pq** | **wfq weight** weight-value | **lpq** } |**shaping** { shaping-value | **shaping-percentage** shaping-percentagevalue } [ **pbs** pbs-value ] | **port-wred** wred-name **low-latency** }* ，配置端口队列模板中各队列的调度参数。d. 执行命令 **quit** ，返回系统视图。e. 执行命令 **interface** interface-type interface-number ，进入接口视图。f. 执行命令 **port-queue-template** port-queue-name **outbound** ，在接口视图下应用端口队列模板。g. （可选）执行命令 **port-queue-alarm** cos-value **buffer percentage**percentage-value ，配置端口队列使用率的告警门限。h. （可选）执行命令 **port-queue-alarm** cos-value { **discard-packet** discardpacket-number | **discard-byte** discard-byte-number | **discard-packet-****ratio** discard-packet-coefficient discard-packet-exponent } [ **interval**interval-time ] ，配置端口队列丢弃告警门限值。i. 执行命令 **commit** ，提交配置。**----**结束文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 31HUAWEI NetEngine40E配置指南 1 QoS#### 1.4.4 配置拥塞避免WRED （ Weighted Random Early Detection ），可以通过设定阈值决定随机丢弃报文的条件，避免使多个 TCP 连接同时降低发送速度，从而避免了 TCP 的全局同步现象。##### 应用环境由于内存资源的有限，当发生网络拥塞时，传统的处理方法是采用尾丢弃，即将超出规格的所有报文都丢弃。对于 TCP 报文，由于大量的报文被丢弃，将造成 TCP 超时，从而引发 TCP 的慢启动和拥塞避免机制，使 TCP 减少报文的发送。当队列同时丢弃多个TCP 连接的报文时，将造成多个 TCP 连接同时进入慢启动和拥塞避免，称之为 TCP 全局同步。这样多个 TCP 连接发向队列的报文将同时减少，使得发向队列的报文的量低于链路发送的速度，减少了带宽的利用率。为了避免 TCP 全局同步这种情况，队列可以采用 WRED 丢弃策略。由于 WRED 随机地丢弃报文，将避免使多个 TCP 连接同时降低发送速度，从而避免了 TCP 的全局同步现象，提高了线路带宽的利用率。说明WRED 丢弃策略通常与 WFQ 队列配合使用。##### 前置配置在配置 WRED 之前，需完成以下任务：       - 配置相关接口的物理参数       - 配置相关接口的链路层属性，保证接口的正常工作       - 配置相关接口的 IP 地址       - 使能路由协议，实现互通##### ******* 配置 WRED 模板配置 WRED 模板中不同颜色报文的高低门限百分比和丢弃概率。##### 背景信息每个 WRED 模板最多支持红、黄、绿三种颜色报文的处理。一般绿色报文设置的丢弃概率比较小，高、低门限值比较大；黄色报文次之；红色报文设置的丢弃概率最大，高、低门限值最小。通过配置 WRED 模板，用户可以为队列设定高低门限百分比和丢弃概率。       - 当报文队列的实际长度占端口队列的长度百分比小于低门限百分比时，不丢弃报文。       - 当报文队列的实际长度占端口队列的长度百分比在低门限百分比和高门限百分比之间时， WRED 开始随机丢弃报文（队列的长度越长，报文被丢弃的概率越高）。       - 当报文队列的实际长度占端口队列的长度百分比大于高门限百分比时，丢弃所有的报文。每种颜色报文的门限值百分比和丢弃概率都是可配置的。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 32HUAWEI NetEngine40E配置指南 1 QoS说明          - 如果用户不配置端口队列的 WRED 模板，系统采用缺省的尾丢弃策略。          - 红色丢弃优先级队列的高低门限百分比可以配置得最小，黄色丢弃优先级队列的高低门限百分比可以配置得稍大些，绿色丢弃优先级队列的高低门限百分比可以配置得最大。          - 用户在实际配置时， WRED 低门限百分比建议从 50% 开始取值，根据不同颜色的丢弃优先级逐级调整。丢弃概率建议取值为 100% 。在待配置路由器上进行以下配置。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **port-wred** port-wred-name ，创建 WRED 模板并进入 WRED 视图。步骤 **3** 执行命令 **color** { **green** | **yellow** | **red** } **low-limit** low-limit-percentage **high-limit**high-limit-percentage **discard-percentage** discard-percentage ，配置不同颜色的报文的高低门限百分比和丢弃概率。步骤 **4** （可选）执行命令 **queue-depth** { queue-depth-value | **buffer-time** queue-depthtime } ，配置调整端口队列的队列深度。步骤 **5** 执行命令 **commit** ，提交配置。**----**结束##### 1.4.4.2 （可选）配置端口队列的联合流量整形当多个优先级的端口队列出接口为同一个主接口时，可以针对同样调度方式的端口队列进行联合流量整形。##### 背景信息请在主接口上进行如下配置。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **interface** interface-type interface-number ，进入接口视图。步骤 **3** 执行命令 **port share-shaping** { **af1** | **af2** | **af3** | **af4** | **be** | **cs6** | **cs7** | **ef** } [*] [ **pq** | **wfq****weight** weight-value | **lpq** ] { shaping-value | **shaping-percentage** shapingpercentage-value } [ **pbs** pbs-value ] ，配置端口队列的联合流量整形。本命令为覆盖式命令，即在同一接口多次配置流量整形参数后，按最后一次配置生效。同一接口下命令 **port share-shaping** 与命令 **share-shaping** （端口队列视图） 互斥，不能同时配置。本命令不支持在 Trunk 成员口下配置，配置了本命令的接口也不能加入 Trunk 口。本命令仅在 LPUI-52-E/LPUI-120-E/LPUF-480-E/LPUF-480/LPUF-480-B/LPUI-480/LPUI-480-B/LPUI-480-L/LPUI-1T/LPUI-1T-B/LPUI-1T-L/LPUI-2T/LPUI-2T-B/LPUF-241/LPUF-241-R/LPUI-241/LPUI-241-B/LPUF-481/LPUF-402-E 单板支持，或在文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 33HUAWEI NetEngine40E配置指南 1 QoSLPUF-120 系列母板 /LPUF-240 系列母板配合 P52-E/P120-E/P120-H/P240-E/P240-H 子卡使用时支持。步骤 **4** 执行命令 **commit** ，提交配置。**----**结束##### 1.4.4.3 应用 WRED基于业务类型应用配置好的 WRED 模板。##### 背景信息在配置了 WRED 模板的路由器上进行以下配置。##### 操作步骤       - 在接口视图上应用 WREDa. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **interface** interface-type interface-number ，进入接口视图。c. 执行命令 **port-queue** cos-value { { **pq** | **wfq** **weight** weight-value | **lpq** } |**shaping** { shaping-value | **shaping-percentage** shaping-percentagevalue } [ **pbs** pbs-value ] | **port-wred** wred-name | **low-latency** } [*]**outbound** ，配置接口出方向的 QoS 服务等级、承诺信息速率和峰值速率。d. （可选）执行命令 **qos buffer-monitor default-queue enable** ，使能默认SQ 队列的缓存监控功能。接口使能默认 SQ 队列的缓存监控功能后，设备会采集接口默认 SQ 队列的缓存值，通过网管或执行命令 **display port-queue statistics interface****outbound default** 观察缓存值变化趋势，可以辅助预判是否可能会出现流量拥塞。e. （可选）执行命令 **port-queue-alarm** cos-value **buffer** **percentage**percentage-value ，配置接口出方向的 QoS 队列的使用率告警门限。f. （可选）执行命令 **port-queue-alarm** cos-value { **discard-packet** discardpacket-number | **discard-byte** discard-byte-number | **discard-packet-****ratio** discard-packet-coefficient discard-packet-exponent } **interval**interval-time ，配置端口队列丢弃告警功能。g. 执行命令 **commit** ，提交配置。h. 执行命令 **quit** ，退回到系统视图。       - 在槽位视图配置的 MTunnel 上应用 WRED 。a. 执行命令 **slot** slot-id ，进入槽位视图。b. 执行命令 **port-queue** cos-value { { **pq** | **wfq** **weight** weight-value | **lpq** } |**shaping** { shaping-value | **shaping-percentage** shaping-percentagevalue } | **port-wred** wred-name } * **outbound** **bind** **mtunnel** ，在分布式组播 VPN 绑定的 MTunnel 上为不同等级的组播报文端口队列配置调度策略，并在调度策略中应用配置好的 WRED 模板。c. 执行命令 **commit** ，提交配置。**----**结束文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 34HUAWEI NetEngine40E配置指南 1 QoS##### 1.4.4.4 检查配置结果WRED 配置成功后，可以查看 WRED 配置情况。##### 操作步骤       - 使用 **display port-wred configuration** [ **verbose** [ port-wred-name ] ] 命令查看 WRED 模板配置的参数。       - 使用 **display port-queue configuration interface** interface-type interfacenumber **outbound** 命令用来显示端口队列配置详细信息。       - 使用 **display port-queue statistics** [ **slot** slot-id | **interface** interface-typeinterface-number ] [ cos-value ] **outbound** [ **default** ] 命令查看端口队列的统计信息。       - 使用 **display port-queue statistics slot** slot-id [ cos-value ] **outbound** **bind****mtunnel** 命令查看分布式组播 VPN 的虚环回 Mtunnel 接口的端口队列的统计信息。**----**结束#### 1.4.5 配置低速链路的队列调度低速链路上，由于网络资源有限，容易产生拥塞。为了避免网络拥塞时报文被统一丢弃，需要将不同业务流量分类到不同的流队列进行 PQ 和 WFQ 队列调度。##### 应用环境低速接口建立的链路上，由于网络资源有限，容易产生拥塞。为了避免网络拥塞时报文被统一丢弃，需要将不同业务流量分类到不同的流队列进行 PQ 和 WFQ 队列调度，优先保证高优先级的报文通过。说明低速链路的队列调度只支持在 Serial 接口、 Trunk-Serial 接口、 Global-MP-Group 接口和 MPgroup 接口下配置。##### 前置任务在配置低速链路的队列调度之前，需完成以下任务：       - 配置相关接口的物理参数       - 配置相关接口的链路层属性，保证接口的正常工作       - 配置相关接口的 IP 地址       - 使能路由协议，实现互通##### ******* 配置 PQ 调度在低速接口的链路上，将不同业务流量分类到不同的队列进行绝对优先级队列调度。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 35HUAWEI NetEngine40E配置指南 1 QoS##### 背景信息说明af2 、 af3 、 af4 、 ef 、 cs6 、 cs7 队列的报文支持配置 PQ 调度。配置 PQ 队列调度的队列优先级高于 WFQ 队列调度。对一个队列如果同时配置 PQ 和 WFQ 的队列调度，后配置的生效。在待配置路由器上进行以下配置。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **interface** interface-type interface-number ，进入接口视图。说明低速链路的队列调度只支持在 Serial 接口、 Trunk-Serial 接口、 Global-MP-Group 接口和 MPgroup 接口下配置。步骤 **3** 执行命令 **port-queue** { **af2** | **af3** | **af4** | **ef** | **cs6** | **cs7** } **pq shaping pir** { pir-value |**percentage** pir-percent } ，配置 PQ 队列调度参数。步骤 **4** 执行命令 **commit** ，提交配置。**----**结束##### 1.4.5.2 配置 WFQ 调度在低速接口的链路上，将不同业务流量分类到不同的队列进行加权公平队列调度。##### 背景信息在待配置路由器上进行以下配置。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **interface** interface-type interface-number ，进入接口视图。说明低速链路的队列调度只支持在 Serial 接口、 Trunk-Serial 接口、 Global-MP-Group 接口和 MPgroup 接口下配置。步骤 **3** 根据想要选择的 WFQ 队列调度模式分别执行以下步骤：       - 选择 Weight 调度模式时，进行以下配置：a. 执行命令 **set port-queue weight-mode** 命令，将接口的 WFQ 队列调度模式配置为 Weight 模式。b. 执行命令 **port-queue** { **cs7** | **cs6** | **ef** | **af4** | **af3** | **af2** | **af1** | **be** } { **wfq****weight** weight-value | **shaping** { shaping-value | **shaping-percentage**shaping-percentage-value } } [*] **outbound** ，配置接口出方向上端口队列的WFQ 调度参数，包括权重和整形速率。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 36HUAWEI NetEngine40E配置指南 1 QoS       - 选择 CIR 调度模式时，执行命令 **port-queue** { **cs6** | **ef** | **af4** | **af3** | **af2** | **af1** | **be** }**wfq** **shaping** **cir** { cir-value | **percentage** cir-percent } [ **pir** { pir-value |**percentage** pir-percent } ] ，配置 WFQ 队列调度参数。步骤 **4** 执行命令 **commit** ，提交配置。**----**结束##### 1.4.5.3 （可选）配置端口队列的缓存时间当有持续的流量过大情况时，进入端口队列的报文会产生拥塞并有较大的时延，此时可根据实际情况设置端口队列的缓存时间。通过配置较小的缓存时间，实现加快报文转发和减少拥塞时延的目的。##### 背景信息说明队列深度的缓存时间，以 64 字节的 PPP 报文为基准。在待配置路由器上进行以下配置。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **interface** interface-type interface-number ，进入接口视图。步骤 **3** 执行命令 **port-queue** { **be** | **af1** | **af2** | **af3** | **af4** | **ef** | **cs6** | **cs7** } **queue-limit** **max-****buffer-time** time-value ，配置端口队列的缓存时间。步骤 **4** 执行命令 **commit** ，提交配置。**----**结束##### 1.4.5.4 （可选）配置端口队列的动态调整当链路成员 down/up 时，可以根据物理带宽的变化动态调整 shaping 速率。##### 背景信息对于 Global MP/MP 接口，当 port-queue pq 命令和 port-queue wfq 命令将 CIR/PIR 的shaping 参数设置为百分比的情况下，设备会按照该百分比计算并下发 shaping 速率。而当链路成员发生 down/up 动作时，将会引起接口的物理带宽发生变化，此时若仍按之前的计算值下发 shaping 速率，会导致 shaping 的调度情况不能与现实情况匹配。配置该命令后，在物理带宽发生变化时，设备会按照 CIR/PIR 的 shaping 参数的百分比重新计算和下发 shaping 速率，实现对 shaping 速率进行动态调整。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **interface** interface-type interface-number ，进入接口视图。步骤 **3** 执行命令 **port-queue dynamic** ，配置在链路成员 down/up 时引起物理带宽变化的情况下，相应的动态调整 shaping 速率。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 37HUAWEI NetEngine40E配置指南 1 QoS步骤 **4** 执行命令 **commit** ，提交配置。**----**结束##### 1.4.5.5 （可选）配置队列调度的低时延震荡模式设置队列调度为低时延震荡模式，降低时延抖动。##### 背景信息MP 履行公网承载 TDM 业务时，由于 TDM 业务能承受的时延抖动小，需要设置队列调度为低时延震荡模式。在待配置 HQoS 功能的路由器上进行以下配置。系统缺省没有使能低时延震荡模式。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **interface** { interface-name | interface-type interface-number } ，进入Global-Mp-Group 接口视图。步骤 **3** 执行命令 **port-queue low-latency-mode** ，配置队列调度为低时延震荡模式。步骤 **4** 执行命令 **commit** ，提交配置。**----**结束##### 1.4.5.6 检查配置结果低速链路的端口队列调度配置成功后，可以查看端口队列的详细配置信息、统计信息。##### 操作步骤步骤 **1** 使用 **display ls-port-queue configuration** **interface** interface-type interfacenumber **outbound** 命令查看端口队列的详细配置信息。步骤 **2** 使用 **display ls-port-queue statistics** **interface** interface-type interface-number[ cos-value ] **outbound** 命令查看端口队列的统计信息。**----**结束#### 1.4.6 维护拥塞避免介绍如何清除拥塞避免的统计信息。##### 1.4.6.1 清空端口队列的统计信息介绍清空端口队列的统计信息的命令。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 38HUAWEI NetEngine40E配置指南 1 QoS##### 背景信息须知清除统计信息后，以前的统计信息将无法恢复，务必仔细确认。在确认需要清空端口队列的统计计数信息后，请在用户视图下执行下面的 **reset** 命令，清除之前的统计信息。##### 操作步骤       - 执行 **reset port-queue statistics** [ **slot** slot-id | **interface** { interface-name |interface-type interface-number } ] [ cos-value ] **outbound** [ **default** ] 命令清空指定接口下的端口队列统计数据。VS 模式下， **slot** 参数仅在 Admin VS 支持。       - 执行 **reset port-queue statistics slot** slot-id [ cos-value ] **outbound** **bind****mtunnel** 命令清空指定分布式组播 VPN 的 Mtunnel 接口所有端口队列或者某一具体优先级队列的统计计数。VS 模式下，该命令仅在 Admin VS 支持。**----**结束#### 1.4.7 配置举例从具体应用场景、配置命令等方面对拥塞避免、拥塞管理的应用进行了详细的描述。##### ******* 配置拥塞管理及拥塞避免示例以特性的典型场景为例，介绍如何配置拥塞管理及拥塞避免。##### 组网需求如 图 **1-7** 所示， Server 、 Telephone 、 PC1 和 PC2 通过 DeviceA 向网络发送数据，其中Server 发送关键业务数据， Telephone 发送语音数据， PC1 和 PC2 发送非关键业务数据。由于 DeviceA 入接口 interface1 的速率大于出接口 interface2 的速率，在 interface2接口处可能发生拥塞，并且可能出现拥塞加剧现象。要求在网络拥塞时保证 Server 和 Telephone 发送的业务数据得到优先发送，且Telephone 可以保证 5Mbit/s 的带宽， Server 可以保证 4Mbit/s 的带宽。同时由于 PC1 和PC2 是 VIP 用户，其数据在发送的过程中也需要一定的带宽保证，可以有少量延迟，但不希望延迟过大。在拥塞加剧的时候需要根据优先级来丢弃报文。需要在 DeviceA 配置 WRED 来与 WFQ 配合调度和丢弃。说明本例中 interface1 ， interface2 分别代表 GE1/0/0 ， GE2/0/0 。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 39HUAWEI NetEngine40E配置指南 1 QoS图 **1-7** 拥塞管理及拥塞避免配置组网图##### 配置思路采用如下的思路配置拥塞管理及拥塞避免。1. 在 DeviceA 入接口 interface1 处标记不同流的业务优先级。2. 配置 WRED 模板，设置报文丢弃的高低门限百分比和丢弃概率。3. 在 DeviceA 的出接口 interface2 为不同业务等级的端口队列配置调度策略，并在调度策略中应用配置好的 WRED 对象。##### 数据准备完成此举例配置，需准备以下数据：       - ACL 列表号、流分类名称、流行为名称、重新标记的业务优先级、流量策略名称       - WRED 模板名称、高低门限百分比、丢弃概率、队列中报文颜色       - WRED 丢弃策略应用的接口和端口队列参数##### 操作步骤步骤 **1** 配置 ACL 规则列表，分别匹配来源于 Server 、 Telephone 、 PC1 和 PC2 的报文<HUAWEI> **system-view**[ ~ HUAWEI] **sysname DeviceA**[*HUAWEI] **commit**[ ~ DeviceA] **acl number 2001**[*DeviceA-acl4-basic-2001] **rule permit source ******** 0.0.0.0**[*DeviceA-acl4-basic-2001] **commit**[ ~ DeviceA-acl4-basic-2001] **quit**[ ~ DeviceA] **acl number 2002**[*DeviceA-acl4-basic-2002] **rule permit source ******** 0.0.0.0**[*DeviceA-acl4-basic-2002] **commit**[ ~ DeviceA-acl4-basic-2002] **quit**[ ~ DeviceA] **acl number 2003**[*DeviceA-acl4-basic-2003] **rule permit source ******** 0.0.0.0**[*DeviceA-acl4-basic-2003] **commit**[ ~ DeviceA-acl4-basic-2003] **quit**[ ~ DeviceA] **acl number 2004**[*DeviceA-acl4-basic-2004] **rule permit source ******** 0.0.0.0**[*DeviceA-acl4-basic-2004] **commit**[ ~ DeviceA-acl4-basic-2004] **return**步骤 **2** 在 DeviceA 的 GE1/0/0 接口上配置复杂流分类，标记各个流的业务优先级<DeviceA> **system-view**[ ~ DeviceA] **traffic classifier aa**文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 40HUAWEI NetEngine40E配置指南 1 QoS[*DeviceA-classifier-aa] **if-match acl 2001**[*DeviceA-classifier-aa] **commit**[ ~ DeviceA-classifier-aa] **quit**[ ~ DeviceA] **traffic classifier bb**[*DeviceA-classifier-bb] **if-match acl 2002**[*DeviceA-classifier-bb] **commit**[ ~ DeviceA-classifier-bb] **quit**[ ~ DeviceA] **traffic classifier cc**[*DeviceA-classifier-cc] **if-match acl 2003**[*DeviceA-classifier-cc] **commit**[ ~ DeviceA-classifier-cc] **quit**[ ~ DeviceA] **traffic classifier dd**[*DeviceA-classifier-dd] **if-match acl 2004**[*DeviceA-classifier-dd] **commit**[ ~ DeviceA-classifier-dd] **quit**[ ~ DeviceA] **traffic behavior aa**[*DeviceA-behavior-aa] **remark ip-precedence 5**[*DeviceA-behavior-aa] **car cir 5000**[*DeviceA-behavior-aa] **commit**[ ~ DeviceA-behavior-aa] **quit**[ ~ DeviceA] **traffic behavior bb**[*DeviceA-behavior-bb] **remark ip-precedence 4**[*DeviceA-behavior-bb] **car cir 4000**[*DeviceA-behavior-bb] **commit**[ ~ DeviceA-behavior-bb] **quit**[ ~ DeviceA] **traffic behavior cc**[*DeviceA-behavior-cc] **remark ip-precedence 3**[*DeviceA-behavior-cc] **commit**[ ~ DeviceA-behavior-cc] **quit**[ ~ DeviceA] **traffic behavior dd**[*DeviceA-behavior-dd] **remark ip-precedence 2**[*DeviceA-behavior-dd] **commit**[ ~ DeviceA-behavior-dd] **quit**[ ~ DeviceA] **traffic policy ee**[*DeviceA-trafficpolicy-ee] **classifier aa behavior aa**[*DeviceA-trafficpolicy-ee] **classifier bb behavior bb**[*DeviceA-trafficpolicy-ee] **classifier cc behavior cc**[*DeviceA-trafficpolicy-ee] **classifier dd behavior dd**[*DeviceA-trafficpolicy-ee] **commit**[ ~ DeviceA-trafficpolicy-ee] **quit**[ ~ DeviceA] **interface gigabitethernet1/0/0**[ ~ DeviceA-GigabitEthernet1/0/0] **undo shutdown**[*DeviceA-GigabitEthernet1/0/0] **traffic-policy ee inbound**[*DeviceA-GigabitEthernet1/0/0] **commit**[ ~ DeviceA-GigabitEthernet1/0/0] **return**说明IP Precedence 与服务等级之间的映射关系如《 HUAWEI NetEngine40E 特性描述 -QoS 优先级映射》中的 IP Precedence/MPLS EXP/802.1p 到服务等级的缺省映射表所示。服务等级的优先级高低取决于具体的队列调度算法配置：          - 如果 8 种类型的队列都配置为 PQ 调度，则 CS7>CS6>EF>AF4>AF3>AF2>AF1>BE ；          - 如果 BE 配置为 PQ 调度（一般不会这么配置），其余 7 种类型的队列配置为 WFQ 调度，则 BE的优先级比其余 7 个都高；          - 如果 8 种类型的队列都配置成 WFQ 调度，则相互之间无优先级高低之分。步骤 **3** 在 DeviceA 上配置 WRED 模板<DeviceA> **system-view**[ ~ DeviceA] **port-wred pw**[*DeviceA-port-wred-pw] **color green low-limit 70 high-limit 100 discard-percentage 100**[*DeviceA-port-wred-pw] **color yellow low-limit 60 high-limit 90 discard-percentage 100**[*DeviceA-port-wred-pw] **color red low-limit 50 high-limit 80 discard-percentage 100**[*DeviceA-port-wred-pw] **commit**[ ~ DeviceA-port-wred-pw] **quit**完成上述配置后，执行命令 **display port-wred configuration verbose** ，可以查看WRED 模板的配置参数。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 41HUAWEI NetEngine40E配置指南 1 QoS<DeviceA> **display port-wred configuration verbose**Port wred name : pw--------------------------------------------------Color  Low-limit  High-limit  Discard-percent--------------------------------------------------green  70      100      100yellow  60      90      100red   50      80      100Queue Depth : 8000Reference relationships : NULL步骤 **4** 在 DeviceA 的 GE2/0/0 接口上配置端口队列并应用配置好的 WRED 对象 pw[ ~ DeviceA] **interface gigabitethernet2/0/0**[ ~ DeviceA-GigabitEthernet2/0/0] **undo shutdown**[*DeviceA-GigabitEthernet2/0/0] **port-queue ef pq port-wred pw outbound**[*DeviceA-GigabitEthernet2/0/0] **port-queue af4 wfq weight 15 shaping 100 port-wred pw outbound**[*DeviceA-GigabitEthernet2/0/0] **port-queue af3 wfq weight 10 shaping 50 port-wred pw outbound**[*DeviceA-GigabitEthernet2/0/0] **port-queue af2 wfq weight 10 shaping 50 port-wred pw outbound**[*DeviceA-GigabitEthernet2/0/0] **commit**[ ~ DeviceA-GigabitEthernet2/0/0] **return**完成上述配置后，执行命令 **display port-queue configuration interface** ，可以显示端口队列的详细配置信息。<DeviceA> **display port-queue configuration interface gigabitethernet 2/0/0 outbound**GigabitEthernet2/0/0 outbound current port-queue configuration:be : arithmetic: wfq        weight: 10     tm weight: 3fact weight: 10.00       shaping(kbps): NAport-wred: NAgreen (low-high limit) (kbytes)         12800 - 12800yellow(low-high limit) (kbytes)         12800 - 12800red  (low-high limit) (kbytes)         12800 - 12800current queue-length   (kbytes)         12800cir:123            cir-percentage:NAcir-arithmetic:pq       cir-weight:NApir:123            pir-percentage:NApir-arithmetic:lpq       pir-weight:NAaf1: arithmetic: wfq        weight: 10     tm weight: 3fact weight: 10.00       shaping(kbps): NAport-wred: NAgreen (low-high limit) (kbytes)         12800 - 12800yellow(low-high limit) (kbytes)         12800 - 12800red  (low-high limit) (kbytes)         12800 - 12800current queue-length   (kbytes)         12800cir:NA             cir-percentage:10cir-arithmetic:pq       cir-weight:NApir:NA             pir-percentage:20pir-arithmetic:wfq       pir-weight:15af2: arithmetic: wfq        weight: 10     tm weight: 3fact weight: 10.00       shaping(kbps): NAport-wred: NAgreen (low-high limit) (kbytes)         12800 - 12800yellow(low-high limit) (kbytes)         12800 - 12800red  (low-high limit) (kbytes)         12800 - 12800current queue-length   (kbytes)         12800cir:NA             cir-percentage:NAcir-arithmetic:NA       cir-weight:NApir:NA             pir-percentage:NApir-arithmetic:NA       pir-weight:NAaf3: arithmetic: wfq        weight: 15     tm weight: 2fact weight: 15.00       shaping(kbps): NAport-wred: NAgreen (low-high limit) (kbytes)         12800 - 12800yellow(low-high limit) (kbytes)         12800 - 12800red  (low-high limit) (kbytes)         12800 - 12800current queue-length   (kbytes)         12800cir:NA             cir-percentage:NAcir-arithmetic:NA       cir-weight:NApir:NA             pir-percentage:NA文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 42HUAWEI NetEngine40E配置指南 1 QoSpir-arithmetic:NA       pir-weight:NAaf4: arithmetic: wfq        weight: 15     tm weight: 2fact weight: 15.00       shaping(kbps): NAport-wred: NAgreen (low-high limit) (kbytes)         12800 - 12800yellow(low-high limit) (kbytes)         12800 - 12800red  (low-high limit) (kbytes)         12800 - 12800current queue-length   (kbytes)         12800cir:NA             cir-percentage:NAcir-arithmetic:NA       cir-weight:NApir:NA             pir-percentage:NApir-arithmetic:NA       pir-weight:NAef : arithmetic: pq         weight: NA     tm weight: 0fact weight: 0.00       shaping(kbps): NAport-wred: NAgreen (low-high limit) (kbytes)         1280 - 1280yellow(low-high limit) (kbytes)         1280 - 1280red  (low-high limit) (kbytes)         1280 - 1280current queue-length   (kbytes)         1280cir:NA             cir-percentage:NAcir-arithmetic:NA       cir-weight:NApir:NA             pir-percentage:NApir-arithmetic:NA       pir-weight:NAcs6: arithmetic: pq         weight: NA     tm weight: 0fact weight: 0.00       shaping(kbps): NAport-wred: NAgreen (low-high limit) (kbytes)         1280 - 1280yellow(low-high limit) (kbytes)         1280 - 1280red  (low-high limit) (kbytes)         1280 - 1280current queue-length   (kbytes)         1280cir:NA             cir-percentage:NAcir-arithmetic:NA       cir-weight:NApir:NA             pir-percentage:NApir-arithmetic:NA       pir-weight:NAcs7: arithmetic: pq         weight: NA     tm weight: 0fact weight: 0.00       shaping(kbps): NAport-wred: NAgreen (low-high limit) (kbytes)         1280 - 1280yellow(low-high limit) (kbytes)         1280 - 1280red  (low-high limit) (kbytes)         1280 - 1280current queue-length   (kbytes)         1280cir:NA             cir-percentage:NAcir-arithmetic:NA       cir-weight:NApir:NA             pir-percentage:NApir-arithmetic:NA       pir-weight:NA步骤 **5** 检查配置结果当网络中有流量通过时，在 DeviceA 上执行命令 **display port-queue statistics** [ **slot**slot-id | **interface** { interface-type interface-number | interface-name } ] [ cosvalue ] **outbound** ，可以看到各种业务等级的流量迅速增长。当网络中流量迅速增加时，可以看到丢弃的流量开始迅速增加，不同的业务流量丢弃的数量符合 WRED 配置的丢弃参数。**----**结束##### 配置文件DeviceA 的配置文件#sysname DeviceA#acl number 2001rule permit source ******** 0#acl number 2002rule permit source ******** 0文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 43HUAWEI NetEngine40E配置指南 1 QoS#acl number 2003rule permit source ******** 0#acl number 2004rule permit source ******** 0#traffic classifier aa operator orif-match acl 2001traffic classifier bb operator orif-match acl 2002traffic classifier cc operator orif-match acl 2003traffic classifier dd operator orif-match acl 2004#traffic behavior aaremark ip-precedence 5car cir 5000traffic behavior bbcar cir 4000remark ip-precedence 4traffic behavior ccremark ip-precedence 3traffic behavior ddremark ip-precedence 2#traffic policy eeclassifier aa behavior aaclassifier bb behavior bbclassifier cc behavior ccclassifier dd behavior dd#port-wred pwcolor green low-limit 70 high-limit 100 discard-percentage 100color yellow low-limit 60 high-limit 90 discard-percentage 100color red low-limit 50 high-limit 80 discard-percentage 100#interface GigabitEthernet1/0/0undo shutdownip address ******** *************traffic-policy ee inbound#interface GigabitEthernet2/0/0undo shutdownip address ********* *************port-queue af2 wfq weight 10 shaping 50 port-wred pw outboundport-queue af3 wfq weight 10 shaping 50 port-wred pw outboundport-queue af4 wfq weight 15 shaping 100 port-wred pw outboundport-queue ef pq port-wred pw outbound#ospf 1area 0.0.0.0network ******** *********network ********* *********#return##### ******* 配置低速链路队列调度示例以特性的典型场景为例，介绍如何在低速链路上，保证不同优先级的报文在拥塞发生时能根据业务等级进行队列调度，优先保证高优先级的流量带宽。##### 组网需求如 图 **1-8** 所示， DeviceA 和 DeviceB 为网络中的两个设备，通过低速链路连接，带宽资源有限。为了保证不同优先级的报文在拥塞发生时能根据业务等级进行队列调度，优先文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 44HUAWEI NetEngine40E配置指南 1 QoS保证高优先级的流量带宽，在 DeviceA 的接口上配置 PQ 和 WFQ 的队列调度，对于高优先级的流量使用 PQ 调度，其他优先级流量使用 WFQ 调度。本例中， ef 队列的流量使用 PQ 调度，并调整 PIR 整形百分比； af2 、 af3 、 af4 队列的流量使用 WFQ 调度，配置相同的 PIR 整形百分比均。说明本例中 interface1 代表 serial2/0/0:0 。图 **1-8** 配置低速链路队列调度组网图##### 配置注意事项无。##### 配置思路采用如下思路配置低速链路的队列调度。1. 配置 DeviceA 和 DeviceB 接口的 IP 地址。2. 在 DeviceA 上配置 PQ 和 WFQ 队列调度。##### 数据准备完成本配置举例，需准备如下数据：       - 各接口的 IP 地址       - PQ 队列调度的 PIR 的整形百分比       - WFQ 队列调度的 PIR 的整形百分比##### 操作步骤步骤 **1** 配置 DeviceA 和 DeviceB 的串口                                              略。具体配置请参见《 HUAWEI NetEngine40E 路由器 配置指南 广域网接入》。步骤 **2** 配置 DeviceA 和 DeviceB 各接口 IP 地址，保证网络互通略。具体配置请参见《 HUAWEI NetEngine40E 路由器 配置指南 -IP 路由》。步骤 **3** 在 DeviceA 的 Serial2/0/0:0 接口上配置 PQ 和 WFQ 队列调度# 配置 PQ 队列调度。<DeviceA> **system view**[ ~ DeviceA] **interface serial 2/0/0:0**[*DeviceA-serial2/0/0:0] **port-queue ef pq shaping pir percentage 60**文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 45
最终截取: ##### ******* 配置拥塞管理及拥塞避免示例


以特性的典型场景为例，介绍如何配置拥塞管理及拥塞避免。

##### 组网需求


如 图 **1-7** 所示， Server 、 Telephone 、 PC1 和 PC2 通过 DeviceA 向网络发送数据，其中
Server 发送关键业务数据， Telephone 发送语音数据， PC1 和 PC2 发送非关键业务数
据。由于 DeviceA 入接口 interface1 的速率大于出接口 interface2 的速率，在 interface2
接口处可能发生拥塞，并且可能出现拥塞加剧现象。


要求在网络拥塞时保证 Server 和 Telephone 发送的业务数据得到优先发送，且
Telephone 可以保证 5Mbit/s 的带宽， Server 可以保证 4Mbit/s 的带宽。同时由于 PC1 和
PC2 是 VIP 用户，其数据在发送的过程中也需要一定的带宽保证，可以有少量延迟，但
不希望延迟过大。在拥塞加剧的时候需要根据优先级来丢弃报文。


需要在 DeviceA 配置 WRED 来与 WFQ 配合调度和丢弃。


说明


本例中 interface1 ， interface2 分别代表 GE1/0/0 ， GE2/0/0 。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 39



HUAWEI NetEngine40E
配置指南 1 QoS


图 **1-7** 拥塞管理及拥塞避免配置组网图

##### 配置思路


采用如下的思路配置拥塞管理及拥塞避免。


1. 在 DeviceA 入接口 interface1 处标记不同流的业务优先级。


2. 配置 WRED 模板，设置报文丢弃的高低门限百分比和丢弃概率。


3. 在 DeviceA 的出接口 interface2 为不同业务等级的端口队列配置调度策略，并在调
度策略中应用配置好的 WRED 对象。

##### 数据准备


完成此举例配置，需准备以下数据：


       - ACL 列表号、流分类名称、流行为名称、重新标记的业务优先级、流量策略名称


       - WRED 模板名称、高低门限百分比、丢弃概率、队列中报文颜色


       - WRED 丢弃策略应用的接口和端口队列参数

##### 操作步骤


步骤 **1** 配置 ACL 规则列表，分别匹配来源于 Server 、 Telephone 、 PC1 和 PC2 的报文


<HUAWEI> **system-view**

[ ~ HUAWEI] **sysname DeviceA**

[*HUAWEI] **commit**

[ ~ DeviceA] **acl number 2001**

[*DeviceA-acl4-basic-2001] **rule permit source ******** 0.0.0.0**

[*DeviceA-acl4-basic-2001] **commit**

[ ~ DeviceA-acl4-basic-2001] **quit**

[ ~ DeviceA] **acl number 2002**

[*DeviceA-acl4-basic-2002] **rule permit source ******** 0.0.0.0**

[*DeviceA-acl4-basic-2002] **commit**

[ ~ DeviceA-acl4-basic-2002] **quit**

[ ~ DeviceA] **acl number 2003**

[*DeviceA-acl4-basic-2003] **rule permit source ******** 0.0.0.0**

[*DeviceA-acl4-basic-2003] **commit**

[ ~ DeviceA-acl4-basic-2003] **quit**

[ ~ DeviceA] **acl number 2004**

[*DeviceA-acl4-basic-2004] **rule permit source ******** 0.0.0.0**

[*DeviceA-acl4-basic-2004] **commit**

[ ~ DeviceA-acl4-basic-2004] **return**


步骤 **2** 在 DeviceA 的 GE1/0/0 接口上配置复杂流分类，标记各个流的业务优先级


<DeviceA> **system-view**

[ ~ DeviceA] **traffic classifier aa**


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 40



HUAWEI NetEngine40E
配置指南 1 QoS


[*DeviceA-classifier-aa] **if-match acl 2001**

[*DeviceA-classifier-aa] **commit**

[ ~ DeviceA-classifier-aa] **quit**

[ ~ DeviceA] **traffic classifier bb**

[*DeviceA-classifier-bb] **if-match acl 2002**

[*DeviceA-classifier-bb] **commit**

[ ~ DeviceA-classifier-bb] **quit**

[ ~ DeviceA] **traffic classifier cc**

[*DeviceA-classifier-cc] **if-match acl 2003**

[*DeviceA-classifier-cc] **commit**

[ ~ DeviceA-classifier-cc] **quit**

[ ~ DeviceA] **traffic classifier dd**

[*DeviceA-classifier-dd] **if-match acl 2004**

[*DeviceA-classifier-dd] **commit**

[ ~ DeviceA-classifier-dd] **quit**

[ ~ DeviceA] **traffic behavior aa**

[*DeviceA-behavior-aa] **remark ip-precedence 5**

[*DeviceA-behavior-aa] **car cir 5000**

[*DeviceA-behavior-aa] **commit**

[ ~ DeviceA-behavior-aa] **quit**

[ ~ DeviceA] **traffic behavior bb**

[*DeviceA-behavior-bb] **remark ip-precedence 4**

[*DeviceA-behavior-bb] **car cir 4000**

[*DeviceA-behavior-bb] **commit**

[ ~ DeviceA-behavior-bb] **quit**

[ ~ DeviceA] **traffic behavior cc**

[*DeviceA-behavior-cc] **remark ip-precedence 3**

[*DeviceA-behavior-cc] **commit**

[ ~ DeviceA-behavior-cc] **quit**

[ ~ DeviceA] **traffic behavior dd**

[*DeviceA-behavior-dd] **remark ip-precedence 2**

[*DeviceA-behavior-dd] **commit**

[ ~ DeviceA-behavior-dd] **quit**

[ ~ DeviceA] **traffic policy ee**

[*DeviceA-trafficpolicy-ee] **classifier aa behavior aa**

[*DeviceA-trafficpolicy-ee] **classifier bb behavior bb**

[*DeviceA-trafficpolicy-ee] **classifier cc behavior cc**

[*DeviceA-trafficpolicy-ee] **classifier dd behavior dd**

[*DeviceA-trafficpolicy-ee] **commit**

[ ~ DeviceA-trafficpolicy-ee] **quit**

[ ~ DeviceA] **interface gigabitethernet1/0/0**

[ ~ DeviceA-GigabitEthernet1/0/0] **undo shutdown**

[*DeviceA-GigabitEthernet1/0/0] **traffic-policy ee inbound**

[*DeviceA-GigabitEthernet1/0/0] **commit**

[ ~ DeviceA-GigabitEthernet1/0/0] **return**


说明


IP Precedence 与服务等级之间的映射关系如《 HUAWEI NetEngine40E 特性描述 -QoS 优先级映
射》中的 IP Precedence/MPLS EXP/802.1p 到服务等级的缺省映射表所示。


服务等级的优先级高低取决于具体的队列调度算法配置：


          - 如果 8 种类型的队列都配置为 PQ 调度，则 CS7>CS6>EF>AF4>AF3>AF2>AF1>BE ；


          - 如果 BE 配置为 PQ 调度（一般不会这么配置），其余 7 种类型的队列配置为 WFQ 调度，则 BE
的优先级比其余 7 个都高；


          - 如果 8 种类型的队列都配置成 WFQ 调度，则相互之间无优先级高低之分。


步骤 **3** 在 DeviceA 上配置 WRED 模板


<DeviceA> **system-view**

[ ~ DeviceA] **port-wred pw**

[*DeviceA-port-wred-pw] **color green low-limit 70 high-limit 100 discard-percentage 100**

[*DeviceA-port-wred-pw] **color yellow low-limit 60 high-limit 90 discard-percentage 100**

[*DeviceA-port-wred-pw] **color red low-limit 50 high-limit 80 discard-percentage 100**

[*DeviceA-port-wred-pw] **commit**

[ ~ DeviceA-port-wred-pw] **quit**

完成上述配置后，执行命令 **display port-wred configuration verbose** ，可以查看
WRED 模板的配置参数。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 41



HUAWEI NetEngine40E
配置指南 1 QoS


<DeviceA> **display port-wred configuration verbose**
Port wred name : pw

--------------------------------------------------
Color  Low-limit  High-limit  Discard-percent

--------------------------------------------------
green  70      100      100
yellow  60      90      100
red   50      80      100
Queue Depth : 8000
Reference relationships : NULL


步骤 **4** 在 DeviceA 的 GE2/0/0 接口上配置端口队列并应用配置好的 WRED 对象 pw


[ ~ DeviceA] **interface gigabitethernet2/0/0**

[ ~ DeviceA-GigabitEthernet2/0/0] **undo shutdown**

[*DeviceA-GigabitEthernet2/0/0] **port-queue ef pq port-wred pw outbound**

[*DeviceA-GigabitEthernet2/0/0] **port-queue af4 wfq weight 15 shaping 100 port-wred pw outbound**

[*DeviceA-GigabitEthernet2/0/0] **port-queue af3 wfq weight 10 shaping 50 port-wred pw outbound**

[*DeviceA-GigabitEthernet2/0/0] **port-queue af2 wfq weight 10 shaping 50 port-wred pw outbound**

[*DeviceA-GigabitEthernet2/0/0] **commit**

[ ~ DeviceA-GigabitEthernet2/0/0] **return**


完成上述配置后，执行命令 **display port-queue configuration interface** ，可以显示
端口队列的详细配置信息。


<DeviceA> **display port-queue configuration interface gigabitethernet 2/0/0 outbound**
GigabitEthernet2/0/0 outbound current port-queue configuration:
be : arithmetic: wfq        weight: 10     tm weight: 3
fact weight: 10.00       shaping(kbps): NA
port-wred: NA
green (low-high limit) (kbytes)         12800 - 12800
yellow(low-high limit) (kbytes)         12800 - 12800
red  (low-high limit) (kbytes)         12800 - 12800
current queue-length   (kbytes)         12800
cir:123            cir-percentage:NA
cir-arithmetic:pq       cir-weight:NA
pir:123            pir-percentage:NA
pir-arithmetic:lpq       pir-weight:NA
af1: arithmetic: wfq        weight: 10     tm weight: 3
fact weight: 10.00       shaping(kbps): NA
port-wred: NA
green (low-high limit) (kbytes)         12800 - 12800
yellow(low-high limit) (kbytes)         12800 - 12800
red  (low-high limit) (kbytes)         12800 - 12800
current queue-length   (kbytes)         12800
cir:NA             cir-percentage:10
cir-arithmetic:pq       cir-weight:NA
pir:NA             pir-percentage:20
pir-arithmetic:wfq       pir-weight:15
af2: arithmetic: wfq        weight: 10     tm weight: 3
fact weight: 10.00       shaping(kbps): NA
port-wred: NA
green (low-high limit) (kbytes)         12800 - 12800
yellow(low-high limit) (kbytes)         12800 - 12800
red  (low-high limit) (kbytes)         12800 - 12800
current queue-length   (kbytes)         12800
cir:NA             cir-percentage:NA
cir-arithmetic:NA       cir-weight:NA
pir:NA             pir-percentage:NA
pir-arithmetic:NA       pir-weight:NA
af3: arithmetic: wfq        weight: 15     tm weight: 2
fact weight: 15.00       shaping(kbps): NA
port-wred: NA
green (low-high limit) (kbytes)         12800 - 12800
yellow(low-high limit) (kbytes)         12800 - 12800
red  (low-high limit) (kbytes)         12800 - 12800
current queue-length   (kbytes)         12800
cir:NA             cir-percentage:NA
cir-arithmetic:NA       cir-weight:NA
pir:NA             pir-percentage:NA


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 42



HUAWEI NetEngine40E
配置指南 1 QoS


pir-arithmetic:NA       pir-weight:NA
af4: arithmetic: wfq        weight: 15     tm weight: 2
fact weight: 15.00       shaping(kbps): NA
port-wred: NA
green (low-high limit) (kbytes)         12800 - 12800
yellow(low-high limit) (kbytes)         12800 - 12800
red  (low-high limit) (kbytes)         12800 - 12800
current queue-length   (kbytes)         12800
cir:NA             cir-percentage:NA
cir-arithmetic:NA       cir-weight:NA
pir:NA             pir-percentage:NA
pir-arithmetic:NA       pir-weight:NA
ef : arithmetic: pq         weight: NA     tm weight: 0
fact weight: 0.00       shaping(kbps): NA
port-wred: NA
green (low-high limit) (kbytes)         1280 - 1280
yellow(low-high limit) (kbytes)         1280 - 1280
red  (low-high limit) (kbytes)         1280 - 1280
current queue-length   (kbytes)         1280
cir:NA             cir-percentage:NA
cir-arithmetic:NA       cir-weight:NA
pir:NA             pir-percentage:NA
pir-arithmetic:NA       pir-weight:NA
cs6: arithmetic: pq         weight: NA     tm weight: 0
fact weight: 0.00       shaping(kbps): NA
port-wred: NA
green (low-high limit) (kbytes)         1280 - 1280
yellow(low-high limit) (kbytes)         1280 - 1280
red  (low-high limit) (kbytes)         1280 - 1280
current queue-length   (kbytes)         1280
cir:NA             cir-percentage:NA
cir-arithmetic:NA       cir-weight:NA
pir:NA             pir-percentage:NA
pir-arithmetic:NA       pir-weight:NA
cs7: arithmetic: pq         weight: NA     tm weight: 0
fact weight: 0.00       shaping(kbps): NA
port-wred: NA
green (low-high limit) (kbytes)         1280 - 1280
yellow(low-high limit) (kbytes)         1280 - 1280
red  (low-high limit) (kbytes)         1280 - 1280
current queue-length   (kbytes)         1280
cir:NA             cir-percentage:NA
cir-arithmetic:NA       cir-weight:NA
pir:NA             pir-percentage:NA
pir-arithmetic:NA       pir-weight:NA


步骤 **5** 检查配置结果


当网络中有流量通过时，在 DeviceA 上执行命令 **display port-queue statistics** [ **slot**
slot-id | **interface** { interface-type interface-number | interface-name } ] [ cosvalue ] **outbound** ，可以看到各种业务等级的流量迅速增长。当网络中流量迅速增加
时，可以看到丢弃的流量开始迅速增加，不同的业务流量丢弃的数量符合 WRED 配置
的丢弃参数。


**----**
结束

##### 配置文件


DeviceA 的配置文件

#
sysname DeviceA
#

acl number 2001
rule permit source ******** 0
#

acl number 2002
rule permit source ******** 0


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 43



HUAWEI NetEngine40E
配置指南 1 QoS


#

acl number 2003
rule permit source ******** 0
#

acl number 2004
rule permit source ******** 0
#
traffic classifier aa operator or
if-match acl 2001
traffic classifier bb operator or
if-match acl 2002
traffic classifier cc operator or
if-match acl 2003
traffic classifier dd operator or
if-match acl 2004

#
traffic behavior aa
remark ip-precedence 5
car cir 5000
traffic behavior bb
car cir 4000
remark ip-precedence 4
traffic behavior cc
remark ip-precedence 3
traffic behavior dd
remark ip-precedence 2
#
traffic policy ee
classifier aa behavior aa
classifier bb behavior bb
classifier cc behavior cc
classifier dd behavior dd
#
port-wred pw
color green low-limit 70 high-limit 100 discard-percentage 100
color yellow low-limit 60 high-limit 90 discard-percentage 100
color red low-limit 50 high-limit 80 discard-percentage 100
#
interface GigabitEthernet1/0/0
undo shutdown
ip address ******** *************
traffic-policy ee inbound
#
interface GigabitEthernet2/0/0
undo shutdown
ip address ********* *************
port-queue af2 wfq weight 10 shaping 50 port-wred pw outbound
port-queue af3 wfq weight 10 shaping 50 port-wred pw outbound
port-queue af4 wfq weight 15 shaping 100 port-wred pw outbound
port-queue ef pq port-wred pw outbound
#
ospf 1
area 0.0.0.0

network ******** *********

network ********* *********

#

return

