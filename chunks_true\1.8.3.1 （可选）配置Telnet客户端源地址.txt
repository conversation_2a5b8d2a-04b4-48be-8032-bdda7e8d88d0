

用户可以配置 Telnet 客户端的源地址信息，从指定的客户端源地址和路由建立 Telnet 连
接，保证安全性。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 236


HUAWEI NetEngine40E
配置指南 1 基础配置

##### 背景信息


用户可以在路由器上指定某一接口，为此接口配置 IP 地址，然后使用该 IP 地址作为
Telnet 连接的源 IP 地址，从而达到进行安全校验的目的。


客户端源地址可以配置为源接口或源 IP 。

##### 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **telnet client-source** { { **-a** source-ip-address | { **-i** interface-type
interface-number | interface-name } } } 或 **telnet ipv6 client-source** **-a** ipv6address [ **-vpn-instance** ipv6-vpn-instance-name ] ，配置 Telnet 客户端源地址。


步骤 **3** 执行命令 **commit** ，提交配置。


**----**
结束
