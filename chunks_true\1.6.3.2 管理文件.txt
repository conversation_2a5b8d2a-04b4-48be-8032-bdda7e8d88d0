

如果用户需要查看、删除、重命名设备上的文件时，可以通过文件系统对文件进行相
应的操作。

##### 背景信息


       - 对文件的管理包括：显示文件的内容、拷贝文件、移动文件、重命名文件、压缩
文件、删除文件以及恢复删除的文件、彻底删除回收站中的文件、运行批处理文
件和配置文件系统提示方式。


       - 当用户需要对某个文件进行操作时，可以执行 **cd** directory 命令，改变当前目录到
文件所处的目录。

##### 操作步骤

       - 执行命令 **more** file-name ，显示文件的内容。

       - 执行命令 **copy** source-filename destination-filename ，拷贝文件。

       - 执行命令 **move** source-filename destination-filename ，移动文件。

       - 执行命令 **rename** source-filename destination-filename ，重新命名文件。

       - 执行命令 **zip** source-filename destination-filename ，压缩文件或目录。

       - 执行命令 **unzip** source-filename destination-filename ，解压缩文件。


说明


**zip** 命令支持压缩文件或者目录， **unzip** 命令仅支持解压缩文件。

       - 执行命令 **delete** [ **/unreserved** ] filename [ **all** ] ，删除文件。


如果使用参数 [ **/unreserved** ] ，则删除后的文件不可恢复。


VS 模式下，该命令仅在 Admin VS 支持。

       - 执行命令 **undelete** filename ，恢复删除的文件。


说明


执行命令 **dir /all** 可以查看所有的文件信息，其中被删除到回收站的文件用“ [ ] ”括起来
表示。


对于执行删除命令行时使用命令 **delete** 中参数 **/unreserved** 后的文件不可恢复。


VS 模式下，该命令仅在 Admin VS 支持。

       - 执行命令 **reset recycle-bin** [ **/f** | filename ] ，彻底删除回收站中的文件。


当需要永久删除某一已丢弃的文件时，可以进行彻底删除回收站中的文件的操
作。命令关键字 **/f** 表示删除回收站中的所有文件，删除时不需要用户再逐个文件
确认是否删除。

       - 执行命令 **tail** file-name ，显示指定文件的最后指定行内容信息。


       - 运行批处理文件或 VSL （ VRP Shell Languages ）脚本文件


当需要对文件一次进行多项处理时，可以进行运行批处理文件的操作。编辑好的
批处理文件要预先保存在设备的存储设备中。


如果已经建立好批处理文件，那么可以执行该文件，以实现执行固定任务的自动
化。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 147


HUAWEI NetEngine40E
配置指南 1 基础配置


a. 执行命令 **system-view** ，进入系统视图。

b. 执行命令 **execute** filename [ parameter &<1-8> ] ，运行批处理文件或 VSL
（ VRP Shell Languages ）脚本文件。


须知


为防止用户信息泄露，指定的批处理文件或 VSL 脚本文件中，请尽量不要携带
用户敏感数据信息（如明文密码、明文密钥等）。


       - 配置文件系统提示方式


当在设备上进行操作时，系统可以给予提示或警示信息（特别是对于可能导致数
据丢失或破坏的操作）。如果需要修改系统对文件操作的提醒方式时，可以进行
配置文件系统提示方式的操作。


a. 执行 **system-view** 命令，进入到系统视图。

b. 执行 **file prompt** { **alert** | **quiet** } 命令，配置文件系统提示方式。


须知


如果将文件操作的提醒方式设置为 quiet ，则对由于用户误操作（比如删除文
件操作）而导致数据丢失的情况不作提示，请慎用。


c. 执行命令 **commit** ，提交配置。


**----**
结束
