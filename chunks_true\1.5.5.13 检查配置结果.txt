

用户通过 STelnet 登录系统配置成功后，可以查看到 SSH 服务器的全局配置信息等内
容。

##### 前提条件


已完成用户通过 STelnet 登录系统的所有配置。

##### 操作步骤


       - 使用 **display ssh user-information** username 命令，在 SSH 服务器端查看 SSH 用
户信息。


       - 使用 **display ssh server** **status** 命令，查看 SSH 服务器的全局配置信息。


       - 使用 **display ssh server** **session** 命令，在 SSH 服务器端查看与 SSH 客户端连接的
会话信息。


       - SSH 协议的白名单 Session-CAR 功能配置成功以后，可以按照如下指导检查配置结
果：


– 基于 IPv4 ：使用命令 **display cpu-defend whitelist session-car** **ssh**
**statistics** **slot** slot-id ，查看指定接口板上的 SSH 协议白名单 Session-CAR 的
统计信息。


如果需要查看某一段时间的统计信息，可以使用命令 **reset cpu-defend**
**whitelist session-car** **ssh** **statistics** **slot** slot-id 先清除指定接口板上的 SSH
协议白名单 Session-CAR 的统计信息，再使用命令 **display cpu-defend**
**whitelist session-car** **ssh** **statistics** **slot** slot-id 。


– 基于 IPv6 ：使用命令 **display cpu-defend whitelist-v6 session-car** **sshv6**
**statistics** **slot** slot-id ，查看指定接口板上的 SSH 协议白名单 Session-CAR 的
统计信息。


如果需要查看某一段时间的统计信息，可以使用命令 **reset cpu-defend**
**whitelist-v6 session-car** **sshv6** **statistics** **slot** slot-id 先清除指定接口板上
的 SSH 协议白名单 Session-CAR 的统计信息，再使用命令 **display cpu-defend**
**whitelist-v6 session-car** **sshv6** **statistics** **slot** slot-id 。


**----**
结束
