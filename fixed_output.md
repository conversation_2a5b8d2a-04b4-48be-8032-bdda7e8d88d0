# 目 录

1.1 1.2 1.3


# 1 基础配置

基础配置 1.1


## 1.1 前 言

1. 2 首次登录配置 当用户需要为第一次上电的设备进行基本配置时，可以通过Console口登录设备，还可 以通过即插即用方式批量部署设备。 1. 3 命令行接口配置 用户对设备的日常操作，主要通过输入命令行来实现。 用户可以在命令视图下编辑和 配置命令行，并显示配置成功或错误的信息。 1. 4 用户界面配置 当用户通过Console口、Telnet或SSH方式登录路由器时，系统会分配相应的用户界 面，用来管理、监控设备和当前用户之间的会话。 1.

5 用户登录配置 用户可以通过Console口、Telnet或SSH（STelnet）方式登录设备，实现对设备的本地 或远程维护。 1. 6 文件系统配置 文件系统实现对存储设备中的文件、目录的管理。 1. 7 配置管理配置 为了保障用户配置的可靠性，系统支持两种配置生效模式。 1. 8 访问其他设备配置 设备可以作为客户端访问网络上的其他设备。 1. 9 ZTP配置 设备可以通过零配置自动部署ZTP（Zero Touch Provisioning）实现空配置下的上电自 动部署。 1.

1 本文档介绍了基础配置的基本概念、在不同应用场景中的配置过程和配置举例。


## 1.2 首次登录配置

当用户需要为第一次上电的设备进行基本配置时，可以通过Console口登录设备，还可 以通过即插即用方式批量部署设备。


### 1.2.1 首次登录概述

设备第一次上电可以使用Console口或通过管理网口登录，使用Console口进行本地登 录是登录设备的最基本的方式，也是配置通过其他方式登录设备的基础。 Console口是一种通信串行端口，由设备的主控板提供。 一块主控板提供一个Console 口，端口类型为EIA/TIA-232 DCE。 用户终端的串行端口可以与设备的Console口直接 连接，实现对设备的本地配置。 Console口状态如下： Connected：Console口处于试连接状态。

Disconnected：与Console口连接断开。 首次登录，系统自动提示用户设置密码。 通过按CTRL+R恢复出厂设置，从而清除用户设置的密码。 在构建大型网络时，为了实现对设备的远程统一管理和控制，以提高部署效率和降低 运维成本，设备首次上电时还可以基于即插即用功能通过DCN或DHCP方式批量部署设 首次登录设备配置注意事项


### 1.2.2 首次登录设备配置注意事项

特性限制


### 1.2.3 通过Console口登录设备

通过Console口连接终端与设备，搭建配置环境。 应用环境 设备第一次上电，需要对此设备进行配置和管理时，可以通过Console口登录。 前置任务 在通过Console口登录设备之前，需要完成以下任务： PC已安装终端仿真程序（如PuTTY.exe软件） 准备好串口配置电缆 如使用第三方的USB转RJ45的串口线缆，为保证线缆传输的信号质量的完整性， 建议线缆长度不大于两米。 建立物理连接


#### 1.2.3.1 建立物理连接

使用配置电缆将设备的Console口与终端COM口进行物理连接。


#### 1.2.3.2 登录设备

通过Console口从PC登录设备，实现对第一次上电的设备进行配置和管理。 背景信息 用户需要根据设备上的Console口的物理属性（包括传输速率、数据位、校验位、停止 位、流控方式），配置终端登录时的相关参数。由于是首次登录设备，终端属性的各 参数值均采用缺省值。 客户端已安装PuTTY.exe软件。 操作步骤 步骤1 打开PuTTY.exe程序，出现如图1-1所示客户端配置界面。选择Serial选项。


### 1.2.4 通过管理网口登录设备

设备第一次上电时，可以通过管理网口使用SSH方式登录。 背景信息 通过终端连接到网络上，如果网络安全性不高，SSH（Secure Shell）可提供安全的信 息保障和强大认证功能，保护设备系统不受IP欺骗等攻击。 缺省情况下，用户可以通 过管理网口直接登录设备。 客户端已安装PuTTY. exe软件。 设备上电后会自动将管理网口（GigabitEthernet0/0/0）绑定到保留VPN（保留VPN为 \\_\\_LOCAL\\_OAM\\_VPN\\_\\_），并为其配置固定IP地址192.

168. 0. 1/24。 用户可以为终端配置192. 168. 0. 0/24网段的其他IP，通过SSH方式登录设备，实现对设备的现场 维护。 如果设备在启动前已经连接DHCP服务器，管理网口的默认地址可能被覆盖或者丢失。 在设备上进行业务配置后，需要及时修改用户名和密码。 管理网口的IP可以修改和删除，并且根 据需要关闭该接口。 操作步骤 步骤1 配置SSH客户端。 打开PuTTY. exe程序，出现如图1-4所示客户端配置界面。

在“Host Name \\(or address\\)”文本框中输入SSH服务器的IP地址。


### 1.2.5 通过即插即用功能批量部署设备

设备首次上电时，还可以基于即插即用功能通过DCN或DHCP方式批量部署设备。 背景信息 在构建大型网络时，为了实现对设备的远程统一管理和控制，以提高部署效率和降低 运维成本，设备首次上电时还可以基于即插即用功能批量部署设备，主要通过以下两 种方式： DCN：设备上电后，所有网元由网元ID（即NEID）自动生成IP地址（即NEIP）， 并通过OSPF扩散LSA形成一张由NEIP和NEID组成的核心路由表。

网管通过网关网 元的IP地址和网关网元上送的目的网元ID进行访问，并通过网关网元远程对网络 中的所有网元进行管理。 DHCP：通过DHCP方式实现的即插即用功能主要表现为ZTP（Zero Touch Provisioning，零配置自动部署），它是一种空配置设备上电启动时可以从文件服 务器获取版本文件并自动加载的功能。 设备如果在没有加载带账号密码的默认配置文件的情况下上电启动，则会进入first-login流程 （即需要新创建用户并设置密码），导致通过即插即用功能上线失败。

在上述过程中，提示信息 和交互确认信息通过SSH标准协议中的SSH\\_MSG\\_CHANNEL\\_DATA字段在设备和网管间交互传 递。 报文结构的定义如下： byte SSH\\_MSG\\_CHANNEL\\_DATA uint32 recipient channel string data 操作步骤 步骤1 上电启动设备。 步骤2 设备根据实际状态进入相应的即插即用流程： 在实现DCN配置的情况下，网管与设备间建立通信网络，网管可以直接对网元进 行远程管理。 在1. 9.

3 配置通过DHCP实现ZTP自动部署的情况下，设备可以从文件服务器获取 版本文件并自动加载。 ----结束


## 1.3 命令行接口配置

用户对设备的日常操作，主要通过输入命令行来实现。用户可以在命令视图下编辑和 配置命令行，并显示配置成功或错误的信息。 命令行接口概述


### 1.3.1 命令行接口概述

命令行接口是用户对命令行操作的常用工具。 通过命令行接口输入命令，可以对路由 器进行配置和管理。 当用户执行需要输入密码的命令时，有些密码会以明文方式显示在屏幕上，为了防止 密码泄露，请用户及时清屏。 命令行接口 命令行接口CLI（Command Line Interface）是用户与路由器进行交互的常用工具。 用 户登录到路由器出现命令行提示符后，即进入命令行接口。 系统向用户提供一系列命 令，用户可以通过命令行接口输入命令，由命令行接口对命令进行解析，实现用户对 路由器的配置和管理。

CLI是一种传统的配置工具，大多数通信产品都提供了CLI功能。 随着通信产品在全球 范围的应用越来越广，用户对CLI的可用性、灵活性、界面友好性提出了更高的要求。 设备在切换为UP形态后，会自动建立cp-config视图。 视图内会保存CP下发到UP的模 板名，此模板内的数据不支持本地修改。 命令行接口有如下特性： 允许通过Console口进行本地配置。 允许通过Telnet、SSH进行本地或远程配置。 提供User-interface视图，为不同的终端用户提供个性化配置管理。

命令分级保护，不同级别的用户只能执行相应级别的命令。 用户可以随时键入“? ”来获得在线帮助。 提供网络测试命令，如tracert、ping等，迅速诊断网络是否正常。 提供种类丰富、内容详尽的调试信息，帮助诊断网络故障。 用telnet命令直接登录并管理其它路由器。 提供FTP服务，方便用户上传、下载文件。 可以执行某条历史命令。 命令行解释器提供不完全匹配和上下文关联等多种智能命令解析方法，方便用户 输入。


### 1.3.2 命令行接口配置注意事项

特性限制 搭建命令行运行环境


### 1.3.3 搭建命令行运行环境

使用命令行之前，可以将命令行的运行环境设置为习惯的界面。 应用环境 在使用命令行配置业务前，用户可以配置命令行的基本运行环境，以便符合实际环境 的要求。 前置任务 在搭建命令行运行环境之前，需要完成以下任务： 路由器安装完毕并加电启动正常。 已经通过客户端登录到路由器上。 配置用户登录警示信息


#### 1.3.3.1 配置用户登录警示信息

当连接到路由器时，会看到一段提示信息。 这段信息可以修改为希望显示的内容。 背景信息 登录警示信息是用户在连接到路由器之后，或者通过了登录验证之后、开始交互配置 之前系统显示的一段提示信息。 使用此配置为用户登录提供明确的指示信息。 设备重启后，如果用户在系统初始化过程中进行登录操作，由于文件系统还没有完成初始化，此 时会提示用户系统未获取到文件。 操作步骤 步骤1 执行命令system-view，进入系统视图。

步骤2 执行命令header login information text file file-name }，设置登录时的警示信 步骤3 执行命令header shell information text file file-name }，设置登录成功后的警示 信息。 步骤4 执行命令commit，提交配置。 ----结束 配置设备名称


#### 1.3.3.2 配置设备名称

设备名称会出现在命令提示符中，可以根据需要更改设备名称。


#### 1.3.3.3 设置命令级别

背景信息 如果用户没有对某条命令单独调整过命令级别，命令级别批量提升后，原注册的所有 命令行按以下原则自动调整： 0级和1级命令保持级别不变。 2级命令提升到10级，3级命令提升到15级。 2～9级和11～14级这些命令级别中没有命令行。 用户可以单独调整需要的命令行 到这些级别中，以实现权限的精细化管理。 不建议随意修改缺省的命令级别。 否则会影响其他用户对命令的使用。 操作步骤 步骤1 执行命令system-view，进入系统视图。

步骤2 执行命令command-privilege level rearrange，批量提升命令的级别。 步骤3 执行命令command-privilege level level view view-name command-key，设置指 定视图内命令的级别。 所有的命令都有默认的视图和优先级，一般不需要用户进行重新设置。 命令级别分为参观、监控、配置、管理4个级别，分别对应标识0、1、2、3，如表1-2 所示。

表1-2 命令级别简介 用户级 命令级 级别名 别（0 （0～ 15） 参观级 网络诊断工具命令（ping、tracert）、从本设 备出发访问外部设备的命令（Telnet客户端）


#### 1.3.3.4 锁定用户界面

为防止未授权的用户操作该终端界面，可以使用命令将用户界面锁住。 操作步骤 步骤1 执行命令configuration exclusive，锁定配置权限给当前操作用户。 锁定用户配置权限后，可以显式地获取独享的配置权限，其他用户无法再获取到配置 权限。 可以执行display configuration-occupied user命令，查看当前锁定配置集用户的信息。 步骤2 执行命令system-view，进入系统视图。

步骤3 执行命令configuration-occupied timeout timeout-value，设置自行解锁时间间 步骤4 执行命令commit，提交配置。 ----结束 允许在系统视图下执行用户视图命令


#### 1.3.3.5 允许在系统视图下执行用户视图命令

对于某些命令只能在用户视图下执行，当用户需要执行该类命令时，必须退出到用户 视图才能成功执行。为了便于用户执行用户视图命令，在不用切换视图的情况下，通 过本配置可实现在系统视图下执行用户视图命令。


#### 1.3.3.6 （可选）配置命令时间戳

需要记录命令行执行时间时，可执行如下步骤。 操作步骤 使能系统时间戳功能 执行命令system-view，进入系统视图。 执行命令timestamp enable，使能系统的时间戳功能。 使能系统的时间戳功能后，用户执行display查询命令时，系统会在显示信息 前打上执行该命令的时间。 执行命令commit，提交配置。 该功能仅对display查询命令生效。

使能当前会话的命令时间戳功能 执行命令terminal command timestamp，使能当前会话的命令时间戳功 使能该功能后，用户输入任意命令回车后都会显示执行时间。 该功能仅对当前会话生效，用户退出系统重新登录后，该功能失效，需重新配 如果用户执行命令undo terminal command timestamp关闭当前会话时间戳功 能，但是已执行timestamp enable命令使能系统的时间戳功能，则用户执行 display查询命令时依然会显示时间戳。 ----结束 如何使用命令行


### 1.3.4 如何使用命令行

命令行的使用包括对命令视图、命令行编辑功能、命令行模板、显示信息和错误信息 的配置和处理。 应用环境 在使用命令行配置业务前，用户需要了解命令行的基本操作。 前置任务 在使用命令行之前，需要完成以下任务： 路由器安装完毕并加电启动正常。


#### 1.3.4.1 进入命令视图

命令行接口分为若干个命令视图，所有命令都注册在某个（或某些）命令视图下。 通 常情况下，必须先进入命令所在的视图才能执行该命令。 两阶段生效模式下，当系统中有未提交的配置时，视图中以\\*标识，如果系统中没有未提交的配 置时，视图中以\~标识。 与路由器建立连接，如果此路由器是缺省配置，则进入用户视图，在屏幕上显示： <HUAWEI> 键入system-view后回车，进入系统视图。

<HUAWEI> system-view \\[\~HUAWEI\\] 在系统视图下键入aaa，则可进入AAA视图。 \\[\~HUAWEI\\] aaa \\[\~HUAWEI-aaa\\] 命令行提示符“HUAWEI”是缺省的主机名，此名称也可以用sysname指定。 通过提示符可以 判断当前所处的视图，例如：“<>”表示用户视图，“\\[\\]”表示除用户视图以外的其它视图。 多级视图中，用户可以直接执行\#，即可直接回到系统视图。 用户可以在任意视图中，执行！

或\#加字符串，此时的用户输入将全部（包括！ 和\#在内）作为 系统的注释行内容，不会产生对应的配置信息。 执行命令quit，可以从当前视图退回到上一级别的视图。 如果在用户视图下执行该命 令，则退出系统。 执行命令return，可以从当前视图退回到用户视图。 如果在用户视图下执行该命令， 仍然处于用户视图。 有些在系统视图下实现的命令，在其它视图下也可以实现，但实现的功能与命令视图 密切相关。

比如启动MPLS协议的命令mpls，在系统视图下执行表示启动全局MPLS能 力，在接口视图下执行表示启动当前接口的MPLS能力。 编辑命令行


#### 1.3.4.2 编辑命令行

命令行编辑功能可以帮助利用某些特定的键进行命令的编辑或者获得帮助。 NE40E的命令行接口提供基本的命令编辑功能，支持多行编辑，每条命令最大长度为 3100个字符。 一些常用的编辑功能如表1-3所示。 表1-3 编辑功能表 功能键 普通按键 若编辑缓冲区未满，则插入到当前光标位置，并向右移动 光标。


#### 1.3.4.3 正则表达式

背景信息 正则表达式描述了一种字符串匹配的模式，由普通字符（例如字符a到z）和特殊字符 （或称“元字符”）组成。 正则表达式作为一个模板，将某个字符模式与所搜索的字 符串进行匹配。 正则表达式一般具有以下功能： 检查字符串中符合某个规则的子字符串，并可以获取该子字符串。 根据匹配规则对字符串进行替换操作。 正则表达式由普通字符和特殊字符组成。 普通字符 普通字符匹配的对象是普通字符本身。 包括所有的大写和小写字母、数字、标点 符号以及一些特殊符号。 例如：a匹配abc中的a，@匹配xxx@xxx.

com中的@。 特殊字符 特殊字符配合普通字符匹配复杂或特殊的字符串组合。 如，^10匹配10. 10. 10. 1， 不匹配2. 2. 2. 2。 表1-4是对特殊字符及其语法意义的使用描述。


#### 1.3.4.4 查询配置信息

完成一组配置之后，可以查看当前生效的参数来验证配置是否正确。 背景信息 已完成基本配置。 操作步骤 执行命令display current-configuration configuration configuration-type configuration-instance config-type-no-inst all inactive include- default \\]，显示当前配置信息。 执行命令display this，显示当前视图的运行配置信息。

对于某些正在生效的配置参数，如果与缺省工作参数相同，则不显示；对于某些 参数，虽然用户已经配置，但如果这些参数所在的功能没有生效，则不予显示。 ----结束 查询诊断信息


#### 1.3.4.5 查询诊断信息

当系统出现故障时，如果难以判断产生故障的模块，可使用本命令收集信息，用于故 障定位。 操作步骤 步骤1 执行命令display diagnostic-information，查询当前系统的诊断信息。 本命令集合了多条常用display命令的输出信息，包括display clock、display version、display current-configuration等等，可以看作是对系统常用display命令的 批量执行。 ----结束 命令行结果显示


#### 1.3.4.6 命令行结果显示

所有的命令行有共同的显示特性，并且可以根据需求，灵活构建显示方式。 显示特性 在一次显示信息超过一屏时，提供暂停功能，在暂停显示时用户可以有三种选择，如 表1-5所示。


#### 1.3.4.7 命令行的错误信息

所有用户键入的命令，如果通过语法检查，则正确执行，否则系统将会向用户报告错 误信息。 常见错误信息参见表1-6。 表1-6 命令行常见错误信息表 英文错误信息 错误原因 Unrecognized command 没有查找到命令 没有查找到关键字 Wrong parameter 参数类型错 参数值越界 Incomplete command 输入命令不完整 Too many parameters 输入参数太多 Ambiguous command 输入命令不明确 命令行别名


#### 1.3.4.8 命令行别名

命令行别名功能可以将设备中的命令行别名为用户执行的字符串，方便用户使用。命 令行别名功能只能在人机模式下使用，机机模式下命令行别名功能不生效。 背景信息 用户可以通过命令terminal command alias打开当前会话的别名特性开关，也可以通 过命令undo terminal command alias关闭当前会话的别名特性开关。关闭当前会话 的别名特性，仅影响当前会话的别名配置功能，并不清除系统中存在的别名配置信


#### 1.3.4.9 命令行智能回退

命令行具有智能回退功能，即在当前视图下可执行其他视图下的命令。 背景信息 命令行支持智能回退，即在当前视图下执行某条命令，如果命令行匹配失败，会自动 退到上一级视图进行匹配，如果仍然失败则继续退到上一级视图匹配，直到退到系统 视图为止。 如果能够匹配到此命令，则会在当前视图下执行此命令并进入相应视图。 用户在配置业务时，为完成所需配置，需进入待配置命令所在视图，才能配置成功， 这样就需要重复执行quit命令退出当前视图，进入所需视图，给用户配置带来很多重复 工作。

智能回退功能，可以在当前视图下执行其他视图的命令，减少重复工作，为用 户配置提供便利。 如果在当前视图下由于模糊匹配发生歧义导致匹配失败时，不进行智能回退。 命令行匹配失败时不进行智能回退。 undo命令不支持智能回退。 任务示例 执行命令terminal command forward matched upper-view使能命令行智能回 退功能。 执行命令system-view，进入系统视图。 执行命令interface GigabitEthernet1/0/0.

1，创建子接口并进入子接口视图。 无需退出当前视图，可直接执行命令interface LoopBack 1，创建LoopBack接口 并进入LoopBack接口视图。


#### 1.3.4.10 启用二次认证功能

二次认证功能可以防止用户误操作造成的业务中断。 背景信息 设备上有些命令，如果用户误操作会关联删除相关特性的配置，导致业务中断，造成 用户网络中断。 为了防止误操作，用户可以启用二次认证功能。

当二次认证功能启用后，执行这些命令时，需要先输入登录密码进行二次认证后该命 令才能生效，命令范围包含：reboot、reset saved-configuration、rollback configuration、undo mpls、undo mpls te、undo mpls rsvp、undo mpls ldp、 undo mpls l2vpn、undo multicast ipv6 routing-enable、undo multicast routing-enable、undo pim、undo igmp、undo bgp、undo stp enable。

为防止误操作导致某些业务不可用，建议使能二次认证功能。 任务示例 执行命令system-view，进入系统视图。 执行命令configuration re-authentication enable，启用二次认证功能。 执行命令commit，提交配置。 如何获得命令帮助


### 1.3.5 如何获得命令帮助

输入命令行或进行配置业务时，命令帮助可以提供配置手册之外的实时帮助。 NE40E的命令行接口提供如下两种在线帮助。 完全帮助 命令行的完全帮助可以通过以下三种方式获取。 在任一命令视图下，键入“? ”获取该命令视图下所有的命令及其简单描述。 <HUAWEI> 键入一条命令，后接以空格分隔的“? ”，如果该位置为关键字，则列出全部关键 字及其简单描述。

举例如下： <HUAWEI> terminal debugging Enable/disable debug information terminal logging Enable/disable log information terminal 其中“debugging”、“logging”是关键字，后面语句是对关键字的描述。 键入一条命令，后接以空格分隔的“? ”，如果该位置为参数，则列出参数取值的 说明和参数作用的描述。 举例如下：


### 1.3.6 如何使用快捷键

可以通过使用系统快捷键或者自定义的快捷键，完成对应命令的输入，简化操作。 应用环境 在使用命令行配置业务时，如果某些命令经常使用，用户可以定义快捷键实现快速键 前置任务 在使用快捷键之前，需要完成以下任务： 路由器安装完毕并加电启动正常。 已经通过客户端登录到路由器上。 快捷键的分类


#### 1.3.6.1 快捷键的分类

在使用快捷键时，会接触到两类快捷键，自定义的快捷键和系统快捷键。了解了分类 后，可以更快速和准确的使用快捷键。 系统中的快捷键分成两类： 提供给用户、可以自由定义的快捷键：包括CTRL\\_G、CTRL\\_L、CTRL\\_O和 CTRL\\_U。用户可以根据自己的需要将这几个快捷键与任意命令进行关联，当使用 快捷键时，系统自动执行它所对应的命令。定义此类快捷键的方法请参见1.3.6.2 定义快捷键。


#### 1.3.6.2 定义快捷键

只有管理级用户有定义快捷键的权限。 操作步骤 步骤1 执行命令system-view，进入系统视图。 步骤2 执行命令hotkey CTRL\\_G CTRL\\_L CTRL\\_O CTRL\\_U command-text，定义快 捷键。 步骤3 执行命令commit，提交配置。 ----结束 查看快捷键配置列表


#### 1.3.6.3 查看快捷键配置列表

在任何允许输入命令的地方都可以键入快捷键，系统执行时，会将该快捷键对应的命 令显示在屏幕上，如同输入了完整的命令一样。 背景信息 如果用户已经输入了命令的一部分，但是还没有键入回车以确认，此时键入快捷键将 会把以前输入的字符全部清空，并将该快捷键对应的命令显示在屏幕上，效果与用户 删除所有的输入，然后重新敲入完整的命令一样。 快捷键的执行与命令一样，也会将命令原形记录在命令缓冲区和日志中以备问题定位 和查询。 操作步骤 步骤1 执行命令display hotkey，查看快捷键的配置列表。

快捷键的功能可能受用户所用的终端影响，例如用户终端本身自定义的快捷键与路由器系统中的 快捷键功能发生冲突，此时如果用户键入快捷键将会被终端程序截获而不能执行它所对应的命令 ----结束 配置会话日志功能开关


### 1.3.7 配置会话日志功能开关

全局会话日志功能默认关闭，单个连接的会话日志功能默认打开，用户可根据需要选 择执行以下步骤配置会话日志功能开关。


### 1.3.8 配置举例

通过配置举例来描述命令行的使用方法。


#### 1.3.8.1 使用Tab键示例

输入Tab键可以提示所有相关的关键字，或者检查关键字是否正确。 组网需求 网络中的任意一台路由器。 配置注意事项


#### 1.3.8.2 定义快捷键示例

如果登录的路由器上定义了快捷键，那么所有的用户均可以使用，不区分用户级别。 组网需求 网络中的任意一台路由器。 配置注意事项 如果用户没有快捷键里面定义的命令的使用权限，那么执行此快捷键后对应的命令不 生效。 配置思路 采用如下思路配置定义快捷键的示例： 定义快捷键CTRL\\_U，与命令display routing-table进行关联。 在提示符\\[\~HUAWEI\\]下键入快捷键“Ctrl+U”即显示。

数据准备 为完成此配置例，需准备如下的数据： 快捷键名 需要关联快捷键的命令名 操作步骤 步骤1 定义快捷键CTRL\\_U，与命令display routing-table进行关联，并执行。 <HUAWEI> system-view \\[\~HUAWEI\\] hotkey ctrl\\_u "display routing-table" \\[\\*HUAWEI\\] commit 步骤2 在提示符\\[\~HUAWEI\\]下键入快捷键“Ctrl+U”即显示。

\\[\~HUAWEI\\] display routing-table Route Flags: relay, download fib, vpn-instance, black hole route ------------------------------------------------------------------------------ Routing Table: Public Destinations Routes Destination/Mask Proto Pre Cost Flags NextHop Interface Direct InLoopBack0 Direct GigabitEthernet0/0/0 Direct InLoopBack0 Direct InLoopBack0 Direct InLoopBack0 Direct InLoopBack0 Direct InLoopBack0 Direct InLoopBack0 ----结束


## 1.4 用户界面配置

当用户通过Console口、Telnet或SSH方式登录路由器时，系统会分配相应的用户界 面，用来管理、监控设备和当前用户之间的会话。 用户界面概述


### 1.4.1 用户界面概述

系统支持的用户界面有Console用户界面和VTY用户界面。 在配置了用户界面、用户管理和终端服务后，用户才能登录到设备对本地或远端的网 络设备进行配置、监控和维护。 用户界面提供登录场所，用户管理确保登录安全，终 端服务则提供登录协议支持。 每个用户界面有对应的用户界面视图（User-interface view），在用户界面视图下网络 管理员可以配置一系列参数，如用户登录时是否需要认证、用户登录后的级别等。

当 用户使用该用户界面登录时，将受到这些参数限制，从而达到统一管理各种用户会话 连接的目的。 目前系统支持的用户界面： Console用户界面：用来管理和监控通过Console口登录的用户。 Console口端口类型为EIA/TIA-232 DCE。 VTY（Virtual Type Terminal，虚拟类型终端）用户界面：用来管理和监控通过 VTY方式登录的用户。

VTY口用于对设备进行Telnet或SSH访问，最多支持21个用户同时通过VTY方式访 同一用户登录的方式不同，分配的用户界面不同；同一用户登录的时间不同，分配的用户界面也 可能不同。 用户界面的编号 用户登录设备时，系统会根据此用户的登录方式自动分配一个当前空闲且编号最小的 相应类型的用户界面，整个登录过程将受到该用户界面视图下的配置约束。 用户界面的编号有两种方式：相对编号方式和绝对编号方式。 相对编号方式 相对编号方式只能唯一指定某种类型的用户界面中的一个或一组，而不能跨类型 操作。

相对编号方式的形式是：用户界面类型＋编号，遵守的规则如下： Console口的编号：CON VTY的编号：第一个为VTY 0，第二个为VTY 1，依此类推。 绝对编号方式 绝对编号方式可以唯一的指定一个用户界面或一组用户界面。 绝对编号的起始编 号是0，每次增长1，并按照Console、VTY的顺序依次分配。


### 1.4.2 用户界面配置注意事项

特性限制


### 1.4.3 配置Console用户界面

Console用户界面用来管理和监控通过Console口登录的用户。 应用环境 当用户需要通过Console口登录设备对设备进行本地维护时，需要配置相应的Console 用户界面，包括Console用户界面的物理属性、终端属性、用户级别以及用户验证方式 等。用户可以根据使用需求以及出于对设备安全性的考虑配置相应的参数。 前置任务 在配置Console用户界面之前，需要完成以下任务： 成功通过Console口登录路由器


#### 1.4.3.1 配置Console用户界面的物理属性

Console用户界面的物理属性包括Console口的传输速率、流控方式、校验位、停止位 和数据位。


#### 1.4.3.2 配置Console用户界面的终端属性

Console用户界面的终端属性包括用户超时断连功能、终端屏幕的显示行数或列数以及 历史命令缓冲区大小。 操作步骤 步骤1 执行命令system-view，进入系统视图。 步骤2 执行命令user-interface ui-type console first-ui-number }，进入Console口用户 界面视图。 步骤3 执行命令shell，启动终端服务。 步骤4 执行命令idle-timeout minutes seconds \\]，设置用户超时断连功能。

在设定的时间内，如果连接始终处于空闲状态，系统将自动断开该连接。 步骤5 执行命令screen-length screen-length，设置终端屏幕每屏显示的行数。 用户可以通过执行命令screen-length temporary，设置终端屏幕的临时显示行数。 步骤6 执行命令history-command max-size size-value，设置历史命令缓冲区大小。


#### 1.4.3.3 配置Console用户界面的用户级别

为了限制不同用户对设备的访问权限，可配置Console用户界面的用户级别，对用户进 行分级管理，从而增加设备管理的安全性。 背景信息 用户的级别与命令级别对应，不同级别的用户登录后，只能使用等于或低于自己级别 的命令。 操作步骤 步骤1 执行命令system-view，进入系统视图。 步骤2 执行命令user-interface ui-type console first-ui-number }，进入Console口用户 界面视图。

步骤3 执行命令user privilege level level，设置用户级别。 如果用户界面下配置的命令级别访问权限与用户名本身对应的操作权限冲突，以用户名本身对应 的级别为准。 例如：用户001对应3级命令，但是在Console 0用户界面上为用户001配置的命令级别是2。 当用 户001通过Console 0登录系统时，能访问3级或3级以下的命令。 步骤4 执行命令commit，提交配置。 ----结束


#### 1.4.3.4 配置Console用户界面的用户验证

系统提供AAA验证和密码验证方式，配置用户验证方式可以提高设备的安全性。 背景信息 password验证方式不安全，强烈建议用户使用aaa验证方式。 操作步骤 设置AAA验证 执行命令system-view，进入系统视图。 执行命令user-interface ui-type console first-ui-number }，进入 Console口用户界面视图。 执行命令authentication-mode aaa，设置用户验证方式为AAA验证。 执行命令quit，退出Console用户界面视图。 执行命令aaa，进入AAA视图。


#### 1.4.3.5 配置Console用户界面去使能

当Console用户界面出现异常或者因其它原因需要暂停时，可以将其去使能。 操作步骤 步骤1 执行命令system-view，进入系统视图。 步骤2 执行命令user-interface ui-type console first-ui-number }，进入Console口用户 界面视图。 步骤3 执行命令shutdown，去使能Console用户界面。 步骤4 执行命令commit，提交配置。 ----结束 检查配置结果


#### 1.4.3.6 检查配置结果

Console用户界面配置成功后，可以查看到用户界面的使用信息、物理属性和配置、本 地用户列表和在线用户等信息。 前提条件 已完成Console用户界面的相关配置。 操作步骤 使用display users all \\]命令，查看用户界面的用户登录信息。 使用display user-interface console 0命令，查看用户界面信息。 VS模式下，该命令仅在Admin VS支持。


### 1.4.4 配置VTY用户界面

VTY用户界面用来管理和监控通过VTY方式登录的用户。 应用环境 当用户需要通过Telnet或SSH方式进行本地或远程配置和管理设备时可以配置相应的 VTY用户界面，包括VTY用户界面的最大个数、呼入呼出限制、终端属性、用户级别以 及用户验证方式等。用户可以根据使用需求以及出于对设备安全性的考虑配置相应的 参数。 前置任务 在配置VTY用户界面之前，需要完成以下任务： 成功通过Console口登录路由器。


#### 1.4.4.1 配置VTY用户界面的最大个数

用户可以配置同时登录设备的VTY类型用户界面的最大个数，实现对登录用户量的限 背景信息 VTY用户界面的最大个数是当前登录设备的Telnet用户和SSH用户的总和。 当配置VTY用户界面最大个数为0时，任何用户（包括网管用户）都无法通过VTY登录 设备。 操作步骤 步骤1 执行命令system-view，进入系统视图。 步骤2 执行命令user-interface maximum-vty number，配置可以同时登录到设备的VTY类 型用户界面的最大个数。

如果配置的VTY用户接口的最大数小于当前的最大数量，不会影响当前在线用 户，也不需要其它配置。 如果配置的VTY用户接口的最大数量大于当前的最大数量，需要为新增加的用户 接口配置验证方式和密码。 因为对于新增用户接口系统默认为使用密码验证。

例如：当前允许最多5个VTY用户同时在线，现在配置允许18个VTY用户同时在 线，那么VTY用户接口5～17就需要使用authentication-mode命令配置验证方式 和密码，配置如下： <HUAWEI> system-view \\[\~HUAWEI\\] user-interface maximum-vty


#### ******* 配置VTY用户界面的呼入呼出限制

用户可以通过访问控制列表（ACL），实现对VTY用户界面的呼入呼出进行限制。 背景信息 确定了对Telnet的控制策略，包括对哪些源IP、目的IP进行控制，控制的动作是允许访 问还是拒绝访问。 通过基本ACL（2000～2999）控制列表实现通过源IP地址对Telnet进行限制。 通过高级ACL（3000～3999）控制列表实现通过源IP地址和目的IP地址对Telnet进 行限制。 操作步骤 步骤1 执行命令system-view，进入系统视图。

步骤2 相比基本ACL，高级ACL支持的过滤规则更加丰富。 除了像基本ACL，可以针对报文源 地址进行过滤，高级ACL还可以针对报文目的地址、报文优先级进行过滤。

请根据情况 不同，执行如下命令： 对于基本ACL： 执行命令acl name basic-acl-name basic basic number basic-acl- number number basic-acl-number match-order config auto acl ipv6 name basic-acl6-name basic number basic-acl6-number match-order config auto \\]，进入ACL或ACL6视图。

对于高级ACL： 执行命令acl name advance-acl-name advance advance number advance-acl-number number advance-acl-number match-order config auto \\]或acl ipv6 name advance-acl6-name advance


#### ******* 配置VTY用户界面的终端属性

VTY用户界面的终端属性包括用户超时断连功能、终端屏幕的显示行数以及历史命令缓 冲区的大小。 操作步骤 步骤1 执行命令system-view，进入系统视图。 步骤2 执行命令user-interface vty first-ui-number last-ui-number \\]，进入VTY用户界面 视图。 步骤3 执行命令shell，启用VTY终端服务。 步骤4 执行命令idle-timeout minutes seconds \\]，设置用户超时断连功能。

在设定的时间内，如果连接始终处于空闲状态，系统将自动断开该连接。 步骤5 执行命令screen-length screen-length，设置终端屏幕每屏显示的行数。 步骤6 执行命令history-command max-size size-value，设置历史命令缓冲区的大小。 步骤7 执行命令commit，提交配置。 ----结束


#### ******* 配置VTY用户界面的用户级别

为了限制不同用户对设备的访问权限，可配置VTY用户界面的用户级别，对用户进行分 级管理，从而增加设备管理的安全性。 背景信息 用户的级别与命令级别对应，不同级别的用户登录后，只能使用等于或低于自己级别 的命令。 操作步骤 步骤1 执行命令system-view，进入系统视图。 步骤2 执行命令user-interface vty first-ui-number last-ui-number \\]，进入VTY用户界面 视图。

步骤3 执行命令user privilege level level，设置用户级别。 如果用户界面下配置的命令级别访问权限与用户名本身对应的操作权限冲突，以用户名本身对应 的级别为准。 例如：用户001对应3级命令，但是在VTY 0用户界面上为用户001配置的命令级别是2。 当用户 001通过VTY 0登录系统时，能访问3级或3级以下的命令。 步骤4 执行命令commit，提交配置。 ----结束


#### 1.4.4.5 配置VTY用户界面的用户验证

系统提供AAA验证和密码验证方式，配置用户验证方式可以提高设备的安全性。 背景信息 password验证方式不安全，强烈建议用户使用aaa验证方式。 操作步骤 设置AAA验证 执行命令system-view，进入系统视图。 执行命令user-interface vty first-ui-number last-ui-number \\]，进入VTY 用户界面视图。 执行命令authentication-mode aaa，设置用户验证方式为AAA验证。 执行命令commit，提交配置。

执行命令quit，退出VTY用户界面视图。 执行命令aaa，进入AAA视图。 执行命令local-user user-name password cipher password irreversible-cipher irreversible-cipher-password \\]，配置本地用户名和密


#### ******* 配置可用VTY通道数的超限告警阈值

通过配置可用VTY通道数的超限告警阈值，用户可以及时了解设备上可用VTY通道的情 背景信息 当设备上可用的VTY数目小于等于设定的阈值时，设备将上报告警。 操作步骤 步骤1 执行命令system-view，进入系统视图。 步骤2 执行命令user-interface vty available-vty-threshold threshold-value，配置可用 VTY通道数的超限告警阈值。 如果配置VTY上报告警的阈值大于当前最大VTY用户数，系统将提示配置无效。 步骤3 执行命令commit，提交配置。 ----结束


#### ******* 使能VTY用户界面的安全策略

去使能VTY用户界面的安全策略会降低VTY用户界面的安全性，强烈建议用户使能VTY 用户界面的安全策略。


#### ******* 检查配置结果

VTY用户界面配置成功后，可以查看到用户界面的使用信息、VTY类型用户界面的最大 个数以及物理属性和配置等信息。 前提条件 已完成VTY用户界面的所有配置。 操作步骤 使用display users all \\]命令，查看用户界面的用户登录信息。 使用display user-interface maximum-vty命令，查看VTY类型用户界面的最大 个数。 使用display user-interface vty ui-number命令，查看物理属性和用户界面配置 信息。

使用display local-user命令，查看本地用户的属性信息。 使用display access-user命令，查看通过AAA认证的用户信息。 使用display vty mode命令，查看VTY模式。 ----结束 配置举例


### 1.4.5 配置举例

配置Console用户界面、VTY用户界面的示例，配置示例中包括组网需求、配置注意事 项和配置思路等。


#### 1.4.5.1 配置Console用户界面示例

在本示例中，通过配置Console用户界面的物理属性、终端属性、用户级别、验证方式 和验证密码，实现通过Console口使用Password方式登录设备。 组网需求 在初始化空配置设备或本地维护设备时，用户需要通过Console用户界面登录并进行配 置。设备管理员可以根据使用需求或对设备安全性的考虑，可配置Console用户界面的 相关属性。


#### 1.4.5.2 配置VTY用户界面示例

在本示例中，通过配置VTY用户界面的最大个数、呼入呼出限制、终端属性、用户级 别、验证方式和验证密码，实现通过Telnet或SSH（Stelnet）方式使用AAA验证登录设 组网需求 当用户需要通过Telnet或SSH方式进行本地或远程配置和管理设备时可以配置相应的 VTY用户界面，包括VTY用户界面的最大个数、呼入呼出限制、终端属性、用户级别以


## 1.5 用户登录配置

用户可以通过Console口、Telnet或SSH（STelnet）方式登录设备，实现对设备的本地 或远程维护。


### 1.5.1 用户登录概述

用户只有成功登录到设备，才能实现对设备的管理与维护。 用户登录设备的方式有： Console口、Telnet或STelnet。 配置了用户界面、用户管理和终端服务后，用户才能登录到设备，对本地或远端的设 备进行配置、监控和维护。 用户界面提供登录入口，用户管理确保登录安全，终端服务则提供登录协议支持。 用户可通过如表1-10所示几种方式登录设备，对设备进行配置和管理。

表1-10 用户登录方式 登录方式 应用场景 配置用户通过 使用终端通过连接设备的 缺省情况下，用户可以直接通 Console口登录系 Console口登录设备，进行第 过Console口本地登录设备。 一次上电和配置。 当用户无法进行远程访问 如果用户登录的是备板Console 口，此时用户只有查询权限，无 设备时，可通过Console进 配置管理权限。 行本地登录。 当设备系统无法启动时， 可通过Console口进行诊断 或进入BootRom进行系统 升级。


### 1.5.2 登录设备命令行界面配置注意事项

特性限制 表1-11 本特性的使用限制 特性限制 涉及产品 Telnet源地址只能配置主IP，不能配置虚IP。若有场景 NE40E NE40E- 需要将报文按照虚IP来进行限制，可以采用配置源地址 X16C/ +ACL的方式来进行限制。 NE40E- X8C/ NE40E- X8A/ NE40E- X3A/ NE40E- X16A/ NetEngine 40E-X8AK


### 1.5.3 配置用户通过Console口登录系统

当用户需要配置第一次上电的设备或在本地维护设备时，可以通过Console口登录。 应用环境 用户可以在本地通过Console口登录设备，尤其是设备第一次上电时只能采用此方式登 当用户无法进行远程访问设备时，可通过Console进行本地登录。 当设备系统无法启动时，可通过Console口进行诊断或进入BootRom进行系统升 前置任务 在配置用户通过Console口登录系统之前，需要完成以下任务： PC已安装终端仿真程序（如PuTTY.exe软件） 准备好串口配置电缆


#### ******* 用户通过Console口登录系统

用户通过Console口连接终端与设备，实现从用户终端登录设备。


#### ******* （可选）配置Console用户界面

当用户通过Console口登录设备实现本地维护时，可以根据使用需求或对设备安全的考 虑，配置相应的Console用户界面属性。 背景信息 Console用户界面的属性在设备上都有缺省值，用户一般不需要另外配置。 但是用户可 以根据使用需求以及对设备安全的考虑，配置相关属性，比如用户界面的终端属性以 及用户验证方式等。 如需配置Console用户界面，请参见配置Console用户界面。

改变Console用户界面属性后会立即生效，所以通过Console口登录设备后配置Console用户界面 属性可能在配置过程中发生连接中断，建议通过其他登录方式配置Console用户界面属性。 若用 户需要通过Console口再次登录设备，需要改变PC机上运行的终端仿真程序的相应配置，使之与 设备上配置的Console用户界面属性保持一致。 检查配置结果


#### 1.5.3.3 检查配置结果

用户通过Console口登录系统配置成功后，可以查看到用户界面的使用信息、物理属性 和配置、本地用户列表和在线用户等内容。 前提条件 已完成用户通过Console口登录系统的相关配置。 操作步骤 使用display users all \\]命令，查看用户界面的用户登录信息。 使用display user-interface console 0命令，查看用户界面信息。


### 1.5.4 配置用户通过Telnet登录系统

Telnet可使用户远程登录设备进行管理与维护。 应用环境 如果网络中有一台或多台设备需要配置和管理，用户无需为每一台设备连接用户终端 进行本地维护。 如果已知待登录设备的IP地址，且非首次登录设备，用户可以通过 Telnet方式从用户终端登录设备，对设备进行远程配置。 用户可以通过此方式在一台用 户终端上维护网络中的多台设备，极大地方便了用户的操作。 设备的IP地址需要通过Console口预先配置。 此协议不安全，建议使用STelnet。

前置任务 在配置用户通过Telnet登录设备之前，必须通过Console口登录设备，更改设备的缺省 配置，以便用户能够通过Telnet方式远程登录设备并实现管理和维护。 更改的缺省配置 如下： 配置设备管理网口的IP地址，确保终端和登录的设备之间路由可达。 配置VTY用户界面的用户级别和验证方式，实现远程管理和维护设备。 使能Telnet服务器功能，以便用户能够通过Telnet方式远程登录设备。


#### ******* 配置VTY用户界面的用户级别和验证方式

为了实现通过Telnet方式远程登录设备维护和管理设备，必须先通过Console口登录设 备，更改用户级别和配置用户验证方式。 背景信息 VTY用户界面的其他属性在设备上都有缺省值，用户一般不需要另外配置。 但是可以根 据用户使用需求，配置相关属性。 具体配置请参见配置VTY用户界面。 操作步骤 配置VTY用户界面的用户级别 执行命令system-view，进入系统视图。

执行命令user-interface vty first-ui-number last-ui-number \\]，进入VTY 用户界面视图。 执行命令user privilege level level，设置用户级别，VTY用户界面的用户级 别和命令级别对应关系如表1-12所示。


#### ******* 使能Telnet服务器功能

用户终端与设备建立Telnet连接之前，建议通过Console口登录设备，开启设备的 Telnet服务器功能，以便用户可通过Telnet方式远程登录设备。 背景信息 请在作为Telnet服务器的设备上，基于不同的网络协议，以下步骤请任选其一执行： 操作步骤 基于IPv4 执行命令system-view，进入系统视图。 执行命令telnet server enable，启动Telnet服务器。 执行命令commit，提交配置。 基于IPv6 执行命令system-view，进入系统视图。

执行命令telnet ipv6 server enable，启动Telnet服务器。 执行命令commit，提交配置。 当关闭Telnet服务器时，现有的Telnet连接不会中断，但是新的连接将不被允许，只能通过 SSH或Console口等其他方式登录设备。 ----结束


#### ******* 用户通过终端Telnet登录到系统

当通过Console口登录设备完成相关配置后，用户可以使用Telnet协议从终端登录到远 端设备，实现对设备的远程维护。 背景信息 从终端通过Telnet访问系统，可以选择使用Windows命令行提示符或第三方软件。 此 处以Windows命令行提示符为例进行演示。 请在PC上进行以下配置。 使用VTY通道登录设备时，为防止VTY连接数量超限导致用户无法接入，用户可执行如下操作查 看并配置允许通过VTY通道登录的用户数。

执行命令display user-interface maximum-vty查看设备允许通过VTY连接登录设备的最大 用户数。 执行命令display user-interface查看用户界面信息。 其中，“+”表示被占用的通道。 如果 通道被占满，会导致后续用户无法登录，如果已登录用户登出后，可能会因为通道被其他用 户占用而无法再次登录成功。 此时，可执行命令user-interface maximum-vty number设 置允许登录的最大用户数目。

操作步骤 步骤1 进入Windows的命令行提示符。 步骤2 执行Windows命令telnet ip-address，通过Telnet方式登录设备。 键入Telnet服务器的IP地址。 按Enter键，出现用户视图的命令行提示符，如<HUAWEI>；至此用户登录到了 Telnet服务器。 ----结束 后续处理 当网络管理员需要将某个登录用户与设备连接断开时，可通过命令kill user-interface ui-number ui-type ui-number1 }清除在线用户。

Console参数仅在Admin-VS支持。


#### ******* 配置Telnet服务器参数

合理的配置Telnet服务器参数可提高系统的安全性。目前，Telnet服务器参数包括侦听 端口号和指定源接口。 背景信息 侦听端口号 如果使用标准的侦听端口号，可能会有攻击者不断访问此端口，导致带宽和服务 器性能的下降，造成其他正常用户无法访问。所以可以重新配置Telnet服务器的侦 听端口号，攻击者不知道Telnet侦听端口号的更改，有效防止了攻击者对Telnet服 务标准端口的访问。 源接口


#### ******* （可选）配置Telnet协议的白名单Session-CAR

通过配置Telnet协议的白名单Session-CAR，可以区分会话限速。 避免发生Telnet Server报文发生流量冲击时，Telnet Server会话间报文互相抢占带宽的情况。 背景信息 当Telnet Server报文发生流量攻击时，Telnet Server会话间报文会发生互相抢占带宽 的情况。 Telnet协议的白名单Session-CAR用于对白名单报文通道进行隔离，实现区分 会话限速，避免Telnet Server会话间报文互相抢占带宽资源。

当默认的白名单Session- CAR的带宽参数不满足业务要求时，可以对带宽参数进行调整，灵活适应业务要求。 操作步骤 步骤1 执行命令system-view，进入系统视图。 步骤2 执行命令whitelist session-car telnet-server cir cir-value cbs cbs-value pir pir- value pbs pbs-value }\\*，配置Telnet协议的白名单Session-CAR的带宽参数。

步骤3 （可选）执行命令whitelist session-car telnet-server disable，去使能Telnet协议的 白名单Session-CAR功能。 一般情况下不建议关闭该功能。 步骤4 执行命令commit，提交配置。 ----结束


#### ******* （可选）配置Telnet访问控制

用户可以配置Telnet访问控制列表，实现只允许指定的客户端登录到设备。 背景信息 当设备作为Telnet服务器时，为提高安全性，可通过配置ACL实现只允许满足匹配条件 的客户端访问服务器。 请在作为Telnet服务器的设备上进行如下的配置。


#### ******* （可选）使能Telnet连接的IP地址阻止功能

Telnet连接的IP地址阻止功能可以阻止认证失败的IP地址通过Telnet登录设备，去使能 状态下，设备容易在安全方面遭受网络攻击。 背景信息 当设备作为Telnet服务器时，为提高安全性，可通过配置IP地址阻止功能防止设备遭受 网络攻击。 请在作为Telnet服务器的设备上进行如下的配置。 操作步骤 步骤1 执行命令system-view，进入系统视图 步骤2 执行命令undo telnet server ip-block disable，使能Telnet连接的IP地址阻止功能。

步骤3 执行命令commit，提交配置。 只有当IP地址阻止功能处于使能状态时，认证失败的IP地址才能被阻止，从而提升设备安全性。 步骤4 认证失败的IP地址将会被锁定5分钟，如果需要对被锁定的IP地址提前解除阻止，可执 行如下步骤： 执行命令quit，退出系统视图，进入用户视图。 执行命令activate vty ip-block ip-address ip-address vpnname vpn- name \\]，解除对Telnet连接中认证失败的IP地址的阻止。 ----结束


#### ******* （可选）配置通过Telnet登录服务器失败次数的告警门限

用户配置在一定时间内通过Telnet登录服务器的告警上报门限和告警恢复门限，可以对 短期内频繁的用户登录失败进行管理。 背景信息 通过产生告警，及时显示给高级管理员，提醒其介入处理。 请在作为Telnet服务器的设备上进行如下的配置。 操作步骤 步骤1 执行命令system-view，进入系统视图。

步骤2 执行命令telnet server login-failed threshold-alarm upper-limit report-times lower-limit resume-times period period-time，配置在一定时间内通过Telnet登录服 务器失败次数的告警上报门限和告警恢复门限。 上报告警门限值report-times必须大于等于取消告警门限值resume-times。 步骤3 执行命令commit，提交配置。 ----结束


#### ******* （可选）配置单个IP地址通过Telnet登录服务器的最大连接数

用户通过配置单个IP地址使用Telnet服务的最大连接数，可以防止单个IP恶意攻击占用 服务器连接数导致其他IP无法登录。 背景信息 当设备作为Telnet服务器时，为提高安全性，可通过配置单个IP地址的最大连接数防止 恶意攻击。 请在作为Telnet服务器的设备上进行如下的配置。 操作步骤 步骤1 执行命令system-view，进入系统视图。 步骤2 执行命令telnet server ip-limit-session limit-session-num，配置单个IP地址通过 Telnet登录到服务器的最大连接数。 步骤3 执行命令commit，提交配置。 ----结束 检查配置结果


#### *******0 检查配置结果

用户通过Telnet登录系统配置成功后，可以查看到当前用户界面连接情况、每个用户界 面连接情况、以及当前建立的所有TCP连接情况等内容。


### 1.5.5 配置用户通过STelnet登录系统

STelnet基于SSH2协议，在不安全网络上提供安全的远程访问。 应用环境 网络中有大量设备需要管理与维护，用户不可能为每台设备连接用户终端，特别是终 端与需要管理的设备之间无可达路由时，用户可以使用Telnet方式从当前设备登录到网 络上另一台设备，从而实现对远程设备的管理与维护。 但是Telnet缺少安全的认证方 式，而且传输过程采用TCP进行简单方式传输，存在很大的安全隐患。 STelnet是一种安全的Telnet服务，建立在SSH连接的基础之上。

SSH可以利用加密和强 大的认证功能提供安全保障，保护设备不受诸如IP地址欺诈等攻击。 SSH客户端使用Stelnet方式登录设备时，如果使用的认证、加密、密钥交换算法为弱 安全算法，设备将提示用户这些算法不安全，建议用户使用更安全的算法或升级客户 用户可执行命令display ssh client session查看SSH客户端使用的认证加密算法，或者 执行命令display security risk feature ssh\\_client查看SSH客户端存在的风险信息及 修复建议。


#### ******* 配置VTY用户界面的用户级别和验证方式

背景信息 VTY用户界面的其他属性在设备上都有缺省值，用户一般不需要另外配置。 但是可以根 据用户使用需求，配置相关属性。 具体配置请参见配置VTY用户界面。 操作步骤 配置VTY用户界面的用户级别 执行命令system-view，进入系统视图。 执行命令user-interface vty first-ui-number last-ui-number \\]，进入VTY 用户界面视图。

执行命令user privilege level level，设置用户级别，VTY用户界面的用户级 别和命令级别对应关系如表1-13所示。 当存在command-privilege level rearrange配置时，level取值范围是0～15； 当不存在command-privilege level rearrange配置时，level取值范围是0～3。

表1-13 用户级别与命令级别对应关系 （0～ 15） 网络诊断工具命令（ping、tracert）、从 本设备出发访问外部设备的命令（Telnet 客户端）等。 1～9 0、1 用于系统维护，包括display等命令。 并不是所有display命令都是监控级，比如管 理配置文件中的display current- configuration命令是3级管理级。 各命令的 级别请参见《HUAWEI NetEngine40E-命令参 考》手册。


#### 1.5.5.2 配置VTY用户界面支持SSH协议

STelnet基于SSH2协议，客户端和服务器之间经过协商，建立安全连接，客户端可以像 操作Telnet一样登录服务器。 背景信息 请在作为SSH服务器的设备上进行如下的配置。 操作步骤 步骤1 执行命令system-view，进入系统视图。 步骤2 执行命令user-interface vty first-ui-number last-ui-number \\]，进入VTY用户界面 视图。 步骤3 执行命令authentication-mode aaa，设置验证方式为AAA验证。

步骤4 执行命令protocol inbound ssh all }，配置VTY支持SSH协议。 如果配置用户界面支持的协议是SSH，必须设置VTY用户界面验证方式为AAA验证，否则 protocol inbound ssh all }将不能配置成功。 步骤5 执行命令commit，提交配置。 ----结束


#### 1.5.5.3 配置SSH用户和认证方式

通过STelnet方式登录设备时，必需配置SSH用户、创建本地密钥对、设置用户验证方 式以及指定SSH用户的服务方式。 背景信息 SSH用户支持RSA、DSA、ECC、SM2、x509v3-ssh-rsa、x509v3-ecdsa-sha2、 password、password-rsa、password-ecc、password-dsa、password-sm2、 password-x509v3-rsa、password-x509v3-ecdsa-sha2和all认证方式。

如果SSH用户使用RSA、DSA、SM2、ECC认证，则在服务端和客户端都需要生成本地 RSA、DSA、SM2、ECC密钥对。 并且服务端要将客户端的公钥配置到本地并进行编 辑，编辑后公钥和本地用户绑定。 RSA、DSA、SM2、x509v3-ssh-rsa与ECC密钥算法的比较如表1-14所示。

表1-14 RSA/DSA，SM2、x509v3-ssh-rsa和ECC密钥算法比较 密钥算法 应用场景 RSA/DSA 是一种公开密钥加密体系，是一种非对称加密算法，通过这种方法可以 有效地提高加密的效率并能简化对密钥的管理。 服务器必须检查用户是 否是合法的SSH用户，检查公钥对于该用户是否合法，用户数字签名是 否合法。 若三者同时满足，则身份认证通过；若其中任何一项不能验证 通过均告验证失败，拒绝该用户的登录请求。 ECC ECC和RSA、DSA算法相同，同属于非对称密码算法。

但是相比RSA、 DSA算法，有如下优势： 相同的安全性，ECC算法的密钥长度比RSA算法更短。 计算量小，处理速度快。 存储空间小。 带宽要求低。 SM2 SM2是非对称密码算法，是基于ECC算法的非对称算法。 x509v3- x509v3-ssh-rsa、x509v3-ecdsa-sha2算法是基于PKI证书的，需要用户 ssh-rsa、 和服务端都绑定相应的PKI证书，扩展性更好，安全性更高。 x509v3- ecdsa- sha2 各种认证方式的应用场景如表1-15所示。


#### 1.5.5.4 使能STelnet服务器功能

背景信息 为了保证更好的安全性，建议不要使用小于3072位的RSA算法，建议您使用更安全的 RSA\\_SHA2\\_256、RSA\\_SHA2\\_512认证算法。 操作步骤 步骤1 执行命令system-view，进入系统视图。

步骤2 （可选）执行命令ssh server publickey dsa ecc rsa sm2 x509v3-ssh-rsa\| rsa-sha2-256 rsa-sha2-512 x509v3-rsa2048-sha256 x509v3-ecdsa-sha2} 配置SSH服务器允许使用的公钥加密算法。 命令中的参数dsa和rsa为弱安全算法，不建议使用，如果确实需要使用，请先执行undo crypto weak-algorithm disable命令使能弱安全算法功能。

步骤3 （可选）创建密钥对的最大数。 请根据使用的密钥实际情况选择如下操作之一。 执行命令rsa key-pair maximum max-keys，配置允许创建的RSA密钥对的最大 执行命令dsa key-pair maximum max-keys，配置允许创建的DSA密钥对的最大 执行命令ecc key-pair maximum max-keys，配置允许创建的ECC密钥对的最大


#### 1.5.5.5 用户通过终端STelnet到服务器

当通过Console口登录设备完成相关配置后，用户可以使用SSH协议从终端登录到设 备，实现对设备的远程维护。 背景信息 从终端（SSH Client）通过STelnet方式登录服务器，如图1-14所示，PC终端上运行支 持SSH1.5及以上版本的客户端软件，与设备建立本地连接。 这里以PuTTY为例介绍SSH客户端通过STelnet方式登录服务器的方法。


#### 1.5.5.6 配置STelnet服务器参数

用户可调整合理的服务器参数，保证服务器的可靠性。 服务器参数包括服务器密钥对 更新时间、SSH认证超时时间、SSH验证重试次数、兼容低版本SSH协议，以及SSH服 务器侦听端口号等。 操作步骤 步骤1 执行命令system-view，进入系统视图。 步骤2 配置SSH服务器参数，根据需要，可执行如表1-19中的一个或多个操作。

表1-19 服务器参数 服务器参数 （可选）配置 执行命令ssh server rekey- 配置服务器密钥对更新时间，使 密钥对更新时 interval interval 得当SSH服务器的更新周期到达 时，自动更新服务器密钥对，从 而可以保证安全性。 （可选）配置 执行命令ssh server timeout 当设置的SSH认证超时时间到达 SSH认证超时 seconds 后，如果用户还未登录成功，则 终止当前连接，确保了安全性。

（可选）配置 执行命令ssh server 配置SSH验证重试次数用来设置 SSH验证重试 authentication-retries times SSH用户请求连接的认证重试次 数，防止非法用户登录。

（可选）使能 执行命令undo ssh server ip- 如果SSH服务器上的客户端IP地 SSH服务器上 block disable 址锁定功能处于使能状态，则被 的客户端IP地 锁定的客户端IP地址不能被认证 址锁定功能 通过，同时会在display ssh server ip-block list命令回显中 显示被锁定的客户端IP地址。

如果SSH服务器上的客户端IP地 址锁定功能去使能，则display ssh server ip-block list命令回 显中会把先前锁定的客户端IP地 址记录删除，新的认证失败的客 户端IP地址也不会被记录显示。 （可选）使能 执行命令ssh server SSH协议有SSH1. X（SSH2. 0之前 兼容低版本 compatible-ssh1x enable 的版本）和SSH2. 0版本。 SSH协议 SSH2. 0协议相比SSH1. X协议来 如果需要允许SSH1.

5的客户端登 说，在结构上做了扩展，可以支 录，则执行ssh server 持更多的认证方法和密钥交换方 compatible-ssh1x enable，使 法，同时提高了服务能力（如 能兼容低版本功能。 SFTP）。 如果SSH协议使能兼容低版本功 能，系统会提示存在安全风险。


#### ******* （可选）配置SSH协议的白名单Session-CAR

通过配置SSH协议的白名单Session-CAR，可以区分会话限速。 避免发生SSH Server报 文发生流量冲击时，SSH Server会话间报文互相抢占带宽的情况。 背景信息 当SSH Server报文发生流量攻击时，SSH Server会话间报文会发生互相抢占带宽的情 况。 SSH协议的白名单Session-CAR用于对白名单报文通道进行隔离，实现区分会话限 速，避免SSH Server会话间报文互相抢占带宽资源。

当默认的白名单Session-CAR的带 宽参数不满足业务要求时，可以对带宽参数进行调整，灵活适应业务要求。 操作步骤 步骤1 执行命令system-view，进入系统视图。 步骤2 执行命令whitelist session-car ssh-server cir cir-value cbs cbs-value pir pir- value pbs pbs-value }\\*，配置SSH协议的白名单Session-CAR的带宽参数。

步骤3 （可选）执行命令whitelist session-car ssh-server disable，去使能SSH协议的白名 单Session-CAR功能。 一般情况下不建议关闭该功能。 步骤4 执行命令commit，提交配置。 ----结束


#### ******* （可选）配置通过SSH登录服务器失败次数的告警门限

用户配置在一定时间内通过SSH登录服务器失败次数的告警上报门限和告警恢复门 限，可以对短期内频繁的用户登录失败进行管理。 背景信息 通过产生告警，及时显示给高级管理员，提醒其介入处理。 请在作为SSH服务器的设备上进行如下的配置。


#### ******* （可选）配置单个IP地址通过SSH登录服务器的最大连接数

用户通过配置单个IP地址使用SSH服务的最大连接数，可以防止单个IP恶意攻击占用服 务器连接数导致其他IP无法登录。 背景信息 当设备作为SSH服务器时，为提高安全性，可通过配置单个IP地址的最大连接数防止恶 意攻击。 请在作为SSH服务器的设备上进行如下的配置。 操作步骤 步骤1 执行命令system-view，进入系统视图。 步骤2 执行命令ssh server ip-limit-session limit-session-num，配置单个IP地址通过SSH登 录到服务器的最大连接数。 步骤3 执行命令commit，提交配置。 ----结束


#### ******** （可选）配置SSH服务器的本地端口转发服务

使能了SSH服务器的本地端口转发服务后，可以与指定地址和端口号建立转发通道。 背景信息 只有当SSH服务器的本地端口转发服务使能后，SSH服务器才能从SSH客户端接收转发 请求信息，建立与指定地址和端口号的TCP连接，即转发通道，它能够将从客户端接收 到的数据转发至指定主机。 操作步骤 步骤1 执行命令system-view，进入系统视图。 步骤2 执行命令ssh server tcp forwarding enable，使能SSH服务器的本地端口转发服务。


#### ******** 配置SSH服务器的键盘交互认证方式

使用口令卡认证方式的SSH用户登录，必须开启键盘交互认证方式。 背景信息 使用口令卡认证方式的SSH用户登录，必须开启键盘交互认证方式，可以使用ssh server authentication-type keyboard-interactive enable开启键盘交互认证方式。 操作步骤 步骤1 执行命令system-view，进入系统视图。 步骤2 执行命令ssh server authentication-type keyboard-interactive enable使能SSH服 务器的键盘交互认证方式。 步骤3 执行命令commit，提交配置。 ----结束


#### ******** 配置SSH会话密钥重协商条件

背景信息 为提升系统安全性，当SSH会话满足下面三个条件中的一个或多个时，系统会重新进 行密钥协商，使用协商出的新密钥来建立SSH会话连接，从而提升系统安全性。 交互报文个数达到配置的密钥重协商条件。 累计报文数据量达到配置的密钥重协商条件。 会话持续时长达到配置的密钥重协商条件。 此命令对IPv4和IPv6客户端均生效。 SSH客户端和服务器端有一方先满足密钥重协商条件，就会发起密钥重协商申请，另外一方响 操作步骤 步骤1 执行命令system-view，进入系统视图。

步骤2 根据SSH服务类型选择相应的命令配置触发密钥重协商条件参数。 执行命令ssh client rekey data-limit data-limit max-patchet max-packet time minutes \\*，配置SSH客户端密钥重协商的触发条件参数。 执行命令ssh server rekey data-limit data-limit max-patchet max-packet time minutes \\*，配置SSH服务端密钥重协商的触发条件参数。

步骤3 执行命令commit，提交配置。 ----结束


#### ******** 检查配置结果

用户通过STelnet登录系统配置成功后，可以查看到SSH服务器的全局配置信息等内 前提条件 已完成用户通过STelnet登录系统的所有配置。 操作步骤 使用display ssh user-information username命令，在SSH服务器端查看SSH用 户信息。 使用display ssh server status命令，查看SSH服务器的全局配置信息。 使用display ssh server session命令，在SSH服务器端查看与SSH客户端连接的 会话信息。

SSH协议的白名单Session-CAR功能配置成功以后，可以按照如下指导检查配置结 基于IPv4：使用命令display cpu-defend whitelist session-car ssh statistics slot slot-id，查看指定接口板上的SSH协议白名单Session-CAR的 统计信息。

如果需要查看某一段时间的统计信息，可以使用命令reset cpu-defend whitelist session-car ssh statistics slot slot-id先清除指定接口板上的SSH 协议白名单Session-CAR的统计信息，再使用命令display cpu-defend whitelist session-car ssh statistics slot slot-id。

基于IPv6：使用命令display cpu-defend whitelist-v6 session-car sshv6 statistics slot slot-id，查看指定接口板上的SSH协议白名单Session-CAR的 统计信息。

如果需要查看某一段时间的统计信息，可以使用命令reset cpu-defend whitelist-v6 session-car sshv6 statistics slot slot-id先清除指定接口板上 的SSH协议白名单Session-CAR的统计信息，再使用命令display cpu-defend whitelist-v6 session-car sshv6 statistics slot slot-id。 ----结束


### 1.5.6 配置用户通过AUX口登录系统

当用户终端和设备之间无可达路由时，用户可以通过设备的AUX口登录设备，实现远 程配置和管理，也可以通过AUX口进行本地维护。


#### ******* 用户通过AUX口登录系统

用户可以通过AUX口配置终端与设备的连接。


#### ******* （可选）配置AUX用户界面

当用户通过AUX口本地或远程配置设备时，可以根据用户使用需求或对设备安全的考 虑，配置相应的AUX用户界面属性。 背景信息 AUX用户界面的属性在设备上都有缺省值，用户一般不需要另外配置。但是用户可以 根据使用需求以及对设备安全的考虑，配置相关属性，比如用户界面的终端属性以及 用户验证方式等。 如需配置AUX用户界面，请参见1.4 用户界面配置。 检查配置结果


#### ******* 检查配置结果

用户通过AUX口登录系统配置成功后，可以查看到用户界面的使用信息、物理属性和 配置、本地用户列表和在线用户等内容。


### 1.5.7 通过Telnet重定向连接其他设备

当用户需要管理远程设备，但终端与远程设备之间无IP网络连接，此时可以通过Telnet 重定向服务来实现。 使用Telnet协议存在安全风险，建议用户使用STelnet。


#### ******* 使能Telnet重定向功能

设备上使能重定向功能后，用户可以在Telnet客户端通过指定的端口号登录其他远程设 备，并对其进行管理和维护。 背景信息 Telnet重定向功能只在某些产品上支持，并且只能对AUX口配置。 请在设备上进行如下的配置。 该配置过程仅在Admin-VS支持。 操作步骤 步骤1 执行命令system-view，进入系统视图。 步骤2 执行命令user-interface aux first-ui-number，进入AUX类型用户界面。 步骤3 执行命令undo shell，禁止在用户界面上启动终端服务。

步骤4 执行命令redirect，使能该用户界面的Telnet重定向功能。 使能Telnet重定向后，指定的端口号就会被分配。 AUX0的编号为33，指定的端口号则为 2033或4033。 2033端口只透传数据报文，4033端口透传所有Telnet报文。 此时可以在Telnet客户端通过指定的端口号登录到远程需要管理的设备。 ----结束


#### ******* 使用Telnet重定向连接其他设备

用户可以通过路由器的Telnet重定向功能，从Telnet客户端登录另一台需要管理的设 背景信息 用户在终端上通过指定的端口号登录到另一台设备。 请在用户终端上进行如下的操作。 操作步骤 执行命令telnet interface-type interface-number interface-name vpn-instance vpn-instance-name source-ip-address host-ip-address port-number \\]，登录到远程设备。

其中，ipv4-address是使能Telnet重定向功能的DeviceB的IP地址或主机名。 ----结束 检查配置结果


#### ******* 检查配置结果

Telnet重定向登录其他设备配置成功后，可以查看当前的TCP状态信息。 前提条件 已完成Telnet重定向登录其他设备的所有配置。 背景信息 执行display tcp status命令查看当前建立的TCP状态信息。 配置用户登录示例


### 1.5.8 配置用户登录示例

配置用户通过Console口、Telnet、STelnet登录系统的示例，本示例中包括组网需求、 配置注意事项和配置思路等。


#### ******* 配置用户通过Console口登录系统示例

在本示例中，在PC端进行登录的设置，实现通过Console口登录系统。 组网需求 如果用户修改了设备的Console用户界面配置参数的默认值，则用户下次通过Console 口登录设备时，必须在用户终端进行相应参数的设置。 图1-24 配置通过Console口登录组网图


#### ******* 配置用户通过Telnet登录系统示例

在本示例中，通过配置VTY用户界面以及用户登录参数，实现从客户端登录设备。 组网需求 用户可以通过终端登录到其它网段上的设备，进行远程维护。 图1-28 配置通过Telnet登录组网图 本例中的Interface1代表接口GigabitEthernet0/0/0。 配置思路 建立物理连接。 配置P1管理网口的IP地址。 配置VTY用户界面的相关参数，包括呼入呼出限制。 配置登录用户的相关参数。 数据准备 为完成此配置举例，需准备如下的数据：


#### ******* 配置IPv6用户通过Telnet登录系统示例

在本示例中，通过配置VTY用户界面以及IPv6用户登录参数，实现从IPv6客户端登录设 组网需求 IPv6用户可以通过终端登录到其它网段上的设备，进行远程维护。 图1-29 配置通过Telnet登录组网图 本例中的Interface1代表接口GigabitEthernet0/0/0。 配置思路 建立物理连接。 配置Device管理网口的IPv6地址。 配置VTY用户界面的相关参数，包括呼入呼出限制。 配置登录用户的相关参数。

数据准备 Telnet客户端已安装PuTTY最新版本（ latest release或者0. 71），且需保证 Telnet客户端和设备Interface1之间IPv6路由通。 为完成此配置举例，需准备如下的数据： Device管理网口的IPv6地址。 VTY用户界面的最大个数为15。 禁止登录的用户登录其他路由器的ACL6号为3001。 VTY用户界面断开连接的时间20分钟。


#### ******* 配置用户通过STelnet登录系统示例

本示例介绍用户终端通过STelnet方式登录SSH服务器，实现STelnet远程登录、管理设 组网需求 网络中有大量设备需要管理与维护，用户不可能为每台设备连接用户终端，特别是终 端与需要管理的设备之间无可达路由时，用户可以使用Telnet方式从当前设备登录到网 络上另一台设备，从而实现对远程设备的管理与维护。 但是Telnet缺少安全的认证方 式，而且传输过程采用TCP进行简单方式传输，存在很大的安全隐患。 STelnet是一种安全的Telnet服务，建立在SSH连接的基础之上。

SSH可以利用加密和强 大的认证功能提供安全保障，保护设备不受诸如IP地址欺诈等攻击。

如图1-32所示，在作为SSH服务器的路由器上使能STelnet服务器功能后，SSH客户端 PC可以通过RSA、DSA、ECC、SM2、x509v3-ssh-rsa、x509v3-ecdsa-sha2、 password、password-rsa、password-ecc、password-dsa、password-sm2、 password-x509v3-rsa、password-x509v3-ecdsa-sha2和all认证方式登录SSH服务 器。

这里以RSA认证方式为例介绍客户端通过STelnet登录服务器的配置过程。 为了提升系统安全性，防止非法用户登录到SSH服务器，用户可以在SSH服务器上配置 ACL规则。 图1-32 配置用户通过STelnet登录系统组网图 本例中的Interface1代表接口GigabitEthernet0/0/0。 这里以管理网口为例介绍STelnet登录的配置方法，实际应用场景中，用户根据组网选择使用客户端 和服务器路由相通的端口。


#### ******* 配置IPv6用户通过STelnet登录系统示例

本示例介绍IPv6用户终端通过STelnet方式登录SSH服务器，实现STelnet远程登录、管 理设备。 组网需求 网络中有大量设备需要管理与维护，用户不可能为每台设备连接用户终端，特别是终 端与需要管理的设备之间无可达路由时，用户可以使用Telnet方式从当前设备登录到网 络上另一台设备，从而实现对远程设备的管理与维护。但是Telnet缺少安全的认证方 式，而且传输过程采用TCP进行简单方式传输，存在很大的安全隐患。 STelnet是一种安全的Telnet服务，建立在SSH连接的基础之上。SSH可以利用加密和强 大的认证功能提供安全保障，保护设备不受诸如IP地址欺诈等攻击。


#### ******* 配置网管设备上的SSH用户与设备通过VPN网络通信示例

介绍网管设备上的SSH用户与设备通过VPN网络通信的配置过程。 组网需求 如图1-48所示，网管、AAA服务器和设备通过VPN网络相连。网管上集成了SSH客户 端、SFTP服务器等功能，网管设备通过VPN网络连接设备后，SSH客户端使用SSH协 议登录设备，和设备之间进行通信。网管设备作为SFTP服务器，和设备（SFTP客户 端）之间使用SFTP协议传输文件。


## 1.6 文件系统配置

文件系统实现对存储设备中的文件、目录的管理。 文件系统概述


### 1.6.1 文件系统概述

用户可以通过Console口、Telnet、STelnet、FTP（File Transfer Protocol）、TFTP （Trivial File Transfer Protocol）或SFTP（Secure File Transfer Protocol）等方式进行 文件操作，实现对文件、目录的管理，包括查看、创建、重命名和删除目录，拷贝、 移动、重命名和删除文件等。

设备运行过程中所需要的文件（如：系统软件、配置文件等）保存在设备的存储设备 中，为了方便用户对存储设备进行有效的管理，设备以文件系统方式对这些文件进行 管理。 文件系统操作包括：目录操作、文件操作等。 用户可通过如下方式对目录、文件进行管理： 通过Console口、Telnet或STelnet方式登录设备后，对目录和文件进行管理。 通过Console口、Telnet或STelnet方式登录设备详细描述，请参见配置用户登录。 通过FTP方式对目录和文件进行管理。

通过TFTP方式对目录和文件进行管理。 通过SFTP方式对目录和文件进行管理。 FTP FTP协议是一种基于TCP/IP协议族的Internet标准应用协议，用于在远端服务器和本地 客户端之间传输文件。 FTP采用两条TCP连接将一个文件从一个系统复制到另一个系 统，连接通常是以客户－服务器的方式建立，这两条TCP连接分别是控制连接（服务器 端为21号端口）和数据连接（服务器端为20号端口）。 控制连接 控制连接建立在客户端与服务器之间。 控制连接始终等待客户端和服务器之间的通信。

并且将相关命令从客户端传送给 服务器，同时将服务器的应答传送给客户端。 数据连接 服务器的数据连接端使用端口20。 服务器执行主动打开数据连接，通常也执行主 动关闭数据连接。 但是，当客户端向服务器发送流形式的文件时，则需要客户端 关闭数据连接。 FTP中传输方式是流方式，并且文件结尾以关闭数据连接为标志，所以对每一个文 件传输或目录列表来说，都要建立一个全新的数据连接。 因此，当一个文件在客 户端与服务器之间传输时，一个数据连接就建立起来了。

在网络中传输数据时，FTP支持以下两种类型的数据传输： 二进制文件类型：以二进制模式将程序文件（比如后缀名为. app、. bin和. btm的文 件，此类文件如果使用ASCII码模式，可能会显示一堆乱码）在数据连接中传输， 不对数据进行任何处理，不需要转换或格式化就可以传输字符，二进制模式比 ASCII码模式更快，并且可以传输所有ASCII值。


### 1.6.2 文件系统管理配置注意事项

特性限制 表1-21 本特性的使用限制 特性限制 涉及产品 SCP不支持和WinSCP工具对接。 NE40E NE40E- X16C/ NE40E- X8C/ NE40E- X8A/ NE40E- X3A/ NE40E- X16A/ NetEngine 40E-X8AK 不支持大于等于4G大小的文件操作，比如dir无法正确 NE40E NE40E- 显示文件大小。

X16C/ NE40E- X8C/ NE40E- X8A/ NE40E- X3A/ NE40E- X16A/ NetEngine 40E-X8AK 文件名区分大小写。 NE40E NE40E- X16C/ NE40E- X8C/ NE40E- X8A/ NE40E- X3A/ NE40E- X16A/ NetEngine 40E-X8AK


### 1.6.3 通过登录系统进行文件操作

用户可以通过登录系统进行文件操作，包括管理存储设备、管理目录和管理文件。 应用环境 当设备无法正常进行信息的存取，需要对异常的存储设备进行修复或用户需要对设备 上的文件或目录进行操作管理时，可以通过直接登录系统的方式实现以上需求。 特别是对存储设备的操作需要通过此种方式。 前置任务 在通过登录系统进行文件操作之前，需要完成以下任务： 成功配置用户登录。 管理目录


#### 1.6.3.1 管理目录

用户可以通过管理目录在逻辑上将文件分级存放。 背景信息 对目录的管理包括：改变当前目录、显示当前目录、显示目录中的文件和子目录列表 以及创建和删除目录。 操作步骤 执行命令cd directory，改变当前所处的目录。 执行命令pwd，查看当前所处的目录。 执行命令dir /all filename \\]，显示目录中的文件和子目录的列表。 所有路径都支持绝对路径或相对于当前工作路径的相对路径。 该命令显示包含的文件信息详见下表：


#### 1.6.3.2 管理文件

如果用户需要查看、删除、重命名设备上的文件时，可以通过文件系统对文件进行相 应的操作。 背景信息 对文件的管理包括：显示文件的内容、拷贝文件、移动文件、重命名文件、压缩 文件、删除文件以及恢复删除的文件、彻底删除回收站中的文件、运行批处理文 件和配置文件系统提示方式。 当用户需要对某个文件进行操作时，可以执行cd directory命令，改变当前目录到 文件所处的目录。 操作步骤 执行命令more file-name，显示文件的内容。

执行命令copy source-filename destination-filename，拷贝文件。 执行命令move source-filename destination-filename，移动文件。 执行命令rename source-filename destination-filename，重新命名文件。 执行命令zip source-filename destination-filename，压缩文件或目录。

执行命令unzip source-filename destination-filename，解压缩文件。 zip命令支持压缩文件或者目录，unzip命令仅支持解压缩文件。 执行命令delete /unreserved filename all \\]，删除文件。 如果使用参数\\[ /unreserved \\]，则删除后的文件不可恢复。 VS模式下，该命令仅在Admin VS支持。 执行命令undelete filename，恢复删除的文件。

执行命令dir /all可以查看所有的文件信息，其中被删除到回收站的文件用“ ”括起来 表示。 对于执行删除命令行时使用命令delete中参数/unreserved后的文件不可恢复。 VS模式下，该命令仅在Admin VS支持。 执行命令reset recycle-bin filename \\]，彻底删除回收站中的文件。 当需要永久删除某一已丢弃的文件时，可以进行彻底删除回收站中的文件的操 作。 命令关键字/f表示删除回收站中的所有文件，删除时不需要用户再逐个文件 确认是否删除。

执行命令tail file-name，显示指定文件的最后指定行内容信息。 运行批处理文件或VSL（VRP Shell Languages）脚本文件 当需要对文件一次进行多项处理时，可以进行运行批处理文件的操作。 编辑好的 批处理文件要预先保存在设备的存储设备中。 如果已经建立好批处理文件，那么可以执行该文件，以实现执行固定任务的自动


### 1.6.4 通过FTP进行文件操作

FTP用于在远端服务器和本地客户端之间传输文件。 应用环境 随着设备的逐步稳定，应用范围在不断扩大，设备升级的范围也越来越大，设备的可 维护性要求也越来越高，特别是设备在线远程加载升级。这不仅可以丰富设备升级维 护手段，方便远程在线升级，还可以节约升级成本，减少客户等待升级的时间，在一 定程度上提高了客户的满意度。但是，实际网络上的丢包、时延、抖动影响了数据传 输。为了可靠的保证在线升级、数据传输，可利用基于TCP的FTP进行在线升级、传输 文件。 此协议不安全，建议使用SFTP。 前置任务 在通过FTP进行文件操作之前，需要完成以下任务： 终端与设备之间三层路由可达。


#### 1.6.4.1 配置FTP类型的本地用户

用户可以配置FTP用户的验证信息、授权方式和授权目录，保证安全性，无权限的用户 将不能访问某些目录。 背景信息 当用户通过FTP进行文件操作时，需要在作为FTP服务器的设备上配置本地用户名及口 令，指定用户的服务类型以及可以访问的目录。 否则用户将无法通过FTP访问设备。 请在作为FTP服务器的设备上进行如下的配置。 操作步骤 步骤1 执行命令system-view，进入系统视图。

步骤2 （可选）执行命令crypto password irreversible-algorithm hmac-sha256，设置用 户密文口令加密算法为hmac-sha256。 步骤3 执行命令aaa，进入AAA视图。 步骤4 执行命令local-user user-name password cipher password irreversible-cipher irreversible-cipher-password \\]，配置本地用户名和口令。

不选择cipher或irreversible-cipher关键字时，密码以交互式输入，系统不回显密 输入的密码为字符串形式，区分大小写，开启用户账户安全策略时，取值范围是8 ～128。 关闭用户账户安全策略时，长度范围是1～128。 开启用户账户安全策略 时，密码不能与用户名及其用户名反向字符串相同，且密码必须包括大写字母、 小写字母、数字及特殊字符。 特殊字符不包括“？ ”和空格。 但是，当输入的密码两端使用双引号时，可在密码中间输 入空格。

如果使用双引号设置带空格密码，双引号之间不能再使用双引号。 如果使用双引号没有设置带空格密码，双引号之间可以再使用双引号。 例如，"Aa 123"45""为不合法密码，"Aa123"45""为合法密码。 选择cipher关键字时，密码可以以简单形式输入，也可以以密文形式输入。 密码以简单形式输入，要求与不选择cipher关键字时一样。 密码以简单形式输 入，系统会回显简单形式的密码，存在安全风险，因此建议使用交互式输入密 无论是简单输入还是密文输入，配置文件中都以密文形式体现。

选择irreversible-cipher关键字时，密码可以以简单形式输入，也可以以不可逆密 文形式输入。 密码以简单形式输入，要求与不选择irreversible-cipher关键字时一样。 无论是简单输入还是不可逆密文输入，配置文件中都以密文形式体现。 步骤5 执行命令local-user user-name service-type ftp，配置FTP服务类型。

步骤6 执行命令local-user user-name ftp-directory directory access-permission read- only read-write \\]，配置FTP用户的授权目录以及进入目录后的操作权限。


#### ******* （可选）指定FTP服务器端口号

FTP服务器的侦听端口号变更后，只有知道当前侦听端口号的用户才能访问设备，确保 安全性。 背景信息 如果使用标准的侦听端口号，可能会有攻击者不断访问此端口，导致带宽和服务器性 能的下降，造成其他正常用户无法访问。 所以可以重新配置FTP服务器的侦听端口号， 使攻击者无法获知更改后的FTP服务器侦听端口号，有效的防止了攻击者对FTP服务标 准端口的访问。 如果变更端口号前FTP服务已经启动，FTP服务将重新启动。 请在作为FTP服务器的设备上进行如下的配置。

操作步骤 步骤1 执行命令system-view，进入系统视图。 步骤2 执行命令ftp ipv6 server port port-number，变更FTP服务器端口号。 如果配置了新的侦听端口号，FTP服务器端会先断开当前已经建立的所有FTP连接，然 后使用新的端口号开始侦听。 步骤3 执行命令commit，提交配置。 ----结束


#### ******* 使能FTP服务器功能

用户通过FTP进行文件操作前需要使能设备的FTP服务器功能。 背景信息 请在作为FTP服务器的设备上进行如下的配置。


#### ******* 配置FTP服务器参数

配置合适的FTP参数，可保证设备的安全性和资源。 背景信息 FTP服务器参数包括以下几种： 指定FTP服务器的源地址或源接口，可限制客户端访问的目的地址，保证安全性。 配置通过FTP方式连接服务器的最大连接数，如果设置的最大连接数小于等于当前 值，那么系统将拒绝新的连接请求，当前连接不会断开。 配置FTP连接空闲时间，如果在连接空闲时间内，FTP服务器和客户端没有消息交 互，则断开它们之间的连接，释放FTP连接资源。 请在作为FTP服务器的设备上进行如下的配置。

操作步骤 步骤1 执行命令system-view，进入系统视图。 步骤2 配置FTP服务器的源地址或源接口，根据需要选择执行以下步骤： 执行命令ftp server-source ip-address interface-type interface- number interface-name }，配置FTP服务器的源地址信息。 配置了FTP服务器的源地址信息后，再执行命令ftp登录服务器时，所输入的服务 器源地址必须与该步骤中配置的一致，否则无法成功登录服务器。

执行命令ftp server-source all-interface，配置FTP服务器的源接口为设备上所 有配置了IP地址的接口。 执行该命令后，FTP服务器会接收来自所有接口的登录连接请求，增加系统安全风险，建 议用户取消配置该命令。 执行命令ftp ipv6 server-source ipv6-address -vpn-instance vpn-instance- name \\]，配置FTP服务器的源IPv6地址信息。

执行命令ftp ipv6 server-source all-interface，配置FTP服务器的IPv6源地址为 设备上所有配置IPv6地址的接口。 执行该命令后，FTP服务器会接收来自所有IPv6地址的登录连接请求，增加系统安全风险， 建议用户取消配置该命令。


#### ******* （可选）配置FTP协议的白名单Session-CAR

通过配置FTP协议的白名单Session-CAR，可以区分会话限速。 避免发生FTP Server报 文发生流量冲击时，FTP Server会话间报文互相抢占带宽的情况。 背景信息 当FTP Server报文发生流量攻击时，FTP Server会话间报文会发生互相抢占带宽的情 况。 FTP协议的白名单Session-CAR用于对白名单报文通道进行隔离，实现区分会话限 速，避免FTP Server会话间报文互相抢占带宽资源。

当默认的白名单Session-CAR的带 宽参数不满足业务要求时，可以对带宽参数进行调整，灵活适应业务要求。 操作步骤 步骤1 执行命令system-view，进入系统视图。 步骤2 执行命令whitelist session-car ftp-server cir cir-value cbs cbs-value pir pir- value pbs pbs-value }\\*，配置FTP协议的白名单Session-CAR的带宽参数。

步骤3 （可选）执行命令whitelist session-car ftp-server disable，去使能FTP协议的白名 单Session-CAR功能。 一般情况下不建议关闭该功能。 步骤4 执行命令commit，提交配置。 ----结束


#### ******* （可选）配置FTP访问控制

用户可以配置FTP访问控制列表，实现只允许指定的客户端登录到设备。


#### ******* （可选）配置IP地址锁定功能

为了提高安全性，FTP支持IP地址锁定功能，防止恶意用户攻击，破坏用户密码。 背景信息 用户FTP登录失败后，根据IP地址记录FTP登录失败的次数，当一定时间内失败次数达 到阈值后，将IP地址锁定，所有通过该IP地址登录的用户均不能正常连接。 操作步骤 步骤1 执行命令system-view，进入系统视图。 步骤2 执行命令undo ftp server ip-block disable，使能FTP服务器上的客户端IP地址锁定 功能。

步骤3 执行命令ftp server ip-block failed-times failed-times period period，配置锁定用 户前的连续认证失败次数和连续失败时间周期。 步骤4 执行命令ftp server ip-block reactive reactive-period，配置锁定用户前的连续认证 失败次数和连续失败时间周期。 步骤5 执行命令commit，提交配置。 步骤6 执行命令quit，进入用户视图。

步骤7 执行命令activate ftp server ip-block ip-address ip-address vpn-instance vpn- name \\]，解除对认证失败的IP地址的阻止。 ----结束


#### ******* 用户通过FTP软件访问系统

当完成设备的FTP服务器的相关设置后，用户可以通过FTP协议从PC访问设备，实现对 设备上文件的管理。 背景信息 从终端通过FTP访问设备，可以选择使用Windows命令行提示符或第三方软件。此处 以Windows命令行提示符为例进行配置。 请在PC上进行以下操作。 操作步骤 步骤1 进入Windows的命令行提示符。 步骤2 执行Windows命令ftp ip-address，通过FTP方式登录设备。 根据提示输入用户名和口令，按Enter键，当出现FTP客户端视图的命令行提示符，如 ftp>，此时用户进入了FTP服务器的工作目录。 ----结束


#### ******* 用户使用FTP命令进行文件操作

当用户成功登录作为FTP服务器的设备后，可以在设备上执行传输文件、管理服务器目 录等操作。 背景信息 FTP文件属性如表1-23所示。 表1-23 文件属性 文件属性 FTP的文件类型 ASCII文件类型 ASCII传输使用ASCII字符，回车键不可用作换行符。

二进制文件类型 FTP的文件连接模 FTP的文件连接模式是针对FTP服务端而言： ACTIVE是主动模式，在建立数据连接时，服务器去连接别 PASV是被动模式，在建立数据连接时，服务器被别人连 建立数据连接时，用ACTIVE模式还是PASV模式，选择权完全 在FTP客户端。 操作步骤 步骤1 根据服务器端IP地址类型不同，进行如下操作：


#### ******** 检查配置结果

通过FTP进行文件操作配置成功后，可以查看到FTP服务器的配置和状态信息、登录的 FTP用户信息等内容。 前提条件 已完成通过FTP进行文件操作的所有配置。


### 1.6.5 通过SFTP进行文件操作

SFTP使得用户可以从远端安全的登录设备进行文件管理，增加了数据传输的安全性。 应用环境 随着设备的逐步稳定，应用范围在不断扩大，设备升级的范围也越来越大，设备的可 维护性要求也越来越高，特别是设备在线远程加载升级。 这不仅可以丰富设备升级维 护手段，方便远程在线升级，还可以节约升级成本，减少客户等待升级的时间，在一 定程度上提高了客户的满意度。 最通用的在线升级、数据传输就是FTP。 但是FTP是明 文传输，甚至是明文传输用户名和密码，存在安全隐患。

而SFTP使得用户可以从远端安全的登入设备进行文件管理，增加了数据传输的安全 性。 同时，由于提供了Client功能，可以在本设备上SFTP到远程设备，进行文件的安 全传输、在线升级。 前置任务 在通过SFTP进行文件操作之前，需要完成以下任务： 终端与设备之间三层路由可达。


#### 1.6.5.1 配置SSH用户并指定服务方式

通过SFTP方式访问设备时，必须要配置SSH用户、产生本地密钥对、设置用户验证方 式以及指定SSH用户的服务方式。


#### 1.6.5.2 使能SFTP服务器功能

在通过SFTP方式访问设备之前，需要首先使能设备的SFTP服务器功能。 背景信息 请在作为SSH服务器的设备上进行如下的配置。 为了保证更好的安全性，建议不要使用小于3072位的RSA算法，建议您使用更安全的 RSA\\_SHA2\\_256、RSA\\_SHA2\\_512认证算法。 操作步骤 步骤1 执行命令system-view，进入系统视图。

步骤2 （可选）执行命令ssh server publickey dsa ecc rsa sm2 x509v3-ssh-rsa\| rsa-sha2-256 rsa-sha2-512 x509v3-rsa2048-sha256 x509v3-ecdsa-sha2 配置SSH服务器允许使用的公钥加密算法。 步骤3 （可选）创建密钥对的最大数。 请根据使用的密钥实际情况选择如下操作之一。

执行命令rsa key-pair maximum max-keys，配置允许创建的RSA密钥对的最大 执行命令dsa key-pair maximum max-keys，配置允许创建的DSA密钥对的最大 执行命令ecc key-pair maximum max-keys，配置允许创建的ECC密钥对的最大


#### 1.6.5.3 配置SFTP服务器参数

用户可以配置是否使能兼容低版本SSH协议，配置或变更SFTP服务器侦听端口号以及 配置服务器密钥对更新时间等。 背景信息 SSH服务器参数如表1-29所示。


#### 1.6.5.4 （可选）配置SSH协议的白名单Session-CAR

通过配置SSH协议的白名单Session-CAR，可以区分会话限速。 避免发生SSH Server报 文发生流量冲击时，SSH Server会话间报文互相抢占带宽的情况。 背景信息 当SSH Server报文发生流量攻击时，SSH Server会话间报文会发生互相抢占带宽的情 况。 SSH协议的白名单Session-CAR用于对白名单报文通道进行隔离，实现区分会话限 速，避免SSH Server会话间报文互相抢占带宽资源。

当默认的白名单Session-CAR的带 宽参数不满足业务要求时，可以对带宽参数进行调整，灵活适应业务要求。 操作步骤 步骤1 执行命令system-view，进入系统视图。 步骤2 执行命令whitelist session-car ssh-server cir cir-value cbs cbs-value pir pir- value pbs pbs-value }\\*，配置SSH协议的白名单Session-CAR的带宽参数。

步骤3 （可选）执行命令whitelist session-car ssh-server disable，去使能SSH协议的白名 单Session-CAR功能。 一般情况下不建议关闭该功能。 步骤4 执行命令commit，提交配置。 ----结束


#### 1.6.5.5 配置SFTP服务授权目录

执行如下步骤配置SSH用户的SFTP服务授权目录。 背景信息 执行ssh user username sftp-directory directoryname命令时，如果用户username 不存在，则新建一个用户名为username的SSH用户，SFTP服务授权目录为所配置的目 录；或者可以使用local-user user-name ftp-directory directory所配置的用户名进行 授权。

如果所配置的授权目录不存在，且不存在默认授权目录，则SFTP客户端连接 SFTP服务器失败。 操作步骤 步骤1 执行命令system-view，进入系统视图。 步骤2 选择执行如下命令，配置SSH用户的SFTP服务授权目录。 执行命令ssh user user-name sftp-directory directoryname，配置指定SSH用户 的SFTP服务授权目录。


#### 1.6.5.6 用户通过SFTP协议访问系统

完成以上配置后，用户可以使用SFTP方式从终端安全地访问设备，从而实现对设备上 文件的管理。 背景信息 从终端通过SFTP访问设备，可以选择使用第三方软件。 此处以使用第三方软件 OpenSSH和Windows命令行提示符为例进行配置。 在PC上安装OpenSSH软件后，请在PC上进行以下配置。 OpenSSH软件的安装请参考该软件的安装说明。 使用OpenSSH软件从终端通过SFTP登录到系统时，需要使用OpenSSH的命令，命令的使用可以 参见该软件的帮助文档。

操作步骤 步骤1 进入Windows的命令行提示符。 步骤2 执行OpenSSH命令，通过SFTP方式访问设备。 当出现SFTP客户端视图的命令行提示符，如sftp>，此时用户进入了SFTP服务器的工作 目录。 ----结束


#### 1.6.5.7 用户使用SFTP命令进行文件操作

用户通过SFTP客户端成功登录设备后，可以在设备上管理目录和文件。 背景信息 用户登录SFTP服务器后可以进行如下操作： 获取SFTP客户端命令帮助。 管理SFTP服务器的目录。 管理SFTP服务器的文件。


#### 1.6.5.8 检查配置结果

通过SFTP进行文件操作配置成功后，可以查看到SSH用户信息和SSH服务器的全局配 置信息等内容。 前提条件 已完成SSH用户的所有配置。 操作步骤 使用display ssh user-information username命令在SSH服务器端查看SSH用户 信息。 使用display ssh server status命令查看SSH服务器的全局配置信息。

使用display ssh server session命令在SSH服务器端查看SSH客户端连接会话信 SSH协议的白名单Session-CAR功能配置成功以后，可以按照如下指导检查配置结 基于IPv4：使用命令display cpu-defend whitelist session-car ssh statistics slot slot-id，查看指定接口板上的SSH协议白名单Session-CAR的 统计信息。

如果需要查看某一段时间的统计信息，可以使用命令reset cpu-defend whitelist session-car ssh statistics slot slot-id先清除指定接口板上的SSH 协议白名单Session-CAR的统计信息，再使用命令display cpu-defend whitelist session-car ssh statistics slot slot-id。

基于IPv6：使用命令display cpu-defend whitelist-v6 session-car sshv6 statistics slot slot-id，查看指定接口板上的SSH协议白名单Session-CAR的 统计信息。

如果需要查看某一段时间的统计信息，可以使用命令reset cpu-defend whitelist-v6 session-car sshv6 statistics slot slot-id先清除指定接口板上 的SSH协议白名单Session-CAR的统计信息，再使用命令display cpu-defend whitelist-v6 session-car sshv6 statistics slot slot-id。 ----结束


### 1.6.6 通过HTTPS下载文件

用户可以通过HTTPS进行版本包下载操作。 前提条件 已通过HTTPS方式登录HTTPS服务器，具体可参考1. 8. 12 通过HTTP登录其他设备。 背景信息 当设备作为客户端时，可以通过HTTPS方式从远端HTTPS服务器获取版本包保存到本 地，用于版本升级。 仅支持HTTPS方式，不支持普通HTTP。

操作步骤 步骤1 执行命令download file-url save-as file-path ssl-policy policy-name ssl- verify peer verify-dns verify-dns vpn-instance vpn-name source-ip ip-address \\]\\*，下载指定URL的版本包到设备的对应路径。 只支持下载以. cc结尾的版本包。 source-ip可以支持IPv4/IPv6地址。 ----结束 文件系统管理配置举例


### 1.6.7 文件系统管理配置举例

文件系统管理的基本使用示例。配置示例中包括组网需求、配置注意事项、配置思路 通过登录系统进行目录管理示例


#### ******* 通过登录系统进行目录管理示例

在本示例中，通过登录设备方式，进行查看目录、拷贝等操作。 组网需求 用户可通过Console口、Telnet或STelnet方式登录设备，对设备上的目录进行操作。 配置注意事项 如果用户通过Console口登录设备，必要已经准备好终端和RS-232电缆。 如果用户通过Telnet或STelnet方式登录设备，Telnet或STelnet服务器相关配置已 经成功配置，且服务器和终端之间三层路由可达。 Telnet或STelnet服务器相关配置，请参见配置用户登录。 本示例采取通过Console口登录设备进行目录管理。


#### ******* 通过登录系统进行文件操作示例

在本示例中，通过登录设备方式，进行查看文件、拷贝等操作。 组网需求 用户可通过Console口、Telnet或STelnet方式登录设备，对设备上的文件进行操作。 文件在存储设备中的路径一定要正确，如果不指定目标文件名，则目标文件名默认为 源文件名，即目标文件和源文件同名。 配置注意事项 如果用户通过Console口登录设备，必要已经准备好终端和RS-232电缆。

如果用户通过Telnet或STelnet方式登录设备，Telnet或STelnet服务器相关配置已 经成功配置，且服务器和终端之间三层路由可达。 Telnet或STelnet服务器相关配置，请参见配置用户登录。 本示例采取通过Console口登录设备进行文件管理。 配置思路 采用如下的思路配置: 配置用户通过Console口登录设备。 查看某目录下有哪些文件。 拷贝文件到该目录下。 查看该目录，可看到文件已经成功拷贝到指定目录。

数据准备 为完成此配置举例，需准备如下的数据: 源文件名和目标文件名 源文件路径和目标文件路径 操作步骤 步骤1 配置用户通过Console口登录设备。 配置用户通过Console口登录设备详细配置，请参考1. 5. 8. 1 配置用户通过Console口登 录系统示例。 步骤2 显示当前目录下的文件信息。

<HUAWEI> dir Directory cfcard:/ Idx Attr Size\\(Byte\\) Date Time\\(LMT\\) FileName -rw- 1,235 Dec 17:10:53 vrpcfg. cfg -rw- 524,575 Jan 10:03:33 private-data.

txt drw- Sep 09:42:52 src drw- Sep 09:42:53 logfile -rw- Sep 09:42:53 $\\_patch\\_rollback\\_state -rw- 11,772 Nov 16:56:55 $\\_patchstate\\_a -rw- Jan 03:09:32 snmpnotilog. txt


#### 1.6.7.3 通过FTP进行文件操作示例

在本示例中，通过使用正确的用户名和密码从用户终端以FTP方式登录到FTP服务器， 实现文件的上传和下载。 组网需求 随着设备的逐步稳定，应用范围在不断扩大，设备升级的范围也越来越大，设备的可 维护性要求也越来越高，特别是设备在线远程加载升级。 这不仅可以丰富设备升级维 护手段，方便远程在线升级，还可以节约升级成本，减少客户等待升级的时间，在一 定程度上提高了客户的满意度。 但是，实际网络上的丢包、时延、抖动影响了数据传 输。

为了可靠的保证在线升级、数据传输，可利用基于TCP的FTP进行在线升级、传输 文件。 如图1-49所示，在作为FTP服务器的设备上使能FTP服务器功能后，从终端仿真程序登 录到FTP服务器，实现文件的上传和下载。 图1-49 通过FTP进行文件操作组网图 本例中interface1代表GigabitEthernet0/0/0。


#### ******* 通过SFTP进行文件操作示例

在本示例中，通过在SSH服务器端生成本地密钥对，并在SSH服务器端配置SSH用户的 用户名和密码，使能SFTP服务，实现SFTP客户端连接SSH服务器后进行文件操作。


#### ******* 通过IPv6 SFTP进行文件操作示例

在本示例中，通过在SSH服务器端生成本地密钥对，并在SSH服务器端配置SSH IPv6用 户的用户名和密码，使能SFTP IPv6服务，实现SFTP客户端连接SSH服务器后进行文件 操作。 组网需求 随着设备的逐步稳定，应用范围在不断扩大，设备升级的范围也越来越大，设备的可 维护性要求也越来越高，特别是设备在线远程加载升级。 这不仅可以丰富设备升级维 护手段，方便远程在线升级，还可以节约升级成本，减少客户等待升级的时间，在一 定程度上提高了客户的满意度。 最通用的在线升级、数据传输就是FTP。

但是FTP是明 文传输，甚至是明文传输用户名和密码，存在安全隐患。 而SFTP使得用户可以从远端安全的登入设备进行文件管理，增加了数据传输的安全 性。 同时，由于提供了Client功能，可以在本设备上SFTP到远程设备，进行文件的安 全传输、在线升级。

如图1-51所示，在作为SSH服务器的设备上使能SFTP IPv6服务器功能后，SFTP IPv6 客户端可以通过RSA、DSA、ECC、SM2、x509v3-ssh-rsa、password、password- rsa、password-ecc、password-dsa、password-sm2、password-x509v3-rsa和all认 证的方式连接到SSH服务端。

图1-51 通过SFTP IPv6进行文件操作组网图 本例中interface1代表GigabitEthernet0/0/0。 配置注意事项 已经成功通过Console口登录作为SFTP服务器的设备，配置用户通过SFTP方式的登录 IPv6地址，建议此IPv6地址配置在逻辑接口上。 配置思路 采用如下的思路配置用户通过SFTP进行文件操作： 创建本地用户并配置。 在SSH服务端使能SFTP IPv6服务功能以及配置用户的服务类型。


## 1.7 配置管理配置

为了保障用户配置的可靠性，系统支持两种配置生效模式。 背景信息 在业务日益丰富的今天，对设备的要求越来越高，包括配置业务时能够使业务整体生 效，在业务配置有问题时能够快速丢弃无效配置，并且要求配置过程对现有业务的影 响降到最低。 为了保障用户配置的可靠性，配置提交技术支持用户进行两阶段生效模式：第一阶段 只进行语法语义检查，第二阶段配置生效到业务。 配置管理概述


### 1.7.1 配置管理概述

系统支持两种配置生效模式：立即生效模式和两阶段生效模式。 缺省为两阶段生效模 式。 系统还支持单板离线方式对接口进行配置。 配置生效模式 系统支持两种配置生效模式：立即生效模式和两阶段生效模式。 缺省为两阶段生效模 立即生效模式是传统的生效模式。 此模式使用命令system-view immediately进入系统视图。 用户在输入命令行并 输入回车键后，系统执行语法检查，如果语法检查通过则配置立即生效。 立即生效模式下，不支持配置回退功能。 两阶段生效模式将系统配置分为两个阶段。

此模式使用命令system-view进入系统视图。 第一阶段用户输入配置命令，系统 在候选数据集执行命令语法和语义检查，对于有错误的配置语句，系统通过命令 行终端提醒用户配置错误及错误原因。 用户完成系列配置命令的输入后，输入 commit命令提交配置，系统进入第二阶段，即配置的提交阶段。 此时系统将候选 数据集上的配置下发到业务，如果业务生效则将候选数据集的配置合并到当前系


### 1.7.2 配置文件管理配置注意事项

特性限制 表1-33 本特性的使用限制 特性限制 涉及产品 系统处于配置提交、回退过程中，可能出现新启动的单 NE40E NE40E- 板较长时间无法注册。 X16C/ NE40E- X8C/ NE40E- X8A/ NE40E- X3A/ NE40E- X16A/ NetEngine 40E-X8AK 配置差异比较命令display configuration changes执行 NE40E NE40E- 时，如果同一个视图中存在重复命令，则当前命令执行 X16C/ 失败。

NE40E- X8C/ NE40E- X8A/ NE40E- X3A/ NE40E- X16A/ NetEngine 40E-X8AK


### 1.7.3 选择配置生效模式

用户可以根据可靠性的不同要求选择立即生效和两阶段生效配置模式。 应用环境 用户在进行配置前必须先进入配置视图。进入配置视图后，系统根据用户选择的配置 模式启动相应的配置事务。如果希望配置能立即生效，可以使用立即生效模式；如果 希望配置完成后再生效，可以使用两阶段生效模式。 前置任务 在进行配置之前，需完成以下任务： 用户成功登录设备并进入用户视图。 配置立即生效模式


#### 1.7.3.1 配置立即生效模式

用户如果希望配置能立即生效，可以配置立即生效模式。 背景信息 用户在进行配置前必须先进入系统视图。 进入系统视图后，系统根据用户选择的配置 模式启动相应的配置事务。 在立即生效模式下，用户在输入命令行并输入回车键后， 系统执行语法检查，如果语法检查通过则配置立即生效。 操作步骤 步骤1 （可选）在用户视图下执行命令configuration exclusive，锁定配置。 当用户希望锁定配置，禁止其他用户同一时间进行配置和提交时，可通过锁定配置来 达到独占当前运行数据集的目的。

如果配置被其他用户锁定，则首先需要联系该用户 解除配置锁定。 可以执行display configuration-occupied user命令，查看当前锁定配置集用户的信 用户锁定配置后，其他用户可以对当前运行数据库进行配置，但是不能提交配置。 如果其他用户需要对运行数据库进行配置，则首先需要由锁定配置的用户进行解锁。 步骤2 执行命令system-view immediately，进入立即生效模式。


#### 1.7.3.2 配置两阶段生效模式

用户如果希望配置完成后再生效，可以配置两阶段生效模式。 背景信息 两阶段生效模式，可以提高配置的安全性和可靠性，将用户配置对业务的异常影响降 到最低。 对于已经生效的业务，如果用户发现配置不符合预期，可以回退之前提交的 配置。 操作步骤 步骤1 （可选）在用户视图下执行命令configuration exclusive，锁定配置。 当用户希望锁定配置，禁止其他用户同一时间进行配置和提交时，可通过锁定配置来 达到独占当前运行数据集的目的。 如果配置被其他用户锁定，则首先需要联系该用户 解除配置锁定。

用户锁定配置后，其他用户可以对当前运行数据库进行配置，但是不能提交配置。 如果其他用户需要对运行数据库进行配置，则首先需要由锁定配置的用户进行解锁。 步骤2 执行命令system-view，进入两阶段生效模式，并且编辑配置。 两阶段生效模式下，用户的提示符如下： <HUAWEI> system-view \\[\~HUAWEI\\]


### 1.7.4 关闭二次确认功能

设备上有些命令，如果用户误操作会引发比较严重的后果。 为防止用户误操作，设备 默认需要进行二次确认。 背景信息 设备上有些undo命令，如果用户误操作会关联删除相关特性的配置，导致业务中断， 造成用户网络中断。

缺省情况下，为了防止用户误操作，执行这些undo命令时，需要 用户进行二次交互确认，命令范围包含：undo mpls、undo mpls te、undo mpls rsvp、undo mpls ldp、undo mpls l2vpn、undo multicast ipv6 routing- enable、undo multicast routing-enable、undo pim、undo igmp、undo bfd、 undo stp enable。

为防止误操作导致某些业务不可用，建议使能二次交互确认功能。 操作步骤 步骤1 执行命令system-view，进入系统视图。 步骤2 执行命令configuration prevent-misoperation disable，关闭二次确认功能。 步骤3 执行命令commit，提交配置。 ----结束


### 1.7.5 配置回退

配置回退能够快速、方便地将配置一次性批量回退到用户指定的历史配置状态，本节 介绍系统回退到指定配置状态的操作步骤。 配置/清除定时配置回退点自动生成时间


#### ******* 配置/清除定时配置回退点自动生成时间

在设备正常运行过程中，通过配置定时配置回退点的生成时间，可以把设备上的配置 保存在配置回退点文件中。 操作步骤 步骤1 执行命令system-view，进入系统视图。 步骤2 执行命令set save-configuration checkpoint daily time time，设置定时生成配置 回退点的生成时间。 步骤3 执行命令commit，提交配置。 步骤4 执行命令quit，返回用户视图。 步骤5 （可选）执行命令clear configuration commit label label-name，删除指定用户标 签的配置回退点。 ----结束 配置回退


#### 1.7.5.2 配置回退

配置回退能够快速、方便地将配置一次性批量回退到用户指定的历史配置状态，本节 介绍系统回退到指定配置状态的操作步骤。 背景信息 用户提交配置后，如果发现配置错误、配置产生故障导致此配置对网络产生了超出预 期的结果时，可以使用此功能将系统的配置批量回退至历史状态。 操作步骤 步骤1 （可选）选择相应的配置生效模式，并进行配置编辑和提交。 两种生效模式的特点如下： 立即生效模式：用户执行命令并回车后，系统就会检测当前配置和历史配置是否产生差 异。 如果有差异，系统就会提交用户的配置操作。

立即生效模式不会生成配置回退点。 两阶段生效模式：用户执行一系列配置命令后，必须输入commit description description \\]命令，系统才会检查当前配置和历史配置的差异，并且生成配置回退点，这 样就可以实现对某个业务一系列命令配置的整体生效。 而且，如果用户希望能够快速找到 要回退的配置点，可以使用参数description description为此次配置添加简单的描述，便于 以后跟踪查看。 推荐使用此模式进行配置编辑和提交。

执行命令system-view immediately，进入立即生效模式的系统视图，然后执行 所需的配置命令进行配置编辑，配置会立即生效。 执行命令system-view，进入两阶段生效模式的系统视图。


### 1.7.6 管理配置文件

用户可以设置下次启动的配置文件、保存配置文件等。 应用环境 用户保存当前系统运行的配置到配置文件，在重启时可以执行配置恢复。 前置任务 在进行配置之前，需完成以下任务： 路由器安装完毕并加电启动正常。 配置了用户账号以及正确的登录验证方式。 终端与路由器之间有可达路由。 用户成功登录设备。 缺省配置文件


#### ******* 缺省配置文件

设备出厂时带有缺省配置文件default.cfg，内容如下： 路由器模式 对于X3A设备： sysname HUAWEI crypto weak-algorithm disable lldp enable undo telnet server enable undo telnet ipv6 server enable undo icmp name timestamp-reply send vpn-instance \\_\\_LOCAL\\_OAM\\_VPN\\_\\_ ipv4-family ipv6-family


#### ******* 空配置启动下的默认配置文件

当设备升级到当前版本并空配置启动时，需要工程师进行现场配置，设备的运维管理 很不方便。 如果希望设备空配置启动后实现即插即用，可以对设备预置一个默认配置 文件，具体请参考1. 7. 6. 6 配置下次启动时加载的配置文件，默认配置文件中携带满足 即插即用的一些配置，设备下次空配置启动后就会使用预置的默认配置文件进行配置 恢复。 default-custom. defcfg文件是设备自带的默认配置文件，只用于设备首次上 线，用户可以根据需要自行修改，具体内容如下：!

Router function begin aaa local-user root password irreversible-cipher $1c$\\]f\\(3Q<j7uS$! 0! \\)8@e\\`\\+lj\\]vQx\\2l&y-$M\\(\|\\n\\_ERFU\\_BF$!

6X$ local-user root service-type ssh local-user root user-group manage-ug local-user root expire 2000-01-01 user-password password-force-change disable snmp-agent protocol source all-interface stelnet server enable


#### ******* 保存配置文件

用户可以保存配置到配置文件，支持自动保存和手工保存两种方式。


#### ******* 比较配置文件

用户可以比较当前的配置文件和下次启动的配置文件或者指定的配置文件是否一致。 背景信息 所比较的配置文件必须以“. cfg” 或“. zip”作为扩展名。 操作步骤 步骤1 执行命令compare configuration configuration-file \\]，比较当前的配置与下次启动 的配置文件或者指定的配置文件的内容是否一致。

当用户完成一系列操作后，可通过本命令从当前配置的首行开始，比较当前配置与下 次启动的配置文件或指定的配置文件内容是否一致，以便用户决定是否需要将当前的 配置保存，设置为下次启动的配置文件。 该命令在比较出不同之处时，将从不同处所在的行开始，分别对当前配置和下次启动 的配置文件或指定的配置文件，显示9行文件内容，如果该不同之处到文件末尾不足9 行，将显示到文件尾为止。 ----结束 加载配置文件


#### ******* 加载配置文件

在设备正常运行过程中，通过加载配置文件可以批量执行命令行，进行功能配置。 背景信息 用户需要将远端服务器或者本地的配置文件加载至当前正在运行的配置数据库中时， 可按如下步骤进行操作。 此功能只能在两阶段生效模式下生效。 操作步骤 步骤1 执行命令system-view，进入系统视图。 步骤2 根据配置文件所在位置，选择执行如下命令之一： 执行命令load configuration file file-name merge relative \\]，加载本地配置 文件并下发配置。

执行命令load configuration server ip-address vpn-instance vpn-instance- name transport-type ftp sftp username user-name password password-value file file-name merge relative \\]，加载远端IPv4服务器上的配 置文件，并将配置下发至当前设备。


#### ******* 配置下次启动时加载的配置文件

用户可以设定需要的配置文件作为系统下次启动时加载的文件。 背景信息 系统重新启动后使用指定的配置文件进行配置恢复，用户可以根据需要设定此配置文 操作步骤 执行命令startup saved-configuration configuration-file，设定系统下次启动时 使用的配置文件。


#### ******* 清除配置文件

用户可以清除本次启动时加载的配置文件。用户可以清除当前设备上加载的配置文 件，也可以一键式清除接口下配置信息。 背景信息 在以下情况下需要清除配置文件： 路由器软件升级之后，配置文件与软件不匹配。 配置文件遭到破坏，或加载了错误的配置文件。 操作步骤 清除当前加载的配置文件 执行命令reset saved-configuration，清除设备当前加载的配置文件。


#### ******* 使能自动核查数据库信息功能

背景信息 设备的数据存储分为中心数据库存储和业务进程数据库存储，各业务进程数据库需要 同步中心数据库的数据。如果出现业务数据库和中心数据库里的数据不一致的情况，


#### 1.7.6.9 使能自动检查主用主控和备用主控配置数据一致性功能

使能自动检查主用主控和备用主控配置数据一致性功能，让系统定时检查配置数据的 一致性，出现数据不一致，设备立即上报告警，提示用户及时分析这类差异给设备造 成的影响。 背景信息 主用主控和备用主控上均有配置数据的存储，正常情况下两者的配置数据一致。 如果 出现主用主控和备用主控中配置数据不一致，此时若发生主备倒换，会导致主用主控 上原有配置丢失。 因此需要使能自动检查主用主控和备用主控配置数据一致性功能， 让系统定时检查配置数据的一致性。

如果出现数据不一致，设备立即上报告警，提示 用户及时分析配置数据不一致给设备造成的影响，这时用户可以尝试保存配置后重启 设备进行修复。 该配置过程仅在Admin-VS支持。 操作步骤 步骤1 执行命令system-view，进入系统视图。 步骤2 执行命令configuration inconsistent slave detect enable，使能自动检查主用主控 和备用主控上的配置数据是否一致功能。 步骤3 执行命令commit，提交配置。 ----结束 锁定系统配置数据


#### *******0 锁定系统配置数据

多用户共同管理场景下，为避免转发器和控制器配置不一致，可按照如下步骤执行锁 定配置操作。


#### *******1 使用配置模板下发配置

设备不同的视图下的配置，可能会出现大量的重复情况。 此时，可以通过创建一个配 置模板，将重复的配置添加到配置模板中，在设备上应用该配置模板即可。 背景信息 设备不同的视图下的配置，可能会出现大量的重复情况。 此时，可以通过创建一个配 置模板，将重复的配置添加到配置模板中，相应视图下应用该配置模板即可，使配置 看起来更简洁。 操作步骤 步骤1 执行命令system-view，进入系统视图。 步骤2 执行命令command group group-name，创建配置模板并进入命令模板视图。

步骤3 根据需要配置的业务视图实例，执行相应业务视图实例命令进入业务视图实例中。 以 接口视图为例，执行命令interface interface-group-name，将Loopback接口与当前 配置模板相关联。 配置模板视图下执行业务视图实例命令，可使用正则表达式。 例如执行命令interface <Loopback. >，将设备上所有的Loopback接口与当前配置模板相关联。 表1-35 正则表达式语法意义描述 特殊字符 转义字符。

将下一个字符（特殊字 \\\\*匹配\\* 符或者普通字符）标记为普通字


#### *******2 查看待提交配置和当前运行配置的差异

用户完成一组配置，在提交之前，可以查看修改的配置和当前运行的配置之间的差 异，如果查看差异时有冲突，需先解决配置冲突后再查看。 背景信息 用户完成一组配置，在提交之前，可以查看修改的配置和当前运行的配置之间的差 异。 在用户修改配置到查看配置差异期间，如果设备上正在运行的配置发生变更，就 会发生配置冲突，用户执行查看配置差异命令时，会提示错误，此时需要先解决配置 冲突，才能查看到配置差异信息。 该操作仅支持在两阶段生效模式下配置。 操作步骤 步骤1 执行命令system-view，进入系统视图。

步骤2 执行命令display configuration candidate changes，查看当前用户未提交的配置和 当前运行配置之间的差异。 执行该步骤时，如果提示设备当前运行配置有变更，需执行步骤3解决配置冲突，然后 再执行该步骤查看配置差异。 步骤3 （可选）执行命令refresh configuration candidate，更新当前用户未提交的配置， 解决配置冲突。 当发生配置冲突时，执行该步骤解决配置冲突。 ----结束


#### *******3 检查配置结果

用户可以查看启动文件列表、某个启动文件的配置信息、当前运行的配置文件。 前提条件 系统加载了下次启动文件。 操作步骤 执行命令display configuration configuration-file，查看指定配置文件的配置信 执行命令display saved-configuration last，查看本次启动时使用的配置文件。 执行命令display saved-configuration configuration，查看系统下次启动时所 用的配置文件。 执行命令display startup，查看本次及下次启动相关的系统软件、配置文件名。 ----结束 配置替换


#### *******4 配置替换

配置文件替换


##### *******4.1 配置文件替换

多台设备同源管理场景下，使用此功能批量替换设备上的配置，实现多台设备配置一 应用环境 管理服务器管理节点设备场景下，管理服务器上存放了节点设备需要的配置，如果管 理服务器的配置发生变更，则相应节点设备的配置也需要同步修改，此时可以加载管 理服务器上的配置文件，替换当前设备上的配置，使节点设备上的配置与管理服务器 上配置保持一致。

配置相同的多台设备，如果其中一台设备的配置发生变更，为保持配置一致，其他设 备的配置需同步变更，此时可以加载配置发生变更的服务器上的配置，替换其他设备 上的配置文件，使所有设备上的配置保持一致。 此功能可替换当前设备上的整个配置文件，也可以只替换某个视图下的配置，这取决 于加载的源配置文件。 如果加载的源文件中包含整个设备的配置，则替换当前设备上 的所有配置，如果加载的源文件是某个视图下保存的配置（视图下保存的配置自动携 带<replace/>标签），则替换相应视图下的配置。

此功能只能在两阶段生效模式下生效。 操作步骤 步骤1 保存配置文件。 如果加载本地设备配置文件替换当前正在运行的配置，则在本地设备上执行以下 命令： 用户视图下执行命令save configuration-file \\]，保存整个设备的配置文件。 业务视图下执行命令save configuration-file，保存相应视图下的配置文件。 执行完成后退出到用户视图。


##### *******4.2 差异配置粘贴

多台设备同源管理场景下，如果不同设备上的配置出现不一致情况，可执行此功能将 差异配置粘贴至其他设备，实现多台设备配置一致。 应用环境 配置相同的多台设备，如果其中一台设备的配置发生变更，为保持配置一致，其他设 备的配置需同步变更，此时用户可使用此功能查询出有差异的配置，将差异配置粘贴 至其他设备，使多台设备配置保持同步。 此功能只能在两阶段生效模式下执行。


##### *******4.3 字符替换

设备上的某个字符串需要替换成新的字符串时，可执行该功能进行批量替换。


### 1.7.7 配置管理配置举例

配置管理的组网举例。配置示例中包括组网需求、配置注意事项和配置思路等。 立即生效模式下用户配置示例


#### 1.7.7.1 立即生效模式下用户配置示例

介绍用户如何在路由器上使用立即生效模式配置业务的例子。 组网需求 如图1-52所示，用户登录路由器。 图1-52 立即生效模式下用户配置组网图 如果用户希望所进行的配置能立即生效，可以配置立即生效模式。 用户在输入命令行并输入回车键后，系统执行语法检查，如果语法检查通过则配置立 即生效。 配置思路 采用如下的思路提交配置： 用户选择立即生效模式。 用户进行配置。 数据准备 接口的IP地址


#### ******* 两阶段生效模式下用户锁定配置示例

介绍在多个用户在一台路由器上进行同时配置时，某个用户锁定配置后其他用户进行 配置时的举例。 组网需求 如图1-53所示，UserA和UserB同时登录路由器。在UserA锁定配置的情况下，UserB 在路由器上进行配置。 图1-53 两阶段生效模式下用户锁定配置组网图 当用户希望锁定配置，禁止其他用户同一时间进行配置和提交时，可通过锁定配置来 达到独占当前运行数据集的目的。此时其他用户进行配置的时候，系统会显示配置被 其他用户锁定的相关提示信息。如果其他用户需要对运行数据库进行配置，则首先需 要由锁定配置的用户解锁。 配置思路 采用如下的思路提交配置： UserA锁定配置。


#### ******* 两阶段生效模式下多用户对同一业务进行相同配置示例

多个用户在一台路由器上对同一业务进行相同配置时系统回应提示信息的例子。 组网需求 如图1-54所示，UserA和UserB同时登录路由器。在UserA对某业务进行配置后， UserB在路由器上进行相同的配置。 图1-54 两阶段生效模式下多用户对同一业务进行相同配置组网图 UserB提交的配置与UserA配置重复，系统会提示用户有冲突的配置。 配置思路 采用如下的思路提交配置：


#### ******* 两阶段生效模式下多用户对同一业务进行不同配置示例

多个用户在一台路由器上对同一业务进行不同配置时的举例。 组网需求 如图1-55所示，UserA和UserB同时登录路由器。在UserA对某业务进行配置后， UserB在路由器上进行同一业务不同的配置。比如，不同的用户在相同的接口下配置IP 地址，且IP地址不同。


#### ******* 两阶段生效模式下多用户对不同业务进行配置示例

多个用户在一台路由器上对不同业务进行配置时系统回应提示信息的例子。 组网需求 如图1-56所示，UserA和UserB同时登录路由器。 在UserA对某业务进行配置后， UserB在路由器上进行不同业务的配置。 图1-56 两阶段生效模式下多用户对不同业务进行配置组网图 UserB提交与UserA不同业务的配置，系统会进行配置叠加。 配置思路 采用如下的思路提交配置： UserA和UserB进行不同业务的配置。 UserA提交配置。 UserB提交配置。

数据准备 接口的IP地址 操作步骤 步骤1 UserA和UserB进行不同业务的配置。 UserA配置路由器接口1/0/4的IP地址为10. 1. 1. 1。 <HUAWEI> system-view \\[\~HUAWEI\\] interface GigabitEthernet 1/0/4 \\[\~HUAWEI-GigabitEthernet1/0/4\\] address UserB使能SFTP服务。 <HUAWEI> system-view


#### ******* 配置回退示例

介绍用户在发现IP地址配置错误后，希望系统一次性恢复到未配置IP地址的状态时的操 作过程。 组网需求 如图1-57所示，用户登录路由器，对路由器的各个接口进行IP地址的配置。 用户在配 置完设备相应接口的IP地址之后，发现IP地址规划错误，所以需要重新配置接口的IP地 址。 如果采用传统方法，必须进入每个接口删除相应的IP地址，再重新配置。 为了解决上述问题，可以使用配置回退功能，使系统的配置统一恢复至所有接口都尚 未配置IP地址的配置状态，这样大大降低了配置恢复的复杂度。

图1-57 配置回退组网图 本例中的Interface1，Interface2，Interface3，Interface4分别代表接口1/0/0、GE1/0/1、GE1/0/2 和GE1/0/3。


#### ******* 管理配置文件示例

本示例中，通过演示说明了保存配置文件、配置系统下次启动时使用的配置文件等基 本操作。 组网需求 如图1-58所示，用户登录设备。 图1-58 管理配置文件组网图 配置注意事项 配置思路 采用如下的思路进行配置： 修改配置。 保存配置文件。


## 1.8 访问其他设备配置

设备可以作为客户端访问网络上的其他设备。 背景信息 当用户需要对网络中其他设备进行管理配置或者进行文件操作时，可以在当前设备通 过Telnet、FTP、TFTP、STelnet、SCP或SFTP访问网络上的其他设备。 访问其他设备概述


### 1.8.1 访问其他设备概述

分别介绍通过Telnet、FTP、TFTP、STelnet、SCP或SFTP方式访问其他设备。 如图1-59所示，用户在PC上通过终端仿真程序或Telnet程序建立与路由器的连接后， 仍可以将当前路由器作为客户端，通过Telnet、FTP、TFTP、STelnet、SCP或SFTP访 问网络上的其他设备。


### 1.8.2 访问其他设备配置注意事项

特性限制 表1-37 本特性的使用限制 特性限制 涉及产品 SSL加载证书文件（身份证书、CA、吊销列表）存在文 NE40E NE40E- 件大小限制，文件大小不能超过（包含）50K。

X16C/ NE40E- X8C/ NE40E- X8A/ NE40E- X3A/ NE40E- X16A/ NetEngine 40E-X8AK 配置使用SSL证书时，单个SSL策略最多加载1本证书、 NE40E NE40E- 4本信任证书、2个证书吊销列表； X16C/ NE40E- 配置使用PKI域下证书时，单个SSL策略最多使用1本证 X8C/ 书、64本信任证书、64个证书吊销列表。

NE40E- X8A/ NE40E- X3A/ NE40E- X16A/ NetEngine 40E-X8AK


### 1.8.3 通过Telnet登录其他设备

Telnet是客户-服务器应用程序，他使用户能够登录到远程设备上，从而使用户能够对 远程设备进行管理与维护。 应用环境 网络中有大量设备需要管理与维护，用户不可能为每台设备连接用户终端，特别是终 端与需要管理的设备之间无可达路由时，用户可以使用Telnet方式从当前设备登录到网 络上另一台设备，从而实现对远程设备的管理与维护。

如图1-62所示，用户可以在PC上通过Telnet登录到Telnet client，但是由于PC与Telnet server之间无可达路由，用户无法远程管理Telnet server，此时用户可以在Telnet client上通过Telnet方式登录到Telnet server，实现对Telnet server的远程管理。 图1-62 当前设备访问其他设备示意图 前置任务 在通过Telnet登录其他设备之前，需要完成以下任务： 成功配置用户通过Telnet登录系统。

Telnet客户端与Telnet服务器之间有可达路由。


#### 1.8.3.1 （可选）配置Telnet客户端源地址

用户可以配置Telnet客户端的源地址信息，从指定的客户端源地址和路由建立Telnet连 接，保证安全性。


#### 1.8.3.2 使用Telnet命令登录其他设备

Telnet是客户-服务器应用协议，用户从Telnet客户端以Telnet方式登录到远端设备，并 对远端设备进行管理与维护。 背景信息 Telnet提供了一种通过终端远程登录到服务器的方式，呈现一个交互式操作界面。 用户 可以先登录到一台设备，再通过Telnet的方式远程登录到网络上的其他设备上，对设备 进行配置和管理，而不需要为每一台设备都连接一个硬件终端。 用户可以在设备上指定某一接口，为此接口配置IP地址，然后使用该IP地址作为Telnet 连接的源IP地址，从而达到安全校验的目的。

Telnet缺少安全的认证方式，而且传输过程采用TCP进行明文传输，存在安全隐患。 对于安全性 较高的网络，建议采用STelnet方式。 操作步骤 根据IP源地址类型，选择执行如下命令： 源地址为IPv4类型。

执行命令telnet interface-type interface-number interface-name vpn-instance vpn-instance-name source-ip-address host-ip- address port-number \\]，使用Telnet协议通过IPv4地址登录到Telnet服务 源地址为IPv6类型。

执行命令telnet ipv6 source-ip6 public-net vpn-instance ipv6- vpn-name ipv6-address -oi interface-type interface-number interface-name port-number \\]，使用Telnet协议通过IPv6地址登录到 Telnet服务器。 ----结束


#### ******* 检查配置结果

用户通过Telnet方式从当前路由器成功登录另一台路由器后，可以查看到当前建立的 TCP连接情况等信息。 前提条件 已完成通过Telnet登录其他设备的所有配置。 操作步骤 使用display tcp status命令查看TCP连接状态。 ----结束


### 1.8.4 通过STelnet登录其他设备

STelnet是一种安全的Telnet服务，用户可以通过STelnet方式从当前设备登录到另一台 设备，对其进行远程管理。 应用环境 网络中有大量设备需要管理与维护，用户不可能为每台设备连接用户终端，特别是终 端与需要管理的设备之间无可达路由时，用户可以使用Telnet方式从当前设备登录到网 络上另一台设备，从而实现对远程设备的管理与维护。 但是Telnet缺少安全的认证方 式，而且传输过程采用TCP进行简单方式传输，存在很大的安全隐患。

而STelnet是一种安全的Telnet服务，建立在SSH连接的基础之上。 SSH可以利用加密和 强大的认证功能提供安全保障，保护设备不受诸如IP地址欺诈等攻击。 如图1-63所 示，设备支持SSH功能，用户可以通过SSH方式登录到远端设备上，对设备进行远程管 理和维护。 此时，当前的设备是客户端，待登录的设备是SSH服务器。 图1-63 通过STelnet登录其他设备示意图 前置任务 在通过STelnet登录其他设备之前，需要完成以下任务： 成功配置用户通过STelnet登录系统。


#### ******* 配置用户首次登录其他设备（使能SSH客户端首次认证功能方式）

使能SSH客户端首次认证功能后，当STelnet客户端第一次登录SSH服务器时，不对SSH 服务器的RSA、DSA、ECC、SM2公钥进行有效性检查。


#### ******* 配置用户首次登录其他设备（SSH客户端为SSH服务器分配公钥）

如果没有使能SSH客户端首次认证功能，那么需要在STelnet客户端登录SSH服务器之 前为服务器分配RSA、DSA、SM2、ECC公钥。 背景信息 如果没有使能SSH客户端首次认证功能，当STelnet客户端第一次登录SSH服务器时， 由于对SSH服务器的RSA、DSA、SM2或ECC公钥有效性检查失败，会导致登录服务器 失败。 所以需要在STelnet客户端登录SSH服务器之前为服务器分配RSA、DSA、 SM2、ECC公钥。

密钥对由SSH服务器创建，密钥对中的公钥传输至STelnet客户端后，客户端对公钥进 行编辑，编辑完成后将此公钥再分配给服务器，这样STelnet客户端才能通过对SSH服 务器有效性检查。 请在作为SSH客户端的设备上进行如下的配置。 为了保证更好的安全性，建议不要使用小于3072位的RSA算法，建议您使用更安全的 RSA\\_SHA2\\_256、RSA\\_SHA2\\_512认证算法。 操作步骤 步骤1 执行命令system-view，进入系统视图。

步骤2 （可选）执行命令ssh client publickey dsa ecc rsa sm2 rsa\\_sha2\\_256 rsa\\_sha2\\_512 \\*，使能相应的SSH客户端公钥算法。


#### 1.8.4.3 （可选）配置SSH客户端支持Keepalive特性

在SSH客户端配置Keepalive特性后，客户端将在设置的周期后发送Keepalive报文给 SSH服务器，检测对端是否可达，以便尽早发现网络故障。


#### 1.8.4.4 使用STelnet命令登录其他设备

用户从SSH客户端以STelnet方式登录到SSH服务器，并对其进行管理和维护。 背景信息 只有当服务器正在侦听的端口号是22时，SSH客户端登录时可以不指定端口号，否则 如果是其他侦听端口号，SSH客户端登录时必须指定端口号。 请在作为SSH客户端的设备上进行如下的配置。 操作步骤 步骤1 执行命令system-view，进入系统视图。

步骤2 （可选）执行命令ssh client cipher des\\_cbc 3des\\_cbc aes128\\_cbc aes192\\_cbc aes256\\_cbc aes128\\_ctr aes192\\_ctr aes256\\_ctr arcfour128 arcfour256 aes128\\_gcm aes256\\_gcm sm4\\_cbc sm4\\_gcm \\*，配置SSH客户 端上的加密算法。

为保证更好的安全性，建议使用以下安全性更高的加密算法：aes128\\_ctr、aes256\\_ctr、 aes192\\_ctr、aes128\\_gcm、aes256\\_gcm。

该命令中的参数des\\_cbc、3des\\_cbc、aes128\\_cbc、aes256\\_cbc、arcfour128、arcfour256、 aes192\\_cbc和sm4\\_cbc算法为弱安全算法不建议使用，如需配置，需执行undo crypto weak- algorithm disable命令使能弱安全算法功能。 为避免安全风险，建议改用更安全的算法。

步骤3 （可选）执行命令ssh client hmac md5 md5\\_96 sha1 sha1\\_96 sha2\\_256 sha2\\_256\\_96 sha2\\_512 sm3 \\*，配置SSH客户端HMAC认证算法。 为保证更好的安全性，建议使用以下安全性更高的HMAC算法：sha2\\_256、sha2\\_512。

该命令中的参数该命令中的参数md5、md5\\_96、sha1、sha1\\_96和sha2\\_256\\_96算法为弱安全 算法不建议使用，如需配置，需执行undo crypto weak-algorithm disable命令使能弱安全算 法功能。 为避免安全风险，建议改用更安全的算法。


#### 1.8.4.5 检查配置结果

通过STelnet登录其他设备功能配置成功后，可以查看到SSH客户端所有SSH服务器与 RSA或ECC公钥之间的对应关系、SSH服务器的全局配置信息以及SSH服务器与客户端 连接的会话信息。 前提条件 已完成通过STelnet登录其他设备功能的所有配置。 操作步骤 使用display ssh server-info命令在SSH客户端查看所有SSH服务器与RSA公钥之 间的对应关系。 ----结束


### 1.8.5 通过TFTP访问其他设备的文件

TFTP也是用于在远端服务器和本地主机之间传输文件，相对于FTP，TFTP没有复杂的 交互存取接口和认证控制，适用于客户端和服务器之间不需要复杂交互的环境。 应用环境 FTP是TCP/IP协议族中最常用的文件传送协议，但是终端和服务器之间交互复杂，对于 没有先进操作系统的终端而言实现文件传输相当困难。 因此，TFTP应运而生，它提供 不复杂、开销不大的服务，是专为终端和服务器间不需要复杂交互而设计。 但是TFTP 只限于简单文件传送操作，不提供存取授权。

目前，HUAWEI NetEngine40E只能作为TFTP客户端，不支持作为TFTP服务器。 前置任务 在通过TFTP访问其他设备的文件之前，需要完成以下任务： 成功配置用户登录。


#### ******* （可选）配置TFTP客户端源地址

用户可以配置TFTP客户端的源地址信息，从指定的客户端源地址建立TFTP连接，保证 安全性。 背景信息 用户可以在设备上指定某一接口，为此接口配置IP地址，然后使用该IP地址作为TFTP 连接的源IP地址，从而达到进行安全校验的目的。 请在作为TFTP客户端的路由器上进行如下的配置。 操作步骤 步骤1 执行命令system-view，进入系统视图。

步骤2 执行命令tftp client-source ip-address interface-type interface-number interface-name }或tftp ipv6 client-source ipv6-address -vpn-instance ipv6- vpn-instance-name \\]，配置TFTP客户端的源地址信息。 参数interface-type指定的接口类型必须是Loopback接口。

配置了TFTP客户端源地址信息后，在服务器端显示的TFTP客户端的源地址信息与该步骤中的配 置一致。 步骤3 执行命令commit，提交配置。 ----结束


#### ******* 配置TFTP访问限制

通过ACL规则配置客户端登录TFTP服务器的访问限制，实现允许当前设备以TFTP方式 可以访问哪些TFTP服务器。


#### ******* 使用TFTP命令下载其他设备的文件

用户可以使用TFTP命令从当前设备下载服务器上的文件到本地。 背景信息 VPN是一个私有网络，它利用公有网络（通常为Internet）连接远端设备或用户。当打 开一个TFTP会话时，TFTP客户端支持在命令中指定vpn-instance-name，TFTP客户端 使用指定的VPN连接远端TFTP服务器。 下载文件时，TFTP客户端向TFTP服务器发送read请求报文。当接收到数据报文时， TFTP客户端再向服务器发送acknowledgement报文。 根据服务器的IP地址类型不同，选择下列步骤中的一种进行操作。


#### ******* 使用TFTP命令向其他设备上传文件

用户可以使用TFTP命令从当前设备上传文件至远端服务器。 背景信息 上传文件时，TFTP客户端向TFTP服务器发送write请求报文。 当接收到数据报文时， TFTP客户端再向服务器发送acknowledgement报文。 根据服务器的IP地址类型不同，选择下列步骤中的一种进行操作。

操作步骤 执行命令tftp source-ip-address interface-type interface-number host-ip-address vpn-instance vpn-instance-name put source-filename destination-filename \\]，使用TFTP上传文件。 参数interface-type必须是Loopback接口。

执行命令tftp ipv6 source-ipv6-address tftp-server-ipv6 vpn-instance vpn-instance-name public-net \\[-oi interface-type interface-number put source-filename destination-filename \\]，使用TFTP上传文件 ----结束 检查配置结果


#### ******* 检查配置结果

通过TFTP访问其他设备的文件配置成功后，可以查看客户端的源地址和配置的访问控 制列表的规则。 前提条件 已完成通过TFTP访问其他设备的文件的所有配置。 操作步骤 使用display tftp-client命令查看设备作为TFTP客户端时的源地址。 使用display acl {acl-number all }命令查看TFTP客户端配置的访问控制列表的 规则。 ----结束


### 1.8.6 通过FTP访问其他设备的文件

配置设备作为FTP客户端，登录网络上的FTP服务器，实现文件的上传和下载等操作。 应用环境 当用户需要与远程FTP服务器进行文件传输、目录管理等操作时，可以配置当前设备为 FTP客户端，通过FTP方式访问远程FTP服务器，以实现远程管理和维护。 前置任务 在通过FTP访问其他设备的文件之前，需要完成以下任务： 成功配置FTP服务器。详细配置如下： 配置FTP类型的本地用户 （可选）指定FTP服务器端口号 使能FTP服务器功能 （可选）配置FTP服务器参数 （可选）配置FTP访问控制


#### ******* （可选）配置FTP客户端源地址

用户可以配置FTP客户端的源地址信息，从指定的客户端源地址建立FTP连接，保证安 全性。 背景信息 用户可以在路由器上指定某一接口，为此接口配置IP地址，然后使用该IP地址作为FTP 连接的源IP地址，从而达到进行安全校验的目的。 请在作为FTP客户端的路由器上进行如下的配置。 操作步骤 步骤1 执行命令system-view，进入系统视图。

步骤2 执行命令ftp client-source ip-address interface-type interface-number interface-name }或ftp ipv6 client-source ipv6-address -vpn-instance ipv6- vpn-instance-name \\]，配置FTP客户端的源地址。

配置了FTP客户端源地址信息后，在FTP服务器端执行display ftp-users命令时，显示 的FTP客户端的源地址信息与该步骤中配置的一致。 步骤3 执行命令commit，提交配置。 ----结束


#### ******* 使用FTP命令连接其他设备

用户可以使用FTP命令从作为FTP客户端的设备登录到其他设备。


#### ******* 通过FTP文件操作命令进行文件操作

用户登录FTP服务器后，可以通过FTP文件操作命令进行文件操作，包括配置文件传输 方式、查看FTP命令在线帮助、上传下载文件、管理目录、管理文件等。

操作步骤 步骤1 根据服务器端IP地址类型不同，进行如下操作： 执行命令ftp source-ip-address interface-type interface-number interface-name host-ip port-number vpn-instance vpn-instance- name public-net \\]，设备使用IPv4地址与FTP服务器建立连接，进入FTP客户 端视图。

执行命令ftp ipv6 source-ip6 host-ipv6-address vpn-instance ipv6- vpn-instance-name public-net -oi interface-type interface-number interface-name port-number \\]，设备使用IPv6地址与FTP服务器建立连接， 进入FTP客户端视图。 步骤2 根据需要，执行如表1-40中的一个或多个操作。

表1-40 文件操作 文件操作 管理文 配置传输文件的 执行命令ascii设置传输的数据类型为ASCII格式。 件操作 文件类型 执行命令binary设置传输的数据类型为二进制模 FTP传输模式由客户端进行选择，系统默认ASCII方 配置传输文件的 执行命令passive设置数据传输的方式为被动方 连接模式 执行命令undo passive设置数据传输的方式为主动 方式。


#### ******* （可选）更改登录用户

更改登录用户主要用于不同权限用户之间的切换。 背景信息 当设备作为FTP客户端，与FTP服务器连接建立成功后，可以更改当前登录的用户，以 实现不同权限用户之间的切换。 不同用户成功切换后，不会影响当前的FTP连接，即 FTP控制连接、数据连接及连接状态都不会变。 更改登录用户时，输入的用户名/密码错误，则会断开当前连接，用户必须重新登录才 能继续访问设备。 HUAWEI NetEngine40E可以在不退出FTP客户端视图的情况下，以其他的用户名登录到FTP服务 器。

所建立的FTP连接，与执行ftp命令建立的FTP连接完全相同。 操作步骤 步骤1 根据服务器端IP地址类型不同，进行如下操作： 执行命令ftp source-ip-address interface-type interface-number interface-name host-ip port-number vpn-instance vpn-instance- name public-net \\]，设备使用IPv4地址与FTP服务器建立连接，进入FTP客户 端视图。

执行命令ftp ipv6 source-ip6 host-ipv6-address vpn-instance ipv6- vpn-instance-name public-net -oi interface-type interface-number interface-name port-number \\]，设备使用IPv6地址与FTP服务器建立连接， 进入FTP客户端视图。 步骤2 执行命令user username，更改当前的登录用户，重新登录FTP服务器。

更改当前的登录用户后，原用户与FTP服务器的连接将断开。 只有3级及3级以上的FTP用户才能通过本命令更改用户身份，登录FTP服务器。


#### ******* 断开与FTP服务器的连接

为了节约系统资源，及保证合法用户能够成功登录FTP服务器，需要及时断开与FTP服 务器连接。 背景信息 登录FTP服务器的用户数达到最大值后，其他合法的用户将无法成功登录。 为了保证合 法用户能够成功登录FTP服务器，需要及时断开与FTP服务器连接。

操作步骤 步骤1 根据服务器端IP地址类型不同，进行如下操作： 执行命令ftp source-ip-address interface-type interface-number interface-name host-ip port-number vpn-instance vpn-instance- name public-net \\]，设备使用IPv4地址与FTP服务器建立连接，进入FTP客户 端视图。

执行命令ftp ipv6 source-ip6 host-ipv6-address vpn-instance ipv6- vpn-instance-name public-net -oi interface-type interface-number interface-name port-number \\]，设备使用IPv6地址与FTP服务器建立连接， 进入FTP客户端视图。

步骤2 断开FTP连接，根据需要，以下步骤任选其一： 执行命令bye/quit终止与FTP服务器的连接，并退回到用户视图。 执行命令close/disconnect终止与FTP服务器的连接，并终止FTP会话，仍保持在 FTP客户端视图。 ----结束 检查配置结果


#### ******* 检查配置结果

通过FTP访问其他设备的文件配置成功后，可以查看到FTP客户端配置的源参数。 前提条件 已完成通过FTP访问其他设备的文件的所有配置。 操作步骤 使用display ftp-client命令查看设备作为FTP客户端时的源参数。 使用display ftp server auth-fail information命令查看所有认证失败的客户 端IP地址。 使用display ftp server ip-block list命令查看因认证失败而被锁定的客户端IP地 ----结束


### 1.8.7 通过SFTP访问其他设备的文件

SFTP是一种安全的FTP服务。 配置设备作为SFTP客户端，服务器通过对客户端的认证 及双向的数据加密，为网络文件传输提供了安全的服务。 应用环境 SFTP是Secure File Transfer Protocol的简称，建立在SSH连接的基础之上，远程用户 可以安全地登录设备，进行文件管理和文件传送等操作，为数据传输提供了更高的安 全保障。 同时，由于设备提供了SFTP客户端功能，可以从本设备安全登录到远程SSH 服务器上，进行文件的安全传输。

前置任务 在配置通过SFTP访问其他设备文件之前，需完成以下任务： 成功配置SFTP服务器。 详细配置如下： 配置SSH用户并指定服务方式 使能SFTP服务器功能 （可选）配置SFTP服务器参数


#### ******* （可选）配置SFTP客户端源地址

用户可以配置SFTP客户端的源地址信息，从指定的客户端源地址建立SFTP连接，保证 安全性。 背景信息 用户可以在设备上指定某一接口，为此接口配置IP地址，然后使用该IP地址作为SFTP 连接的源IP地址，从而达到进行安全校验的目的。 客户端源地址可以配置为源接口或源IP。 请在作为SFTP客户端的设备上进行如下的配置。 操作步骤 步骤1 执行命令system-view，进入系统视图。 步骤2 执行以下命令配置SFTP的源地址或源接口信息。

IPv4场景下：执行命令sftp client-source source-ip-address public-net vpn-instance ipv6-vpn-instance-name }或sftp client-source interface- type interface-number interface-name IPv6场景下：执行命令sftp ipv6 client-source source-ipv6-address -vpn- instance ipv6-vpn-instance-name 步骤3 执行命令commit，提交配置。

----结束


#### ******* 配置用户首次登录其他设备（使能SSH客户端首次认证功能方式）

使能SSH客户端首次认证功能后，当SFTP客户端第一次登录SSH服务器时，不对SSH服 务器的RSA、DSA、SM2或ECC公钥进行有效性检查。 背景信息 如果配置了使能SSH客户端首次认证功能，那么在SFTP客户端第一次登录SSH服务器 后，不对SSH服务器的RSA、DSA、SM2或ECC公钥进行有效性检查。 登录后，系统将 自动分配并保存RSA、DSA、SM2或ECC公钥，为下次登录时认证。 请在作为SSH客户端的设备上进行如下的配置。

操作步骤 步骤1 执行命令system-view，进入系统视图。 步骤2 执行命令ssh client first-time enable，使能SSH客户端首次登录功能。 步骤3 执行命令commit，提交配置。 ----结束


#### ******* 配置用户首次登录其他设备（SSH客户端为SSH服务器分配公钥）

如果没有使能SSH客户端首次认证功能，那么需要在SFTP客户端登录SSH服务器之前 为服务器分配RSA、DSA、SM2、ECC公钥。 背景信息 如果没有使能SSH客户端首次认证功能，当SFTP客户端第一次登录SSH服务器时，由 于对SSH服务器的RSA、DSA、SM2、ECC公钥有效性检查失败，从而会导致登录服务 器失败。 所以需要在SFTP客户端登录SSH服务器之前为服务器分配RSA、DSA、SM2、 ECC公钥。 请在作为SSH客户端的设备上进行如下的配置。

为了保证更好的安全性，建议不要使用小于3072位的RSA算法，建议您使用更安全的 RSA\\_SHA2\\_256、RSA\\_SHA2\\_512认证算法。 操作步骤 步骤1 执行命令system-view，进入系统视图。 步骤2 （可选）执行命令ssh client publickey dsa ecc rsa sm2 rsa\\_sha2\\_256 rsa\\_sha2\\_512 \\*，使能相应的SSH客户端公钥算法。

命令中的参数dsa和rsa为弱安全算法，不建议使用，如果确实需要使用，请先执行undo crypto weak-algorithm disable命令使能弱安全算法功能。 步骤3 根据选择的算法执行如下命令： 执行命令rsa peer-public-key key-name，进入RSA公共密钥视图。


#### 1.8.7.4 使用SFTP命令连接其他设备（SSH服务器）

用户可以从SSH客户端以SFTP方式登录到SSH服务器上。 背景信息 SFTP客户端访问SSH服务器的命令跟STelnet客户端很相似，支持携带源地址，选择密 钥交换算法、加密算法和HMAC算法。 在作为SSH客户端的设备上进行如下的配置。


#### 1.8.7.5 通过SFTP文件操作命令进行文件操作

用户可以通过SFTP客户端管理SSH服务器上的目录和文件，以及查看SFTP客户端命令 帮助。 背景信息 当SFTP客户端登录到SSH服务器之后，用户可以在SFTP客户端进行如下操作： 创建并删除SSH服务器上的目录，以及显示当前的工作目录和指定目录下的文件 或目录信息 改变文件名、删除文件、显示文件列表、上传下载文件 查看SFTP客户端命令帮助 请在作为SSH客户端的路由器上进行如下的配置。 操作步骤 步骤1 执行命令system-view，进入系统视图。

步骤2 执行基于网络协议的如下操作步骤： 基于IPv4协议的配置情况： 执行命令sftp source-ip-address -force-receive-pubkey host-ip- address port-number prefer\\_kex prefer\\_kex prefer\\_ctos\\_cipher prefer\\_ctos\\_cipher prefer\\_stoc\\_cipher prefer\\_stoc\\_cipher prefer\\_ctos\\_hmac prefer\\_ctos\\_hmac prefer\\_stoc\\_hmac prefer\\_stoc\\_hmac prefer\\_ctos\\_compress zlib prefer\\_stoc\\_compress zlib public-net -vpn-instance vpn-instance-name -ki interval count identity-key identity-key-type user-identity-key user- key \\*，以SFTP方式通过使用IPv4的地址登录到SSH服务器上，并进入SFTP客户 端视图。

基于IPv6协议的配置情况： 执行命令sftp ipv6 -force-receive-pubkey source-ipv6-address host- ipv6-address -vpn-instance vpn-instance-name public-net -oi interface-name interface-type interface-number port-number prefer\\_kex prefer\\_kex prefer\\_ctos\\_cipher prefer\\_ctos\\_cipher prefer\\_stoc\\_cipher prefer\\_stoc\\_cipher prefer\\_ctos\\_hmac prefer\\_ctos\\_hmac prefer\\_stoc\\_hmac prefer\\_stoc\\_hmac prefer\\_ctos\\_compress zlib prefer\\_stoc\\_compress zlib -ki interval -kc count identity-key identity-key-type user-identity-key user- key \\]\\*，以SFTP方式通过使用IPv6的地址登录到SSH服务器上，并进入SFTP客户 端视图。


#### ******* 检查配置结果

通过SFTP访问其他设备的文件配置成功后，可以查看到SSH客户端源地址、客户端所 有的SSH服务器与RSA或ECC公钥之间的对应关系、SSH服务器的全局配置信息以及与 客户端连接的会话信息。


### 1.8.8 配置SFTP客户端通过一键式文件操作命令进行文件操作

设备作为SFTP客户端，使用一键式文件操作命令，从本地上传文件到SFTP服务器，或 者从SFTP服务器下载文件至本地。 前提条件 已完成SFTP服务器端的配置，且SFTP客户端和服务器端的路由相通。 操作步骤 步骤1 执行命令system-view，进入系统视图。 步骤2 执行命令ssh client first-time enable，使能SSH客户端首次认证功能。 步骤3 执行命令commit提交配置。

步骤4 基于网络协议执行如下操作步骤： 基于IPv4建立SFTP连接 执行命令sftp client-transfile get put source-address interface- type interface-number interface-name host-ip host-ipv4 port public-net -vpn-instance vpn-instance-name prefer\\_kex prefer\\_kex identity-key identity-key-type prefer\\_ctos\\_cipher prefer\\_ctos\\_cipher prefer\\_stoc\\_cipher prefer\\_stoc\\_cipher prefer\\_ctos\\_hmac prefer\\_ctos\\_hmac prefer\\_stoc\\_hmac prefer\\_stoc\\_hmac -ki interval -kc count username user-name password password sourcefile destination destination source-file \\]，通过 IPv4方式连接SFTP服务器，并从服务器上下载文件至SFTP客户端或者从SFTP客户 端上传文件至服务器。

基于IPv6建立SFTP连接 执行命令sftp client-transfile put get ipv6 source-ipv6-address host-ip host-ipv6 -oi interface-type interface-number interface-name port public-net -vpn-instance vpn-instance-name prefer\\_kex prefer\\_kex identity-key identity-key-type prefer\\_ctos\\_cipher prefer\\_ctos\\_cipher prefer\\_stoc\\_cipher prefer\\_stoc\\_cipher prefer\\_ctos\\_hmac prefer\\_ctos\\_hmac prefer\\_stoc\\_hmac prefer\\_stoc\\_hmac -ki interval -kc count \\*username user-name password password sourcefile source-file destination destination \\]，通过 IPv6方式连接SFTP服务器，并从服务器上下载文件至SFTP客户端或者从SFTP客户 端上传文件至服务器。

----结束


### 1.8.9 通过SCP访问其他设备的文件

SCP（Secure Copy Protocol）客户端和服务器之间经过协商，建立安全连接，客户端 可以上传文件至服务器或从服务器下载文件至本地。 应用环境 SCP是基于SSH2协议的安全文件传输方式，支持批量操作。 前置任务 在通过SCP访问其他设备的文件之前，需要完成以下任务： SCP客户端和SCP服务器之间路由可达。


#### ******* 配置SCP服务器端

配置SCP服务器，使得客户端和服务器之间经过协商建立安全连接，实现安全的远程访 背景信息 SCP协议基于SSH2协议。 用户界面必须支持SSH协议，才能通过SCP访问其他设备文 操作步骤 步骤1 配置VTY用户界面支持SSH协议，请参考配置VTY用户界面支持SSH协议 步骤2 配置SSH用户，请参见为SSH用户配置验证方式并指定服务方式 步骤3 根据SCP服务的类型，选择执行如下步骤之一。

基于IPv4：执行命令scp server enable或scp ipv4 server enable，使能SCP服 基于IPv6：执行命令scp server enable或scp ipv6 server enable，使能IPv6 SCP服务。 步骤4 （可选）执行命令ssh server dh-exchange min-len min-len，配置与SSH客户端进 行Diffie-hellman-group-exchange密钥交换时，支持的最小密钥长度。

如果SSH客户端支持大于1024bits的Diffie-hellman-group-exchange密钥交换算法时，建议执行 ssh server dh-exchange min-len命令配置最小密钥长度为3072bits，以提高安全性。 步骤5 执行命令commit提交配置。 ----结束


#### ******* 配置SCP客户端

SCP客户端和服务器之间经过协商，建立安全连接，客户端可以上传文件至服务器或从 服务器下载文件至本地。


#### ******* 检查配置结果

通过SCP访问其他设备的文件成功配置后，可以查看到SCP客户端的源地址。 前提条件 已完成通过SCP访问其他设备的文件的所有配置。 操作步骤 使用display scp-client命令在SCP客户端查看源配置信息。 使用display ssh server-info命令，在SSH客户端查看SSH服务器与RSA公钥之间 的对应关系。 ----结束


### 1.8.10 配置SSL策略加密算法套

客户端与服务端之间进行认证时，会为SSL算法协商提供加密算法列表。 本文介绍如何 配置加密算法套允许使用的加密算法，使用安全的算法可增强系统安全性。 背景信息 客户端与服务端之间进行认证时，会为SSL算法协商提供加密算法列表。 对于安全性要 求较高的系统，使用更安全的加密算法可增强系统安全性。 操作步骤 步骤1 执行命令system-view，进入系统视图。

步骤2 执行命令ssl cipher-suite-list customization-policy-name，创建SSL策略加密算法套 并进入SSL算法套定制视图。

步骤3 执行命令set cipher-suite tls1\\_ck\\_rsa\\_with\\_aes\\_256\\_sha tls1\\_ck\\_rsa\\_with\\_aes\\_128\\_sha tls1\\_ck\\_dhe\\_rsa\\_with\\_aes\\_256\\_sha tls1\\_ck\\_dhe\\_dss\\_with\\_aes\\_256\\_sha tls1\\_ck\\_dhe\\_rsa\\_with\\_aes\\_128\\_sha tls1\\_ck\\_dhe\\_dss\\_with\\_aes\\_128\\_sha tls12\\_ck\\_rsa\\_aes\\_128\\_cbc\\_sha tls12\\_ck\\_rsa\\_aes\\_256\\_cbc\\_sha tls12\\_ck\\_rsa\\_aes\\_128\\_cbc\\_sha256 tls12\\_ck\\_rsa\\_aes\\_256\\_cbc\\_sha256 tls12\\_ck\\_dhe\\_dss\\_aes\\_128\\_cbc\\_sha tls12\\_ck\\_dhe\\_rsa\\_aes\\_128\\_cbc\\_sha tls12\\_ck\\_dhe\\_dss\\_aes\\_256\\_cbc\\_sha tls12\\_ck\\_dhe\\_rsa\\_aes\\_256\\_cbc\\_sha tls12\\_ck\\_dhe\\_dss\\_aes\\_128\\_cbc\\_sha256 tls12\\_ck\\_dhe\\_rsa\\_aes\\_128\\_cbc\\_sha256 tls12\\_ck\\_dhe\\_dss\\_aes\\_256\\_cbc\\_sha256 tls12\\_ck\\_dhe\\_rsa\\_aes\\_256\\_cbc\\_sha256 tls12\\_ck\\_rsa\\_with\\_aes\\_128\\_gcm\\_sha256 tls12\\_ck\\_rsa\\_with\\_aes\\_256\\_gcm\\_sha384 tls12\\_ck\\_dhe\\_rsa\\_with\\_aes\\_128\\_gcm\\_sha256 tls12\\_ck\\_dhe\\_rsa\\_with\\_aes\\_256\\_gcm\\_sha384 tls12\\_ck\\_dhe\\_dss\\_with\\_aes\\_128\\_gcm\\_sha256 tls12\\_ck\\_dhe\\_dss\\_with\\_aes\\_256\\_gcm\\_sha384 tls12\\_ck\\_ecdhe\\_rsa\\_with\\_aes\\_128\\_gcm\\_sha256


### 1.8.11 配置绑定SSL Policy

部署SSL策略可防止传输的数据被篡改，提高系统安全性。 背景信息 传统的一些协议（如Syslog）不具备安全机制，采用明文形式传输数据，不能验证通 信双方的身份，无法防止传输的数据被篡改，安全性很低。 安全协议SSL利用数据加 密、身份验证和消息完整性验证机制，为基于TCP可靠连接的应用层协议提供了安全性 保证。 操作步骤 在SSL策略视图下部署SSL策略： 执行命令system-view，进入系统视图。 执行命令ssl policy policy-name，配置SSL策略并进入SSL策略视图。

（可选）执行命令ecdh group nist curve brainpool ffdhe ，配置 ECDHE算法的椭圆曲线参数。 执行命令ssl minimum version tls1. 1 tls1. 2 tls1. 3 }，配置当前SSL策略所采 用的最低版本。 已经配置了TLS1. 0的版本升级到不支持配置TLS1. 0的新版本后，SSL策略支持的最低版本为 TLS1. 0，可以通过ssl minimum version tls1. 1 tls1. 2 tls1.

3 }命令重新配置当前SSL 策略所采用的最低版本，该过程不可逆。 在未配置ssl minimum version命令的情况下升级到新版本后，升级后SSL策略支持的最低 版本为TLS1. 1，缺省情况下支持的最低版本为TLS1. 2，可以通过ssl minimum version tls1. 1 tls1. 2 tls1. 3 }命令重新配置当前SSL策略所采用的最低版本。


### 1.8.12 通过HTTP登录其他设备

HTTP（Hypertext Transfer Protocol）即超文本传输协议，是用于从WWW服务器传 输超文本到本地浏览器的传送协议。HTTP是一个应用层协议，由请求和响应构成，是 一个标准的客户端/服务器模型。 背景信息 当用户需要进行从HTTP服务器端下载证书，可以使用HTTP协议。HTTP是Hypertext Transfer Protocol的简称，它用来在Internet上传递Web页面信息。 使用HTTP协议存在安全风险。


### 1.8.13 配置Telnet、SSH协议报文的DSCP值

配置Telnet、SSH协议报文的优先级，可以通过修改DSCP字段的值来实现。 背景信息 设备可以发出NETCONF、Telnet、SSH等多种协议报文，可以通过命令host-packet type统一配置这些协议报文的DSCP值，这种情况下，同时发送大量协议报文可能造成 网络拥塞，为解决这一问题，可配置设备发出去的协议报文使用不同的DSCP。 操作步骤 步骤1 执行命令system-view，进入系统视图。

步骤2 根据服务类型和待配置的协议报文类型，选择执行下列命令： 执行命令ssh client dscp value，配置客户端SSH协议报文的DSCP值。 执行命令telnet client dscp value，配置客户端Telnet协议报文的DSCP值。 执行命令ssh server dscp value，配置服务器端SSH协议报文的DSCP值。 执行命令telnet server dscp value，配置服务器端Telnet协议报文的DSCP值。 步骤3 执行命令commit，提交配置。

----结束 任务示例 执行命令display current-configuration，可以查看配置的DSCP值。

<HUAWEI> system-view \\[\~HUAWEI\\] display current-configuration include-default include dscp Info: will take long time the content you search too much the string you input too long, you can press CTRL\\_C break.

telnet server dscp telnet client dscp ssh server dscp ssh client dscp 访问其他设备配置举例


### 1.8.14 访问其他设备配置举例

配置设备访问其他设备的示例，示例中包括组网需求、配置注意事项和配置思路等。


#### 1.8.14.1 通过Telnet登录其他设备配置示例

配置Telnet登录其他设备的示例。在本示例中，通过配置用户验证方式和密码，实现 Telnet登录。 组网需求 使用Telnet协议存在安全风险，建议使用STelnet V2登录设备。


#### 1.8.14.2 通过STelnet登录其他设备配置示例（RSA认证方式）

通过STelnet登录其他设备配置的示例。 在本示例中，通过在STelnet客户端和SSH服务 器端生成本地密钥对，在SSH服务器端生成RSA公钥、并为用户绑定该RSA公钥，实现 Stelnet客户端连接SSH服务器。 组网需求 网络中有大量设备需要管理与维护，用户不可能为每台设备连接用户终端，特别是终 端与需要管理的设备之间无可达路由时，用户可以使用Telnet方式从当前设备登录到网 络上另一台设备，从而实现对远程设备的管理与维护。

但是Telnet缺少安全的认证方 式，而且传输过程采用TCP进行简单方式传输，存在很大的安全隐患。 而STelnet是一种安全的Telnet服务，建立在SSH连接的基础之上。 SSH可以利用加密和 强大的认证功能提供安全保障，保护设备不受诸如IP地址欺诈等攻击。

如图1-65所 示，SSH服务器端STelnet服务使能后，STelnet客户端通过RSA、DSA、ECC、SM2、 x509v3-ssh-rsa、password、password-rsa、password-ecc、password-dsa、 password-sm2、password-x509v3-rsa和all认证方式登录到SSH服务器端。 图1-65 通过STelnet登录其他设备组网图 本例中的Interface1代表接口GigabitEthernet0/0/0。


#### ******** 通过STelnet登录其他设备配置示例（DSA认证方式）

通过STelnet登录其他设备配置的示例。 在本示例中，通过在STelnet客户端和SSH服务 器端生成本地密钥对，在SSH服务器端生成DSA公钥、并为用户绑定该DSA公钥，实现 Stelnet客户端连接SSH服务器。 组网需求 网络中有大量设备需要管理与维护，用户不可能为每台设备连接用户终端，特别是终 端与需要管理的设备之间无可达路由时，用户可以使用Telnet方式从当前设备登录到网 络上另一台设备，从而实现对远程设备的管理与维护。

但是Telnet缺少安全的认证方 式，而且传输过程采用TCP进行简单方式传输，存在很大的安全隐患。 而STelnet是一种安全的Telnet服务，建立在SSH连接的基础之上。 SSH可以利用加密和 强大的认证功能提供安全保障，保护设备不受诸如IP地址欺诈等攻击。

如图1-66所 示，SSH服务器端STelnet服务使能后，STelnet客户端可以通过RSA、DSA、ECC、 SM2、x509v3-ssh-rsa、password、password-rsa、password-ecc、password-dsa、 password-sm2、password-x509v3-rsa和all认证方式登录到SSH服务器端。 图1-66 通过STelnet登录其他设备组网图 本例中的Interface1代表接口GigabitEthernet0/0/0。


#### ******** 通过STelnet登录其他设备配置示例（ECC认证方式）

通过STelnet登录其他设备配置的示例。 在本示例中，通过在STelnet客户端和SSH服务 器端生成本地密钥对，在SSH服务器端生成ECC公钥、并为用户绑定该ECC公钥，实现 Stelnet客户端连接SSH服务器。 组网需求 网络中有大量设备需要管理与维护，用户不可能为每台设备连接用户终端，特别是终 端与需要管理的设备之间无可达路由时，用户可以使用Telnet方式从当前设备登录到网 络上另一台设备，从而实现对远程设备的管理与维护。

但是Telnet缺少安全的认证方 式，而且传输过程采用TCP进行简单方式传输，存在很大的安全隐患。 而STelnet是一种安全的Telnet服务，建立在SSH连接的基础之上。 SSH可以利用加密和 强大的认证功能提供安全保障，保护设备不受诸如IP地址欺诈等攻击。

SSH服务器端 STelnet服务使能后，STelnet客户端可以通过RSA、DSA、ECC、SM2、x509v3-ssh- rsa、password、password-rsa、password-ecc、password-dsa、password-sm2、 password-x509v3-rsa和all认证方式登录到SSH服务器端。 如图1-67所示，配置两个登 录用户为Client001和Client002，分别使用password方式和ECC方式登录SSH服务器。

图1-67 通过STelnet登录其他设备组网图 本例中的Interface1代表接口GigabitEthernet0/0/0。 配置思路 采用如下的思路配置通过STelnet登录其他设备： 在SSH服务器上配置用户Client001和Client002，分别使用不同的认证方式登录 SSH服务器。 分别在STelnet客户端Client002和SSH服务器端生成本地密钥对，并为用户 Client002绑定SSH客户端的ECC公钥，实现客户端登录服务器端时，对客户端进 行验证。


#### ******** 通过STelnet登录其他设备配置示例（SM2认证方式）

在本示例中，通过在STelnet客户端和SSH服务器端生成本地密钥对，在SSH服务器端 生成SM2公钥、并为用户绑定该SM2公钥，实现Stelnet客户端连接SSH服务器。 组网需求 网络中有大量设备需要管理与维护，用户不可能为每台设备连接用户终端，特别是终 端与需要管理的设备之间无可达路由时，用户可以使用Telnet方式从当前设备登录到网 络上另一台设备，从而实现对远程设备的管理与维护。 但是Telnet缺少安全的认证方 式，而且传输过程采用TCP进行简单方式传输，存在很大的安全隐患。

而STelnet是一种安全的Telnet服务，建立在SSH连接的基础之上。 SSH可以利用加密和 强大的认证功能提供安全保障，保护设备不受诸如IP地址欺诈等攻击。 SSH服务器端 STelnet服务使能后，STelnet客户端可以通过RSA、DSA、ECC、SM2、x509v3-ssh- rsa、password、password-rsa、password-ecc、password-dsa、password-sm2、 password-x509v3-rsa和all认证方式登录到SSH服务器端。

如图1-68所示，配置两个登 录用户为client001和client002，分别使用password方式和SM2方式登录SSH服务器。 图1-68 通过STelnet登录其他设备组网图 本例中的Interface1代表接口GigabitEthernet0/0/0。


#### ******** 通过TFTP访问其他设备文件配置示例

在本示例中，通过在TFTP服务器端运行TFTP软件，并设置源文件在服务器中的位置， 实现文件的上传和下载。 组网需求 FTP是TCP/IP协议族中最常用的文件传送协议，但是终端和服务器之间交互复杂，对于 没有先进操作系统的终端而言实现文件传输相当困难。因此，TFTP应运而生，它提供 不复杂、开销不大的服务，是专为终端和服务器间不需要复杂交互而设计。但是TFTP 只限于简单文件传送操作，不提供存取授权。 如图1-69所示，从终端登录到TFTP客户端，再从TFTP服务器上传、下载文件。 图1-69 配置通过TFTP访问其他设备文件组网图


#### ******** 通过FTP访问其他设备文件配置示例

在本示例中，通过从FTP客户端登录FTP服务器，实现从FTP服务器中下载系统软件和 配置文件到客户端。


#### ******** 通过SFTP访问其他设备文件配置示例（RSA认证方式）

在本示例中，通过在SFTP客户端和SSH服务器端生成本地密钥对，在SSH服务器端生 成RSA公钥、并为用户绑定该RSA公钥，实现SFTP客户端连接SSH服务器。 组网需求 SFTP建立在SSH连接的基础之上，远程用户可以安全地登录设备，进行文件管理和文 件传送等操作，为数据传输提供了更高的安全保障。 同时，由于设备提供了SFTP客户 端功能，可以从本设备安全登录到远程SSH服务器上，进行文件的安全传输。

如图1-72所示，SSH服务器端SFTP服务使能后，SFTP客户端可以通过RSA、DSA、 ECC、SM2、x509v3-ssh-rsa、password、password-rsa、password-ecc、password- dsa、password-sm2、password-x509v3-rsa和all认证方式登录到SSH服务器端进行文 件的访问。 图1-72 通过SFTP访问其他设备文件组网图 本例中的Interface1代表接口GigabitEthernet0/0/0。


#### 1.8.14.9 通过SFTP访问其他设备文件配置示例（DSA认证方式）

在本示例中，通过在SFTP客户端和SSH服务器端生成本地密钥对，在SSH服务器端生 成DSA公钥、并为用户绑定该DSA公钥，实现SFTP客户端连接SSH服务器。 组网需求 SFTP建立在SSH连接的基础之上，远程用户可以安全地登录设备，进行文件管理和文 件传送等操作，为数据传输提供了更高的安全保障。 同时，由于设备提供了SFTP客户 端功能，可以从本设备安全登录到远程SSH服务器上，进行文件的安全传输。

如图1-73所示，SSH服务器端SFTP服务使能后，SFTP客户端可以通过RSA、DSA、 ECC、SM2、x509v3-ssh-rsa、password、password-rsa、password-ecc、password- dsa、password-sm2、password-x509v3-rsa和all认证方式登录到SSH服务器端进行文 件的访问。 图1-73 通过SFTP访问其他设备文件组网图 本例中的Interface1代表接口GigabitEthernet0/0/0。


#### 1.8.14.10 通过SFTP访问其他设备文件配置示例（ECC认证方式）

在本示例中，通过在SFTP客户端和SSH服务器端生成本地密钥对，在SSH服务器端生 成ECC公钥、并为用户绑定该ECC公钥，实现SFTP客户端连接SSH服务器。 组网需求 SFTP建立在SSH连接的基础之上，远程用户可以安全地登录设备，进行文件管理和文 件传送等操作，为数据传输提供了更高的安全保障。 同时，由于设备提供了SFTP客户 端功能，可以从本设备安全登录到远程SSH服务器上，进行文件的安全传输。

如图1-74所示，SSH服务器端SFTP服务使能后，SFTP客户端可以通过RSA、DSA、 ECC、SM2、x509v3-ssh-rsa、password、password-rsa、password-ecc、password- dsa、password-sm2、password-x509v3-rsa和all认证方式登录到SSH服务器端进行文 件的访问。 图1-74 通过SFTP访问其他设备文件组网图 本例中的Interface1代表接口GigabitEthernet0/0/0。

配置思路 采用如下思路配置SFTP客户端连接SSH服务器的示例： 在SSH服务器上配置用户client001和client002，分别使用不同的认证方式登录 SSH服务器。 分别在SFTP客户端Client002和SSH服务器端生成本地密钥对，并为用户client002 绑定SSH客户端的ECC公钥，实现客户端登录服务器端时，对客户端进行验证。 SSH服务器端SFTP服务使能。 配置SSH用户的服务方式和授权目录。

用户client001和client002分别以SFTP方式登录SSH服务器，实现访问服务器上的 文件。


#### 1.8.14.11 通过SFTP访问其他设备文件配置示例（SM2认证方式）

在本示例中，通过在SFTP客户端和SSH服务器端生成本地密钥对，在SSH服务器端生 成SM2公钥、并为用户绑定该SM2公钥，实现SFTP客户端连接SSH服务器。 组网需求 SFTP建立在SSH连接的基础之上，远程用户可以安全地登录设备，进行文件管理和文 件传送等操作，为数据传输提供了更高的安全保障。 同时，由于设备提供了SFTP客户 端功能，可以从本设备安全登录到远程SSH服务器上，进行文件的安全传输。

如图1-75所示，SSH服务器端SFTP服务使能后，SFTP客户端可以通过RSA、DSA、 ECC、SM2、x509v3-ssh-rsa、password、password-rsa、password-ecc、password- dsa、password-sm2、password-x509v3-rsa和all认证方式登录到SSH服务器端进行文 件的访问。 图1-75 通过SFTP访问其他设备文件组网图 本例中的Interface1代表接口GigabitEthernet0/0/0。

配置思路 采用如下思路配置SFTP客户端连接SSH服务器的示例： 在SSH服务器上配置用户client001和client002，分别使用不同的认证方式登录 SSH服务器。


#### 1.8.14.12 配置SSH服务器支持其他端口号访问的示例

在本示例中，通过设定SSH服务器端的侦听端口号为其他端口号，实现只有合法的用 户才能建立SSH连接。 组网需求 SSH协议的标准侦听端口号为22，如果攻击者不断访问标准端口，将会使带宽和服务 器性能不断下降，从而导致其他正常用户无法访问。 设定SSH服务器端的侦听端口号为其他端口号，攻击者并不知道SSH侦听端口号的更 改，仍然发送标准端口号22的socket连接，SSH服务器检测发现请求连接端口号不是 侦听的端口号，就不建立socket连接。

这样只有合法的用户采用SSH服务器设定的非标准侦听端口才能建立socket连接，进行 SSH协议的版本号协商、算法协商及会话密钥生成、认证、会话请求、会话阶段等过 图1-76 配置SSH服务器支持其他端口号访问组网图 本例中的Interface1代表接口GigabitEthernet0/0/0。 配置思路 采用如下的思路配置SSH服务器支持其他端口号访问的示例： 在SSH服务器上配置用户client001和client002，分别使用不同的认证方式登录 SSH服务器。


#### 1.8.14.13 配置公网SSH客户端访问私网SSH服务器的示例

配置公网SSH客户端访问私网SSH服务器的示例。 在本示例中，通过配置公网用户的 SSH相关属性信息，实现公网用户分别以STelnet方式和SFTP方式访问私网设备。 组网需求 如图1-77所示，作为SSH客户端的设备PE1位于MPLS骨干网中，作为SSH服务器的CE1 位于AS号为65410的私网中。 公网用户可以通过PE1安全地访问和管理私网设备CE1。

图1-77 配置公网SSH客户端访问私网SSH服务器组网图 本例中的Interface1，Interface2，Interface3分别代表接口GE1/0/1、GE2/0/1和GE1/0/2。 配置思路 采用如下的思路配置SSH支持私网访问： 在作为SSH客户端的PE设备上配置VPN实例，实现将CE接入PE。 在PE与CE之间建立EBGP对等体关系，引入VPN路由。


#### 1.8.14.14 通过SCP访问其他设备文件配置示例

在本示例中，通过登录SCP服务器，实现从SCP服务器中下载文件至客户端。


#### 1.8.14.15 通过HTTP访问其他设备配置示例

在本示例中，通过从HTTP客户端登录HTTP服务器，实现从HTTP服务器中下载证书。 组网需求 当HTTP客户端需要进行从HTTP服务器端下载证书，可以使用HTTP协议。如图1-79所 示，HTTP客户端的设备和HTTP服务器之间路由可达，用户可通过从HTTP客户端登录 HTTP服务器，实现从HTTP服务器中下载证书到客户端。 HTTP服务器支持SSL策略，为了提高数据传输的安全性，建议HTTP客户端配置SSL策


## 1.9 ZTP配置

设备可以通过零配置自动部署ZTP（Zero Touch Provisioning）实现空配置下的上电自 动部署。 背景信息 通过配置ZTP，可以实现空配置设备上电自动部署，在大规模部署网络设备时可通过 ZTP提高部署效率。 VS模式下，该特性仅在Admin-VS支持。


### 1.9.1 ZTP概述

ZTP是指空配置设备上电启动时采用的一种自动加载版本文件（包括系统软件、配置文 件、补丁文件）的功能。 在部署网络设备时，设备硬件安装完成后，需要管理员到安装现场对设备进行软件调 试。当设备数量较多、分布较广时，管理员需要在每一台设备上进行手工配置，既影 响了部署的效率，又需要较高的人力成本。 设备运行ZTP功能，可以从文件服务器获取版本文件并自动加载，实现设备的免现场配 置、部署，从而降低人力成本，提升部署效率。 实现设备的免现场配置、部署，降低人力成本，提升部署效率。


### 1.9.2 ZTP配置注意事项

特性限制 表1-42 本特性的使用限制 特性限制 涉及产品 ZTPv4不支持QinQ接口VLAN学习，请合理规 NE40E NE40E-X16C/ NE40E-X8C/ NE40E-X8A/ NE40E-X3A/ NE40E-X16A/ NetEngine 40E- X8AK ZTP运行过程中，禁止配置命令set save- NE40E NE40E-X16C/ configuration、undo set save-configuration。

NE40E-X8C/ NE40E-X8A/ 否则：1、会影响ZTP正常运行 2、用户配置可能 NE40E-X3A/ 失效。 NE40E-X16A/ NetEngine 40E- X8AK 评估开局阶段周期，参照周期来设置DHCP服务 NE40E NE40E-X16C/ 器的地址租期，租期过短会导致上线失败。 建议 NE40E-X8C/ 为DHCP服务器地址设置长周期。

NE40E-X8A/ NE40E-X3A/ NE40E-X16A/ NetEngine 40E- X8AK ZTP运行过程中： NE40E NE40E-X16C/ NE40E-X8C/ 1、禁止配置mtp assistant disable和assistant NE40E-X8A/ scheduler suspend命令关闭OPS内置脚本维护助 NE40E-X3A/ NE40E-X16A/ 2、禁止配置undo enable\\(OPS视图\\)关闭OPS功 NetEngine 40E- X8AK 否则影响ZTP功能。

同一台设备不可同时和两台及以上DHCP服务器 NE40E NE40E-X16C/ 在一个广播域中，否则ZTP功能失效。 NE40E-X8C/ NE40E-X8A/ NE40E-X3A/ NE40E-X16A/ NetEngine 40E- X8AK


### 1.9.3 配置通过DHCP实现ZTP自动部署

通过DHCP完成ZTP实现自动部署以降低人力成本，提升部署效率。 应用环境 在部署网络设备时，设备硬件安装完成后，需要管理员到安装现场对设备进行软件调 试。当设备数量较多、分布较广时，管理员需要在每一台设备上进行手工配置，既影 响了部署的效率，又需要较高的人力成本。 设备运行ZTP功能，通过DHCP实现自动部署，实现设备的免现场配置、部署，从而降 低人力成本，提升部署效率。 前置任务 在配置ZTP之前，需要完成以下任务： DHCP服务器、文件服务器到待配置设备的网关间路由可达。 确保待配置设备中没有启动配置文件。 编辑中间文件


#### 1.9.3.1 编辑中间文件

操作步骤 步骤1 中间文件可以是ini文件、cfg文件或Python脚本文件。其中，ini文件和cfg文件的使用 要求低且配置简单，Python脚本文件对用户要求高，所以推荐首次使用ZTP的用户选 择ini文件或者cfg文件作为中间文件。文件格式请参见ini格式的中间文件、cfg格式的 中间文件或Python格式的中间文件。 ----结束


#### ******* 配置DHCPv4 Server和DHCPv4 Relay

背景信息 需要运行ZTP的设备在上电之前，须先部署DHCPv4服务器，以确保作为DHCP客户端 的空配置设备能正常获取到IP地址、网关及中间文件服务器地址、中间文件名称等信


#### ******* 配置DHCPv6 Server和DHCPv6 Relay

背景信息 需要运行ZTP的设备在上电之前，须先部署DHCPv6服务器，以确保作为DHCP客户端 的空配置设备能正常获取到IP地址、网关及中间文件服务器地址、中间文件名称等信 运行ZTP的设备进入DHCPv6 Solicit阶段后，在发送DHCPv6 Solicit消息时会携带 DHCPv6 option 6，DHCPv6 option 6用来携带客户端请求的选项代码。 DHCPv6服务器上需配置的Options字段见表1-44。

表1-44 Options字段说明 Option编号 是否可选 Option作用 申请的IA地址，IPv6地址以及生存期。 中间文件路径，中间文件的名称为\\*. ini、\\*. py或 \\*. cfg。

中间文件名格式如下： tftp://hostname/path/filename ftp://\\[username\\[:password\\]@\\]hostname/ path/filename sftp://\\[username\\[:password\\]@\\]hostname/ path/filename


#### ******* 配置文件服务器

背景信息 文件服务器用于存放空配置设备需要下载的文件，包括中间文件、版本文件等。 用户 可以将路由器配置为文件服务器，但由于文件服务器需要占用设备的存储资源，因此 在使用路由器作为文件服务器时，需要考虑存储空间的问题。 所以在ZTP网络中，一般 需要部署第三方服务器，配置的具体方法请参见第三方服务器的操作指导。 用户可以将中间文件和版本文件部署在同一个文件服务器上。 文件服务器可以是 TFTP/FTP/SFTP服务器。 文件服务器与空配置设备的缺省网关之间必须路由可达。

后续处理 配置完文件服务器后，将中间文件、版本文件存放至文件服务器。 为充分保证文件服务器的安全，建议配置的文件服务器用户名唯一，并将其权限设置为只读，防 止被非法修改。 ZTP过程结束后，请关闭相应的文件服务器功能。 上电启动设备


#### 1.9.3.5 上电启动设备

背景信息 上述配置步骤完成后，将待配置设备上电启动。设备将自动下载版本文件并重新启 动，完成自动部署。 操作步骤 步骤1 上电启动设备。 ----结束


#### 1.9.3.6 （可选）加载预配置脚本

背景信息 当设备空配置启动时，在进入ZTP流程之前，如果需要对设备进行预配置命令下发，则 需要设置预配置脚本。 操作步骤 步骤1 根据文件类型和格式要求编辑预配置脚本，文件格式请参见预配置脚本。 步骤2 上传预配置脚本至主控板的存储介质中。 本设备支持FTP，TFTP以及SFTP上传文件，请参考通过FTP访问其他设备的文件，通过TFTP访 问其他设备的文件以及1. 8. 7 通过SFTP访问其他设备的文件操作。 请根据实际情况选择文件上传 方式上传到设备。

步骤3 执行命令set ztp pre-configuration file-name，加载预配置脚本。 若用户希望设备空配置启动时不执行ZTP预配置流程，可以执行命令reset ztp pre- configuration，清空预配置脚本。 步骤4 执行命令display ztp status，可以查看当前设备预配置脚本的配置状态。 在设备软件大包从低版本升级到当前版本时，若设置的启动配置文件为vrpcfg.

zip，加载的预配 置脚本会被执行（若不想执行预配置脚本，可以通过执行命令reset ztp pre-configuration清空 预配置脚本）；当设置其它配置文件时，预配置脚本不会被执行。 ----结束 （可选）配置自动补丁修复


#### ******* （可选）配置自动补丁修复

通过配置ZTP自动补丁修复，可以解决ZTP开局自动部署时遇到的非环境问题（环境问 题如设备初始不稳定、服务器未启动等）。 背景信息 在ZTP开局自动部署过程中，出现非环境问题（环境问题如设备初始不稳定、服务器未 启动等）时，不需要工程师前往站点维修或设备返厂维修，ZTP支持自动补丁修复机 制。 通过人工定位问题根因，联系工程师制作修复补丁，ZTP运行过程中可以自主识 别、设置修复补丁，并重启设备使修复补丁生效。 设备重启后ZTP会再次运行，由于此 时问题已被修复，ZTP可以顺利完成开局部署。

操作步骤 步骤1 将修复补丁信息配置在ZTP的中间文件中，配置详情见自动补丁修复机制。 步骤2 将修复补丁上传到文件服务器中，并将修改后的ZTP中间文件重新上传到文件服务器 步骤3 空配置重启设备，ZTP重新运行，识别中间文件中的修复补丁信息，自动完成修复过


#### 1.9.3.8 开启ZTP功能

背景信息 为了使设备空配置启动时能够自动执行ZTP流程，需要开启设备的ZTP功能。 操作步骤 步骤1 执行命令set ztp enable，配置设备下次空配置启动时执行ZTP流程。 若用户希望设备空配置启动时不执行ZTP流程，也可以使用命令set ztp disable关闭设 备的ZTP功能。 步骤2 执行命令display ztp status，可以查看设备下次空配置启动是否执行ZTP流程。 ----结束 检查配置结果


#### 1.9.3.9 检查配置结果

操作步骤 步骤1 设备启动完成后，登录设备并通过命令display startup查看设备的启动文件是否与要 求的一致。 步骤2 执行命令display ztp status查看设备是否是通过ZTP完成部署。 步骤3 如果设备没有完成自动配置，可以通过设备上保存的ZTP日志查看出错原因。 设备执行ZTP流程的信息会被保存在cfcard:/ztp目录下，文件名为：ztp\\_年月日时分 秒.log。 ----结束 配置举例


### 1.9.4 配置举例

介绍通过DHCP实现自动部署的配置示例，示例中包括组网需求、配置注意事项和配置 思路等。


#### 1.9.4.1 配置通过DHCP实现ZTP自动部署示例

组网需求 如图1-80所示，某网络中新增两台空配置设备RouterA和RouterB，连接到现网设备 RouterC上。RouterC作为RouterA和RouterB的出口网关。RouterC与DHCP服务器、 文件服务器之间路由可达。

