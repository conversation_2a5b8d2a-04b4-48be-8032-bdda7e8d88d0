

命令行编辑功能可以帮助利用某些特定的键进行命令的编辑或者获得帮助。


NE40E 的命令行接口提供基本的命令编辑功能，支持多行编辑，每条命令最大长度为
3100 个字符。


一些常用的编辑功能如 表 **1-3** 所示。


表 **1-3** 编辑功能表

|功能键|功能|
|---|---|
|普通按键|若编辑缓冲区未满，则插入到当前光标位置，并向右移动<br>光标。|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 19


HUAWEI NetEngine40E
配置指南 1 基础配置

##### 后续处理

|功能键|功能|
|---|---|
|退格键BackSpace|删除光标位置的前一个字符，光标左移，若已经到达命令<br>首，则系统无响应。|
|上光标键↑或<Ctrl_P>|访问上一条历史命令。如果还有更早的历史命令，则取出<br>上一条命令。|
|下光标键↓或<Ctrl_N>|访问下一条历史命令。如果还有更新的历史命令，则取出<br>下一条命令，否则清空命令。|
|Tab键|输入不完整的关键字后按下Tab键，系统自动执行部分帮<br>助：<br>●如果与之匹配的关键字唯一，则系统用此完整的关键字<br>替代原输入并换行显示，光标距词尾空一格；<br>●对于不匹配或者匹配的关键字不唯一的情况，首先显示<br>前缀，继续按Tab键循环翻词，此时光标距词尾不空<br>格，按空格键输入下一个单词；<br>●如果输入错误关键字，按Tab键后，换行显示，输入的<br>关键字不变。|



终端将用户键入的历史命令自动保存，即记录用户在键盘上的任何输入，以“ Enter ”
键为一条记录。执行 **display history-command all-users** 或 **display history-**
**command** 命令可以查看用户最近执行过的历史命令，便于用户查找信息。执行 **reset**
**history-command all-users** 或 **reset history-command** 命令可以清除历史命令记
录。
