

用户登录 FTP 服务器后，可以通过 FTP 文件操作命令进行文件操作，包括配置文件传输
方式、查看 FTP 命令在线帮助、上传下载文件、管理目录、管理文件等。

##### 操作步骤


步骤 **1** 根据服务器端 IP 地址类型不同，进行如下操作：


       - 执行命令 **ftp** [ [ **-a** source-ip-address | **-i** { interface-type interface-number |
interface-name } ] host-ip [ port-number ] [ **vpn-instance** vpn-instancename ] | **public-net** ] ，设备使用 IPv4 地址与 FTP 服务器建立连接，进入 FTP 客户
端视图。


       - 执行命令 **ftp** **ipv6** [ **-a** source-ip6 ] host-ipv6-address [ [ **vpn-instance** ipv6vpn-instance-name ] | **public-net** ] [ **-oi** { interface-type interface-number |
interface-name } ] [ port-number ] ，设备使用 IPv6 地址与 FTP 服务器建立连接，
进入 FTP 客户端视图。


步骤 **2** 根据需要，执行如 表 **1-40** 中的一个或多个操作。


表 **1-40** 文件操作








|文件操作|Col2|操作|
|---|---|---|
|管理文<br>件操作|配置传输文件的<br>文件类型|●执行命令**ascii**设置传输的数据类型为ASCII格式。<br>●执行命令**binary**设置传输的数据类型为二进制模<br>式。<br>FTP传输模式由客户端进行选择，系统默认ASCII方<br>式。|
|管理文<br>件操作|配置传输文件的<br>连接模式|●执行命令**passive**设置数据传输的方式为被动方<br>式。<br>●执行命令**undo passive**设置数据传输的方式为主动<br>方式。|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 248


HUAWEI NetEngine40E
配置指南 1 基础配置

|文件操作|Col2|操作|
|---|---|---|
||上传文件|●执行命令**put** local-filename [ remote-<br>filename ]，从本地设备上传文件至远端服务器。<br>●执行命令**mput** local-filenames，从本地设备上传<br>多个文件至远端服务器。<br>说明<br>用户也可在用户视图下执行如下命令将本地文件上传到FTP<br>服务器。<br>●<br>基于IPv4网络<br>执行命令**ftp client-transfle** **put** [**-a** source-ipv4 |**-i**<br>{ interface-type interface-number | interface-name } ]<br>**host-ip** ipv4-address [**port** portnumber ] [**vpn-**<br>**instance** vpn-instancename |**public-net** ]**username**<br>user-name**sourcefle** localfilename [**destination**<br>remotefilename ]<br>●<br>基于IPv6网络<br>执行命令**ftp client-transfle** **put** **ipv6** [**-a** source-<br>ipv6 ]**host-ip** ipv6-address [ [**vpn-instance** ipv6-vpn-<br>name ] |**public** ] [**port** port-number ]**username**<br>username**sourcefle** local-filename [**destination**<br>remote-filename ]|
||下载文件|●执行命令**get** remote-filename [ local-<br>filename ]，从远端服务器下载文件并存储在本<br>地。<br>●执行命令**mget** remote-filenames，从远端服务器<br>下载多个文件并存储在本地。<br>说明<br>用户也可在用户视图下执行如下命令将FTP服务器上的文件<br>下载到本地。<br>●<br>基于IPv4网络<br>执行命令**ftp client-transfle** **get** [**-a** source-ipv4 |**-i**<br>{ interface-type interface-number | interface-name } ]<br>**host-ip** ipv4-address [**port** portnumber ] [**vpn-**<br>**instance** vpn-instancename |**public-net** ]**username**<br>user-name**sourcefle** localfilename [**destination**<br>remotefilename ]<br>●<br>基于IPv6网络<br>执行命令**ftp client-transfle** **get** **ipv6** [**-a** source-<br>ipv6 ]**host-ip** ipv6-address [ [**vpn-instance** ipv6-vpn-<br>name ] |**public** ] [**port** port-number ]**username**<br>username**sourcefle** local-filename [**destination**<br>remote-filename ]|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 249


HUAWEI NetEngine40E
配置指南 1 基础配置








|文件操作|Col2|操作|
|---|---|---|
||使能系统的提示<br>功能|●如果在客户端视图下执行命令**prompt**使能文件传<br>输提示功能，则用户在进行文件上传或下载时，系<br>统会提示是否确认执行此操作。<br>●如果在客户端视图下再次执行命令**prompt**，则去<br>使能文件传输提示功能。<br>说明<br>本命令只应用在使用**mput**命令和**mget**命令的多文件传<br>输场景中。其中对于使用**mget**命令的场景，如果被传输<br>的文件已经在本地存在，无论系统提示的功能被使能还<br>是被禁止，系统都会提示用户是否覆盖本地已存在的文<br>件。|
||打开FTP<br>verbose开关|执行命令**verbose**。<br>打开verbose开关，将显示所有的FTP响应信息。在文<br>件传送完成后，将显示与传输速率相关的统计信息。|
||在FTP服务器上<br>文件内容的后面<br>添加本地文件内<br>容|执行命令**append** local-filename [ remote-<br>filename ]。<br>如果FTP服务器上没有remote-filename参数定义的文<br>件，则新建该文件，并将本地文件内容添加到新建文<br>件的尾部。|
||删除服务器上文<br>件|执行命令**delete** remote-filename。|
|管理目<br>录操作|改变远程FTP服<br>务器上的工作路<br>径|执行命令**cd** pathname。|
|管理目<br>录操作|改变FTP服务器<br>端的工作路径到<br>上一级目录|执行命令**cdup**。|
|管理目<br>录操作|显示FTP服务器<br>端工作路径|执行命令**pwd**。|
|管理目<br>录操作|显示目录中的文<br>件和子目录的列<br>表|执行命令**dir** [ remote-directory [ local-<br>filename ] ]。<br>如果指定远程文件时没有指定路径名称，那么系统将<br>在用户的授权目录下搜索指定的文件。|
|管理目<br>录操作|显示FTP服务器<br>上指定远程目录<br>或文件的信息|执行命令**ls** [ remote-directory [ local-filename ] ]。|
|管理目<br>录操作|显示或者改变<br>FTP客户端的工<br>作路径|执行命令**lcd** [ directory ]。<br>与**pwd**不同的是，**lcd** [ directory ]命令执行后显示的<br>是FTP客户端的本地工作路径，而**pwd**显示的则是远<br>端FTP服务器的工作路径。|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 250


HUAWEI NetEngine40E
配置指南 1 基础配置









|文件操作|Col2|操作|
|---|---|---|
||在FTP服务器上<br>创建目录|执行命令**mkdir** remote-directory。<br>创建的目录可以为字母和数字等的组合，但不可以为<br><、>、?、\、：等特殊字符。|
||在FTP服务器上<br>删除目录|执行命令**rmdir** remote-directory。|
|查看FTP命令的在线帮助|查看FTP命令的在线帮助|执行命令**remotehelp** [ command ]。|
|使能FTP客户端断点续传服<br>务|使能FTP客户端断点续传服<br>务|在系统视图下执行命令**ftp client resumable-**<br>**transfer enable**。|


**----**
结束
