

在本示例中，通过配置 VTY 用户界面以及用户登录参数，实现从客户端登录设备。

##### 组网需求


用户可以通过终端登录到其它网段上的设备，进行远程维护。


图 **1-28** 配置通过 Telnet 登录组网图


说明


本例中的 Interface1 代表接口 GigabitEthernet0/0/0 。

##### 配置思路


1. 建立物理连接。


2. 配置 P1 管理网口的 IP 地址。


3. 配置 VTY 用户界面的相关参数，包括呼入呼出限制。


4. 配置登录用户的相关参数。

##### 数据准备


为完成此配置举例，需准备如下的数据：


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 108


HUAWEI NetEngine40E
配置指南 1 基础配置


       - P1 管理网口的 IP 地址。


       - VTY 用户界面的最大个数为 10 。


       - 禁止登录的用户登录其他设备的 ACL 号为 3001 。


       - VTY 用户界面断开连接的时间 20 分钟。


       - VTY 用户界面的终端屏幕每屏显示的行数为 30 。


       - VTY 用户界面的历史命令缓冲区大小为 20 。


       - Telnet 访问的用户信息（验证方式为 AAA ，用户名为“ huawei ”，密码为
“ YsHsjx_202206 ”）。

##### 配置注意事项


当网络所处环境不足够安全时，我们建议选择安全的协议。安全协议举例参见：
*********** 配置用户通过 **STelnet** 登录系统示例 。

##### 操作步骤


步骤 **1** 在 PC 端和设备端分别和网络连接


步骤 **2** 配置登录地址


<HUAWEI> **system-view**

[ ~ HUAWEI] **sysname P1**

[*HUAWEI] **commit**

[ ~ P1] **interface GigabitEthernet0/0/0**

[ ~ P1-GigabitEthernet0/0/0] **undo shutdown**

[*P1-GigabitEthernet0/0/0] **ip address ************* ***************

[*P1-GigabitEthernet0/0/0] **commit**

[ ~ P1-GigabitEthernet0/0/0] **quit**


步骤 **3** 使能 Telnet 服务


[ ~ P1] **telnet server enable**

[*P1] **telnet server-source -i GigabitEthernet0/0/0**

[*P1] **commit**


步骤 **4** 在设备端配置 VTY 用户界面


# 配置 VTY 用户界面的最大个数。


[ ~ P1] **user-interface maximum-vty 10**

[*P1] **commit**


# 配置登录的用户禁止登录其他设备。


[ ~ P1] **acl 3001**

[*P1-acl4-advance-3001] **rule deny tcp source any destination-port eq telnet**

[*P1-acl4-advance-3001] **quit**

[*P1] **user-interface vty 0 9**

[*P1-ui-vty0-9] **acl 3001 outbound**


# 配置 VTY 用户界面的终端属性。


[*P1-ui-vty0-9] **shell**

[*P1-ui-vty0-9] **idle-timeout 20**

[*P1-ui-vty0-9] **screen-length 30**

[*P1-ui-vty0-9] **history-command max-size 20**


# 配置 VTY 用户界面的用户验证方式。


[*P1-ui-vty0-9] **authentication-mode aaa**

[*P1-ui-vty0-9] **commit**

[ ~ P1-ui-vty0-9] **quit**


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 109


HUAWEI NetEngine40E
配置指南 1 基础配置


步骤 **5** 在设备端配置登录用户参数


# 配置登录验证方式。


[ ~ P1] **aaa**

[*P1-aaa] **local-user huawei password**
Please configure the password (8-128)
Enter Password:
Confirm Password:


说明


          - 不选择 **cipher** 或 **irreversible-cipher** 关键字时，密码以交互式输入，系统不回显密码。


输入的密码为字符串形式，区分大小写，开启用户账户安全策略时，取值范围是 8 ～ 128 。关
闭用户账户安全策略时，长度范围是 1 ～ 128 。开启用户账户安全策略时，密码不能与用户名
及其用户名反向字符串相同，且密码必须包括大写字母、小写字母、数字及特殊字符。


特殊字符不包括“？”和空格。但是，当输入的密码两端使用双引号时，可在密码中间输入
空格。


–
如果使用双引号设置带空格密码，双引号之间不能再使用双引号。


–
如果使用双引号没有设置带空格密码，双引号之间可以再使用双引号。


例如， "Aa 123"45"" 为不合法密码， "Aa123"45"" 为合法密码。


          - 选择 **cipher** 关键字时，密码可以以简单形式输入，也可以以密文形式输入。


密码以简单形式输入，要求与不选择 **cipher** 关键字时一样。密码以简单形式输入，系统会回
显简单形式的密码，存在安全风险，因此建议使用交互式输入密码。


无论是简单输入还是密文输入，配置文件中都以密文形式体现。


          - 选择 **irreversible-cipher** 关键字时，密码可以以简单形式输入，也可以以不可逆密文形式输
入。


密码以简单形式输入，要求与不选择 **irreversible-cipher** 关键字时一样。


无论是简单输入还是不可逆密文输入，配置文件中都以密文形式体现。


[*P1-aaa] **local-user huawei service-type telnet**

[*P1-aaa] **local-user huawei level 3**

[*P1-aaa] **commit**

[ ~ P1-aaa] **quit**


步骤 **6** 配置客户端登录


进入 Windows 的命令行提示符执行相关命令，通过 Telnet 方式登录设备。


回车后，在登录窗口输入用户名和密码，验证通过后，出现用户视图的命令行提示
符。至此用户进入了用户视图配置环境。


**----**
结束

##### P1 的配置文件


#

sysname P1
#

acl number 3001
rule 5 deny tcp destination-port eq telnet
#

aaa
local-user huawei password irreversible-cipher $1c$]zV2B\j!z:$hRujV[%/IE|
0MwBQ}5sAX(RdE[oj#5otqG6=@>KK$
local-user huawei service-type telnet
local-user huawei level 3

#
interface GigabitEthernet0/0/0
undo shutdown
ip address ************* *************
#


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 110


HUAWEI NetEngine40E
配置指南 1 基础配置


telnet server enable
telnet server-source -i GigabitEthernet0/0/0
#
user-interface maximum-vty 10
#
user-interface vty 0 9
authentication-mode aaa
history-command max-size 20
idle-timeout 20 0
screen-length 30
acl 3001 outbound

#

return
