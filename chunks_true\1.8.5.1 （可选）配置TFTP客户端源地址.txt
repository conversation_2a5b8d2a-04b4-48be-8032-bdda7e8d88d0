

用户可以配置 TFTP 客户端的源地址信息，从指定的客户端源地址建立 TFTP 连接，保证
安全性。

##### 背景信息


用户可以在设备上指定某一接口，为此接口配置 IP 地址，然后使用该 IP 地址作为 TFTP
连接的源 IP 地址，从而达到进行安全校验的目的。


请在作为 TFTP 客户端的路由器上进行如下的配置。

##### 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **tftp client-source** { **-a** ip-address | **-i** { interface-type interface-number |
interface-name } } 或 **tftp ipv6 client-source** **-a** ipv6-address [ **-vpn-instance** ipv6vpn-instance-name ] ，配置 TFTP 客户端的源地址信息。


说明


参数 interface-type 指定的接口类型必须是 Loopback 接口。


配置了 TFTP 客户端源地址信息后，在服务器端显示的 TFTP 客户端的源地址信息与该步骤中的配
置一致。


步骤 **3** 执行命令 **commit** ，提交配置。


**----**
结束
