

设备上使能重定向功能后，用户可以在 Telnet 客户端通过指定的端口号登录其他远程设
备，并对其进行管理和维护。

##### 背景信息


Telnet 重定向功能只在某些产品上支持，并且只能对 AUX 口配置。


请在设备上进行如下的配置。


说明


该配置过程仅在 Admin-VS 支持。

##### 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **user-interface aux** first-ui-number ，进入 AUX 类型用户界面。


步骤 **3** 执行命令 **undo shell** ，禁止在用户界面上启动终端服务。


步骤 **4** 执行命令 **redirect** ，使能该用户界面的 Telnet 重定向功能。


说明


          - 使能 Telnet 重定向后，指定的端口号就会被分配。 AUX0 的编号为 33 ，指定的端口号则为
2033 或 4033 。 2033 端口只透传数据报文， 4033 端口透传所有 Telnet 报文。


          - 此时可以在 Telnet 客户端通过指定的端口号登录到远程需要管理的设备。


**----**
结束


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 104


HUAWEI NetEngine40E
配置指南 1 基础配置
