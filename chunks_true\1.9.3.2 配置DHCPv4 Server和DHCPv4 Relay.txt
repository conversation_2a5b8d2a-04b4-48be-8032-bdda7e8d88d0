

需要运行 ZTP 的设备在上电之前，须先部署 DHCPv4 服务器，以确保作为 DHCP 客户端
的空配置设备能正常获取到 IP 地址、网关及中间文件服务器地址、中间文件名称等信
息。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 353


HUAWEI NetEngine40E
配置指南 1 基础配置


运行 ZTP 的设备进入 DHCP discover 阶段后，在发送 DHCP discover 消息时会携带 DHCP
option 60 和 61 。 DHCP option 60 (Vendor class identifier) 用来携带设备厂商及型号
信息； DHCP option 61 (Client-identifier) 用来携带设备序列号。


DHCPv4 服务器上需配置的 Options 字段见 表 **1-43** 。


表 **1-43** Options 字段说明

|Option编号|是否可选|Option作用|
|---|---|---|
|1|必选|设置IP地址的子网掩码。|
|3|必选|设置DHCP客户端的出口网关。|
|6|可选|设置DNS服务器的IP地址。当用户设置中间文件服<br>务器的主机名为域名类型时（如<br>“www.ztp.com”），需要部署DNS服务器来将域<br>名转换为相应的IP地址。如果设置的主机名为IP地<br>址，则不需要再部署DNS服务器。|
|66|可选|设置中间文件服务器的主机名。文件服务器可以是<br>TFTP/FTP/SFTP服务器，格式如下：<br>●tftp://hostname<br>●ftp://[username[:password]@]hostname<br>●sftp://<br>[username[:password]@]hostname[:port]<br>其中username、password、port参数为可选项。<br>hostname既可以是域名也可以是IP地址，如果设置<br>的是域名地址，则需要部署DNS服务器。port的取<br>值范围为0～65535，超出范围按照默认端口22处<br>理，仅在SFTP服务器地址为IPv4情况下支持配置端<br>口号。<br>说明<br>当hostname为IP地址时，可不配置文件传输类型，此时<br>默认为TFTP。|
|67|必选|设置中间文件名。中间文件的名称为*.ini、*.py或<br>*.cfg。<br>●中间件文件名长度不要超过64个字符，超过64<br>字符时可能会导致文件下载失败。<br>●中间件文件名中不能包含特殊字符，如：&、<br>>、<、"或者'。<br>●中间文件名格式为：path/filename。其中path<br>可以是不包括文件服务器主机名的相对路径，如<br>“/script/ztp_script.py”，也可以是包括服务器<br>主机名的绝对路径，如“sftp://********/script/<br>ztp_script.py”。若使用相对路径，则需要设置<br>Option 66。|
|150|可选|设置TFTP服务器的IP地址。|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 354


HUAWEI NetEngine40E
配置指南 1 基础配置


说明


ZTP 通过 DHCPv4 申请的 IPv4 地址租期至少为 1 小时且不支持续租。


当待配置设备与 DHCPv4 服务器不在同一网段时，需要配置 DHCP 中继以转发 DHCP 的交互报文。


以下配置步骤以路由器为例，如果选择其他类型设备作为 DHCPv4 服务器或中继，请参
考相应的资料配置。

##### 操作步骤


步骤 **1** 在作为 DHCPv4 服务器的设备上配置地址池，详细配置请参见“配置地址池”，地址池
中 **Option** 编号： 6 、 66 、 67 、 150 的配置请参见“（可选）配置 DHCPv4 自定义选
项”。


步骤 **2** 配置地址池为接入用户分配地址，详细配置请参见“配置本地地址池为接入用户分配
地址示例”。


步骤 **3** （可选）如果组网中存在 DHCP 中继，在作为 DHCP 中继的设备上进行配置，详细配置
请参见“配置 DHCP Relay ”。


**----**
结束
