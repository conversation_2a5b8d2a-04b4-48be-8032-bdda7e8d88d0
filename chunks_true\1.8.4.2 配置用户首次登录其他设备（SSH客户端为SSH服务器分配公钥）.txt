

如果没有使能 SSH 客户端首次认证功能，那么需要在 STelnet 客户端登录 SSH 服务器之
前为服务器分配 RSA 、 DSA 、 SM2 、 ECC 公钥。

##### 背景信息


如果没有使能 SSH 客户端首次认证功能，当 STelnet 客户端第一次登录 SSH 服务器时，
由于对 SSH 服务器的 RSA 、 DSA 、 SM2 或 ECC 公钥有效性检查失败，会导致登录服务器
失败。所以需要在 STelnet 客户端登录 SSH 服务器之前为服务器分配 RSA 、 DSA 、
SM2 、 ECC 公钥。


密钥对由 SSH 服务器创建，密钥对中的公钥传输至 STelnet 客户端后，客户端对公钥进
行编辑，编辑完成后将此公钥再分配给服务器，这样 STelnet 客户端才能通过对 SSH 服
务器有效性检查。


请在作为 SSH 客户端的设备上进行如下的配置。


说明


为了保证更好的安全性，建议不要使用小于 3072 位的 RSA 算法，建议您使用更安全的
RSA_SHA2_256 、 RSA_SHA2_512 认证算法。

##### 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** （可选）执行命令 **ssh client publickey** { **dsa** | **ecc** | **rsa** | **sm2** | **rsa_sha2_256** |
**rsa_sha2_512** } [*] ，使能相应的 SSH 客户端公钥算法。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 239


HUAWEI NetEngine40E
配置指南 1 基础配置


说明


命令中的参数 **dsa** 和 **rsa** 为弱安全算法，不建议使用，如果确实需要使用，请先执行 **undo crypto**
**weak-algorithm disable** 命令使能弱安全算法功能。


步骤 **3** 根据选择的算法执行如下命令：


       - 执行命令 **rsa peer-public-key** key-name ，进入 RSA 公共密钥视图。


       - 执行命令 **dsa peer-public-key** key-name **encoding-type** enc-type ，进入 DSA 公
共密钥视图。


       - 执行命令 **ecc peer-public-key** key-name ，进入 ECC 公共密钥视图。


       - 执行命令 **sm2 peer-public-key** key-name ，进入 SM2 公共密钥视图。


步骤 **4** 执行命令 **public-key-code begin** ，进入公共密钥编辑视图。


步骤 **5** 输入 hex-data ，编辑公共密钥。


键入的公共密钥必须是按公钥格式编码的十六进制字符串，由 SSH 服务器随机生成。


说明


进入公共密钥编辑视图后，即可将服务器上产生的 RSA 、 DSA 、 SM2 、 ECC 公钥传送到客户端。
请采用拷贝粘贴方式将 RSA 、 DSA 、 SM2 、 ECC 公钥配置到作为 SSH 客户端的设备上。


步骤 **6** 执行命令 **public-key-code end** ，退出公共密钥编辑视图。


如果用户配置的公钥字符串中存在非法字符或不符合编码规则，那么将会显示相关提
示信息，且用户配置的密钥将被丢弃，本次配置失败；如果配置的密钥合法，将会保
存到客户公钥链表中。


       - 如果未输入合法的密钥编码 hex-data ，执行本步骤后，将无法生成密钥。


       - 如果 步骤 **2** 中指定的 key-name 已经在其他窗口下被删除，执行本步骤时，系统会
提示错误且直接退到系统视图。


步骤 **7** 执行命令 **peer-public-key end** ，退出公共密钥视图，回到系统视图。


步骤 **8** 根据选择的算法执行如下命令：


       - 执行命令 **ssh client peer** server-name **assign rsa-key** key-name ，为 SSH 服务器
分配 RSA 公钥。


       - 执行命令 **ssh client peer** server-name **assign dsa-key** key-name ，为 SSH 服务
器分配 DSA 公钥。


       - 执行命令 **ssh client peer** server-name **assign ecc-key** key-name ，为 SSH 服务器
分配 ECC 公钥。


       - 执行命令 **ssh client peer** server-name **assign sm2-key** key-name ，为 SSH 服务
器分配 SM2 公钥。


步骤 **9** 执行命令 **commit** ，提交配置。


**----**
结束
