

介绍网管设备上的 SSH 用户与设备通过 VPN 网络通信的配置过程。

##### 组网需求


如 图 **1-48** 所示，网管、 AAA 服务器和设备通过 VPN 网络相连。网管上集成了 SSH 客户
端、 SFTP 服务器等功能，网管设备通过 VPN 网络连接设备后， SSH 客户端使用 SSH 协
议登录设备，和设备之间进行通信。网管设备作为 SFTP 服务器，和设备（ SFTP 客户
端）之间使用 SFTP 协议传输文件。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 133


HUAWEI NetEngine40E
配置指南 1 基础配置


图 **1-48** 配置网管设备上的 SSH 用户与设备通过 VPN 网络通信组网图


说明


本例中 interface1 代表 GE1/0/0 ， interface2 代表 GE2/0/0 ， interface3 代表 GE3/0/0 ，这三个接口
绑定同一个 VPN 实例。

##### 配置注意事项


请确保设备和网管之间路由可达。

##### 配置思路


采用如下思路配置设备使用 SNMPv3 本地用户与网管通过 VPN 网络通信：


1. 配置 VPN 实例。


2. 设备与网管和 HWTACACS 服务器相连的接口绑定 VPN 实例。


3. 配置网管管理设备时使用的默认 VPN 实例。


4. 配置 HWTACACS 服务器。


5. 配置 AAA 本地用户并将接入业务类型配置为 SSH ，认证方式为 HWTACACS 。


6. 配置 SSH 用户及其认证、服务方式。


7. 配置 SNMPv3 USM 用户，允许网管接入。


8. 配置 SFTP 客户端使用 SFTP 协议传输文件。


说明


配置网管管理设备时使用的默认 VPN 实例后，设备上的 HWTACACS 服务器、 TFTP 客户端、 FTP
客户端、 SFTP 客户端、 SCP 客户端、 SNMP 模块、 Info Center 模块、 PM 模块、 IP FPM 模块，如
果没有配置其他 VPN 实例，则都会处于默认 VPN 网络内。如果配置了其他 VPN 实例，则会优先选
择各特性下配置的 VPN 实例。如果需要访问公网，必须使用参数 public-net 。

##### 数据准备


为完成此配置，需要准备如下数据：


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 134


HUAWEI NetEngine40E
配置指南 1 基础配置


       - VPN 实例名称。


       - SNMP 版本。


       - SNMPv3 USM 用户名称和密码。


       - 本地用户的认证方式为 AAA ，用户名为“ sshuser001 ”，服务类型为 SSH ，认证
方式为 HWTACACS 。

##### 操作步骤


步骤 **1** 配置 VPN 实例。


<HUAWEI> **system-view**

[ ~ HUAWEI] **sysname DeviceA**

[*HUAWEI] **commit**

[ ~ DeviceA] **ip vpn-instance vrf1**

[*DeviceA-vpn-instance-vrf1] **ipv4-family**

[*DeviceA-vpn-instance-vrf1-af-ipv4] **route-distinguisher 22:1**

[*DeviceA-vpn-instance-vrf1-af-ipv4] **vpn-target 111:1 both**

[*DeviceA-vpn-instance-vrf1-af-ipv4] **quit**

[*DeviceA-vpn-instance-vrf1] **quit**

[*DeviceA] **commit**


步骤 **2** # 配置接口绑定 VPN 实例。


[ ~ DeviceA] **interface gigabitethernet 1/0/0**

[ ~ DeviceA-GigabitEthernet1/0/0] **undo shutdown**

[*DeviceA-GigabitEthernet1/0/0] **ip binding vpn-instance vrf1**

[*DeviceA-GigabitEthernet1/0/0] **ip-address ******** ***************

[*DeviceA-GigabitEthernet1/0/0] **quit**

[*DeviceA] **interface gigabitethernet 2/0/0**

[*DeviceA-GigabitEthernet2/0/0] **undo shutdown**

[*DeviceA-GigabitEthernet2/0/0] **ip binding vpn-instance vrf1**

[*DeviceA-GigabitEthernet2/0/0] **ip-address ******** ***************

[*DeviceA-GigabitEthernet2/0/0] **quit**

[*DeviceA] **interface gigabitethernet 3/0/0**

[*DeviceA-GigabitEthernet3/0/0] **undo shutdown**

[*DeviceA-GigabitEthernet3/0/0] **ip binding vpn-instance vrf1**

[*DeviceA-GigabitEthernet3/0/0] **ip-address ******** ***************

[*DeviceA-GigabitEthernet3/0/0] **quit**

[*DeviceA] **commit**


步骤 **3** 配置网管管理设备时使用的默认 VPN 实例。


[ ~ DeviceA] **set net-manager vpn-instance vrf1**


说明


执行该命令配置的 VPN 会影响设备上的如下业务模块： TFTP 客户端、 FTP 客户端、 SFTP 客户
端、 SCP 客户端、 Info Center 模块、 SNMP 模块、 PM 模块、 IP FPM 模块，以及 TACACS 模块。如
果需要访问公网，必须使用参数 public-net 。


[*DeviceA] **commit**


步骤 **4** 配置 HWTACACS 服务器。


# 启动 HWTACACS 协议功能并配置 HWTACACS 服务器模板 ht 。


[ ~ DeviceA] **hwtacacs enable**

[*DeviceA] **hwtacacs-server template ht**


# 配置 HWTACACS 主认证和授权服务器 IP 地址和端口。


[*DeviceA-hwtacacs-ht] **hwtacacs-server authentication ******** 49**

[*DeviceA-hwtacacs-ht] **hwtacacs-server authorization ******** 49**


# 配置 HWTACACS 服务器密钥。


[*DeviceA-hwtacacs-ht] **hwtacacs-server shared-key cipher it-is-my-secret123**


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 135


HUAWEI NetEngine40E
配置指南 1 基础配置


[*DeviceA-hwtacacs-ht] **commit**

[ ~ DeviceA-hwtacacs-ht] **quit**


# 进入 AAA 视图。


[ ~ DeviceA] **aaa**


# 配置认证方案 scheme1 ，认证方法为 HWTACACS 认证。


[ ~ DeviceA-aaa] **authentication-scheme scheme1**

[*DeviceA-aaa-authen-scheme1] **authentication-mode hwtacacs**

[*DeviceA-aaa-authen-scheme1] **commit**

[ ~ DeviceA-aaa-authen-scheme1] **quit**


# 配置授权方案 scheme2 ，授权方法为 HWTACACS 授权。


[ ~ DeviceA-aaa] **authorization-scheme scheme2**

[*DeviceA-aaa-author-scheme2] **authorization-mode hwtacacs**

[*DeviceA-aaa-author-scheme2] **commit**

[ ~ DeviceA-aaa-author-scheme2] **quit**


# 配置 huawei 域，在域下采用 HWTACACS 认证方案 scheme1 、 HWTACACS 授权方案
scheme2 、 HWTACACS 模板 ht 。


[ ~ DeviceA-aaa] **domain huawei**

[*DeviceA-aaa-domain-huawei] **authentication-scheme scheme1**

[*DeviceA-aaa-domain-huawei] **authorization-scheme scheme2**

[*DeviceA-aaa-domain-huawei] **hwtacacs-server ht**

[*DeviceA-aaa-domain-huawei] **commit**

[ ~ DeviceA-aaa-domain-huawei] **quit**


步骤 **5** 创建 AAA 本地用户“ sshuser001 ”，接入类型为 SSH ，认证方式为 HWTACACS 。


# 配置本地用户 sshuser001 ，域是 huawei 。配置完成后， sshuser001 将使用 huawei 域
下的认证授权方式。


[ ~ DeviceA-aaa] **local-user sshuser001@huawei password**
Please configure the password (8-128)
Enter Password:
Confirm Password:

[*DeviceA-aaa] **local-user sshuser001@huawei service-type ssh**

[*DeviceA-aaa] **commit**

[ ~ DeviceA-aaa] **quit**


步骤 **6** 配置 SSH 用户的认证和服务方式。


[*DeviceA] **ssh authentication-type default password**

[*DeviceA] **ssh user sshuser001 service-type stelnet snetconf**

[*DeviceA] **commit**


步骤 **7** 在 SSH 服务器端使能 STelnet 功能。


[ ~ DeviceA] **stelnet server enable**

[*DeviceA] **ssh server-source** **-i gigabitethernet 3/0/0**

[*DeviceA] **commit**


步骤 **8** 配置 SNMPv3 USM 用户，允许网管接入。


# 使能 SNMP Agent 。


[*DeviceA] **snmp-agent**

[*DeviceA] **commit**


# 配置 SNMP 版本为 SNMPv3 。


[ ~ DeviceA] **snmp-agent sys-info version v3**

[*DeviceA] **commit**


# 配置 MIB 视图。


[ ~ DeviceA] **snmp-agent mib-view included iso iso**


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 136


HUAWEI NetEngine40E
配置指南 1 基础配置


[*DeviceA] **commit**


# 配置用户组和用户，对用户的数据进行认证和加密。


[ ~ DeviceA] **snmp-agent group v3 admin privacy write-view iso notify-view iso read-view iso**

[*DeviceA] **snmp-agent usm-user v3 nms-admin group admin**

[*DeviceA] **snmp-agent usm-user v3 nms-admin authentication-mode sha**
Please configure the authentication password (10-255)
Enter Password:
Confirm Password:

[*DeviceA] **snmp-agent usm-user v3 nms-admin privacy-mode aes128**
Please configure the privacy password (10-255)
Enter Password:
Confirm Password:

[*DeviceA] **commit**


# 配置告警功能。


[ ~ DeviceA] **snmp-agent target-host trap address udp-domain ******** vpn-instance vrf1 params**
**securityname nms-admin v3 privacy**

[*DeviceA] **snmp-agent trap enable**

[*DeviceA] **commit**


步骤 **9** 设备作为 SFTP 客户端，与 SFTP 服务器之间通过 VPN 网络传输文件。


网管设备作为 SFTP 服务器，其配置请参考网管设备服务器配置。设备与网管之间进行
文件传输时使用的用户名和密码必需和 SFTP 服务器保持一致。

[ ~ DeviceA] **ssh client first-time enable**

[*DeviceA] **commit**

[ ~ DeviceA] **sftp client-transfile get -a ******** host-ip ******** username sshuser002 password**
**YsHsjx_202206 sourcefile aaa.txt**


步骤 **10** 验证配置结果。


配置完成后，可以执行下面的命令，检查配置内容是否生效。


# 查看 SNMP 版本。


<DeviceA> **display snmp-agent sys-info version**
The contact person for this managed node:
R&D Beijing, Huawei Technologies co.,Ltd.


The physical location of this node:
Beijing China


SNMP version running in the system:
SNMPv3


# 查看本地用户信息。


<DeviceA> **display snmp-agent usm-user**
User name: nms-admin,
Engine ID: 800007DB0300259E0370C3 active
Authentication Protocol: sha
Privacy Protocol: aes128
Group-name: admin
State: Active


**----**
结束

##### 配置文件


       - DeviceA 配置文件


#
sysname DeviceA
#

hwtacacs enable

#


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 137


HUAWEI NetEngine40E
配置指南 1 基础配置


ip vpn-instance vrf1
ipv4-family
route-distinguisher 22:1
apply-label per-instance
vpn-target 111:1 export-extcommunity
vpn-target 111:1 import-extcommunity
#
hwtacacs-server template ht
hwtacacs-server authentication ******** vpn-instance vrf1
hwtacacs-server authorization ******** vpn-instance vrf1
hwtacacs-server shared-key cipher %^%#x@ZaCImt|
X79[^A&]DEYC6[>U]OD(8n&BVHvsu2R{=zVSySB'|H[;I`|ef#%^%#
#

aaa
local-user sshuser001@huawei password irreversible-cipher $1c$\h[;D"`M79$GN]A=y;*4EFG
%t>vIJI=rJvxWe/V%Xbd;(J+AzC+$
local-user sshuser001@huawei service-type ssh
#

authentication-scheme scheme1

authentication-mode hwtacacs

#

authorization-scheme scheme2

authorization-mode hwtacacs

#
accounting-scheme default0
#
accounting-scheme default1
#

domain huawei

authentication-scheme scheme1

authorization-scheme scheme2

hwtacacs-server ht

#
interface GigabitEthernet1/0/0
undo shutdown
ip binding vpn-instance vrf1
ip-address ******** *************
interface GigabitEthernet2/0/0
undo shutdown
ip binding vpn-instance vrf1
ip-address ******** *************
interface GigabitEthernet3/0/0
undo shutdown
ip binding vpn-instance vrf1
ip-address ******** *************
#

snmp-agent
snmp-agent local-engineid 800007DB0300313D6A1FA0
#
snmp-agent sys-info version v3
snmp-agent group v3 admin privacy write-view iso notify-view iso read-view iso
snmp-agent target-host trap address udp-domain ******** vpn-instance vrf1 params securityname
nms-admin v3 privacy
#
snmp-agent mib-view included iso iso
snmp-agent usm-user v3 nms-admin group admin
snmp-agent usm-user v3 nms-admin authentication-mode sha %#%##/L&Fd]S.!i*S7<\jCh2DkfkE4+:<
%Wap|8zZWwPL+[a>h$wy>VJsp9(L{%B%#%#
snmp-agent usm-user v3 nms-admin privacy-mode aes128 %#
%#CM-]HDuhH6VX)**J<186nf({M823f(0Z73++7(A#%,1jODj}D>_HS>W,'Ss=%#%#
#

stelnet server enable
ssh server-source -i gigabitethernet 3/0/0
ssh user sshuser001
ssh authorization-type default password
ssh user sshuser001 service-type stelnet snetconf
#
ssh client first-time enable


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 138


HUAWEI NetEngine40E
配置指南 1 基础配置


#

return
