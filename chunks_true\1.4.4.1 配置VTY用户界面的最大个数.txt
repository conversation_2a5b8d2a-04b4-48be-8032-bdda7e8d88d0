

用户可以配置同时登录设备的 VTY 类型用户界面的最大个数，实现对登录用户量的限
制。

##### 背景信息


VTY 用户界面的最大个数是当前登录设备的 Telnet 用户和 SSH 用户的总和。


须知


当配置 VTY 用户界面最大个数为 0 时，任何用户（包括网管用户）都无法通过 VTY 登录
设备。

##### 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **user-interface maximum-vty** number ，配置可以同时登录到设备的 VTY 类
型用户界面的最大个数。


       - 如果配置的 VTY 用户接口的最大数小于当前的最大数量，不会影响当前在线用
户，也不需要其它配置。


       - 如果配置的 VTY 用户接口的最大数量大于当前的最大数量，需要为新增加的用户
接口配置验证方式和密码。因为对于新增用户接口系统默认为使用密码验证。


例如：当前允许最多 5 个 VTY 用户同时在线，现在配置允许 18 个 VTY 用户同时在
线，那么 VTY 用户接口 5 ～ 17 就需要使用 **authentication-mode** 命令配置验证方式
和密码，配置如下：


<HUAWEI> **system-view**

[ ~ HUAWEI] **user-interface maximum-vty 18**


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 42


HUAWEI NetEngine40E
配置指南 1 基础配置


[*HUAWEI] **user-interface vty 5 17**

[*HUAWEI-ui-vty5-17] **authentication-mode password**

[*HUAWEI-ui-vty5-17] **set authentication-mode password**
Please configure the login password (8-16)
Enter Password:
Confirm Password:


说明


设置的密码必须满足以下要求：


–
密码采取交互式输入，系统不回显输入的密码。


– 输入的密码为字符串形式，区分大小写，长度范围是 8 ～ 16 。输入的密码至少包含两种
类型字符，包括大写字母、小写字母、数字及特殊字符。


–
特殊字符不包括“？”和空格。但是，当输入的密码两端使用双引号时，可在密码中间
输入空格。

#### ▪ 如果使用双引号设置带空格密码，双引号之间不能再使用双引号。 ▪ 如果使用双引号没有设置带空格密码，双引号之间可以再使用双引号。


例如， "Aa 123"45"" 为不合法密码， "Aa123"45"" 为合法密码。


配置文件中将以密文形式体现设置的密码。


步骤 **3** 执行命令 **commit** ，提交配置。


**----**
结束
