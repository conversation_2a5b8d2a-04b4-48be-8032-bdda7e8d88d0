

用户可以比较当前的配置文件和下次启动的配置文件或者指定的配置文件是否一致。

##### 背景信息


说明


所比较的配置文件必须以“ .cfg ” 或“ .zip ”作为扩展名。

##### 操作步骤


步骤 **1** 执行命令 **compare configuration** [ configuration-file ] ，比较当前的配置与下次启动
的配置文件或者指定的配置文件的内容是否一致。


当用户完成一系列操作后，可通过本命令从当前配置的首行开始，比较当前配置与下
次启动的配置文件或指定的配置文件内容是否一致，以便用户决定是否需要将当前的
配置保存，设置为下次启动的配置文件。


该命令在比较出不同之处时，将从不同处所在的行开始，分别对当前配置和下次启动
的配置文件或指定的配置文件，显示 9 行文件内容，如果该不同之处到文件末尾不足 9
行，将显示到文件尾为止。


**----**
结束
