#!/usr/bin/env python3
"""
深入调查PDF结构，找出真正的1.1前言内容
"""

import fitz
import pdfplumber
from main2 import PDFToMarkdownConverter

def investigate_page_10():
    """调查页码10的详细内容"""
    print("=== 调查页码10的详细内容 ===")
    
    with PDFToMarkdownConverter("2.pdf") as converter:
        # 页码10 (索引9)
        page_idx = 9
        fitz_page = converter.fitz_doc[page_idx]
        plumber_page = converter.plumber_doc.pages[page_idx]
        
        print(f"页面尺寸: {plumber_page.width} x {plumber_page.height}")
        
        # 获取所有文本块
        blocks = fitz_page.get_text("dict")["blocks"]
        
        print(f"\n页面文本块数量: {len(blocks)}")
        
        for i, block in enumerate(blocks):
            if "lines" in block:
                print(f"\n--- 文本块 {i} ---")
                print(f"位置: {block['bbox']}")
                
                for line in block["lines"]:
                    line_text = ""
                    for span in line["spans"]:
                        line_text += span["text"]
                    
                    if line_text.strip():
                        print(f"  文本: {line_text.strip()}")
                        
                        # 检查是否包含"1.1"或"前言"
                        if "1.1" in line_text or "前言" in line_text:
                            print(f"  *** 找到1.1前言相关文本 ***")
                            print(f"  位置: {line['bbox']}")
                            
                            # 显示这个位置之后的内容
                            print(f"  后续内容:")
                            start_y = line['bbox'][3]  # 这行的底部
                            
                            # 提取这个Y坐标之后的内容
                            words = plumber_page.extract_words()
                            following_words = []
                            
                            for word in words:
                                if word["top"] > start_y and word["top"] < start_y + 200:  # 后续200像素内的内容
                                    following_words.append(word["text"])
                            
                            if following_words:
                                following_text = " ".join(following_words[:50])  # 前50个词
                                print(f"  后续文本: {following_text}")

def investigate_real_content():
    """调查真正的1.1前言内容应该是什么"""
    print("\n=== 调查真正的1.1前言内容 ===")
    
    # 使用pymupdf4llm提取页码10的内容
    import pymupdf4llm
    
    # 只提取页码10
    doc = fitz.open("2.pdf")
    page_10 = doc[9]  # 页码10，索引9
    
    # 提取页面文本
    page_text = page_10.get_text()
    print(f"页码10的原始文本 (前1000字符):")
    print(page_text[:1000])
    
    # 查找"1.1 前言"之后的内容
    lines = page_text.split('\n')
    found_preface = False
    preface_content = []
    
    for line in lines:
        if "1.1" in line and "前" in line:
            found_preface = True
            print(f"\n找到1.1前言标题: {line}")
            continue
        
        if found_preface:
            if line.strip() and not line.startswith("1.2"):
                preface_content.append(line.strip())
            elif line.startswith("1.2"):
                break
    
    if preface_content:
        print(f"\n真正的1.1前言内容:")
        print("\n".join(preface_content[:10]))  # 前10行
    
    doc.close()

def compare_with_baseline():
    """与基准文档对比，找出真正应该提取的内容"""
    print("\n=== 与基准文档对比分析 ===")
    
    # 读取基准文档
    try:
        with open('baseline_pymupdf4llm.md', 'r', encoding='utf-8') as f:
            baseline_content = f.read()
    except FileNotFoundError:
        print("基准文档未找到")
        return
    
    # 查找基准文档中真正的前言内容（不是目录）
    # 基准文档的问题是把目录当成了前言，我们需要找到真正的前言
    
    # 在基准文档中搜索可能的前言关键词
    preface_keywords = ["概述", "本文档", "产品版本", "License", "读者对象"]
    
    lines = baseline_content.split('\n')
    
    for i, line in enumerate(lines):
        for keyword in preface_keywords:
            if keyword in line and "目录" not in line and "..." not in line:
                print(f"在基准文档第{i+1}行找到前言关键词 '{keyword}': {line}")
                
                # 显示周围的内容
                start = max(0, i-2)
                end = min(len(lines), i+5)
                print("周围内容:")
                for j in range(start, end):
                    marker = ">>> " if j == i else "    "
                    print(f"{marker}{lines[j]}")
                print()

def extract_correct_preface():
    """尝试正确提取1.1前言内容"""
    print("\n=== 尝试正确提取1.1前言内容 ===")
    
    with PDFToMarkdownConverter("2.pdf") as converter:
        # 查找1.1前言的真实位置
        toc = converter.fitz_doc.get_toc()
        
        preface_entry = None
        for level, title, page in toc:
            if "1.1" in title and "前" in title:
                preface_entry = (level, title, page)
                break
        
        if not preface_entry:
            print("在TOC中未找到1.1前言")
            return
        
        print(f"TOC中的1.1前言: {preface_entry}")
        
        # 在指定页面查找真正的前言内容
        page_idx = preface_entry[2] - 1
        page = converter.plumber_doc.pages[page_idx]
        
        # 使用更精确的方法查找前言内容
        words = page.extract_words()
        
        # 查找"前言"关键词的位置
        preface_start = None
        for word in words:
            if "前" in word["text"] and "言" in word["text"]:
                preface_start = word["bottom"]
                print(f"找到'前言'关键词，位置: {word}")
                break
        
        if preface_start:
            # 查找下一个章节标题的位置
            next_section_start = None
            for word in words:
                if word["top"] > preface_start and ("1.2" in word["text"] or "首次登录" in word["text"]):
                    next_section_start = word["top"]
                    print(f"找到下一章节，位置: {word}")
                    break
            
            # 提取前言内容
            if next_section_start:
                preface_words = []
                for word in words:
                    if preface_start < word["top"] < next_section_start:
                        # 过滤页眉页脚和目录内容
                        if not converter.is_header_footer(word["text"], page.height, word["top"]):
                            if not converter.is_table_of_contents(word["text"]):
                                preface_words.append(word["text"])
                
                if preface_words:
                    preface_text = " ".join(preface_words)
                    print(f"\n提取的前言内容 ({len(preface_text)} 字符):")
                    print(preface_text[:500])

if __name__ == "__main__":
    investigate_page_10()
    investigate_real_content()
    compare_with_baseline()
    extract_correct_preface()
