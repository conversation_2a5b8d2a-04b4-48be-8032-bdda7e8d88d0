

完成一组配置之后，可以查看当前生效的参数来验证配置是否正确。

##### 背景信息


已完成基本配置。

##### 操作步骤

       - 执行命令 **display current-configuration** [ **configuration** [ configuration-type

[ configuration-instance ] | config-type-no-inst ] | **all** | **inactive** ] [ **include-**
**default** ] ，显示当前配置信息。


       - 执行命令 **display this** ，显示当前视图的运行配置信息。


对于某些正在生效的配置参数，如果与缺省工作参数相同，则不显示；对于某些
参数，虽然用户已经配置，但如果这些参数所在的功能没有生效，则不予显示。


**----**
结束
