

用户可以通过 Console 口、 Telnet 、 STelnet 、 FTP （ File Transfer Protocol ）、 TFTP
（ Trivial File Transfer Protocol ）或 SFTP （ Secure File Transfer Protocol ）等方式进行
文件操作，实现对文件、目录的管理，包括查看、创建、重命名和删除目录，拷贝、
移动、重命名和删除文件等。


设备运行过程中所需要的文件（如：系统软件、配置文件等）保存在设备的存储设备
中，为了方便用户对存储设备进行有效的管理，设备以文件系统方式对这些文件进行
管理。


文件系统操作包括：目录操作、文件操作等。


用户可通过如下方式对目录、文件进行管理：


       - 通过 Console 口、 Telnet 或 STelnet 方式登录设备后，对目录和文件进行管理。


通过 Console 口、 Telnet 或 STelnet 方式登录设备详细描述，请参见 配置用户登录 。


       - 通过 FTP 方式对目录和文件进行管理。


       - 通过 TFTP 方式对目录和文件进行管理。


       - 通过 SFTP 方式对目录和文件进行管理。

##### FTP 概述


FTP 协议是一种基于 TCP/IP 协议族的 Internet 标准应用协议，用于在远端服务器和本地
客户端之间传输文件。 FTP 采用两条 TCP 连接将一个文件从一个系统复制到另一个系
统，连接通常是以客户－服务器的方式建立，这两条 TCP 连接分别是控制连接（服务器
端为 21 号端口）和数据连接（服务器端为 20 号端口）。


       - 控制连接


控制连接建立在客户端与服务器之间。


控制连接始终等待客户端和服务器之间的通信。 并且将相关命令从客户端传送给
服务器，同时将服务器的应答传送给客户端。


       - 数据连接


服务器的数据连接端使用端口 20 。服务器执行主动打开数据连接，通常也执行主
动关闭数据连接。但是，当客户端向服务器发送流形式的文件时，则需要客户端
关闭数据连接。


FTP 中传输方式是流方式，并且文件结尾以关闭数据连接为标志，所以对每一个文
件传输或目录列表来说，都要建立一个全新的数据连接。因此，当一个文件在客
户端与服务器之间传输时，一个数据连接就建立起来了。


在网络中传输数据时， FTP 支持以下两种类型的数据传输：


       - 二进制文件类型：以二进制模式将程序文件（比如后缀名为 .app 、 .bin 和 .btm 的文
件，此类文件如果使用 ASCII 码模式，可能会显示一堆乱码）在数据连接中传输，
不对数据进行任何处理，不需要转换或格式化就可以传输字符，二进制模式比
ASCII 码模式更快，并且可以传输所有 ASCII 值。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 139


HUAWEI NetEngine40E
配置指南 1 基础配置


       - ASCII 码文件类型：以 ASCII 码模式传输文本格式的文件（比如后缀名为 .txt 、 .bat
和 .cfg 的文件，此类文件如果使用二进制模式，可能导致文件执行出错），将回
车、换行转换为本机的回车字符。

##### TFTP 概述


TFTP 协议是一种基于 UDP 的文件传输应用协议，使用 UDP 端口 69 在远端服务器和本地
主机之间传输文件。相对于 FTP ， TFTP 没有复杂的交互存取接口和认证控制，适用于
客户端和服务器之间不需要复杂交互的环境。


TFTP 支持以下传输模式：


       - 二进制模式：用于传输程序文件。


       - ASCII 码模式：用于传输文本文件。


说明


目前，设备实现情况是只能作为 TFTP 客户端，且只能使用二进制模式传输文件。


TFTP 传输请求是由客户端发起的：


       - 当 TFTP 客户端需要从服务器下载文件时，由客户端向 TFTP 服务器发送读请求包，
然后从服务器接收数据，并向服务器发送确认。


       - 当 TFTP 客户端需要向服务器上传文件时，由客户端向 TFTP 服务器发送写请求包，
然后向服务器发送数据，并接收服务器的确认。

##### SFTP 概述


SFTP 利用 SSH 协议提供的安全通道，使得远程用户可以安全地登录设备进行文件管理
和文件传送等操作，为数据传输提供了更高的安全保障。同时，由于设备支持作为客
户端的功能，用户可以从本地设备安全登录到远程设备上，进行文件的安全传输。


当 SFTP 服务器端或是与客户端的连接存在故障时，客户端需要及时了解故障的存在，
并主动断开连接。为了实现上述目标，客户端以 SFTP 方式登录服务器时，配置无数据
接收时发送 Keepalive 报文的间隔时间和服务器端的无应答限制次数：


       - 如果在指定时间间隔内未收到数据，客户端将发送 Keepalive 报文至服务器端。


       - 如果服务端的无应答次数超过配置的次数，客户端将主动断开连接。

##### 文件系统管理方式的应用场景


表 **1-20** 文件系统管理方式应用场景






|文件系统管<br>理方式|优点|缺点|应用场景|
|---|---|---|---|
|通过Console<br>口、Telnet<br>或STelnet方<br>式|用户可直接登录设备，对目录、文件进行管理。<br>通过Console口、Telnet或STelnet方式登录设备详细描述，请参见配<br>置用户登录。|用户可直接登录设备，对目录、文件进行管理。<br>通过Console口、Telnet或STelnet方式登录设备详细描述，请参见配<br>置用户登录。|用户可直接登录设备，对目录、文件进行管理。<br>通过Console口、Telnet或STelnet方式登录设备详细描述，请参见配<br>置用户登录。|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 140


HUAWEI NetEngine40E
配置指南 1 基础配置












|文件系统管<br>理方式|优点|缺点|应用场景|
|---|---|---|---|
|FTP|●基于TCP，具有<br>TCP所有特点。<br>●具有授权和认证<br>功能。<br>●FTP可以在两个不<br>同文件系统主机<br>之间传输文件。|●FTP的命令复杂、<br>繁多。<br>●FTP所占的内存要<br>比TFTP大。<br>●明文传输数据，甚<br>至是明文传输用户<br>名和口令，存在安<br>全隐患|FTP适用于存在一定<br>时延、丢包和抖动的<br>网络。<br>应用在设备的版本升<br>级、以及各种文件传<br>输场景中。|
|TFTP|●基于UDP连接。<br>●TFTP所占的内存<br>要比FTP小。|●TFTP只支持文件<br>传输，不支持交<br>互。<br>●TFTP不允许用户<br>列出目录内容或者<br>与服务器协商来决<br>定那些可得到的文<br>件名。<br>●TFTP没有授权和<br>认证，且是明文传<br>输数据，存在安全<br>隐患，易于黑客和<br>网络病毒传输。|在网络条件良好的实<br>验室局域网中，可以<br>使用TFTP进行版本的<br>在线加载和升级。<br>适用于客户机和服务<br>器之间不需要复杂交<br>互的环境<br>详细配置，请参见通<br>过**TFTP**访问其他设<br>备的文件。|
|SFTP|数据进行了严格加密<br>和完整性保护，安全<br>性高。|●数据传输效率低。<br>●用户终端需要安装<br>支持SFTP客户端<br>的第三方软件。|适用于网络安全性要<br>求高的场景。|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 141


HUAWEI NetEngine40E
配置指南 1 基础配置
