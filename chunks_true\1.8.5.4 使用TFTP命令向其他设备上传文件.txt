

用户可以使用 TFTP 命令从当前设备上传文件至远端服务器。

##### 背景信息


上传文件时， TFTP 客户端向 TFTP 服务器发送 write 请求报文。当接收到数据报文时，
TFTP 客户端再向服务器发送 acknowledgement 报文。


根据服务器的 IP 地址类型不同，选择下列步骤中的一种进行操作。

##### 操作步骤


       - 执行命令 **tftp** [ **-a** source-ip-address | **-i** interface-type interface-number ]
host-ip-address [ **vpn-instance** vpn-instance-name ] **put** source-filename

[ destination-filename ] ，使用 TFTP 上传文件。


参数 interface-type 必须是 Loopback 接口。


       - 执行命令 **tftp ipv6** [ **-a** source-ipv6-address ] tftp-server-ipv6 [ [ **vpn-instance**
vpn-instance-name | **public-net** ] ] [ **-oi** interface-type interface-number ] **put**
source-filename [ destination-filename ] ，使用 TFTP 上传文件


**----**
结束
