#!/usr/bin/env python3
"""
详细调试标题和内容提取问题
"""

from main2 import PDFToMarkdownConverter

def debug_specific_titles():
    """调试特定标题的内容提取"""
    print("=== 详细调试特定标题 ===")
    
    with PDFToMarkdownConverter("2.pdf") as converter:
        titles = converter.extract_titles_from_toc()
        
        # 按页码和位置排序标题
        def sort_key(title):
            if title.bbox:
                return (title.page_num, title.bbox["top"])
            else:
                toc = converter.fitz_doc.get_toc()
                for i, (level, text, page) in enumerate(toc):
                    if text.strip() == title.text.strip() and page == title.page_num:
                        return (title.page_num, 1000 + i)
                return (title.page_num, 9999)
        
        titles.sort(key=sort_key)
        
        # 找到1.1前言和1.2首次登录配置
        target_titles = []
        for i, title in enumerate(titles):
            if "1.1 前" in title.text or "1.2 首次登录配置" in title.text:
                target_titles.append((i, title))
        
        print(f"找到目标标题: {len(target_titles)} 个")
        
        for idx, (i, title) in enumerate(target_titles):
            print(f"\n--- 标题 {idx+1}: {title.text} ---")
            print(f"索引: {i}")
            print(f"页码: {title.page_num}")
            print(f"级别: {title.level}")
            if title.bbox:
                print(f"位置: top={title.bbox['top']:.1f}, bottom={title.bbox['bottom']:.1f}")
            else:
                print("位置: 无bbox信息")
            
            # 查看前后标题
            if i > 0:
                prev_title = titles[i-1]
                print(f"前一个标题: {prev_title.text} (页码{prev_title.page_num})")
            
            if i < len(titles) - 1:
                next_title = titles[i+1]
                print(f"后一个标题: {next_title.text} (页码{next_title.page_num})")
                if next_title.bbox:
                    print(f"后一个标题位置: top={next_title.bbox['top']:.1f}")
            
            # 计算内容提取范围
            start_page = title.page_num - 1
            start_y = title.bbox["bottom"] if title.bbox else 0
            
            if i + 1 < len(titles):
                next_title = titles[i + 1]
                
                if next_title.page_num == title.page_num:
                    end_page = start_page
                    end_y = next_title.bbox["top"] if next_title.bbox else float('inf')
                else:
                    end_page = start_page
                    end_y = float('inf')
                    
                    if title.level <= 2 and next_title.page_num <= title.page_num + 1:
                        end_page = next_title.page_num - 1
                        end_y = next_title.bbox["top"] if next_title.bbox else float('inf')
            else:
                end_page = start_page
                end_y = float('inf')
            
            print(f"内容提取范围: 页码{start_page+1}(y>{start_y:.1f}) 到 页码{end_page+1}(y<{end_y:.1f})")
            
            # 实际提取内容
            content_parts = []
            for page_num in range(start_page, end_page + 1):
                if page_num >= len(converter.plumber_doc.pages):
                    break
                    
                page = converter.plumber_doc.pages[page_num]
                page_height = page.height
                
                current_start_y = start_y if page_num == start_page else 0
                current_end_y = end_y if page_num == end_page else page_height
                
                print(f"  页面{page_num+1}: y范围 {current_start_y:.1f} - {current_end_y:.1f}")
                
                words = page.extract_words()
                page_content = []
                
                for word in words:
                    if word["top"] < current_start_y or word["top"] > current_end_y:
                        continue
                    
                    if converter.is_header_footer(word["text"], page_height, word["top"]):
                        continue
                    
                    if converter.is_table_of_contents(word["text"]):
                        continue
                    
                    page_content.append(word["text"])
                
                if page_content:
                    page_text = " ".join(page_content)
                    content_parts.append(page_text)
                    print(f"  页面{page_num+1}内容长度: {len(page_text)} 字符")
                    print(f"  页面{page_num+1}内容预览: {page_text[:100]}...")
            
            full_content = " ".join(content_parts)
            cleaned_content = converter.clean_text(full_content)
            
            print(f"最终内容长度: {len(cleaned_content)} 字符")
            print(f"最终内容预览: {cleaned_content[:200]}...")

if __name__ == "__main__":
    debug_specific_titles()
