#!/usr/bin/env python3
# pdf2md_slice.py
import fitz
from pathlib import Path

PDF = '2.pdf'
MD  = '1.md'

# --------------------------------------------------
# 1. 读取目录（带页内 y 坐标，方便精确定位）
# --------------------------------------------------
def load_toc_with_pos(doc):
    toc = []
    for lvl, title, page,dest in doc.get_toc(simple=False):
        page -= 1                                  # get_toc 页码从 1 起
        # 以该页最上方 y=0 作为默认起始位置
        toc.append((lvl, title, page, 0.0,dest))
    return toc

# --------------------------------------------------
# 2. 读取所有文字块（含页码、y 坐标、文本）
# --------------------------------------------------
def load_all_blocks(doc):
    blocks = []
    for p in range(doc.page_count):
        for b in doc[p].get_text("dict")["blocks"]:
            if "lines" not in b:
                continue
            y0 = b["bbox"][1]          # 块顶部 y
            text = "".join(
                span["text"]
                for line in b["lines"]
                for span in line["spans"]
            )
            if text.strip():
                blocks.append((p, y0, text.strip()))
    # 按 页码→y 升序，方便后面二分
    blocks.sort(key=lambda x: (x[0], x[1]))
    return blocks

# --------------------------------------------------
# 3. 截取「标题 A ~ 标题 B」之间的所有文字
# --------------------------------------------------
def slice_text(blocks, start_page, start_y, end_page, end_y):
    """返回 list[str]"""
    out = []
    for p, y, txt in blocks:
        if (p == start_page and y < start_y) or (p < start_page):
            continue
        if (p == end_page and y >= end_y) or (p > end_page):
            break
        out.append(txt)
    return out

# --------------------------------------------------
# 4. 主流程
# --------------------------------------------------
def main():
    doc = fitz.open(PDF)
    toc  = load_toc_with_pos(doc)
    print(toc)[0]
    blocks = load_all_blocks(doc)

    # 末尾加个哨兵，方便取最后一段
    toc.append((999, "EOF", doc.page_count, 0.0))

    lines = []
    for i in range(len(toc) - 1):
        lvl, title, sp, sy = toc[i]
        _, _, ep, ey = toc[i + 1]

        body = slice_text(blocks, sp, sy, ep, ey)
        # Markdown 标题
        lines.append(f'{"#" * lvl} {title}\n')
        if body:
            lines.append("```\n"+"".join(body)+"\n```")
            lines.append('\n\n')

    Path(MD).write_text("".join(lines), encoding='utf-8')
    print(f"已写出 {MD}  （共 {len(toc)-1} 个标题）")

if __name__ == "__main__":
    main()