

设备不同的视图下的配置，可能会出现大量的重复情况。此时，可以通过创建一个配
置模板，将重复的配置添加到配置模板中，在设备上应用该配置模板即可。

##### 背景信息


设备不同的视图下的配置，可能会出现大量的重复情况。此时，可以通过创建一个配
置模板，将重复的配置添加到配置模板中，相应视图下应用该配置模板即可，使配置
看起来更简洁。

##### 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **command group** group-name ，创建配置模板并进入命令模板视图。


步骤 **3** 根据需要配置的业务视图实例，执行相应业务视图实例命令进入业务视图实例中。以
接口视图为例，执行命令 **interface** interface-group-name ，将 Loopback 接口与当前
配置模板相关联。


说明


配置模板视图下执行业务视图实例命令，可使用正则表达式。例如执行命令 **interface**
**<Loopback.>** ，将设备上所有的 Loopback 接口与当前配置模板相关联。


表 **1-35** 正则表达式语法意义描述






|特殊字符|功能|举例|
|---|---|---|
|\|转义字符。将下一个字符（特殊字<br>符或者普通字符）标记为普通字<br>符。|\*匹配*|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 209


HUAWEI NetEngine40E
配置指南 1 基础配置












|特殊字符|功能|举例|
|---|---|---|
|^|匹配行首的位置。|^10匹配10.10.10.1，不匹配<br>2.2.2.2|
|$|匹配行尾的位置。|1$匹配10.10.10.1，不匹配<br>10.10.10.2|
|*|匹配前面的子正则表达式零次或多<br>次。|10*可以匹配1、10、100、1000、<br>……<br>(10)*可以匹配空、10、1010、<br>101010、……|
|+|匹配前面的子正则表达式一次或多<br>次。|10+可以匹配10、100、1000、<br>……<br>(10)+可以匹配10、1010、<br>101010、……|
|?|匹配前面的子正则表达式零次或一<br>次。<br>说明<br>当前，在华为公司数据通信设备上通<br>过命令行运用正则表达式输入？时，<br>系统显示为命令行帮助功能。华为公<br>司数据通信设备不支持正则表达式输<br>入？特殊字符。|10?可以匹配1或者10<br>(10)?可以匹配空或者10|
|.|匹配任意单个字符。|a.b匹配任何一个以a开头，以b结<br>尾含有三个字符的字符串<br>0.0可以匹配0x0、020、……<br>.oo.可以匹配book、look、tool、<br>……|
|()|一对圆括号内的正则表达式作为一<br>个子正则表达式，匹配子表达式并<br>获取这一匹配。<br>如果圆括号中内容为空，则等价于<br>空串。<br>如果模式串只有()，则可匹配任意<br>字符串。<br>如果模式串中的右括号没有匹配的<br>左括号，则右括号就作为普通字<br>符。<br>如果模式串中的左括号没有匹配的<br>右括号，则为非法模式串。|100(200)+可以匹配100200、<br>100200200、……<br>(ab)匹配abcab<br>()匹配任意字符串<br>a()b匹配12ab12<br>a)b匹配za)bc<br>a(b为非法模式串|
|_|匹配一个符号，包括逗号、左大括<br>号、右大括号、左括号、右括号和<br>空格，在表达式的开头或结尾时还<br>可作起始符、结束符（同^ ，<br>$）。|_65001_可以匹配20 65001 30、<br>20 65001、65001 30、65001、<br>……|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 210


HUAWEI NetEngine40E
配置指南 1 基础配置



|特殊字符|功能|举例|
|---|---|---|
|x|y|匹配x或y。|100|200匹配100或者200<br>1(2|3)4匹配124或者134，而不匹<br>配1234、14、1224、1334|
|[xyz]|匹配正则表达式中的任意一个字<br>符。不可同时匹配多个字符，也不<br>可匹配同一个字符多次。|[123]匹配255中的2<br>[abc]匹配字符“a”、“b”、<br>“c”|
|[^xyz]|匹配字符串中非“x”、“y”、<br>“z”的字符。只要字符串中有非<br>“x”、“y”、“z”的字符，就<br>能匹配到。|[^123]匹配除123之外的任何字符<br>[^abc]匹配除“a”、“b”、<br>“c”之外的任何字符|
|[a-z]|匹配正则表达式指定范围内的任意<br>一个字符。不可同时匹配多个字<br>符，也不可匹配同一个字符多次。|[0-9]匹配指定范围内的任意数字<br>[a-z]匹配指定范围内的任意字母<br>[z-a]为非法模式串|
|[^a-d]|匹配字符串中除“a”、“b”、<br>“c”、“d”以外的其他字符。只<br>要字符串中有a～d范围外的字符，<br>就能匹配到。|[^0-9]匹配所有非数字字符<br>[^a-z]匹配除字母以外的其他任意<br>字符<br>[^z-a]为非法模式串|


说明






          - 除非特别说明，上表中涉及到的字符指的是可以打印的字符，包括字母、数字、空格及特殊
符号等。


步骤 **4** 配置需要下发的数据。以使能 IPv6 功能为例，执行命令 **ipv6 enable** ，使能 IPv6 功能。


步骤 **5** （可选）执行命令 **display this command group candidate merge** ，查看配置模板
中的配置信息。


步骤 **6** （可选）执行命令 **display this command group candidate** ，查看配置模板中发生变
更的配置信息。


步骤 **7** 执行命令 **end-group** ，结束并提交配置，退出至系统视图。


如果不需要使用当前的配置模板，可以执行命令 **abort** ，放弃当前正在配置的模板，并
退出配置模板视图。


步骤 **8** 执行命令 **interface** interface-type interface-number ，创建 Loopback 接口。


步骤 **9** 进入对应接口视图，执行命令 **apply-command-group** group-name & < 1-8 > ，应用
配置模板。


说明


          - 指定业务视图下执行此命令，配置将下发至指定业务视图实例中。


          - 同一业务视图实例中可应用多个配置模板。


          - 可同时应用多个配置模板。


          - NULL 接口视图不支持应用配置模板下的配置。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 211


HUAWEI NetEngine40E
配置指南 1 基础配置


如果某个业务视图实例无需应用配置模板中的配置，可在配置模板视图下执行命令
**undo** command-string ，删除指定业务视图实例。


如果业务视图实例中有多余或者错误的配置，可在配置模板视图下进入相应的业务视
图实例，执行此命令 **undo** command-string ，删除多余或者错误的配置。


说明


**undo** command-string 命令中的参数取值只能是配置模板中关联的业务视图实例及其下面已配
置的命令，如果删除的是业务视图实例，则实例下应用此模板中的配置会一起删除。


**----**
结束

##### 后续处理

       - 执行命令 **display current-configuration** [ **inheritance** [ **no-comment** ] ] ，查
看从模板继承的配置信息。


       - 进入相应视图，执行命令 **display this** [ **inheritance** [ **no-comment** ] ] ，查看系
统当前视图从模板继承的配置信息。

       - 执行命令 **display configuration apply-command-group fail-result** ，查看最近
五次应用配置模板失败的原因。


       - 执行命令 **display command-group** groupName **applied** ，查看配置模板应用的
视图数量及视图名称。
