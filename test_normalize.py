#!/usr/bin/env python3
"""
测试标准化函数
"""

import re

def normalize_title(text):
    """智能标准化标题文本"""
    # 1. 基本清理
    text = text.strip()
    # 2. 标准化空格
    text = re.sub(r'\s+', ' ', text)
    # 3. 处理中英文之间的空格差异（这是关键！）
    # Console口 vs Console 口 - 移除英文字母和中文字符之间的空格
    text = re.sub(r'([a-zA-Z])\s+([^\sa-zA-Z0-9])', r'\1\2', text)
    # Tab键 vs Tab 键 - 移除英文字母和中文字符之间的空格  
    text = re.sub(r'([a-zA-Z])\s+([一-龯])', r'\1\2', text)
    return text

def test_normalize():
    """测试标准化函数"""
    
    test_cases = [
        ("1.2.3 通过Console口登录设备", "1.2.3 通过Console 口登录设备"),
        ("******* 使用Tab键示例", "******* 使用Tab 键示例"),
        ("1.4.3 配置Console用户界面", "1.4.3 配置Console 用户界面"),
    ]
    
    print("=== 标准化函数测试 ===")
    
    for toc_title, raw_title in test_cases:
        print(f"\nTOC标题: '{toc_title}'")
        print(f"Raw标题: '{raw_title}'")
        
        toc_normalized = normalize_title(toc_title)
        raw_normalized = normalize_title(raw_title)
        
        print(f"TOC标准化: '{toc_normalized}'")
        print(f"Raw标准化: '{raw_normalized}'")
        print(f"匹配结果: {toc_normalized == raw_normalized}")
        
        if toc_normalized != raw_normalized:
            print(f"差异分析:")
            print(f"  TOC字符: {list(toc_normalized)}")
            print(f"  Raw字符: {list(raw_normalized)}")

if __name__ == "__main__":
    test_normalize()
