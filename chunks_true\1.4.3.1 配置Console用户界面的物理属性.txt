
Console 用户界面的物理属性包括 Console 口的传输速率、流控方式、校验位、停止位
和数据位。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 37


HUAWEI NetEngine40E
配置指南 1 基础配置

##### 背景信息


当用户通过 Console 口登录设备时，终端仿真程序的下列属性要和设备的物理属性必须
保持一致，否则不能登录成功登录设备。


说明


该配置过程仅在 Admin-VS 支持。

##### 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **user-interface** { ui-type | **console** first-ui-number } ，进入 Console 口用户
界面视图。


步骤 **3** 执行命令 **speed** speed-value ，设置传输速率。


传输速率包括 300 、 600 、 1200 、 2400 、 4800 、 9600 、 19200 、 38400 、 57600 、
115200 ，单位是 bit/s 。


步骤 **4** 执行命令 **flow-control** flowtype ，设置流控方式。


步骤 **5** 执行命令 **parity** paritytype ，设置校验位。


步骤 **6** 执行命令 **stopbits** stopbitvalue ，设置停止位。


步骤 **7** 执行命令 **databits** databitvalue ，设置数据位。


步骤 **8** 执行命令 **commit** ，提交配置。


**----**
结束
