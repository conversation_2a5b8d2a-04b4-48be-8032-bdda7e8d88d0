

命令行别名功能可以将设备中的命令行别名为用户执行的字符串，方便用户使用。命
令行别名功能只能在人机模式下使用，机机模式下命令行别名功能不生效。

##### 背景信息


用户可以通过命令 **terminal command alias** 打开当前会话的别名特性开关，也可以通
过命令 **undo terminal command alias** 关闭当前会话的别名特性开关。关闭当前会话
的别名特性，仅影响当前会话的别名配置功能，并不清除系统中存在的别名配置信


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 26


HUAWEI NetEngine40E
配置指南 1 基础配置


息。当继续执行打开当前会话的别名特性开关后，配置文件中的别名配置信息继续生
效。用户可以通过命令 **display terminal command alias** 查看命令别名特性的开关状
态。

##### 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **command alias** ，进入命令行别名视图。


步骤 **3** 执行命令 **alias** alias-string [ **parameter** parameter & <1-32> ] **command**
command ，进行命令别名配置。


**----**
结束

##### 后续处理


       - 用户完成命令别名配置后，可以执行命令 **display command alias** 查看别名配置
信息。
