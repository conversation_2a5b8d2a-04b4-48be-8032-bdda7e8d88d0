

多台设备同源管理场景下，如果不同设备上的配置出现不一致情况，可执行此功能将
差异配置粘贴至其他设备，实现多台设备配置一致。

##### 应用环境


配置相同的多台设备，如果其中一台设备的配置发生变更，为保持配置一致，其他设
备的配置需同步变更，此时用户可使用此功能查询出有差异的配置，将差异配置粘贴
至其他设备，使多台设备配置保持同步。


此功能只能在两阶段生效模式下执行。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 215


HUAWEI NetEngine40E
配置指南 1 基础配置

##### 前置条件


用户已经对系统进行了配置操作，并且系统生成配置回退点。可执行命令 **display**
**configuration commit list** [ **verbose** ] [ number-of-commits | **label** ] 查看配置回
退点列表。

##### 操作步骤


步骤 **1** 查看配置差异。


       - 查看设备上的配置文件中保存的配置和当前运行配置之间携带标签的差异信息

– 执行命令 **display configuration changes** **running file** file-name **with-**
**tag** ，查看当前运行配置与指定的配置文件之间的差异。

– 执行命令 **display configuration changes** **file** file-name **running** **with-**
**tag** ，查看指定配置文件与当前运行配置之间的差异。

– 执行命令 **display configuration changes** **running label** label **with-tag** ，
查看当前运行配置与指定用户标签的配置之间的差异。

– 执行命令 **display configuration changes** **label** label **running** **with-tag** ，
查看指定用户标签的配置与当前运行配置之间的差异。


       - 查看配置回退点记录的携带标签的配置变更信息

– 执行命令 **display configuration commit changes** [ **at** commit-id | **since**
commit-id | **last** number-of-commits ] **with-tag** ，查看配置回退点记录的
携带标签的配置变更信息。


步骤 **2** 复制差异配置，粘贴至本地设备，替换当前设备运行配置。


1. 执行命令 **system-view** ，进入系统视图。

2. 执行命令 **load configuration terminal** ，进入差异配置粘贴视图。


在配置差异粘贴视图下粘贴差异配置需要以下两个步骤：


a. 进入配置差异粘贴视图后，将查询到的差异配置直接复制粘贴至当前设备。

b. 粘贴结束符： **end-diff** 或者 **abort** 。

#### ▪ end-diff ：结束差异配置粘贴，并退出差异配置粘贴视图。 ▪ abort ：取消差异配置粘贴，并退出差异配置粘贴视图。

3. （可选）执行命令 **display configuration candidate** ，查看替换后的配置是否符
合预期：


– 如果符合预期，请继续执行步骤 d 。


–
如果不符合预期，请执行命令 **clear configuration candidate** 清除替换后的
内容，重新执行步骤 b 粘贴差异配置。


4. 执行命令 **commit** ，提交配置。


**----**
结束

