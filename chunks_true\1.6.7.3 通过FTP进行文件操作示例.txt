

在本示例中，通过使用正确的用户名和密码从用户终端以 FTP 方式登录到 FTP 服务器，
实现文件的上传和下载。

##### 组网需求


随着设备的逐步稳定，应用范围在不断扩大，设备升级的范围也越来越大，设备的可
维护性要求也越来越高，特别是设备在线远程加载升级。这不仅可以丰富设备升级维
护手段，方便远程在线升级，还可以节约升级成本，减少客户等待升级的时间，在一
定程度上提高了客户的满意度。但是，实际网络上的丢包、时延、抖动影响了数据传
输。为了可靠的保证在线升级、数据传输，可利用基于 TCP 的 FTP 进行在线升级、传输
文件。


如 图 **1-49** 所示，在作为 FTP 服务器的设备上使能 FTP 服务器功能后，从终端仿真程序登
录到 FTP 服务器，实现文件的上传和下载。


图 **1-49** 通过 FTP 进行文件操作组网图


说明


本例中 interface1 代表 GigabitEthernet0/0/0 。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 179


HUAWEI NetEngine40E
配置指南 1 基础配置

##### 配置注意事项


已经成功通过 Console 口登录作为 FTP 服务器的设备，配置用户通过 FTP 方式的登录 IP 地
址，建议此 IP 地址配置在逻辑接口上。


当网络所处环境不足够安全时，我们建议选择安全的协议。安全协议举例参见：
*********** 通过 **SFTP** 进行文件操作示例 。

##### 配置思路


采用如下的思路配置通过 FTP 进行文件操作基本功能：


1. 配置 FTP 服务器的 IP 地址。


2. 使能 FTP 服务器功能。


3. 配置 FTP 用户的验证信息、用户授权方式及访问目录。


4. 使用正确的用户名和密码以 FTP 方式登录到 FTP 服务器。


5. 上传文件至服务器、下载文件到用户终端。

##### 数据准备


为完成此配置举例，需准备如下的数据：


       - FTP 服务器的 IP 地址为 ************** 。


       - 在服务器端设置 FTP 用户名为“ huawei ”。


       - 上传的文件在用户终端的指定路径下，需下载的文件在 FTP 服务器指定的路径。

##### 操作步骤


步骤 **1** 配置 FTP 服务器的 IP 地址


<HUAWEI> **system-view**

[ ~ HUAWEI] **sysname server**

[*HUAWEI] **commit**

[ ~ server] **interface GigabitEthernet0/0/0**

[ ~ server-GigabitEthernet0/0/0] **undo shutdown**

[*server-GigabitEthernet0/0/0] **ip address ************** *************

[*server-GigabitEthernet0/0/0] **quit**

[*server] **commit**


步骤 **2** 使能 FTP 服务器功能


[ ~ server] **interface LoopBack 0**

[ ~ server-LoopBack0] **ip address ******** *****************

[*server-LoopBack0] **quit**

[*server] **ftp server enable**

[*server] **ftp server-source -i loopback** **0**

[*server] **commit**


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 180


HUAWEI NetEngine40E
配置指南 1 基础配置


步骤 **3** 在 FTP 服务器上配置 FTP 用户的验证信息、授权方式和授权目录


[ ~ server] **aaa**

[*server-aaa] **local-user huawei password**
Please configure the password (8-128)
Enter Password:
Confirm Password:


说明


密码以交互式输入，系统不回显密码。


特殊字符不包括“？”和空格。但是，当输入的密码两端使用双引号时，可在密码中间输入空
格。


          - 如果使用双引号设置带空格密码，双引号之间不能再使用双引号。


          - 如果使用双引号没有设置带空格密码，双引号之间可以再使用双引号。


例如， "Aa 123"45"" 为不合法密码， "Aa123"45"" 为合法密码。


[*server-aaa] **local-user huawei service-type ftp**

[*server-aaa] **local-user huawei ftp-directory cfcard:/**

[*server-aaa] **local-user huawei level 3**

[*server-aaa] **quit**

[*server] **commit**


步骤 **4** 进入 Windows 命令行提示符输入，执行 **ftp** 命令，然后使用正确的用户名和密码与 FTP
服务器建立 FTP 连接。


步骤 **5** 在用户终端实现文件的上传和下载。


说明


用户在进行文件下载前或文件上传后可以使用 **dir** 命令，查看需要下载的文件或是所上传的文件
的详细信息。


**----**
结束

##### FTP Server 的配置文件


#

sysname server
#

FTP server enable
FTP server-source -i loopback 0
#

aaa
local-user huawei password cipher @%@%:BC"Qzkbh*9PhJU|U>mX,cZQ@%@%
local-user huawei service-type ftp
local-user huawei level 3
local-user huawei ftp-directory cfcard:/
#
interface GigabitEthernet0/0/0
undo shutdown
ip address ************** ***********
#
interface LoopBack 0
ip address ******** ***************
#

return
