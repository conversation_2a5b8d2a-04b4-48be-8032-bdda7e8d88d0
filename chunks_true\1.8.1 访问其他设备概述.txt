

分别介绍通过 Telnet 、 FTP 、 TFTP 、 STelnet 、 SCP 或 SFTP 方式访问其他设备。


如 图 **1-59** 所示，用户在 PC 上通过终端仿真程序或 Telnet 程序建立与路由器的连接后，
仍可以将当前路由器作为客户端，通过 Telnet 、 FTP 、 TFTP 、 STelnet 、 SCP 或 SFTP 访
问网络上的其他设备。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 230


HUAWEI NetEngine40E
配置指南 1 基础配置


图 **1-59** 访问其他设备示意图

##### Telnet 概述


Telnet 协议在 TCP/IP 协议族中属于应用层协议，通过网络提供远程登录和虚拟终端功
能。 NE40E 提供的 Telnet 服务包括：


       - Telnet server ：用户在 PC 上运行 Telnet 客户端程序登录到设备，对设备进行配置管
理。此时，设备提供 Telnet server 服务。


       - Telnet client ：用户在 PC 上通过终端仿真程序或 Telnet 客户端程序建立与设备的连
接后，再执行 **telnet** 命令登录到其它设备，对其进行配置管理。如 图 **1-60** 所示，
CE 此时既作为 Telnet server ，同时也提供 Telnet client 服务。


图 **1-60** Telnet server 提供 Telnet client 服务示意图


       - 中断 Telnet 服务


图 **1-61** Telnet 快捷键使用示意图


在 Telnet 连接过程中，可以使用两种快捷键来中断连接。如 图 **1-61** 所示， P1 通过
Telnet 登录到 P2 ，再 Telnet 连接到 P3 ，形成级联结构。 P1 是 P2 的 Telnet 客户端，
P2 是 P3 的 Telnet 客户端，以此结构说明两种快捷键的用法。


–
<Ctrl_]> 快捷键——通知服务器端断开连接


在网络畅通的情况下，键入 <Ctrl_]> 将通知 Telnet 服务器中断本次 Telnet 登
录，即， Telnet 服务器端主动断开连接。


例如，在 P3 上键入 <Ctrl_]> ，将退回到 P2 的提示符。


<P3> Select **Ctrl_]** to return to the prompt of P2
The connection was closed by the remote host.


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 231


HUAWEI NetEngine40E
配置指南 1 基础配置


<P2>


此时键入 <Ctrl_]> ，将退回到 P1 的提示符。


<P2> **Ctrl_]**
The connection was closed by the remote host.
<P1>


说明


如果由于某些原因网络连接断开，快捷键的指令无法到达 Telnet 服务器端，输入无
效。


–
<Ctrl_K> 快捷键——客户端主动断开连接


当服务器端故障且客户端无法感知时，客户端输入任何指令服务器均无响
应，这种情况下键入 <Ctrl_K> 快捷键，则 Telnet 客户端主动中断并退出 Telnet
连接。


例如，在 P3 上键入 <Ctrl_K> ，将直接中断并退出 Telnet 连接。


<P3> **Ctrl_K**
<P1>


须知


当远端用户登录数达到 VTY 类型用户界面的最大个数时，系统会提示所有的
用户接口都在使用，不允许 Telnet 。

##### FTP 概述


FTP 协议是一种基于 TCP/IP 协议族的 Internet 标准应用协议，用于在远端服务器和本地
客户端之间传输文件。 FTP 采用两条 TCP 连接将一个文件从一个系统复制到另一个系
统，连接通常是以客户－服务器的方式建立，这两条 TCP 连接分别是控制连接（服务器
端为 21 号端口）和数据连接（服务器端为 20 号端口）。


       - 控制连接：将命令从客户端传送到服务器端，并传回服务器的应答， IP 对控制连
接的服务特点是最大限度地减少延迟。


       - 数据连接：在客户和服务器之间传输数据，因此 IP 对数据连接的服务特点是最大
限度地提高吞吐量。


FTP 有两种文件传输模式：


       - 二进制模式，用于传输程序文件（比如后缀名为 .app 、 .bin 和 .btm 的文件）。


       - ASCII 码模式，用于传输文本格式的文件（比如后缀名为 .txt 、 .bat 和 .cfg 的文
件）。


设备提供的 FTP 功能包括：


       - 设备作为 FTP 客户端：用户在 PC 上通过终端仿真程序或 Telnet 程序连接到设备，执
行 **ftp** 命令建立设备与远程 FTP 服务器的连接并访问远程主机上的文件，对远程主
机上的文件进行操作。


       - 设备作为 FTP 服务器：用户运行 FTP 客户端程序，登录设备并进行文件操作。


用户登录前，网络管理员需要事先配置好 FTP 服务器的 IP 地址。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 232


HUAWEI NetEngine40E
配置指南 1 基础配置

##### TFTP 概述


TFTP 协议是一种基于 UDP 的文件传输应用协议，使用 UDP 端口 69 在远端服务器和本地
主机之间传输文件。相对于 FTP ， TFTP 没有复杂的交互存取接口和认证控制，适用于
客户端和服务器之间不需要复杂交互的环境。


TFTP 文件传输模式与 FTP 一样，支持二进制和 ASCII 码两种模式。


说明


          - 目前， HUAWEI NetEngine40E 支持的 TFTP 只能使用二进制模式传输文件。


          - 目前， HUAWEI NetEngine40E 只能作为 TFTP 客户端，不支持作为 TFTP 服务器。


TFTP 传输请求是由客户端发起的：


       - 当 TFTP 客户端需要从服务器下载文件时，由客户端向 TFTP 服务器发送读请求包，
然后从服务器接收数据，并向服务器发送确认。


       - 当 TFTP 客户端需要向服务器上传文件时，由客户端向 TFTP 服务器发送写请求包，
然后向服务器发送数据，并接收服务器的确认。

##### STelnet 概述


STelnet 基于 SSH2.0 协议，客户端和服务器之间经过协商，建立安全连接，客户端可以
像操作 Telnet 一样登录服务器。


       - 设备支持 STelnet 客户端、 STelnet 服务器功能


为了方便用户的使用，设备不仅提供 STelnet 服务器功能，同时也可以作为 STelnet
客户端访问其他 STelnet 服务器。


       - 支持使能 / 去使能 STelnet 服务器功能


在不需要 STelnet 服务器情况下可以将其去使能，该功能在全局模式下配置。

##### SCP 概述


SCP 是基于 SSH2.0 的安全协议。合法用户通过客户端登录时，输入正确的用户名以及
对应的密码和私钥。通过服务器的验证后，用户可以实现对网络文件的远程传输管
理，而系统会对用户的数据采用协商出来的会话密钥对数据加密。


攻击者没有正确的私钥和密码，无法通过服务器的认证。并且攻击者无法获得其他用
户和服务器之间的会话密钥，因此后续服务器和指定客户端的通讯报文只有指定客户
端和服务器才能解密。即使攻击者窃听到通讯报文，也不能解密，实现了网络数据传
输的安全性。


       - 支持 SCP 客户端、服务器功能


为了方便用户的使用，设备既支持 SCP 客户端功能，同时也支持 SCP 服务器功能。
即设备既可以作为 SCP 服务器也可以作为 SCP 客户端接入 SCP 服务器。


       - 支持使能 / 去使能 SCP 服务器功能（默认关闭）。


在不需要 SCP 服务器情况下，可以将其去使能。该功能在全局模式下配置。


       - 支持客户端与服务器之间通过透明文件系统进行的运转。即对于所有的文件操作
来说，一个标准的文件系统可以用来访问远端单板上的文件。


       - 支持文件目录中多文件的递归传输。


例如，文件目录 directory 下包含多个文件以及子目录， SCP 可实现将整个 directory
目录下的文件进行传输，并保持原有的文件目录格式。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 233


HUAWEI NetEngine40E
配置指南 1 基础配置

##### SFTP 概述


SFTP 利用 SSH 协议提供的安全通道，使得远程用户可以安全地登录设备进行文件管理
和文件传送等操作，为数据传输提供了更高的安全保障。同时，由于设备支持作为客
户端的功能，用户可以从本地设备安全登录到远程设备上，进行文件的安全传输。


当 SFTP 服务器端或是与客户端的连接存在故障时，客户端需要及时了解故障的存在，
并主动断开连接。为了实现上述目标，客户端以 SFTP 方式登录服务器时，配置无数据
接收时发送 Keepalive 报文的间隔时间和服务器端的无应答限制次数：


       - 如果在指定时间间隔内未收到数据，客户端将发送 Keepalive 报文至服务器端。


       - 如果服务端的无应答次数超过配置的次数，客户端将主动断开连接。
