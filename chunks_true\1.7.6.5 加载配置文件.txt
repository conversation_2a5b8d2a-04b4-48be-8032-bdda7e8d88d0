

在设备正常运行过程中，通过加载配置文件可以批量执行命令行，进行功能配置。

##### 背景信息


用户需要将远端服务器或者本地的配置文件加载至当前正在运行的配置数据库中时，
可按如下步骤进行操作。


此功能只能在两阶段生效模式下生效。

##### 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 根据配置文件所在位置，选择执行如下命令之一：

       - 执行命令 **load configuration** **file** file-name **merge** [ **relative** ] ，加载本地配置
文件并下发配置。

       - 执行命令 **load configuration** **server** ip-address [ **vpn-instance** vpn-instancename ] **transport-type** { **ftp** | **sftp** } **username** user-name **password**
password-value **file** file-name **merge** [ **relative** ] ，加载远端 IPv4 服务器上的配
置文件，并将配置下发至当前设备。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 204


HUAWEI NetEngine40E
配置指南 1 基础配置


       - 执行命令 **load configuration** **server ipv6** ipv6-address [ **vpn-instance** vpninstance-name ] **transport-type** { **ftp** | **sftp** } **username** user-name **password**
password-value **file** file-name **merge** [ **relative** ] ，加载远端 IPv6 服务器上的配
置文件，并将配置下发至当前设备。

       - 执行命令 **load configuration** **server** **http url** url-address [ **vpn-instance** vpninstance-name ] **merge** [ **relative** ] ，根据 URL 获取远端服务器上的配置文件，
并将配置下发至当前设备。


说明


不建议手工构造配置文件。如果构造配置文件格式错误，可能导致配置恢复失败或者配置恢复发
生错误。


指定的待加载配置文件必须存在，且必须满足以下条件：


          - 配置文件中只能包含配置命令、视图切换命令、 # ，其他类型（如 display 查询命令、 reset/
save/ping 等维护命令、 quit 、 commit 、 return 、升级兼容命令、一阶段命令等）命令执行
时设备会报错，并继续加载后续命令。


          - 配置文件中的交互类型命令仅支持 Y/N 自动交互。


          - 配置文件中命令的缩进要正确。系统视图下的命令需顶格，系统视图下的一级视图需顶
格，一级视图下的命令需缩进一个空格，多级视图依次缩进一格。


          - # 号如果顶格，表示回退到系统视图；非顶格，则只是用来隔离命令块，但缩进需正确，
和其下方的命令块对齐。如果使用错误，可能会导致配置丢失或者命令在非预期视图执
行。


          - 配置文件必须以 *.zip 、 *.cfg 、 *.txt 、 *.dat 、 *.bat 作为扩展名，或者不带扩展名。 FTP 或者
SFTP 模式下，支持带服务器目录文件名。文件名不包括特殊字符“ ~ ”“ ? ”“             - ”“ / ”
“ \ ”“ : ” “ " ” “ | ”“ < ”“        - ”“ [ ”“ ] ”。其中，


– *.cfg 和 *.txt 为纯文本格式，可直接查看其内容。指定为待加载的配置文件后，替换时
系统对里面的命令逐条进行恢复。


–
*.zip 是 *.cfg 格式文件的压缩，占用空间较小。指定为待加载的配置文件后，系统先将
文件解压成 .cfg 格式，然后逐条恢复。 *.cfg 文件名必须和 *.zip 文件名一致，否则会导
致配置文件加载失败。


– *.dat 是压缩文件格式，可以是二进制格式或者文本格式文件。只能执行从华为设备导
出的 *.dat 文件，且不能手工修改，否则会导致配置文件加载失败。


– *.bat 是批处理文件，为纯文本格式文件，可以手工修改。


步骤 **3** 执行命令 **commit** ，提交配置。


**----**
结束
