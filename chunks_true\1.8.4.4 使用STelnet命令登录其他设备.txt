

用户从 SSH 客户端以 STelnet 方式登录到 SSH 服务器，并对其进行管理和维护。

##### 背景信息


只有当服务器正在侦听的端口号是 22 时， SSH 客户端登录时可以不指定端口号，否则
如果是其他侦听端口号， SSH 客户端登录时必须指定端口号。


请在作为 SSH 客户端的设备上进行如下的配置。

##### 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** （可选）执行命令 **ssh client cipher** { **des_cbc** | **3des_cbc** | **aes128_cbc** |
**aes192_cbc** | **aes256_cbc** | **aes128_ctr** | **aes192_ctr** | **aes256_ctr** | **arcfour128** |
**arcfour256** | **aes128_gcm** | **aes256_gcm** | **sm4_cbc** | **sm4_gcm** } [*] ，配置 SSH 客户
端上的加密算法。


说明


为保证更好的安全性，建议使用以下安全性更高的加密算法： aes128_ctr 、 aes256_ctr 、
aes192_ctr 、 aes128_gcm 、 aes256_gcm 。


该命令中的参数 **des_cbc** 、 **3des_cbc** 、 **aes128_cbc** 、 **aes256_cbc** 、 **arcfour128** 、 **arcfour256** 、
**aes192_cbc** 和 **sm4_cbc** 算法为弱安全算法不建议使用，如需配置，需执行 **undo crypto weak-**
**algorithm disable** 命令使能弱安全算法功能。为避免安全风险，建议改用更安全的算法。


步骤 **3** （可选）执行命令 **ssh client hmac** { **md5** | **md5_96** | **sha1** | **sha1_96** | **sha2_256** |
**sha2_256_96** | **sha2_512** | **sm3** } [*] ，配置 SSH 客户端 HMAC 认证算法。


说明


为保证更好的安全性，建议使用以下安全性更高的 HMAC 算法： sha2_256 、 sha2_512 。


该命令中的参数该命令中的参数 **md5** 、 **md5_96** 、 **sha1** 、 **sha1_96** 和 **sha2_256_96** 算法为弱安全
算法不建议使用，如需配置，需执行 **undo crypto weak-algorithm disable** 命令使能弱安全算
法功能。为避免安全风险，建议改用更安全的算法。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 241


HUAWEI NetEngine40E
配置指南 1 基础配置


步骤 **4** （可选）执行命令 **ssh client key-exchange** { **dh_group14_sha1** | **dh_group1_sha1**
| **dh_group_exchange_sha1** | **dh_group_exchange_sha256** | **dh_group16_sha512**
| **ecdh_sha2_nistp256** | **ecdh_sha2_nistp384** | **ecdh_sha2_nistp521** | **sm2_kep** |
**curve25519_sha256** } [*] ，配置 SSH 客户端上的密钥交换算法列表。


说明


为保证更好的安全性，建议使用以下安全性更高的密钥交换算法： dh_group16_sha512 。


命令中的参数 **dh_group_exchange_sha1** 、 **dh_group1_sha1** 和 **dh_group14_sha1** 算法为弱安
全算法不建议使用，如需配置，需执行 **undo crypto weak-algorithm disable** 命令使能弱安全
算法功能。为避免安全风险，建议改用更安全的算法。


步骤 **5** 执行命令 **commit** ，提交配置。


步骤 **6** 在用户视图或系统视图下，根据 IP 源地址类型，选择执行如下命令：


       - 源地址为 IPv4 类型。


执行命令 **stelnet** [ **-a** source-ip-address ] [ **-force-receive-pubkey** ] host-ipaddress [ server-port ] [ [ **prefer_kex** prefer_kex ] | [ **prefer_ctos_cipher**
prefer_ctos_cipher ] | [ **prefer_stoc_cipher** prefer_stoc_cipher ] |

[ **prefer_ctos_hmac** prefer_ctos_hmac ] | [ **prefer_stoc_hmac**
prefer_stoc_hmac ] | [ **prefer_ctos_compress zlib** ] | [ **prefer_stoc_compress**
**zlib** ] | [ **-vpn-instance** vpn-instance-name ] | [ **-ki** interval ] | [ **-kc** count ] |

[ **identity-key** identity-key-type ] | [ **user-identity-key** user-key ] ] [*] ，使用
STelnet 协议通过 IPv4 地址登录到 SSH 服务器。


       - 源地址为 IPv6 类型。


执行命令 **stelnet ipv6** [ **-a** source-ipv6-address ] [ **-force-receive-pubkey** ]
host-ipv6-address [ [ **public-net** | **-vpn-instance** vpn-instance-name ] | [ **-oi**
{ interface-name | interface-type interface-number } ] | [ server-port ] |

[ **prefer_kex** prefer_kex ] | [ **prefer_ctos_cipher** prefer_ctos_cipher ] |

[ **prefer_stoc_cipher** prefer_stoc_cipher ] | [ **prefer_ctos_hmac**
prefer_ctos_hmac ] | [ **prefer_stoc_hmac** prefer_stoc_hmac ] |

[ **prefer_ctos_compress zlib** ] | [ **prefer_stoc_compress zlib** ] | [ **-ki** interval ]
| [ **-kc** count ] | [ **identity-key** identity-key-type ] | [ **user-identity-key** userkey ] ] [*] ，使用 STelnet 协议通过 IPv6 地址登录到 SSH 服务器。


**----**
结束
