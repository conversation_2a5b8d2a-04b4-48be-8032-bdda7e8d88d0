

在本示例中，通过在 TFTP 服务器端运行 TFTP 软件，并设置源文件在服务器中的位置，
实现文件的上传和下载。

##### 组网需求


FTP 是 TCP/IP 协议族中最常用的文件传送协议，但是终端和服务器之间交互复杂，对于
没有先进操作系统的终端而言实现文件传输相当困难。因此， TFTP 应运而生，它提供
不复杂、开销不大的服务，是专为终端和服务器间不需要复杂交互而设计。但是 TFTP
只限于简单文件传送操作，不提供存取授权。


如 图 **1-69** 所示，从终端登录到 TFTP 客户端，再从 TFTP 服务器上传、下载文件。


图 **1-69** 配置通过 TFTP 访问其他设备文件组网图


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 298


HUAWEI NetEngine40E
配置指南 1 基础配置

##### 注意事项


当网络所处环境不足够安全时，我们建议选择安全的协议。安全协议举例参见：
************* 通过 **SFTP** 访问其他设备文件配置示例（ **ECC** 认证方式） 。

##### 配置思路


采用如下的思路配置通过 TFTP 访问其他设备文件：


1. 在 TFTP 服务器端运行 TFTP 软件，并设置源文件在服务器中的位置。


2. 在 TFTP 客户端上使用 TFTP 命令实现下载文件。


3. 在 TFTP 客户端上使用 TFTP 命令实现上传文件。

##### 数据准备


为完成此配置例，需准备如下的数据：


       - TFTP 服务器端安装 TFTP 软件。


       - 源文件在 TFTP 服务器中的路径。


       - 目标文件名及在 TFTP 客户端存放的路径。

##### 操作步骤


步骤 **1** 启动 TFTP 服务器


设置 TFTP 服务器的 Current Directory 目录为下载文件所在的目录，界面如 图 **1-70** 所
示。


图 **1-70** 设置 TFTP 服务器基础目录


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 299


HUAWEI NetEngine40E
配置指南 1 基础配置


说明


由于计算机使用的 TFTP 服务器软件不同，屏幕显示可能不同。


在远端系统中启动 **tftpservermt** 登录该系统，进入 TFTP 服务器路径，并执行如下命
令。


**/home/<USER>/tftpserver -v -i tftpserver.ini**
TFTP Server MultiThreaded Version 1.61 Unix Built 1611
starting TFTP...
username: root
alias / is mapped to /home/
permitted clients: all
server port range: all
max blksize: 65464

default blksize: 512

default timeout: 3
file read allowed: Yes
file create allowed: Yes
file overwrite allowed: Yes
thread pool size: 1
listening on: 0.0.0.0:69
Accepting requests..


步骤 **2** 通过计算机终端仿真程序登录到 TFTP 客户端下载文件。


<HUAWEI> **tftp 10.18.26.141 get a.txt cfcard:/b.txt**
Warning: **cfcard:/b.txt** exists, overwrite? Please select

[Y/N]:y
Transfer file in binary mode.
Please wait for a while...
/
3338 bytes transferred
File transfer completed


步骤 **3** 验证配置结果


在 TFTP 客户端上执行命令 **dir** ，可以看到下载的目标文件存在于指定的目录。


<HUAWEI> **dir**
Directory of 0/17#cfcard:/


Idx Attr   Size(Byte) Date    Time(LMT)  FileName
0 -rw-   3,338    Jan 25 2011 09:27:41  b.txt
1 -rw-   103,265,123 Jan 25 2011 06:49:07  V800R023C10SPC500B020D0123.cc
2 -rw-   92,766,274 Jan 25 2011 06:49:10  V800R023C10SPC500SPC007B008D1012.cc


109,867,396 KB total (102,926,652 KB free)


步骤 **4** 通过计算机终端仿真程序登录到 TFTP 客户端上传文件。


<HUAWEI> **tftp 10.111.16.160 put sample.txt**
Info: Transfer file in binary mode.
Please wait for a while...
\   100% [***********]
File transfer completed


**----**
结束

##### 配置文件


无
