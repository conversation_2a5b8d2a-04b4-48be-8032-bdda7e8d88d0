

通过 STelnet 登录其他设备配置的示例。在本示例中，通过在 STelnet 客户端和 SSH 服务
器端生成本地密钥对，在 SSH 服务器端生成 DSA 公钥、并为用户绑定该 DSA 公钥，实现
Stelnet 客户端连接 SSH 服务器。

##### 组网需求


网络中有大量设备需要管理与维护，用户不可能为每台设备连接用户终端，特别是终
端与需要管理的设备之间无可达路由时，用户可以使用 Telnet 方式从当前设备登录到网
络上另一台设备，从而实现对远程设备的管理与维护。但是 Telnet 缺少安全的认证方
式，而且传输过程采用 TCP 进行简单方式传输，存在很大的安全隐患。


而 STelnet 是一种安全的 Telnet 服务，建立在 SSH 连接的基础之上。 SSH 可以利用加密和
强大的认证功能提供安全保障，保护设备不受诸如 IP 地址欺诈等攻击。如 图 **1-66** 所
示， SSH 服务器端 STelnet 服务使能后， STelnet 客户端可以通过 RSA 、 DSA 、 ECC 、
SM2 、 x509v3-ssh-rsa 、 password 、 password-rsa 、 password-ecc 、 password-dsa 、
password-sm2 、 password-x509v3-rsa 和 all 认证方式登录到 SSH 服务器端。


图 **1-66** 通过 STelnet 登录其他设备组网图


说明


本例中的 Interface1 代表接口 GigabitEthernet0/0/0 。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 278


HUAWEI NetEngine40E
配置指南 1 基础配置

##### 注意事项


配置两个登录用户为 Client001 和 Client002 ，分别使用 password 方式和 DSA 方式登录
SSH 服务器。

##### 配置思路


采用如下的思路配置通过 STelnet 登录其他设备：


1. 在 SSH 服务器上配置用户 Client001 和 Client002 ，分别使用不同的认证方式登录
SSH 服务器。


2. 分别在 STelnet 客户端 Client002 和 SSH 服务器端生成本地密钥对，并为用户
Client002 绑定 SSH 客户端的 DSA 公钥，实现客户端登录服务器端时，对客户端进
行验证。


3. SSH 服务器端 STelnet 服务使能。


4. 配置 SSH 用户 Client001 和 Client002 的服务方式为 STelnet 。


5. 使能 SSH 客户端首次认证功能。


6. 用户 Client001 和 Client002 分别以 STelnet 方式实现登录 SSH 服务器。

##### 数据准备


为完成此配置举例，需准备如下的数据：


       - 用户 Client001 ，登录验证方式为 password 。


       - 用户 Client002 ，验证方式为 DSA ，并为其分配公钥 dsakey001 。


       - SSH 服务器的 IP 地址为 ******** 。

##### 操作步骤


步骤 **1** 在服务器端生成本地密钥对


<HUAWEI> **system-view**

[ ~ HUAWEI] **sysname SSH Server**

[*HUAWEI] **commit**

[ ~ SSH Server] **dsa local-key-pair create**
Info: The key name will be: SSH SERVER_Host_DSA
Info: The key modulus can be any one of the following : 2048.
Info: Key pair generation will take a short while.
Info: Generating keys...
Info: Succeeded in creating the DSA host keys.


步骤 **2** 在服务器端创建 SSH 用户


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 279


HUAWEI NetEngine40E
配置指南 1 基础配置


说明


SSH 用户主要有 Password 、 RSA 、 password-rsa 、 ECC 、 password-ecc 、 DSA 、 password-dsa 、
SM2 、 password-sm2 或 all 这几种认证方式：


          - 如果 SSH 用户的认证方式为 password 、 password-rsa 、 password-dsa 、 password-sm2 和
password-ecc 时，必须配置同名的 local-user 用户。


          - 如果 SSH 用户的认证方式为 RSA 、 password-rsa 、 DSA 、 password-dsa 、 SM2 、 passwordsm2 、 ECC 、 password-ecc 和 all ，服务器端应保存 SSH 客户端的 RSA 、 DSA 、 SM2 或 ECC 公
钥。


# 配置 VTY 用户界面。


[ ~ SSH Server] **user-interface vty 0 4**

[ ~ SSH Server-ui-vty0-4] **authentication-mode aaa**

[*SSH Server-ui-vty0-4] **protocol inbound ssh**

[*SSH Server-ui-vty0-4] **user privilege level 3**

[*SSH Server-ui-vty0-4] **commit**

[ ~ SSH Server-ui-vty0-4] **quit**


       - 创建 SSH 用户 Client001 。


# 新建用户名为 Client001 的 SSH 用户，且认证方式为 password 。


[ ~ SSH Server] **ssh user client001**

[*SSH Server] **ssh user client001 authentication-type password**

[*SSH Server] **ssh server cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr**

[*SSH Server] **ssh server hmac sha2_512 sha2_256**

[*SSH Server] **ssh server key-exchange dh_group_exchange_sha256**

[*SSH Server] **ssh server publickey rsa_sha2_256 rsa_sha2_512**

[*SSH Server] **ssh server dh-exchange min-len 3072**

[*SSH Server] **ssh client publickey rsa_sha2_256 rsa_sha2_512**

[*SSH Server] **ssh client cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr**

[*SSH Server] **ssh client hmac sha2_512 sha2_256**

[*SSH Server] **ssh client key-exchange dh_group_exchange_sha256**

[*SSH Server] **commit**


# 为 SSH 用户 Client001 配置密码。


[ ~ SSH Server] **aaa**

[*SSH Server-aaa] **local-user client001 password**
Please configure the password (8-128)
Enter Password:
Confirm Password:


说明


设置的密码必须满足以下要求：


–
密码采取交互式输入，系统不回显输入的密码。


– 输入的密码为字符串形式，区分大小写，长度范围是 8 ～ 16 。输入的密码至少包含两种
类型字符，包括大写字母、小写字母、数字及特殊字符。


–
特殊字符不包括“？”和空格。但是，当输入的密码两端使用双引号时，可在密码中间
输入空格。

#### ▪ 如果使用双引号设置带空格密码，双引号之间不能再使用双引号。 ▪ 如果使用双引号没有设置带空格密码，双引号之间可以再使用双引号。


例如， "Aa 123"45"" 为不合法密码， "Aa123"45"" 为合法密码。


配置文件中将以密文形式体现设置的密码。


[*SSH Server-aaa] **local-user client001 service-type ssh**

[*SSH Server-aaa] **commit**

[ ~ SSH Server-aaa] **quit**


       - 创建 SSH 用户 Client002 。


# 新建用户名为 Client002 的 SSH 用户，且认证方式为 DSA 。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 280


HUAWEI NetEngine40E
配置指南 1 基础配置


[ ~ SSH Server] **ssh user client002**

[*SSH Server] **ssh user client002 authentication-type dsa**

[*SSH Server] **ssh server cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr**

[*SSH Server] **ssh server hmac sha2_512 sha2_256**

[*SSH Server] **ssh server key-exchange dh_group_exchange_sha256**

[*SSH Server] **ssh server publickey rsa_sha2_256 rsa_sha2_512**

[*SSH Server] **ssh server dh-exchange min-len 3072**

[*SSH Server] **ssh client publickey rsa_sha2_256 rsa_sha2_512**

[*SSH Server] **ssh client cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr**

[*SSH Server] **ssh client hmac sha2_512 sha2_256**

[*SSH Server] **ssh client key-exchange dh_group_exchange_sha256**

[*SSH Server] **ssh authorization-type default root**

[*SSH Server] **commit**


步骤 **3** 配置服务器端 DSA 公钥


# 客户端 Client002 生成客户端的本地密钥对


<HUAWEI> **system-view**

[ ~ HUAWEI] **sysname client002**

[*HUAWEI] **commit**

[ ~ client002] **dsa local-key-pair create**
Info: The key name will be: client002_Host_DSA
Info: The key modulus can be any one of the following : 2048.
Info: Key pair generation will take a short while.
Info: Generating keys...
Info: Succeeded in creating the DSA host keys.

[*client002] **commit**


# 查看客户端上生成 DSA 公钥。


[*client002] **display dsa local-key-pair public**

========================================================

Time of Key pair created : 2013-05-21 17:18:17
Key name  : client002_Host_DSA
Key modulus : 1024
Key type  : DSA Encryption Key

========================================================

Key code:


3082019F

028180

A49C5EAF 906C80B1 C474CCB0 D47C6965 22DFCF3C

9602BAD8 FCE8F7E3 7A69BE18 8CB7D858 6B50EEBC

54BFB089 61A0DD31 5F7F3080 F0DB47E4 ECDCC10E

7EC18D31 35CD78F7 E002FB6B 4CB59BA5 E2CDB898

43FAD059 98B8EEA8 E7395FC7 CA9D1655 47927368

9914AF09 6CFDC125 6CC8A07F DDDE603B F31C4EA4

0B752AC7 817E877F

0214

CBC5C0BC 2D7B6DFE 15A7F9A3 6F6ED15B 6ECC9F27

028180

6D3202E7 4DCAC5DB 97034305 8D79FDB2 76D5CAA2

C8D00C3D 666F61D4 F2E36445 4027FD04 0D61B2A3

AF3CED6B C36CC68D E8DF35F9 FAF802ED 73BCBD66

C55AE0F6 69530C14 1B33A5A1 CF77D636 75A5EF3B

264AB66E 2A8CFFB1 690E45F8 6FACF1B3 E2A11328

C14BA7F3 CA0D198B 3ED94368 45BA5E89 F1ADB79E

F459F826 B9A5CF6D

028180

409C0AE7 1DDDDA8C F3924608 DC32728C D6FA51FB

B4933D03 E30780E1 676AA9EE E3A9B677 97DB1D3A

57AF479C 3BDC4096 291B4548 43D88851 DCFEB04D

593F1459 9145FB0B 071CEEE5 5F951E64 CA6C4C16

6192B926 9AD8764E E9F8661C 8EC08D08 BD83BCE3

E054EE39 20207689 433B07A1 1219B9F3 945E88F0

3A8FC0FB 9883905B


Host public Key for PEM format Code:
---- BEGIN SSH2 PUBLIC KEY ---

文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 281


HUAWEI NetEngine40E
配置指南 1 基础配置


AAAAB3NzaC1kc3MAAACBAKScXq+QbICxxHTMsNR8aWUi3888lgK62Pzo9+N6ab4Y
jLfYWGtQ7rxUv7CJYaDdMV9/MIDw20fk7NzBDn7BjTE1zXj34AL7a0y1m6XizbiY
Q/rQWZi47qjnOV/Hyp0WVUeSc2iZFK8JbP3BJWzIoH/d3mA78xxOpAt1KseBfod/
AAAAFQDLxcC8LXtt/hWn+aNvbtFbbsyfJwAAAIBtMgLnTcrF25cDQwWNef2ydtXK
osjQDD1mb2HU8uNkRUAn/QQNYbKjrzzta8Nsxo3o3zX5+vgC7XO8vWbFWuD2aVMM
FBszpaHPd9Y2daXvOyZKtm4qjP+xaQ5F+G+s8bPioRMowUun88oNGYs+2UNoRbpe
ifGtt570WfgmuaXPbQAAAIBAnArnHd3ajPOSRgjcMnKM1vpR+7STPQPjB4DhZ2qp
7uOptneX2x06V69HnDvcQJYpG0VIQ9iIUdz+sE1ZPxRZkUX7Cwcc7uVflR5kymxM
FmGSuSaa2HZO6fhmHI7AjQi9g7zj4FTuOSAgdolDOwehEhm585ReiPA6j8D7mIOQ
Ww==

---- END SSH2 PUBLIC KEY ---

Public key code for pasting into OpenSSH authorized_keys file:
ssh-dss AAAAB3NzaC1kc3MAAACBAKScXq
+QbICxxHTMsNR8aWUi3888lgK62Pzo9+N6ab4YjLfYWGtQ7rxUv7CJYaDdMV9/
MIDw20fk7NzBDn7BjTE1zXj34AL7a0y1m6XizbiYQ/rQWZi47qjnOV/Hyp0WVUeSc2iZFK8JbP3BJWzIoH/
d3mA78xxOpAt1KseBfod/AAAAFQDLxcC8LXtt/hWn
+aNvbtFbbsyfJwAAAIBtMgLnTcrF25cDQwWNef2ydtXKosjQDD1mb2HU8uNkRUAn/
QQNYbKjrzzta8Nsxo3o3zX5+vgC7XO8vWbFWuD2aVMMFBszpaHPd9Y2daXvOyZKtm4qjP+xaQ5F+G
+s8bPioRMowUun88oNGYs+2UNoRbpeifGtt570WfgmuaXPbQAAAIBAnArnHd3ajPOSRgjcMnKM1vpR
+7STPQPjB4DhZ2qp7uOptneX2x06V69HnDvcQJYpG0VIQ9iIUdz
+sE1ZPxRZkUX7Cwcc7uVflR5kymxMFmGSuSaa2HZO6fhmHI7AjQi9g7zj4FTuOSAgdolDOwehEhm585ReiPA6j
8D7mIOQWw== dsa-key


# 将客户端上产生的 DSA 公钥传送到服务器端。


[*SSH Server] **dsa peer-public-key dsakey001 encoding-type der**
Info: Enter "DSA public key" view, return system view with "peer-public-key end".

[*SSH Server-dsa-public-key] **public-key-code begin**
Info: Enter "DSA key code" view, return last view with "public-key-code end".

[*SSH Server-dsa-public-key-dsa-key-code] **3082019F**

[*SSH Server-dsa-public-key-dsa-key-code] **028180**

[*SSH Server-dsa-public-key-dsa-key-code] **A49C5EAF 906C80B1 C474CCB0 D47C6965 22DFCF3C**

[*SSH Server-dsa-public-key-dsa-key-code] **9602BAD8 FCE8F7E3 7A69BE18 8CB7D858 6B50EEBC**

[*SSH Server-dsa-public-key-dsa-key-code] **54BFB089 61A0DD31 5F7F3080 F0DB47E4 ECDCC10E**

[*SSH Server-dsa-public-key-dsa-key-code] **7EC18D31 35CD78F7 E002FB6B 4CB59BA5 E2CDB898**

[*SSH Server-dsa-public-key-dsa-key-code] **43FAD059 98B8EEA8 E7395FC7 CA9D1655 47927368**

[*SSH Server-dsa-public-key-dsa-key-code] **9914AF09 6CFDC125 6CC8A07F DDDE603B F31C4EA4**

[*SSH Server-dsa-public-key-dsa-key-code] **0B752AC7 817E877F**

[*SSH Server-dsa-public-key-dsa-key-code] **0214**

[*SSH Server-dsa-public-key-dsa-key-code] **CBC5C0BC 2D7B6DFE 15A7F9A3 6F6ED15B 6ECC9F27**

[*SSH Server-dsa-public-key-dsa-key-code] **028180**

[*SSH Server-dsa-public-key-dsa-key-code] **6D3202E7 4DCAC5DB 97034305 8D79FDB2 76D5CAA2**

[*SSH Server-dsa-public-key-dsa-key-code] **C8D00C3D 666F61D4 F2E36445 4027FD04 0D61B2A3**

[*SSH Server-dsa-public-key-dsa-key-code] **AF3CED6B C36CC68D E8DF35F9 FAF802ED 73BCBD66**

[*SSH Server-dsa-public-key-dsa-key-code] **C55AE0F6 69530C14 1B33A5A1 CF77D636 75A5EF3B**

[*SSH Server-dsa-public-key-dsa-key-code] **264AB66E 2A8CFFB1 690E45F8 6FACF1B3 E2A11328**

[*SSH Server-dsa-public-key-dsa-key-code] **C14BA7F3 CA0D198B 3ED94368 45BA5E89 F1ADB79E**

[*SSH Server-dsa-public-key-dsa-key-code] **F459F826 B9A5CF6D**

[*SSH Server-dsa-public-key-dsa-key-code] **028180**

[*SSH Server-dsa-public-key-dsa-key-code] **409C0AE7 1DDDDA8C F3924608 DC32728C D6FA51FB**

[*SSH Server-dsa-public-key-dsa-key-code] **B4933D03 E30780E1 676AA9EE E3A9B677 97DB1D3A**

[*SSH Server-dsa-public-key-dsa-key-code] **57AF479C 3BDC4096 291B4548 43D88851 DCFEB04D**

[*SSH Server-dsa-public-key-dsa-key-code] **593F1459 9145FB0B 071CEEE5 5F951E64 CA6C4C16**

[*SSH Server-dsa-public-key-dsa-key-code] **6192B926 9AD8764E E9F8661C 8EC08D08 BD83BCE3**

[*SSH Server-dsa-public-key-dsa-key-code] **E054EE39 20207689 433B07A1 1219B9F3 945E88F0**

[*SSH Server-dsa-public-key-dsa-key-code] **3A8FC0FB 9883905B**

[*SSH Server-dsa-public-key-dsa-key-code] **public-key-code end**

[*SSH Server-dsa-public-key] **peer-public-key end**

[*SSH Server] **commit**


步骤 **4** 为 SSH 用户 Client002 绑定 SSH 客户端的 DSA 公钥。


[ ~ SSH Server] **ssh user client002 assign dsa-key dsakey001**

[*SSH Server] **commit**


步骤 **5** SSH 服务器端 STelnet 服务使能


# 使能 STelnet 服务功能。


[ ~ SSH Server] **stelnet server enable**


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 282


HUAWEI NetEngine40E
配置指南 1 基础配置


[*SSH Server] **ssh server-source** **-i GigabitEthernet0/0/0**

[*SSH Server] **commit**


步骤 **6** 配置 SSH 用户 Client001 、 Client002 的服务方式为 STelnet


[ ~ SSH Server] **ssh user client001 service-type stelnet**

[*SSH Server] **ssh user client002 service-type stelnet**

[*SSH Server] **commit**


步骤 **7** STelnet 客户端连接 SSH 服务器


# 第一次登录，需要使能 SSH 客户端首次认证功能。


使能客户端 Client001 首次认证功能。


<HUAWEI> **system-view**

[ ~ HUAWEI] **sysname client001**

[*HUAWEI] **commit**

[ ~ client001] **ssh client first-time enable**

[*client001] **commit**


使能客户端 Client002 首次认证功能


[ ~ client002] **ssh client first-time enable**

[*client002] **commit**


# STelnet 客户端 Client001 用 password 认证方式连接 SSH 服务器，输入配置的用户名和
密码。


[ ~ client001] **stelnet **********
Please input the username:client001
Trying ******** ...
Press CTRL+K to abort

Connected to ******** ...
The server is not authenticated. Continue to access it?(Y/N):y
Save the server's public key?(Y/N):y
The server's public key will be saved with the name ********. Please wait...
Enter password:


显示登录成功信息如下：


Info: The max number of VTY users is 20, and the number
of current VTY users on line is 6.
The current login time is 2011-01-06 11:42:42.
<SSH Server>


# STelnet 客户端 Client002 用 DSA 认证方式连接 SSH 服务器。


[ ~ client002] **stelnet **********
Trying ******** ...
Press CTRL + K to abort

Connected to ******** ...
Please input the username: client002
Please select public key type for user authentication [R for RSA/D for DSA/E for ECC] Please select [R/D/E]:
Enter password:
Warning: The initial password poses security risks.
The password needs to be changed. Change now? [Y/N]:n
Info: The max number of VTY users is 15, the number of current VTY users online is 1, and total number of
terminal users online is 1.
The current login time is 2015-07-13 15:33:08.
The last login time is 2015-07-13 15:26:18 from 127.0.0.1 through SSH.
<SSH Server>


如果登录成功，用户将进入用户视图。如果登录失败，用户将收到 **Session is**
**disconnected** 的信息。


步骤 **8** 验证配置结果


# 查看 SSH 状态信息。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 283


HUAWEI NetEngine40E
配置指南 1 基础配置


[ ~ SSH Server] **display ssh server status**
SSH Version                : 2.0
SSH authentication timeout (Seconds)    : 60
SSH authentication retries (Times)     : 3
SSH server key generating interval (Hours) : 0
SSH version 1.x compatibility       : Enable
SSH server keepalive            : Disable
SFTP IPv4 server              : Enable

SFTP IPv6 server              : Enable

STELNET IPv4 server            : Enable

STELNET IPv6 server            : Enable

SNETCONF IPv4 server            : Enable

SNETCONF IPv6 server            : Enable
SNETCONF IPv4 server port(830)       : Disable
SNETCONF IPv6 server port(830)       : Disable
SCP IPv4 server              : Enable

SCP IPv6 server              : Enable
SSH port forwarding            : Disable
SSH IPv4 server port            : 22
SSH IPv6 server port            : 22
ACL name                  :

ACL number                 :

ACL6 name                 :

ACL6 number                :
SSH server ip-block            : Enable


# 查看 SSH 用户信息。


[ ~ SSH Server] **display ssh user-information**

---------------------------------------------------
User Name        : client001
Authentication-Type   : password
User-public-key-name  : User-public-key-type  : Sftp-directory     : Service-type      : stelnet


User Name        : client002
Authentication-Type   : dsa
User-public-key-name  : dsakey001
User-public-key-type  : Sftp-directory     : Service-type      : stelnet

---------------------------------------------------

**----**
结束

##### 配置文件


       - SSH 服务器的配置文件


#

sysname SSH Server
#
interface GigabitEthernet0/0/0
undo shutdown
ip address ******** ***********
#
dsa peer-public-key dsakey001
public-key-code begin
3082019F

028180

A49C5EAF 906C80B1 C474CCB0 D47C6965 22DFCF3C

9602BAD8 FCE8F7E3 7A69BE18 8CB7D858 6B50EEBC

54BFB089 61A0DD31 5F7F3080 F0DB47E4 ECDCC10E

7EC18D31 35CD78F7 E002FB6B 4CB59BA5 E2CDB898

43FAD059 98B8EEA8 E7395FC7 CA9D1655 47927368

9914AF09 6CFDC125 6CC8A07F DDDE603B F31C4EA4

0B752AC7 817E877F


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 284


HUAWEI NetEngine40E
配置指南 1 基础配置


0214

CBC5C0BC 2D7B6DFE 15A7F9A3 6F6ED15B 6ECC9F27

028180

6D3202E7 4DCAC5DB 97034305 8D79FDB2 76D5CAA2

C8D00C3D 666F61D4 F2E36445 4027FD04 0D61B2A3

AF3CED6B C36CC68D E8DF35F9 FAF802ED 73BCBD66

C55AE0F6 69530C14 1B33A5A1 CF77D636 75A5EF3B

264AB66E 2A8CFFB1 690E45F8 6FACF1B3 E2A11328

C14BA7F3 CA0D198B 3ED94368 45BA5E89 F1ADB79E

F459F826 B9A5CF6D

028180

409C0AE7 1DDDDA8C F3924608 DC32728C D6FA51FB

B4933D03 E30780E1 676AA9EE E3A9B677 97DB1D3A

57AF479C 3BDC4096 291B4548 43D88851 DCFEB04D

593F1459 9145FB0B 071CEEE5 5F951E64 CA6C4C16

6192B926 9AD8764E E9F8661C 8EC08D08 BD83BCE3

E054EE39 20207689 433B07A1 1219B9F3 945E88F0

3A8FC0FB 9883905B
public-key-code end
peer-public-key end
#

stelnet server enable
ssh server-source -i GigabitEthernet0/0/0
ssh user client001
ssh user client001 authentication-type password
ssh user client001 service-type stelnet
ssh user client002
ssh user client002 assign dsa-key dsakey001
ssh user client002 authentication-type dsa
ssh authorization-type default root
ssh user client002 service-type stelnet
#
ssh server cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr
ssh server hmac sha2_512 sha2_256
ssh server key-exchange dh_group_exchange_sha256
#
ssh server publickey rsa_sha2_256 rsa_sha2_512
#
ssh server dh-exchange min-len 3072
#
ssh client publickey dsa ecc rsa rsa_sha2_256 rsa_sha2_512
#
ssh client cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr
ssh client hmac sha2_512 sha2_256
ssh client key-exchange dh_group_exchange_sha256
#

aaa
local-user client001 password cipher @%@%UyQs4,KTtSwJo(4QmW#K,LC:@%@%
local-user client001 service-type ssh
#
user-interface vty 0 4
authentication-mode aaa
protocol inbound ssh
user privilege level 3
#

return


       - SSH 客户端 Client001 的配置文件


#
sysname client001
#
interface GigabitEthernet0/0/0
ip address ******** *************
#
ssh client first-time enable
#

return


       - SSH 客户端 Client002 的配置文件


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 285


HUAWEI NetEngine40E
配置指南 1 基础配置


#
sysname client002
#
interface GigabitEthernet0/0/0
ip address ******** *************
#
ssh client first-time enable
#

return
