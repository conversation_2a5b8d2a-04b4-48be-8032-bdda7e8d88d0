

使能 SSH 客户端首次认证功能后，当 SFTP 客户端第一次登录 SSH 服务器时，不对 SSH 服
务器的 RSA 、 DSA 、 SM2 或 ECC 公钥进行有效性检查。

##### 背景信息


如果配置了使能 SSH 客户端首次认证功能，那么在 SFTP 客户端第一次登录 SSH 服务器
后，不对 SSH 服务器的 RSA 、 DSA 、 SM2 或 ECC 公钥进行有效性检查。登录后，系统将
自动分配并保存 RSA 、 DSA 、 SM2 或 ECC 公钥，为下次登录时认证。


请在作为 SSH 客户端的设备上进行如下的配置。

##### 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **ssh client first-time enable** ，使能 SSH 客户端首次登录功能。


步骤 **3** 执行命令 **commit** ，提交配置。


**----**
结束
