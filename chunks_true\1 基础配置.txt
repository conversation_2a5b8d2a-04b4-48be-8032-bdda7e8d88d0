

1.1 前 言


1.2 首次登录配置
当用户需要为第一次上电的设备进行基本配置时，可以通过 Console 口登录设备，还可
以通过即插即用方式批量部署设备。


1.3 命令行接口配置
用户对设备的日常操作，主要通过输入命令行来实现。用户可以在命令视图下编辑和
配置命令行，并显示配置成功或错误的信息。


1.4 用户界面配置
当用户通过 Console 口、 Telnet 或 SSH 方式登录路由器时，系统会分配相应的用户界
面，用来管理、监控设备和当前用户之间的会话。


1.5 用户登录配置
用户可以通过 Console 口、 Telnet 或 SSH （ STelnet ）方式登录设备，实现对设备的本地
或远程维护。


1.6 文件系统配置
文件系统实现对存储设备中的文件、目录的管理。


1.7 配置管理配置
为了保障用户配置的可靠性，系统支持两种配置生效模式。


1.8 访问其他设备配置
设备可以作为客户端访问网络上的其他设备。


1.9 ZTP 配置
设备可以通过零配置自动部署 ZTP （ Zero Touch Provisioning ）实现空配置下的上电自
动部署。
