import pymupdf4llm
import pymupdf
import json
import logging
from dataclasses import dataclass
from typing import List, Dict, Any, Optional
import re
from pathlib import Path
import time
from concurrent.futures import ProcessPoolExecutor, as_completed

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('pdf_converter.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def print_dict(str):
    """格式化打印字典"""
    print(json.dumps(str, ensure_ascii=False, indent=2))


@dataclass
class TitleInfo:
    """标题信息"""
    level: int
    text: str
    page_num: int
    original_text: str  # TOC中的原始文本

    
def clean_title_text(title: str) -> str:
    """清理标题文本"""
    # 移除多余的空格和特殊字符
    title = re.sub(r'\s+', ' ', title.strip())
        
    # 移除可能的页码信息
    title = re.sub(r'\s*\.\.\.\s*\d+\s*$', '', title)
    title = re.sub(r'\s*\d+\s*$', '', title)
        
    return title.strip()
    
def extract_toc_titles(doc: pymupdf.Document) -> List[TitleInfo]:
    """提取TOC标题信息"""
    logger.info("正在提取TOC标题信息...")
    
    toc = doc.get_toc()
    if not toc:
        logger.warning("PDF中没有找到目录信息")
        return []
    
    titles = []
    for item in toc:
        level, title, page_num = item
        # print(item)
        
        # 清理标题文本
        title_info = TitleInfo(
            level=level,
            text=clean_title_text(title),
            page_num=page_num,
            original_text=title
        )
        titles.append(title_info)
    
    logger.info(f"提取到 {len(titles)} 个标题")
    
    return titles

def extract_text_between_substrings(main_string_list: List[str], start_substring: str, end_substring: str) -> str:
    """
    在行列表中提取两个子字符串之间的内容。
    注意: 这里的匹配逻辑是根据用户的原始代码实现的，用于处理标题行。
    """
    def _normalize_for_match(text: str) -> str:
        """规范化文本，移除 Markdown 标记和多余空格以便匹配"""
        return re.sub(r'[*#\s]+', '', text)
    
    # 提取所有带有 # 或 ** 的行，并进行规范化
    hash_lines = []
    for i, line in enumerate(main_string_list):
        if '#' in line or '**' in line:
            hash_lines.append((i, _normalize_for_match(line)))

    start_title = _normalize_for_match(start_substring)
    end_title = _normalize_for_match(end_substring)
    start_index = -1
    end_index = -1
    
    # 精准匹配
    for i, normalized_line in hash_lines: 
        if start_title == normalized_line:
            start_index = i
        if end_title == normalized_line:
            end_index = i

    # 模糊匹配
    if start_index == -1:
        for i, normalized_line in hash_lines:
            if start_title in normalized_line:
                start_index = i
                break
    if end_index == -1:
        for i, normalized_line in hash_lines:
            if end_title in normalized_line:
                end_index = i
                break
                
                
    if start_index  == end_index:
        # 如果找不到结束标题或开始和结束标题相同，则提取到文档末尾
        end_index = len(main_string_list)-1
        
    if start_index == -1 or end_index==-1:
        logger.warning(f"无法在文档中找到开始标题: '{start_substring}'")
        print(start_index)
        print(end_index)
        return None
        
    # 提取内容并进行简单的替换
    result_content = main_string_list[start_index + 1 : end_index]
    return re.sub(r"\n-+\n","\n",("\n".join(result_content)).replace('#', '//#'))
    


def extract_content_optimized(file_url: str) -> str:
    """
    优化后的内容提取函数。
    一次性读取整个PDF并转换为Markdown，然后通过处理内存中的文本列表来提取内容。
    """
    logger.info(f"开始处理文件: {file_url}")
    
    # 步骤 1: 一次性读取整个PDF并转换为Markdown
    doc = pymupdf.open(file_url)
    
    # 获取完整的Markdown内容字符串
    full_markdown_string = pymupdf4llm.to_markdown(doc,show_progress=True)
    
    md_path = Path('./raw_pdf/md/tmp_NE40E V800R023C10SPC500 配置指南 用户接入.md')
    md_path.write_text(full_markdown_string, encoding='utf-8')
    
    # 步骤 2: 将完整的Markdown字符串转换为行列表
    all_lines = full_markdown_string.split('\n')
    
    # 步骤 3: 提取TOC信息
    titles_metadata_list = extract_toc_titles(doc)
    if not titles_metadata_list:
        logger.warning("没有可用的目录标题，跳过内容提取。")
        return full_markdown_string # 或者返回空字符串，取决于需求
        
    result_content_list = []
    
    # 步骤 4: 遍历目录，并从内存中的行列表中提取内容
    for index, title_metadata in enumerate(titles_metadata_list):
        start_title = title_metadata.original_text
        
        # 确定下一个标题作为结束标记
        if index < len(titles_metadata_list) - 1:
            end_title = titles_metadata_list[index + 1].original_text
        else:
            # 最后一个标题，没有结束标记
            end_title = start_title 
            
        content = extract_text_between_substrings(all_lines, start_title, end_title)
        
        if content is not None:
            # 如果成功提取到内容，则格式化并添加到结果列表
            markdown_heading = "#" * int(title_metadata.level)
            result_content_list.append(f"{markdown_heading} {title_metadata.original_text}\n{content}")
        else:
            markdown_heading = "#" * int(title_metadata.level)
            result_content_list.append(f"{markdown_heading}\n")
            logger.warning(f"无法为标题 '{title_metadata.original_text}' 提取到内容。")
            
    return '\n'.join(result_content_list)

def process_single_pdf(pdf_file: Path):
    """
    处理单个PDF文件的函数，用于并行执行。
    """
    try:
        markdown = extract_content_optimized(str(pdf_file))
        OUT_DIR = pdf_file.parent / 'md'
        OUT_DIR.mkdir(exist_ok=True)
        md_path = OUT_DIR / f'{pdf_file.stem}.md'
        
        # 写入 Markdown 文件
        md_path.write_text(markdown, encoding='utf-8')
        logger.info(f'Markdown 文件已成功保存到: {md_path}')
        return f'成功处理文件: {pdf_file.name}'
    except Exception as e:
        logger.error(f"处理文件 {pdf_file.name} 时出错: {e}")
        return f'处理文件 {pdf_file.name} 失败'

if __name__ == '__main__':
    # PDF_DIR = Path(r'./raw_pdf')  # PDF 存放目录
    
    # # 收集所有PDF文件
    # pdf_files = list(PDF_DIR.glob('*.pdf'))
    # if not pdf_files:
    #     logger.warning("在指定目录中没有找到PDF文件。")
    # else:
    #     # 使用 ProcessPoolExecutor 进行并行处理
    #     # max_workers 可以根据您的CPU核心数进行调整，None表示使用所有核心
    #     with ProcessPoolExecutor(max_workers=None) as executor:
    #         # 提交所有任务
    #         futures = [executor.submit(process_single_pdf, pdf_file) for pdf_file in pdf_files]
            
    #         # 等待所有任务完成并获取结果
    #         for future in as_completed(futures):
    #             result = future.result()
    #             print(result)

    # PDF_DIR = Path(r'./raw_pdf')  # PDF 存放目录
    # OUT_DIR = PDF_DIR / 'md'  # 生成 Markdown 的子目录
    # OUT_DIR.mkdir(exist_ok=True)

    # for pdf_file in PDF_DIR.glob('*.pdf'):
    #     #记录每个转换的时间
    #     start_time = time.time()
    #     markdown = extract_content_optimized(str(pdf_file))  # 调用优化后的函数
    #     md_path = OUT_DIR / f'{pdf_file.stem}.md'
        
    #     # 写入 Markdown 文件
    #     md_path.write_text(markdown, encoding='utf-8')
    #     logger.info(f'Markdown 文件已成功保存到: {md_path}')
    #     end_time = time.time()
    #     logger.info(f'转换完成，耗时: {end_time - start_time:.2f} 秒')
    
    markdown = extract_content_optimized('./raw_pdf/NE40E V800R023C10SPC500 配置指南 QoS.pdf')  # 调用优化后的函数
    md_path = Path('./raw_pdf/md/NE40E V800R023C10SPC500 配置指南 用户接入.md')
    md_path.write_text(markdown, encoding='utf-8')
    
