#!/usr/bin/env python3
"""
全面分析两个markdown文档的差异，找出内容混淆的规律
"""

import re
from typing import List, Dict, Tuple

def extract_sections(markdown_content: str) -> List[Dict[str, str]]:
    """提取markdown中的所有章节"""
    sections = []
    lines = markdown_content.split('\n')
    
    current_section = None
    current_content = []
    
    for line in lines:
        # 检查是否是标题行
        if re.match(r'^#{1,6}\s+', line):
            # 保存前一个章节
            if current_section:
                sections.append({
                    'title': current_section,
                    'content': '\n'.join(current_content).strip()
                })
            
            # 开始新章节
            current_section = line.strip()
            current_content = []
        else:
            if current_section:  # 只有在有标题的情况下才收集内容
                current_content.append(line)
    
    # 保存最后一个章节
    if current_section:
        sections.append({
            'title': current_section,
            'content': '\n'.join(current_content).strip()
        })
    
    return sections

def analyze_content_patterns(sections: List[Dict[str, str]]) -> Dict[str, any]:
    """分析内容模式"""
    patterns = {
        'avg_content_length': 0,
        'short_sections': [],
        'long_sections': [],
        'empty_sections': [],
        'content_types': {
            '概述类': [],
            '配置类': [],
            '示例类': [],
            '检查类': []
        }
    }
    
    total_length = 0
    for section in sections:
        content_length = len(section['content'])
        total_length += content_length
        
        if content_length == 0:
            patterns['empty_sections'].append(section['title'])
        elif content_length < 100:
            patterns['short_sections'].append((section['title'], content_length))
        elif content_length > 1000:
            patterns['long_sections'].append((section['title'], content_length))
        
        # 分析内容类型
        title = section['title']
        content = section['content']
        
        if '概述' in title or '前言' in title:
            patterns['content_types']['概述类'].append((title, content[:200]))
        elif '配置' in title and '示例' not in title:
            patterns['content_types']['配置类'].append((title, content[:200]))
        elif '示例' in title:
            patterns['content_types']['示例类'].append((title, content[:200]))
        elif '检查' in title:
            patterns['content_types']['检查类'].append((title, content[:200]))
    
    patterns['avg_content_length'] = total_length / len(sections) if sections else 0
    
    return patterns

def compare_specific_sections(baseline_sections: List[Dict[str, str]], 
                            our_sections: List[Dict[str, str]]) -> List[Dict[str, any]]:
    """对比特定章节的差异"""
    comparisons = []
    
    # 创建标题到内容的映射
    baseline_map = {s['title']: s['content'] for s in baseline_sections}
    our_map = {s['title']: s['content'] for s in our_sections}
    
    # 找到共同的标题
    common_titles = set(baseline_map.keys()) & set(our_map.keys())
    
    for title in sorted(common_titles):
        baseline_content = baseline_map[title]
        our_content = our_map[title]
        
        # 计算差异
        baseline_len = len(baseline_content)
        our_len = len(our_content)
        
        # 检查内容是否相似
        baseline_words = set(baseline_content.split())
        our_words = set(our_content.split())
        
        if baseline_words and our_words:
            similarity = len(baseline_words & our_words) / len(baseline_words | our_words)
        else:
            similarity = 0.0
        
        comparison = {
            'title': title,
            'baseline_length': baseline_len,
            'our_length': our_len,
            'length_ratio': our_len / baseline_len if baseline_len > 0 else float('inf'),
            'similarity': similarity,
            'baseline_preview': baseline_content[:300],
            'our_preview': our_content[:300]
        }
        
        comparisons.append(comparison)
    
    return comparisons

def find_content_mixing_patterns(comparisons: List[Dict[str, any]]) -> Dict[str, any]:
    """找出内容混淆的模式"""
    patterns = {
        'oversized_sections': [],  # 内容过长的章节
        'undersized_sections': [], # 内容过短的章节
        'low_similarity': [],      # 相似度低的章节
        'potential_mixing': []     # 可能存在内容混淆的章节
    }
    
    for comp in comparisons:
        # 内容长度异常
        if comp['length_ratio'] > 3:  # 我们的内容比基准长3倍以上
            patterns['oversized_sections'].append(comp)
        elif comp['length_ratio'] < 0.3:  # 我们的内容比基准短70%以上
            patterns['undersized_sections'].append(comp)
        
        # 相似度低
        if comp['similarity'] < 0.3:
            patterns['low_similarity'].append(comp)
        
        # 可能的内容混淆（长度异常且相似度低）
        if comp['length_ratio'] > 2 and comp['similarity'] < 0.5:
            patterns['potential_mixing'].append(comp)
    
    return patterns

def main():
    print("=== 全面分析两个markdown文档的差异 ===")
    
    # 读取两个文档
    try:
        with open('baseline_pymupdf4llm.md', 'r', encoding='utf-8') as f:
            baseline_content = f.read()
        
        with open('fixed_output.md', 'r', encoding='utf-8') as f:
            our_content = f.read()
    except FileNotFoundError as e:
        print(f"文件未找到: {e}")
        return
    
    print(f"基准文档长度: {len(baseline_content)} 字符")
    print(f"我们的文档长度: {len(our_content)} 字符")
    
    # 提取章节
    baseline_sections = extract_sections(baseline_content)
    our_sections = extract_sections(our_content)
    
    print(f"基准文档章节数: {len(baseline_sections)}")
    print(f"我们的文档章节数: {len(our_sections)}")
    
    # 分析内容模式
    print("\n=== 基准文档内容模式 ===")
    baseline_patterns = analyze_content_patterns(baseline_sections)
    print(f"平均章节长度: {baseline_patterns['avg_content_length']:.1f} 字符")
    print(f"空章节数: {len(baseline_patterns['empty_sections'])}")
    print(f"短章节数 (<100字符): {len(baseline_patterns['short_sections'])}")
    print(f"长章节数 (>1000字符): {len(baseline_patterns['long_sections'])}")
    
    print("\n=== 我们的文档内容模式 ===")
    our_patterns = analyze_content_patterns(our_sections)
    print(f"平均章节长度: {our_patterns['avg_content_length']:.1f} 字符")
    print(f"空章节数: {len(our_patterns['empty_sections'])}")
    print(f"短章节数 (<100字符): {len(our_patterns['short_sections'])}")
    print(f"长章节数 (>1000字符): {len(our_patterns['long_sections'])}")
    
    # 对比特定章节
    print("\n=== 章节对比分析 ===")
    comparisons = compare_specific_sections(baseline_sections, our_sections)
    
    # 找出内容混淆模式
    mixing_patterns = find_content_mixing_patterns(comparisons)
    
    print(f"内容过长的章节: {len(mixing_patterns['oversized_sections'])}")
    print(f"内容过短的章节: {len(mixing_patterns['undersized_sections'])}")
    print(f"相似度低的章节: {len(mixing_patterns['low_similarity'])}")
    print(f"可能内容混淆的章节: {len(mixing_patterns['potential_mixing'])}")
    
    # 详细分析问题章节
    print("\n=== 内容混淆问题章节详细分析 ===")
    for i, comp in enumerate(mixing_patterns['potential_mixing'][:5]):  # 只显示前5个
        print(f"\n{i+1}. {comp['title']}")
        print(f"   长度比例: {comp['length_ratio']:.2f} (我们的/基准)")
        print(f"   相似度: {comp['similarity']:.2f}")
        print(f"   基准内容预览: {comp['baseline_preview'][:150]}...")
        print(f"   我们的内容预览: {comp['our_preview'][:150]}...")
    
    # 分析1.1前言的具体问题
    print("\n=== 1.1前言章节详细分析 ===")
    for comp in comparisons:
        if "1.1 前" in comp['title']:
            print(f"标题: {comp['title']}")
            print(f"基准长度: {comp['baseline_length']} 字符")
            print(f"我们的长度: {comp['our_length']} 字符")
            print(f"长度比例: {comp['length_ratio']:.2f}")
            print(f"相似度: {comp['similarity']:.2f}")
            print(f"\n基准内容:")
            print(comp['baseline_preview'])
            print(f"\n我们的内容:")
            print(comp['our_preview'])
            break
    
    # 寻找规律
    print("\n=== 内容混淆规律分析 ===")
    
    # 分析过长章节的共同特征
    oversized_titles = [comp['title'] for comp in mixing_patterns['oversized_sections']]
    print(f"过长章节的标题模式:")
    for title in oversized_titles[:10]:
        print(f"  - {title}")
    
    # 分析是否存在跨章节内容混淆
    print(f"\n检查是否存在跨章节内容混淆...")
    for comp in mixing_patterns['potential_mixing'][:3]:
        our_content = comp['our_preview']
        # 检查是否包含其他章节的标题关键词
        other_chapter_keywords = ['1.2', '1.3', '1.4', '1.5', '配置', '登录', '管理']
        found_keywords = [kw for kw in other_chapter_keywords if kw in our_content]
        if found_keywords:
            print(f"  {comp['title']} 包含其他章节关键词: {found_keywords}")

if __name__ == "__main__":
    main()
