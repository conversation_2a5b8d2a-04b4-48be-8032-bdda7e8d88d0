

VTY 用户界面配置成功后，可以查看到用户界面的使用信息、 VTY 类型用户界面的最大
个数以及物理属性和配置等信息。

##### 前提条件


已完成 VTY 用户界面的所有配置。

##### 操作步骤


       - 使用 **display users** [ **all** ] 命令，查看用户界面的用户登录信息。


       - 使用 **display user-interface maximum-vty** 命令，查看 VTY 类型用户界面的最大
个数。


       - 使用 **display user-interface** **vty** ui-number 命令，查看物理属性和用户界面配置
信息。


       - 使用 **display local-user** 命令，查看本地用户的属性信息。


       - 使用 **display access-user** 命令，查看通过 AAA 认证的用户信息。


       - 使用 **display vty mode** 命令，查看 VTY 模式。


**----**
结束
