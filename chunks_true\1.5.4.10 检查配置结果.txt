

用户通过 Telnet 登录系统配置成功后，可以查看到当前用户界面连接情况、每个用户界
面连接情况、以及当前建立的所有 TCP 连接情况等内容。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 71


HUAWEI NetEngine40E
配置指南 1 基础配置

##### 前提条件


已完成用户通过 Telnet 登录系统的所有配置。

##### 操作步骤


       - 使用 **display users** [ **all** ] 命令，查看用户界面连接情况。


       - 使用 **display tcp status** 命令，查看当前建立的所有 TCP 连接情况。


       - 使用 **display telnet server status** 命令，查看 Telnet 服务器的状态和配置信息。


       - Telnet 协议的白名单 Session-CAR 功能配置成功以后，可以按照如下指导检查配置
结果：


– 基于 IPv4 ：使用命令 **display cpu-defend whitelist session-car** **telnet**
**statistics** **slot** slot-id ，查看指定接口板上的 Telnet 协议白名单 Session-CAR
的统计信息。


如果需要查看某一段时间的统计信息，可以使用命令 **reset cpu-defend**
**whitelist session-car** **telnet** **statistics** **slot** slot-id 先清除指定接口板上的
Telnet 协议白名单 Session-CAR 的统计信息，再使用命令 **display cpu-defend**
**whitelist session-car** **telnet** **statistics** **slot** slot-id 。


– 基于 IPv6 ：使用命令 **display cpu-defend whitelist-v6 session-car**
**telnetv6** **statistics** **slot** slot-id ，查看指定接口板上的 Telnet 协议白名单
Session-CAR 的统计信息。


如果需要查看某一段时间的统计信息，可以使用命令 **reset cpu-defend**
**whitelist-v6 session-car** **telnetv6** **statistics** **slot** slot-id 先清除指定接口板
上的 Telnet 协议白名单 Session-CAR 的统计信息，再使用命令 **display cpu-**
**defend whitelist-v6 session-car** **telnetv6** **statistics** **slot** slot-id 。


       - 使用 **display vty ip-block all** 命令，查看所有认证失败的 IP 地址。


       - 使用 **display vty ip-block list** 命令，查看因为认证失败而被阻止的 IP 地址列表。


**----**
结束
