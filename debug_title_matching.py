#!/usr/bin/env python3
"""
调试标题匹配问题
"""

import fitz
import re
from main3 import PDFToMarkdownConverter

def debug_specific_titles():
    """调试特定标题的匹配问题"""
    
    with PDFToMarkdownConverter("2.pdf", {"debug_mode": True}) as converter:
        # 生成raw markdown
        raw_markdown = converter.generate_raw_markdown()
        
        # 提取TOC标题
        toc_titles = converter.extract_toc_titles()
        
        # 测试几个特定标题
        test_titles = [
            "1.2 首次登录配置",
            "1.2.3 通过Console口登录设备",
            "1.3.8.1 使用Tab键示例"
        ]
        
        for test_title in test_titles:
            print(f"\n=== 测试标题: {test_title} ===")
            
            # 找到对应的TitleInfo
            title_info = None
            for title in toc_titles:
                if test_title in title.text:
                    title_info = title
                    break
            
            if not title_info:
                print(f"在TOC中未找到标题: {test_title}")
                continue
            
            print(f"TOC中的完整标题: {title_info.text}")
            
            # 测试三种匹配策略
            lines = raw_markdown.split('\n')
            
            # 1. 精确匹配
            print("\n--- 精确匹配测试 ---")
            for i in range(len(lines) - 1, -1, -1):
                line = lines[i].strip()
                if title_info.text == line:
                    print(f"精确匹配成功，行号: {i}, 内容: {line}")
                    break
            else:
                print("精确匹配失败")
            
            # 2. 带#的行匹配
            print("\n--- 带#的行匹配测试 ---")
            hash_lines = []
            for i, line in enumerate(lines):
                if '#' in line:
                    hash_lines.append((i, line))
            
            title_no_space = re.sub(r'\s+', '', title_info.text)
            found_hash = False
            for i, line in reversed(hash_lines):
                line_no_space = re.sub(r'\s+', '', line)
                if title_no_space in line_no_space:
                    print(f"带#行匹配成功，行号: {i}, 内容: {line}")
                    found_hash = True
                    break
            
            if not found_hash:
                print("带#行匹配失败")
                # 显示一些带#的行作为参考
                print("前10个带#的行:")
                for i, (line_num, line) in enumerate(hash_lines[:10]):
                    print(f"  {line_num}: {line}")
            
            # 3. 模糊匹配
            print("\n--- 模糊匹配测试 ---")
            keywords = [word for word in title_info.text.split() if len(word) > 1]
            print(f"关键词: {keywords}")
            
            found_fuzzy = False
            for i in range(len(lines) - 1, -1, -1):
                line = lines[i].strip()
                if not line:
                    continue
                    
                matches = 0
                for keyword in keywords:
                    if keyword in line:
                        matches += 1
                
                if matches >= len(keywords) * 0.6:
                    print(f"模糊匹配成功，行号: {i}, 匹配度: {matches}/{len(keywords)}, 内容: {line}")
                    found_fuzzy = True
                    break
            
            if not found_fuzzy:
                print("模糊匹配失败")

                # 搜索包含部分关键词的行
                print("包含部分关键词的行:")
                for keyword in keywords:
                    count = 0
                    for i, line in enumerate(lines):
                        if keyword in line and count < 3:
                            print(f"  关键词'{keyword}'在行{i}: {line.strip()}")
                            count += 1

def analyze_raw_markdown():
    """分析raw markdown的结构"""
    with PDFToMarkdownConverter("2.pdf", {"debug_mode": True}) as converter:
        raw_markdown = converter.generate_raw_markdown()

        lines = raw_markdown.split('\n')
        print(f"Raw markdown总行数: {len(lines)}")

        # 查找包含"1.2.3"的行
        print("\n包含'1.2.3'的行:")
        for i, line in enumerate(lines):
            if "1.2.3" in line:
                print(f"行{i}: {line}")

        # 查找包含"Console"的行
        print("\n包含'Console'的行:")
        count = 0
        for i, line in enumerate(lines):
            if "Console" in line and count < 10:
                print(f"行{i}: {line}")
                count += 1

if __name__ == "__main__":
    debug_specific_titles()
    print("\n" + "="*50)
    analyze_raw_markdown()
