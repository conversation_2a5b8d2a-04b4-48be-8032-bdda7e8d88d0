# 目  录
目 录
1 基础配置......................................................................................................................................1
1.1 前 言............................................................................................................................................................................................. 1
1.2 首次登录配置............................................................................................................................................................................. 6
1.2.1 首次登录概述..........................................................................................................................................................................7
1.2.2 首次登录设备配置注意事项............................................................................................................................................... 7
1.2.3 通过Console 口登录设备................................................................................................................................................... 7
1.2.3.1 建立物理连接...................................................................................................................................................................... 7
1.2.3.2 登录设备...............................................................................................................................................................................8
1.2.4 通过管理网口登录设备......................................................................................................................................................11
1.2.5 通过即插即用功能批量部署设备.................................................................................................................................... 13
1.3 命令行接口配置...................................................................................................................................................................... 14
1.3.1 命令行接口概述...................................................................................................................................................................14
1.3.2 命令行接口配置注意事项................................................................................................................................................. 15
1.3.3 搭建命令行运行环境.......................................................................................................................................................... 15
1.3.3.1 配置用户登录警示信息.................................................................................................................................................. 15
******* 配置设备名称....................................................................................................................................................................15
******* 设置命令级别....................................................................................................................................................................16
1.3.3.4 锁定用户界面....................................................................................................................................................................17
1.3.3.5 允许在系统视图下执行用户视图命令........................................................................................................................ 17
1.3.3.6 （可选）配置命令时间戳.............................................................................................................................................. 18
1.3.4 如何使用命令行...................................................................................................................................................................18
1.3.4.1 进入命令视图....................................................................................................................................................................19
1.3.4.2 编辑命令行........................................................................................................................................................................ 19
1.3.4.3 正则表达式........................................................................................................................................................................ 20
1.3.4.4 查询配置信息....................................................................................................................................................................25
1.3.4.5 查询诊断信息....................................................................................................................................................................25
1.3.4.6 命令行结果显示............................................................................................................................................................... 25
1.3.4.7 命令行的错误信息........................................................................................................................................................... 26
1.3.4.8 命令行别名........................................................................................................................................................................ 26
1.3.4.9 命令行智能回退............................................................................................................................................................... 27
1.3.4.10 启用二次认证功能.........................................................................................................................................................28
1.3.5 如何获得命令帮助.............................................................................................................................................................. 28
1.3.6 如何使用快捷键...................................................................................................................................................................29
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
ii
1.3.6.1 快捷键的分类....................................................................................................................................................................29
1.3.6.2 定义快捷键........................................................................................................................................................................ 31
1.3.6.3 查看快捷键配置列表.......................................................................................................................................................31
1.3.7 配置会话日志功能开关......................................................................................................................................................31
1.3.8 配置举例................................................................................................................................................................................32
******* 使用Tab 键示例...............................................................................................................................................................32
******* 定义快捷键示例............................................................................................................................................................... 34
1.4 用户界面配置...........................................................................................................................................................................35
1.4.1 用户界面概述....................................................................................................................................................................... 35
1.4.2 用户界面配置注意事项......................................................................................................................................................37
1.4.3 配置Console 用户界面..................................................................................................................................................... 37
1.4.3.1 配置Console 用户界面的物理属性............................................................................................................................ 37
1.4.3.2 配置Console 用户界面的终端属性............................................................................................................................ 38
1.4.3.3 配置Console 用户界面的用户级别............................................................................................................................ 39
1.4.3.4 配置Console 用户界面的用户验证............................................................................................................................ 39
1.4.3.5 配置Console 用户界面去使能..................................................................................................................................... 41
1.4.3.6 检查配置结果....................................................................................................................................................................41
1.4.4 配置VTY 用户界面............................................................................................................................................................. 42
1.4.4.1 配置VTY 用户界面的最大个数.................................................................................................................................... 42
1.4.4.2 配置VTY 用户界面的呼入呼出限制........................................................................................................................... 43
1.4.4.3 配置VTY 用户界面的终端属性.................................................................................................................................... 45
1.4.4.4 配置VTY 用户界面的用户级别.................................................................................................................................... 46
1.4.4.5 配置VTY 用户界面的用户验证.................................................................................................................................... 46
1.4.4.6 配置可用VTY 通道数的超限告警阈值....................................................................................................................... 48
1.4.4.7 使能VTY 用户界面的安全策略.................................................................................................................................... 48
1.4.4.8 检查配置结果....................................................................................................................................................................49
1.4.5 配置举例................................................................................................................................................................................49
1.4.5.1 配置Console 用户界面示例......................................................................................................................................... 49
1.4.5.2 配置VTY 用户界面示例................................................................................................................................................. 51
1.5 用户登录配置...........................................................................................................................................................................53
1.5.1 用户登录概述....................................................................................................................................................................... 54
1.5.2 登录设备命令行界面配置注意事项................................................................................................................................ 59
1.5.3 配置用户通过Console 口登录系统................................................................................................................................59
******* 用户通过Console 口登录系统..................................................................................................................................... 59
1.5.3.2 （可选）配置Console 用户界面.................................................................................................................................62
1.5.3.3 检查配置结果....................................................................................................................................................................62
1.5.4 配置用户通过Telnet 登录系统........................................................................................................................................63
******* 配置VTY 用户界面的用户级别和验证方式.............................................................................................................. 63
******* 使能Telnet 服务器功能................................................................................................................................................. 66
******* 用户通过终端Telnet 登录到系统................................................................................................................................67
******* 配置Telnet 服务器参数................................................................................................................................................. 67
******* （可选）配置Telnet 协议的白名单Session-CAR.................................................................................................. 69
HUAWEI NetEngine40E
配置指南
目 录
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
iii
******* （可选）配置Telnet 访问控制.................................................................................................................................... 69
******* （可选）使能Telnet 连接的IP 地址阻止功能.........................................................................................................70
******* （可选）配置通过Telnet 登录服务器失败次数的告警门限................................................................................ 71
******* （可选）配置单个IP 地址通过Telnet 登录服务器的最大连接数......................................................................71
******** 检查配置结果................................................................................................................................................................. 71
1.5.5 配置用户通过STelnet 登录系统..................................................................................................................................... 72
******* 配置VTY 用户界面的用户级别和验证方式.............................................................................................................. 73
******* 配置VTY 用户界面支持SSH 协议.............................................................................................................................. 75
******* 配置SSH 用户和认证方式.............................................................................................................................................76
******* 使能STelnet 服务器功能............................................................................................................................................... 84
******* 用户通过终端STelnet 到服务器..................................................................................................................................86
******* 配置STelnet 服务器参数............................................................................................................................................... 94
******* （可选）配置SSH 协议的白名单Session-CAR...................................................................................................... 97
******* （可选）配置通过SSH 登录服务器失败次数的告警门限.................................................................................... 97
******* （可选）配置单个IP 地址通过SSH 登录服务器的最大连接数..........................................................................98
*******0 （可选）配置SSH 服务器的本地端口转发服务................................................................................................... 98
*******1 配置SSH 服务器的键盘交互认证方式.................................................................................................................... 99
*******2 配置SSH 会话密钥重协商条件................................................................................................................................. 99
*******3 检查配置结果...............................................................................................................................................................100
1.5.6 配置用户通过AUX 口登录系统.................................................................................................................................... 100
******* 用户通过AUX 口登录系统..........................................................................................................................................100
1.5.6.2 （可选）配置AUX 用户界面..................................................................................................................................... 103
1.5.6.3 检查配置结果................................................................................................................................................................. 103
1.5.7 通过Telnet 重定向连接其他设备.................................................................................................................................104
******* 使能Telnet 重定向功能...............................................................................................................................................104
1.5.7.2 使用Telnet 重定向连接其他设备............................................................................................................................. 105
1.5.7.3 检查配置结果................................................................................................................................................................. 105
1.5.8 配置用户登录示例............................................................................................................................................................ 105
******* 配置用户通过Console 口登录系统示例................................................................................................................. 105
******* 配置用户通过Telnet 登录系统示例......................................................................................................................... 108
******* 配置IPv6 用户通过Telnet 登录系统示例...............................................................................................................111
******* 配置用户通过STelnet 登录系统示例.......................................................................................................................115
******* 配置IPv6 用户通过STelnet 登录系统示例............................................................................................................ 124
******* 配置网管设备上的SSH 用户与设备通过VPN 网络通信示例........................................................................... 133
1.6 文件系统配置........................................................................................................................................................................ 139
1.6.1 文件系统概述.....................................................................................................................................................................139
1.6.2 文件系统管理配置注意事项...........................................................................................................................................142
1.6.3 通过登录系统进行文件操作...........................................................................................................................................144
1.6.3.1 管理目录.......................................................................................................................................................................... 144
1.6.3.2 管理文件.......................................................................................................................................................................... 147
1.6.4 通过FTP 进行文件操作...................................................................................................................................................148
******* 配置FTP 类型的本地用户...........................................................................................................................................149
HUAWEI NetEngine40E
配置指南
目 录
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
iv
******* （可选）指定FTP 服务器端口号..............................................................................................................................150
******* 使能FTP 服务器功能....................................................................................................................................................150
******* 配置FTP 服务器参数....................................................................................................................................................151
******* （可选）配置FTP 协议的白名单Session-CAR.................................................................................................... 152
******* （可选）配置FTP 访问控制...................................................................................................................................... 152
******* （可选）配置IP 地址锁定功能................................................................................................................................. 153
******* 用户通过FTP 软件访问系统...................................................................................................................................... 154
******* 用户使用FTP 命令进行文件操作..............................................................................................................................154
******** 检查配置结果...............................................................................................................................................................157
1.6.5 通过SFTP 进行文件操作................................................................................................................................................ 158
******* 配置SSH 用户并指定服务方式................................................................................................................................. 158
1.6.5.2 使能SFTP 服务器功能................................................................................................................................................. 167
1.6.5.3 配置SFTP 服务器参数................................................................................................................................................. 169
1.6.5.4 （可选）配置SSH 协议的白名单Session-CAR....................................................................................................172
1.6.5.5 配置SFTP 服务授权目录.............................................................................................................................................172
1.6.5.6 用户通过SFTP 协议访问系统.................................................................................................................................... 173
1.6.5.7 用户使用SFTP 命令进行文件操作........................................................................................................................... 173
1.6.5.8 检查配置结果................................................................................................................................................................. 175
1.6.6 通过HTTPS 下载文件..................................................................................................................................................... 176
1.6.7 文件系统管理配置举例................................................................................................................................................... 176
******* 通过登录系统进行目录管理示例...............................................................................................................................176
1.6.7.2 通过登录系统进行文件操作示例...............................................................................................................................178
******* 通过FTP 进行文件操作示例...................................................................................................................................... 179
******* 通过SFTP 进行文件操作示例.................................................................................................................................... 181
******* 通过IPv6 SFTP 进行文件操作示例.......................................................................................................................... 184
1.7 配置管理配置........................................................................................................................................................................ 186
1.7.1 配置管理概述.....................................................................................................................................................................186
1.7.2 配置文件管理配置注意事项...........................................................................................................................................188
1.7.3 选择配置生效模式............................................................................................................................................................ 191
******* 配置立即生效模式.........................................................................................................................................................191
1.7.3.2 配置两阶段生效模式.................................................................................................................................................... 192
1.7.4 关闭二次确认功能............................................................................................................................................................ 193
1.7.5 配置回退............................................................................................................................................................................. 194
******* 配置/清除定时配置回退点自动生成时间................................................................................................................194
1.7.5.2 配置回退.......................................................................................................................................................................... 194
1.7.6 管理配置文件.....................................................................................................................................................................196
******* 缺省配置文件................................................................................................................................................................. 196
******* 空配置启动下的默认配置文件...................................................................................................................................201
******* 保存配置文件................................................................................................................................................................. 202
******* 比较配置文件................................................................................................................................................................. 204
******* 加载配置文件................................................................................................................................................................. 204
******* 配置下次启动时加载的配置文件...............................................................................................................................205
HUAWEI NetEngine40E
配置指南
目 录
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
v
1.7.6.7 清除配置文件................................................................................................................................................................. 206
1.7.6.8 使能自动核查数据库信息功能...................................................................................................................................207
1.7.6.9 使能自动检查主用主控和备用主控配置数据一致性功能................................................................................... 208
*******0 锁定系统配置数据...................................................................................................................................................... 208
*******1 使用配置模板下发配置............................................................................................................................................. 209
*******2 查看待提交配置和当前运行配置的差异............................................................................................................... 212
*******3 检查配置结果...............................................................................................................................................................213
*******4 配置替换....................................................................................................................................................................... 213
*******4.1 配置文件替换........................................................................................................................................................... 213
*******4.2 差异配置粘贴........................................................................................................................................................... 215
*******4.3 字符替换.................................................................................................................................................................... 216
1.7.7 配置管理配置举例............................................................................................................................................................ 219
1.7.7.1 立即生效模式下用户配置示例...................................................................................................................................219
******* 两阶段生效模式下用户锁定配置示例......................................................................................................................220
******* 两阶段生效模式下多用户对同一业务进行相同配置示例................................................................................... 221
******* 两阶段生效模式下多用户对同一业务进行不同配置示例................................................................................... 222
******* 两阶段生效模式下多用户对不同业务进行配置示例............................................................................................224
******* 配置回退示例................................................................................................................................................................. 225
******* 管理配置文件示例.........................................................................................................................................................229
1.8 访问其他设备配置............................................................................................................................................................... 230
1.8.1 访问其他设备概述............................................................................................................................................................ 230
1.8.2 访问其他设备配置注意事项...........................................................................................................................................234
1.8.3 通过Telnet 登录其他设备..............................................................................................................................................236
1.8.3.1 （可选）配置Telnet 客户端源地址......................................................................................................................... 236
1.8.3.2 使用Telnet 命令登录其他设备..................................................................................................................................237
1.8.3.3 检查配置结果................................................................................................................................................................. 238
1.8.4 通过STelnet 登录其他设备............................................................................................................................................238
******* 配置用户首次登录其他设备（使能SSH 客户端首次认证功能方式）............................................................238
******* 配置用户首次登录其他设备（SSH 客户端为SSH 服务器分配公钥）............................................................239
1.8.4.3 （可选）配置SSH 客户端支持Keepalive 特性....................................................................................................240
1.8.4.4 使用STelnet 命令登录其他设备............................................................................................................................... 241
1.8.4.5 检查配置结果................................................................................................................................................................. 242
1.8.5 通过TFTP 访问其他设备的文件................................................................................................................................... 243
******* （可选）配置TFTP 客户端源地址........................................................................................................................... 243
******* 配置TFTP 访问限制..................................................................................................................................................... 243
******* 使用TFTP 命令下载其他设备的文件....................................................................................................................... 244
******* 使用TFTP 命令向其他设备上传文件....................................................................................................................... 245
******* 检查配置结果................................................................................................................................................................. 245
1.8.6 通过FTP 访问其他设备的文件......................................................................................................................................246
******* （可选）配置FTP 客户端源地址..............................................................................................................................246
******* 使用FTP 命令连接其他设备...................................................................................................................................... 246
******* 通过FTP 文件操作命令进行文件操作..................................................................................................................... 248
HUAWEI NetEngine40E
配置指南
目 录
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
vi
******* （可选）更改登录用户................................................................................................................................................ 251
******* 断开与FTP 服务器的连接...........................................................................................................................................252
******* 检查配置结果................................................................................................................................................................. 252
1.8.7 通过SFTP 访问其他设备的文件................................................................................................................................... 253
******* （可选）配置SFTP 客户端源地址........................................................................................................................... 253
******* 配置用户首次登录其他设备（使能SSH 客户端首次认证功能方式）............................................................254
******* 配置用户首次登录其他设备（SSH 客户端为SSH 服务器分配公钥）............................................................254
******* 使用SFTP 命令连接其他设备（SSH 服务器）..................................................................................................... 255
******* 通过SFTP 文件操作命令进行文件操作...................................................................................................................257
******* 检查配置结果................................................................................................................................................................. 258
1.8.8 配置SFTP 客户端通过一键式文件操作命令进行文件操作....................................................................................259
1.8.9 通过SCP 访问其他设备的文件..................................................................................................................................... 260
******* 配置SCP 服务器端........................................................................................................................................................260
******* 配置SCP 客户端............................................................................................................................................................ 260
******* 检查配置结果................................................................................................................................................................. 262
1.8.10 配置SSL 策略加密算法套............................................................................................................................................ 262
1.8.11 配置绑定SSL Policy...................................................................................................................................................... 263
1.8.12 通过HTTP 登录其他设备.............................................................................................................................................266
1.8.13 配置Telnet、SSH 协议报文的DSCP 值.................................................................................................................. 268
1.8.14 访问其他设备配置举例................................................................................................................................................. 268
1.8.14.1 通过Telnet 登录其他设备配置示例.......................................................................................................................268
1.8.14.2 通过STelnet 登录其他设备配置示例（RSA 认证方式）................................................................................. 271
1.8.14.3 通过STelnet 登录其他设备配置示例（DSA 认证方式）.................................................................................278
1.8.14.4 通过STelnet 登录其他设备配置示例（ECC 认证方式）..................................................................................286
1.8.14.5 通过STelnet 登录其他设备配置示例（SM2 认证方式）................................................................................ 292
1.8.14.6 通过TFTP 访问其他设备文件配置示例................................................................................................................ 298
1.8.14.7 通过FTP 访问其他设备文件配置示例...................................................................................................................300
1.8.14.8 通过SFTP 访问其他设备文件配置示例（RSA 认证方式）............................................................................. 303
1.8.14.9 通过SFTP 访问其他设备文件配置示例（DSA 认证方式）.............................................................................310
1.8.14.10 通过SFTP 访问其他设备文件配置示例（ECC 认证方式）........................................................................... 318
1.8.14.11 通过SFTP 访问其他设备文件配置示例（SM2 认证方式）.......................................................................... 324
1.8.14.12 配置SSH 服务器支持其他端口号访问的示例...................................................................................................330
1.8.14.13 配置公网SSH 客户端访问私网SSH 服务器的示例........................................................................................ 336
1.8.14.14 通过SCP 访问其他设备文件配置示例................................................................................................................ 345
1.8.14.15 通过HTTP 访问其他设备配置示例..................................................................................................................... 348
1.9 ZTP 配置................................................................................................................................................................................. 350
1.9.1 ZTP 概述..............................................................................................................................................................................350
1.9.2 ZTP 配置注意事项............................................................................................................................................................ 351
1.9.3 配置通过DHCP 实现ZTP 自动部署............................................................................................................................ 353
1.9.3.1 编辑中间文件................................................................................................................................................................. 353
******* 配置DHCPv4 Server 和DHCPv4 Relay................................................................................................................. 353
******* 配置DHCPv6 Server 和DHCPv6 Relay................................................................................................................. 355
HUAWEI NetEngine40E
配置指南
目 录
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
vii
******* 配置文件服务器............................................................................................................................................................. 356
1.9.3.5 上电启动设备................................................................................................................................................................. 356
1.9.3.6 （可选）加载预配置脚本............................................................................................................................................357
******* （可选）配置自动补丁修复....................................................................................................................................... 357
1.9.3.8 开启ZTP 功能................................................................................................................................................................ 358
1.9.3.9 检查配置结果................................................................................................................................................................. 358
1.9.4 配置举例............................................................................................................................................................................. 358
1.9.4.1 配置通过DHCP 实现ZTP 自动部署示例................................................................................................................358
HUAWEI NetEngine40E
配置指南
目 录
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
viii
HUAWEI NetEngine40E



# 1 基础配置
1 基础配置



## 1.1 前 言
1.2 首次登录配置
当用户需要为第一次上电的设备进行基本配置时，可以通过Console口登录设备，还可
以通过即插即用方式批量部署设备。
1.3 命令行接口配置
用户对设备的日常操作，主要通过输入命令行来实现。用户可以在命令视图下编辑和
配置命令行，并显示配置成功或错误的信息。
1.4 用户界面配置
当用户通过Console口、Telnet或SSH方式登录路由器时，系统会分配相应的用户界
面，用来管理、监控设备和当前用户之间的会话。
1.5 用户登录配置
用户可以通过Console口、Telnet或SSH（STelnet）方式登录设备，实现对设备的本地
或远程维护。
1.6 文件系统配置
文件系统实现对存储设备中的文件、目录的管理。
1.7 配置管理配置
为了保障用户配置的可靠性，系统支持两种配置生效模式。
1.8 访问其他设备配置
设备可以作为客户端访问网络上的其他设备。
1.9 ZTP配置
设备可以通过零配置自动部署ZTP（Zero Touch Provisioning）实现空配置下的上电自
动部署。
1.1 前 言
概述
本文档介绍了基础配置的基本概念、在不同应用场景中的配置过程和配置举例。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
1
License 依赖
License的详细信息，请查阅License使用指南。
●
企业网用户：
–
NE40E License 使用指南：License使用指南
–
NE40E-X8AK License 使用指南：License使用指南
产品版本
与本文档相对应的产品版本如下所示。
产品名称
产品版本
HUAWEI NetEngine40E
V800R023C10SPC500
iMaster NCE-IP
V100R023C10SPC100
 
读者对象
本文档主要适用于以下工程师：
●
数据配置工程师
●
调测工程师
●
网络监控工程师
●
系统维护工程师
安全声明
●
受限公开声明
产品资料中主要介绍了您在使用华为设备时，在网络部署及维护时，需要使用的
命令。对用于生产、装备、返厂检测维修的接口、命令，不在资料中说明。
对于部分仅用于工程实施、定位故障的高级命令以及升级兼容命令，如使用不
当，将可能导致设备异常或者业务中断，建议较高权限的工程师使用。如您需
要，请向华为公司申请。
●
加密算法声明
使用加密算法时，DES/3DES/RSA（3072位以下)/MD5（数字签名场景和口令加
密）/SHA1（数字签名场景）加密算法安全性低，存在安全风险，在协议支持的
加密算法选择范围内，建议使用更安全的加密算法，例如AES/RSA（3072位及以
上）/SHA2/HMAC-SHA2。
出于安全性考虑，不建议使用不安全协议Telnet、FTP、TFTP；不建议使用特性
BGP、LDP、PCEP、MSDP、DCN、TCP-AO、MSTP、VRRP、E-trunk、AAA、
IPSEC、BFD、QX、端口扩展、SSH、SNMP、IS-IS、RIP、SSL、NTP、OSPF、
Keychain中的弱安全算法。如果确实需要使用，请执行undo crypto weak-
algorithm disable命令使能弱安全算法功能。详细步骤请参见《配置指南》。
出于安全性考虑，不建议使用该特性中的弱安全算法，若当前系统已关闭弱安全
算法功能，配置弱安全算法会提示Error信息。如果确实需要使用弱安全算法，请
先执行undo crypto weak-algorithm disable命令使能弱安全算法功能。
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
2
●
密码配置声明
–
当密码加密方式为cipher时，输入以%^%#......%^%#为起始和结束符的合法
密文（本设备可以解密的密文）时，在设备上查看配置文件时显示的是和配
置相同的密文，请不要采用该方式直接配置密码。
–
为保证设备安全，请定期修改密码。
●
MAC地址、公网IP地址使用的声明
–
出于特性介绍及配置示例的需要，产品资料中会使用真实设备的MAC地址、
公网的IP地址，如无特殊说明，出现的真实设备的MAC地址、公网的IP地址
均为示意，不指代任何实际意义。
–
因开源及第三方软件中自带公网地址（包括公网IP地址、公网URL地址/域
名、邮箱地址），本产品没有使用这些公网地址，这遵循业界实践，也符合
开源软件使用规范。
–
出于功能特性实现的需要，设备会使用如下公网地址
表1-1 公网地址列表
公网地址
说明
http://www.huawei.com
华为官方网站地址
<EMAIL>
华为企业用户服务邮箱
 
●
个人数据声明
–
您购买的产品、服务或特性在业务运营或故障定位的过程中将可能获取或使
用用户的某些个人数据，因此您有义务根据所适用国家的法律制定必要的用
户隐私政策并采取足够的措施以确保用户的个人数据受到充分的保护。
–
废弃、回收或者再利用设备时，请注意根据需要备份或清除设备中的数据，
避免数据泄露的安全风险。如需支持，请联系售后技术支持人员。
●
预置证书使用声明
在生产阶段预置于华为设备的华为证书是华为设备必备的出厂身份凭证，对其使
用声明如下：
–
华为预置证书仅用于部署阶段为设备接入客户网络建立初始安全通道以及设
备对接，华为不对预置证书的安全性做承诺与保证。
–
对于将华为预置证书作为业务证书使用而导致的安全风险和安全事件，由客
户自行处置并承担后果。
–
华为预置证书有效期自2041年起开始过期，可以通过display pki cert_list
domain default命令查看实际的有效期。
–
预置证书过期后，使用预置证书的业务会中断。
–
华为建议客户通过部署PKI系统对现网设备、软件签发证书并做好证书的生命
周期管理（为保证安全性推荐使用短有效期的证书）。
–
华为产品中用于产品入网初始化配置和连接时使用的华为PKI根证书支持禁用
（当验证华为新网元入网时，可配置重启该证书）。建议客户完成产品入网
配置并为产品配置客户CA签发的证书后，将该根证书禁用。对于客户未禁用
华为PKI根证书而带来的安全风险和安全事件，由客户自行处置并承担后果。
●
产品生命周期政策
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
3
华为公司对产品生命周期的规定以“产品生命周期终止政策”为准，该政策的详
细内容请参见如下网址：https://support.huawei.com/ecolumnsweb/zh/
warranty-policy。
●
漏洞处理流程
华为公司对产品漏洞管理的规定以“漏洞处理流程”为准，该流程的详细内容请
参见如下网址：https://www.huawei.com/cn/psirt/vul-response-process
如企业客户须获取漏洞信息，请参见如下网址：https://
securitybulletin.huawei.com/enterprise/cn/security-advisory
●
华为企业业务最终用户许可协议(EULA)
本最终用户许可协议是最终用户（个人、公司或其他任何实体）与华为公司就华
为软件的使用所缔结的协议。最终用户对华为软件的使用受本协议约束，该协议
的详细内容请参见如下网址：https://e.huawei.com/cn/about/eula。
●
产品资料生命周期策略
华为公司针对随产品版本发布的售后客户资料（产品资料），发布了“产品资料
生命周期策略”，该策略的详细内容请参见如下网址：https://
support.huawei.com/enterprise/zh/bulletins-website/
ENEWS2000017760。
●
华为预置证书权责说明
华为公司对随设备出厂的预置数字证书，发布了“华为设备预置数字证书权责说
明”，该说明的详细内容请参见如下网址：https://support.huawei.com/
enterprise/zh/bulletins-service/ENEWS2000015766。
●
设备升级、打补丁的声明
对设备进行升级或打补丁操作时，请使用软件数字签名（OpenPGP）验证工具验
证软件。为避免软件被篡改或替换，防止给用户带来安全风险，建议用户进行此
项操作。
●
特性声明
–
NetStream功能，出于对网络流量的统计管理，可能涉及对最终用户的通信
内容分析，建议您在所适用法律法规允许的目的和范围内方可启用相应的功
能。在采集、存储用户通信内容的过程中，您应采取足够的措施以确保用户
的通信内容受到严格保护。
–
镜像功能，可能基于运维目的需要对某些最终用户的通信内容进行分析，建
议您在所适用法律法规允许的目的和范围内方可启用相应的功能。在采集、
存储用户通信内容的过程中，您应采取足够的措施以确保用户的通信内容受
到严格保护。
–
报文头获取功能，出于检测通信传输中的故障和错误的目的，可能涉及采集
或存储个人用户某些通信内容。本公司无法单方采集或存储用户通信内容。
建议您只有在所适用法律法规允许的目的和范围内方可启用相应的功能。在
采集、存储用户通信内容的过程中，您应采取足够的措施以确保用户的通信
内容受到严格保护。
●
可靠性设计声明
对于网络规划和站点设计，必须严格遵守可靠性设计原则，具备设备级和方案级
保护。设备级保护包括双网双平面，双机、跨板双链路的规划原则，避免出现单
点，单链路故障。方案级指FRR、VRRP等快速收敛保护机制。在应用方案级保护
时，应避免保护方案的主备路径经过相同链路或者传输，以免方案级保护不生
效。
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
4
特别声明
●
本文档中包含了NE40E支持的所有产品内容，如果需要了解在本国销售的设备或
单板等硬件相关信息，请查阅硬件描述章节。
●
本手册仅作为使用指导，其内容依据实验室设备信息编写。手册提供的内容具有
一般性的指导意义，并不确保涵盖所有使用场景。因版本升级、设备型号不同、
板卡限制不同、配置文件不同等原因，可能造成手册中提供的内容与用户使用的
设备界面不一致。请以用户设备界面的信息为准，本手册不再针对前述情况造成
的差异一一说明。
●
本手册中提供的最大值是设备在实验室特定场景（例如被测试设备上只有某种类
型的单板，或者只配置了某一种协议）达到的最大值。在现实网络中，由于设备
硬件配置不同、承载的业务不同等原因可能会使设备测试出的最大值与手册中提
供的数据不一致。
●
本手册中出现的接口编号仅作示例，并不代表设备上实际具有此编号的接口，实
际使用中请以设备上存在的接口编号为准。
●
本手册中的硬件照片仅供参考，具体请以发货的硬件实物为准。
●
本手册中体现设备支持的相关硬件板卡，存在特定客户定制的需求，实际支持以
售前销售界面为准。
●
出于特性介绍及配置示例的需要，产品资料中会使用公网IP地址，如无特殊说
明，资料里出现的公网IP地址均为示意，不指代任何实际意义。
●
本手册中配置指南出现的“XX配置注意事项”，请结合产品的实际特性支持情况
来使用。
●
本手册中的日志参考和告警参考，记录的是对应产品上注册的日志和告警信息。
实际应用中可触发的日志和告警，取决于当前产品所支持的业务功能。
●
本文档中描述的所有设备尺寸数据均为设计尺寸，不包含尺寸公差。在部件制造
过程中，由于加工或测量等因素的影响，实际尺寸存在一定的偏差。
符号约定
在本文中可能出现下列标志，它们所代表的含义如下。
符号
说明
表示如不避免则将会导致死亡或严重伤害的具有高等
级风险的危害。
表示如不避免则可能导致死亡或严重伤害的具有中等
级风险的危害。
表示如不避免则可能导致轻微或中度伤害的具有低等
级风险的危害。
用于传递设备或环境安全警示信息。如不避免则可能
会导致设备损坏、数据丢失、设备性能降低或其它不
可预知的结果。
“须知”不涉及人身伤害。
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
5
符号
说明
对正文中重点信息的补充说明。
“说明”不是安全警示信息，不涉及人身、设备及环
境伤害信息。
 
命令格式约定
格式
意义
粗体
命令行关键字（命令中保持不变、必须照输的部分）采用
加粗字体表示。
斜体
命令行参数（命令中必须由实际值进行替代的部分）采用
斜体表示。
[ ]
表示用“[ ]”括起来的部分在命令配置时是可选的。
{ x | y | ... }
表示从两个或多个选项中选取一个。
[ x | y | ... ]
表示从两个或多个选项中选取一个或者不选。
{ x | y | ... } *
表示从两个或多个选项中选取多个，最少选取一个，最多
选取所有选项。
[ x | y | ... ] *
表示从两个或多个选项中选取多个或者不选。
&<1-n>
表示符号&前面的参数可以重复1～n次。
#
由“#”开始的行表示为注释行。
 
修订记录
修改记录累积了每次文档变更的说明。最新版本的文档包含以前所有文档版本的更新
内容。
产品版本
文档版本
发布日期
V800R023C10SPC500
02
2024-06-30
V800R023C10SPC500
01
2024-03-30
 
HUAWEI NetEngine40E
配置指南
1 基础配置



## 1.2 首次登录配置
当用户需要为第一次上电的设备进行基本配置时，可以通过Console口登录设备，还可
以通过即插即用方式批量部署设备。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
6
HUAWEI NetEngine40E
配置指南
1 基础配置



### 1.2.1 首次登录概述
设备第一次上电可以使用Console口或通过管理网口登录，使用Console口进行本地登
录是登录设备的最基本的方式，也是配置通过其他方式登录设备的基础。
Console口是一种通信串行端口，由设备的主控板提供。一块主控板提供一个Console
口，端口类型为EIA/TIA-232 DCE。用户终端的串行端口可以与设备的Console口直接
连接，实现对设备的本地配置。
Console口状态如下：
●
Connected：Console口处于试连接状态。
●
Disconnected：与Console口连接断开。
说明
●
首次登录，系统自动提示用户设置密码。
●
通过按CTRL+R恢复出厂设置，从而清除用户设置的密码。
在构建大型网络时，为了实现对设备的远程统一管理和控制，以提高部署效率和降低
运维成本，设备首次上电时还可以基于即插即用功能通过DCN或DHCP方式批量部署设
备。



### 1.2.2 首次登录设备配置注意事项
特性限制
无
1.2.3 通过Console 口登录设备
通过Console口连接终端与设备，搭建配置环境。
应用环境
设备第一次上电，需要对此设备进行配置和管理时，可以通过Console口登录。
前置任务
在通过Console口登录设备之前，需要完成以下任务：
●
PC已安装终端仿真程序（如PuTTY.exe软件）
●
准备好串口配置电缆
注意
如使用第三方的USB转RJ45的串口线缆，为保证线缆传输的信号质量的完整性，
建议线缆长度不大于两米。
1.2.3.1 建立物理连接
使用配置电缆将设备的Console口与终端COM口进行物理连接。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
7



### 1.2.3 通过Console口登录设备
#### 1.2.3.1 建立物理连接
使用配置电缆将设备的Console口与终端COM口进行物理连接。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
7
背景信息
说明
请使用产品随机附带的配置电缆连接PC机和设备，对应线缆信息请参见《硬件描述》中的“设
备线缆”。连接时请认准接口上的标识，以免误插入其他接口。
操作步骤
步骤1 所有设备上电，自检正常。
步骤2 使用配置电缆将PC的COM口和设备的主用主控板上的Console口相连。主控板上的
ACT指示灯常亮，则表示该主控板为主用主控板。
----结束
HUAWEI NetEngine40E
配置指南
1 基础配置



#### 1.2.3.2 登录设备
通过Console口从PC登录设备，实现对第一次上电的设备进行配置和管理。
背景信息
用户需要根据设备上的Console口的物理属性（包括传输速率、数据位、校验位、停止
位、流控方式），配置终端登录时的相关参数。由于是首次登录设备，终端属性的各
参数值均采用缺省值。
客户端已安装PuTTY.exe软件。
操作步骤
步骤1 打开PuTTY.exe程序，出现如图1-1所示客户端配置界面。选择Serial选项。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
8
图1-1 客户端配置界面
步骤2 选择客户端配置界面左侧目录树（Category）中的连接协议Serial，出现如图1-2所示
界面。设置端口通信参数，与设备的缺省值保持一致。（下图仅为示例，配置时以设
备的实际缺省为准）
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
9
图1-2 设置连接端口示意图
步骤3 单击“Open”，如果连接正常则会提示用户配置验证密码，系统会自动保存此密码配
置，如图1-3所示。
图1-3 登录界面
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
10
密码设置成功后，系统将出现用户视图的命令行提示符，如<HUAWEI>，至此用户进
入了用户视图配置环境。
此时用户可以键入命令，配置设备或查看设备运行状态，需要帮助可以随时键入
“?”。
说明
设置的密码必须满足以下要求：
●
密码采取交互式输入，系统不回显输入的密码。
●
输入的密码为字符串形式，区分大小写，长度范围是8～16。输入的密码至少包含两种类型
字符，包括大写字母、小写字母、数字及特殊字符。
●
特殊字符不包括“？”和空格。但是，当输入的密码两端使用双引号时，可在密码中间输入
空格。
–
如果使用双引号设置带空格密码，双引号之间不能再使用双引号。
–
如果使用双引号没有设置带空格密码，双引号之间可以再使用双引号。
例如，"Aa 123"45""为不合法密码，"Aa123"45""为合法密码。
配置文件中将以密文形式体现设置的密码。
----结束
HUAWEI NetEngine40E
配置指南
1 基础配置



### 1.2.4 通过管理网口登录设备
设备第一次上电时，可以通过管理网口使用SSH方式登录。
背景信息
通过终端连接到网络上，如果网络安全性不高，SSH（Secure Shell）可提供安全的信
息保障和强大认证功能，保护设备系统不受IP欺骗等攻击。缺省情况下，用户可以通
过管理网口直接登录设备。
客户端已安装PuTTY.exe软件。
说明
设备上电后会自动将管理网口（GigabitEthernet0/0/0）绑定到保留VPN（保留VPN为
__LOCAL_OAM_VPN__），并为其配置固定IP地址***********/24。
用户可以为终端配置***********/24网段的其他IP，通过SSH方式登录设备，实现对设备的现场
维护。
如果设备在启动前已经连接DHCP服务器，管理网口的默认地址可能被覆盖或者丢失。
在设备上进行业务配置后，需要及时修改用户名和密码。管理网口的IP可以修改和删除，并且根
据需要关闭该接口。
操作步骤
步骤1 配置SSH客户端。
打开PuTTY.exe程序，出现如图1-4所示客户端配置界面。在“Host Name (or IP
address)”文本框中输入SSH服务器的IP地址。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
11
图1-4 SSH 客户端配置界面
步骤2 单击“Open”，如果连接正常则会提示用户输入用户名和密码，如图1-5所示。
图1-5 SSH 客户端登录认证界面
说明
此处的用户名和密码需输入设备的缺省账号及密码，详见设备定制及缺省帐号与密码清单。
通过配置Console口进而使用STelnet方式登录设备的详细过程可参考1.5.5 配置用户通过STelnet
登录系统。
步骤3 设备空配置启动时，如果.defcfg文件中没有预置账号，STelnet登录时会进入first-login
流程，此时会提示新创建用户并设置密码，如图1-6所示。
通过user-security-policy first-login-linkage enable命令，可以在首次登录设备
时，限制Console和STelnet只能有一个流程创建用户。该命令仅支持在配置文件中生
效，对于已经登录的用户无法执行。
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
12
图1-6 设备进入first-login 流程界面
创建成功后连接会被关闭，用户需要重新用新创建的用户登录。
----结束
HUAWEI NetEngine40E
配置指南
1 基础配置



### 1.2.5 通过即插即用功能批量部署设备
设备首次上电时，还可以基于即插即用功能通过DCN或DHCP方式批量部署设备。
背景信息
在构建大型网络时，为了实现对设备的远程统一管理和控制，以提高部署效率和降低
运维成本，设备首次上电时还可以基于即插即用功能批量部署设备，主要通过以下两
种方式：
●
DCN：设备上电后，所有网元由网元ID（即NEID）自动生成IP地址（即NEIP），
并通过OSPF扩散LSA形成一张由NEIP和NEID组成的核心路由表。网管通过网关网
元的IP地址和网关网元上送的目的网元ID进行访问，并通过网关网元远程对网络
中的所有网元进行管理。
●
DHCP：通过DHCP方式实现的即插即用功能主要表现为ZTP（Zero Touch
Provisioning，零配置自动部署），它是一种空配置设备上电启动时可以从文件服
务器获取版本文件并自动加载的功能。
说明
设备如果在没有加载带账号密码的默认配置文件的情况下上电启动，则会进入first-login流程
（即需要新创建用户并设置密码），导致通过即插即用功能上线失败。在上述过程中，提示信息
和交互确认信息通过SSH标准协议中的SSH_MSG_CHANNEL_DATA字段在设备和网管间交互传
递。报文结构的定义如下：
byte      SSH_MSG_CHANNEL_DATA
uint32    recipient channel
string    data
操作步骤
步骤1 上电启动设备。
步骤2 设备根据实际状态进入相应的即插即用流程：
●
在实现DCN配置的情况下，网管与设备间建立通信网络，网管可以直接对网元进
行远程管理。
●
在1.9.3 配置通过DHCP实现ZTP自动部署的情况下，设备可以从文件服务器获取
版本文件并自动加载。
----结束
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
13
HUAWEI NetEngine40E
配置指南
1 基础配置



## 1.3 命令行接口配置
用户对设备的日常操作，主要通过输入命令行来实现。用户可以在命令视图下编辑和
配置命令行，并显示配置成功或错误的信息。



### 1.3.1 命令行接口概述
命令行接口是用户对命令行操作的常用工具。通过命令行接口输入命令，可以对路由
器进行配置和管理。
须知
当用户执行需要输入密码的命令时，有些密码会以明文方式显示在屏幕上，为了防止
密码泄露，请用户及时清屏。
命令行接口
命令行接口CLI（Command Line Interface）是用户与路由器进行交互的常用工具。用
户登录到路由器出现命令行提示符后，即进入命令行接口。系统向用户提供一系列命
令，用户可以通过命令行接口输入命令，由命令行接口对命令进行解析，实现用户对
路由器的配置和管理。
CLI是一种传统的配置工具，大多数通信产品都提供了CLI功能。随着通信产品在全球
范围的应用越来越广，用户对CLI的可用性、灵活性、界面友好性提出了更高的要求。
设备在切换为UP形态后，会自动建立cp-config视图。视图内会保存CP下发到UP的模
板名，此模板内的数据不支持本地修改。
命令行接口有如下特性：
●
允许通过Console口进行本地配置。
●
允许通过Telnet、SSH进行本地或远程配置。
●
提供User-interface视图，为不同的终端用户提供个性化配置管理。
●
命令分级保护，不同级别的用户只能执行相应级别的命令。
●
用户可以随时键入“?”来获得在线帮助。
●
提供网络测试命令，如tracert、ping等，迅速诊断网络是否正常。
●
提供种类丰富、内容详尽的调试信息，帮助诊断网络故障。
●
用telnet命令直接登录并管理其它路由器。
●
提供FTP服务，方便用户上传、下载文件。
●
可以执行某条历史命令。
●
命令行解释器提供不完全匹配和上下文关联等多种智能命令解析方法，方便用户
输入。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
14
HUAWEI NetEngine40E
配置指南
1 基础配置



### 1.3.2 命令行接口配置注意事项
特性限制
无



### 1.3.3 搭建命令行运行环境
使用命令行之前，可以将命令行的运行环境设置为习惯的界面。
应用环境
在使用命令行配置业务前，用户可以配置命令行的基本运行环境，以便符合实际环境
的要求。
前置任务
在搭建命令行运行环境之前，需要完成以下任务：
●
路由器安装完毕并加电启动正常。
●
已经通过客户端登录到路由器上。



#### 1.3.3.1 配置用户登录警示信息
当连接到路由器时，会看到一段提示信息。这段信息可以修改为希望显示的内容。
背景信息
登录警示信息是用户在连接到路由器之后，或者通过了登录验证之后、开始交互配置
之前系统显示的一段提示信息。使用此配置为用户登录提供明确的指示信息。
说明
设备重启后，如果用户在系统初始化过程中进行登录操作，由于文件系统还没有完成初始化，此
时会提示用户系统未获取到文件。
操作步骤
步骤1 执行命令system-view，进入系统视图。
步骤2 执行命令header login { information text | file file-name }，设置登录时的警示信
息。
步骤3 执行命令header shell { information text | file file-name }，设置登录成功后的警示
信息。
步骤4 执行命令commit，提交配置。
----结束



#### ******* 配置设备名称
设备名称会出现在命令提示符中，可以根据需要更改设备名称。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
15
操作步骤
步骤1 执行命令system-view，进入系统视图。
步骤2 执行命令sysname host-name，设置设备名称。
步骤3 执行命令commit，提交配置。
----结束
HUAWEI NetEngine40E
配置指南
1 基础配置



#### ******* 设置命令级别
背景信息
如果用户没有对某条命令单独调整过命令级别，命令级别批量提升后，原注册的所有
命令行按以下原则自动调整：
●
0级和1级命令保持级别不变。
●
2级命令提升到10级，3级命令提升到15级。
●
2～9级和11～14级这些命令级别中没有命令行。用户可以单独调整需要的命令行
到这些级别中，以实现权限的精细化管理。
须知
不建议随意修改缺省的命令级别。否则会影响其他用户对命令的使用。
操作步骤
步骤1 执行命令system-view，进入系统视图。
步骤2 执行命令command-privilege level rearrange，批量提升命令的级别。
步骤3 执行命令command-privilege level level view view-name command-key，设置指
定视图内命令的级别。
所有的命令都有默认的视图和优先级，一般不需要用户进行重新设置。
命令级别分为参观、监控、配置、管理4个级别，分别对应标识0、1、2、3，如表1-2
所示。
表1-2 命令级别简介
用户
级别
（0～
3）
用户级
别（0
～
15）
命令级
别
级别名
称
说明
0
0
0
参观级
网络诊断工具命令（ping、tracert）、从本设
备出发访问外部设备的命令（Telnet客户端）
等。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
16
用户
级别
（0～
3）
用户级
别（0
～
15）
命令级
别
级别名
称
说明
1
1～9
0、1
监控级
用于系统维护，包括display等命令。
说明
并不是所有display命令都是监控级，比如管理配置
文件中的display current-configuration命令是3级
管理级。
2
10～
14
0、
1、2
配置级
业务配置命令。
3
15
0、
1、
2、3
管理级
用于系统基本运行的命令，对业务提供支撑作
用，包括文件系统、FTP、TFTP、配置文件切
换命令、备板控制命令、用户管理命令、命令
级别设置命令，设备重启reboot命令、用于业
务故障诊断的debugging命令等。
 
步骤4 执行命令commit，提交配置。
----结束
HUAWEI NetEngine40E
配置指南
1 基础配置



#### 1.3.3.4 锁定用户界面
为防止未授权的用户操作该终端界面，可以使用命令将用户界面锁住。
操作步骤
步骤1 执行命令configuration exclusive，锁定配置权限给当前操作用户。
锁定用户配置权限后，可以显式地获取独享的配置权限，其他用户无法再获取到配置
权限。
说明
可以执行display configuration-occupied user命令，查看当前锁定配置集用户的信息。
步骤2 执行命令system-view，进入系统视图。
步骤3 执行命令configuration-occupied timeout timeout-value，设置自行解锁时间间
隔。
步骤4 执行命令commit，提交配置。
----结束



#### 1.3.3.5 允许在系统视图下执行用户视图命令
对于某些命令只能在用户视图下执行，当用户需要执行该类命令时，必须退出到用户
视图才能成功执行。为了便于用户执行用户视图命令，在不用切换视图的情况下，通
过本配置可实现在系统视图下执行用户视图命令。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
17
操作步骤
步骤1 执行命令system-view，进入系统视图。
步骤2 执行命令run command-line，允许在系统视图下执行用户视图命令。
----结束
HUAWEI NetEngine40E
配置指南
1 基础配置



#### 1.3.3.6 （可选）配置命令时间戳
需要记录命令行执行时间时，可执行如下步骤。
操作步骤
●
使能系统时间戳功能
a.
执行命令system-view，进入系统视图。
b.
执行命令timestamp enable，使能系统的时间戳功能。
使能系统的时间戳功能后，用户执行display查询命令时，系统会在显示信息
前打上执行该命令的时间。
c.
执行命令commit，提交配置。
说明
该功能仅对display查询命令生效。
●
使能当前会话的命令时间戳功能
a.
执行命令terminal command timestamp，使能当前会话的命令时间戳功
能。
说明
●
使能该功能后，用户输入任意命令回车后都会显示执行时间。
●
该功能仅对当前会话生效，用户退出系统重新登录后，该功能失效，需重新配
置。
●
如果用户执行命令undo terminal command timestamp关闭当前会话时间戳功
能，但是已执行timestamp enable命令使能系统的时间戳功能，则用户执行
display查询命令时依然会显示时间戳。
----结束



### 1.3.4 如何使用命令行
命令行的使用包括对命令视图、命令行编辑功能、命令行模板、显示信息和错误信息
的配置和处理。
应用环境
在使用命令行配置业务前，用户需要了解命令行的基本操作。
前置任务
在使用命令行之前，需要完成以下任务：
●
路由器安装完毕并加电启动正常。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
18
●
已经通过客户端登录到路由器上。
HUAWEI NetEngine40E
配置指南
1 基础配置



#### 1.3.4.1 进入命令视图
命令行接口分为若干个命令视图，所有命令都注册在某个（或某些）命令视图下。通
常情况下，必须先进入命令所在的视图才能执行该命令。
说明
两阶段生效模式下，当系统中有未提交的配置时，视图中以*标识，如果系统中没有未提交的配
置时，视图中以~标识。
# 与路由器建立连接，如果此路由器是缺省配置，则进入用户视图，在屏幕上显示：
<HUAWEI>
# 键入system-view后回车，进入系统视图。
<HUAWEI> system-view
[~HUAWEI]
# 在系统视图下键入aaa，则可进入AAA视图。
[~HUAWEI] aaa
[~HUAWEI-aaa] 
说明
命令行提示符“HUAWEI”是缺省的主机名，此名称也可以用sysname指定。通过提示符可以
判断当前所处的视图，例如：“<>”表示用户视图，“[]”表示除用户视图以外的其它视图。
多级视图中，用户可以直接执行#，即可直接回到系统视图。
用户可以在任意视图中，执行！或#加字符串，此时的用户输入将全部（包括！和#在内）作为
系统的注释行内容，不会产生对应的配置信息。
执行命令quit，可以从当前视图退回到上一级别的视图。如果在用户视图下执行该命
令，则退出系统。
执行命令return，可以从当前视图退回到用户视图。如果在用户视图下执行该命令，
仍然处于用户视图。
有些在系统视图下实现的命令，在其它视图下也可以实现，但实现的功能与命令视图
密切相关。比如启动MPLS协议的命令mpls，在系统视图下执行表示启动全局MPLS能
力，在接口视图下执行表示启动当前接口的MPLS能力。



#### 1.3.4.2 编辑命令行
命令行编辑功能可以帮助利用某些特定的键进行命令的编辑或者获得帮助。
NE40E的命令行接口提供基本的命令编辑功能，支持多行编辑，每条命令最大长度为
3100个字符。
一些常用的编辑功能如表1-3所示。
表1-3 编辑功能表
功能键
功能
普通按键
若编辑缓冲区未满，则插入到当前光标位置，并向右移动
光标。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
19
功能键
功能
退格键BackSpace
删除光标位置的前一个字符，光标左移，若已经到达命令
首，则系统无响应。
上光标键↑或<Ctrl_P>
访问上一条历史命令。如果还有更早的历史命令，则取出
上一条命令。
下光标键↓或<Ctrl_N>
访问下一条历史命令。如果还有更新的历史命令，则取出
下一条命令，否则清空命令。
Tab键
输入不完整的关键字后按下Tab键，系统自动执行部分帮
助：
●如果与之匹配的关键字唯一，则系统用此完整的关键字
替代原输入并换行显示，光标距词尾空一格；
●对于不匹配或者匹配的关键字不唯一的情况，首先显示
前缀，继续按Tab键循环翻词，此时光标距词尾不空
格，按空格键输入下一个单词；
●如果输入错误关键字，按Tab键后，换行显示，输入的
关键字不变。
 
后续处理
终端将用户键入的历史命令自动保存，即记录用户在键盘上的任何输入，以“Enter”
键为一条记录。执行display history-command all-users或display history-
command命令可以查看用户最近执行过的历史命令，便于用户查找信息。执行reset
history-command all-users或reset history-command命令可以清除历史命令记
录。
HUAWEI NetEngine40E
配置指南
1 基础配置



#### 1.3.4.3 正则表达式
背景信息
正则表达式描述了一种字符串匹配的模式，由普通字符（例如字符a到z）和特殊字符
（或称“元字符”）组成。正则表达式作为一个模板，将某个字符模式与所搜索的字
符串进行匹配。
正则表达式一般具有以下功能：
●
检查字符串中符合某个规则的子字符串，并可以获取该子字符串。
●
根据匹配规则对字符串进行替换操作。
正则表达式由普通字符和特殊字符组成。
●
普通字符
普通字符匹配的对象是普通字符本身。包括所有的大写和小写字母、数字、标点
符号以及一些特殊符号。例如：a匹配abc中的a，@匹配***********中的@。
●
特殊字符
特殊字符配合普通字符匹配复杂或特殊的字符串组合。如，^10匹配10.10.10.1，
不匹配2.2.2.2。
表1-4是对特殊字符及其语法意义的使用描述。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
20
表1-4 特殊字符及其语法意义描述
特殊字符
功能
举例
\
转义字符。将下一个字符（特殊
字符或者普通字符）标记为普通
字符。
“\*”匹配“*”
^
匹配行首的位置。
“^10”匹配“10.10.10.1”，不
匹配“2.2.2.2”
$
匹配行尾的位置。
“1$”匹配“10.10.10.1”，不
匹配“10.10.10.2”
*
匹配前面的子正则表达式零次或
多次。
“10*”可以匹配“1”、
“10”、“100”、“1000”、
……
“(10)*”可以匹配空、
“10”、“1010”、
“101010”、……
+
匹配前面的子正则表达式一次或
多次。
“10+”可以匹配“10”、
“100”、“1000”、……
“(10)+”可以匹配“10”、
“1010”、“101010”、……
?
匹配前面的子正则表达式零次或
一次。
说明
当前，在华为公司数据通信设备上
通过命令行运用正则表达式输入？
时，系统显示为命令行帮助功能。
华为公司数据通信设备不支持正则
表达式输入？特殊字符。
“10?”可以匹配“1”或者
“10”
“(10)?”可以匹配空或者
“10”
.
匹配任意单个字符。
“a.b”匹配任何一个以“a”开
头，以“b”结尾含有三个字符
的字符串
“0.0”可以匹配“0x0”、
“020”、……
“.oo.”可以匹配“book”、
“look”、“tool”、……
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
21
特殊字符
功能
举例
()
一对圆括号内的正则表达式作为
一个子正则表达式，匹配子表达
式并获取这一匹配。
如果圆括号中内容为空，则等价
于空串。
如果模式串只有()，则可匹配任
意字符串。
如果模式串中的右括号没有匹配
的左括号，则右括号就作为普通
字符。
如果模式串中的左括号没有匹配
的右括号，则为非法模式串。
“100(200)+”可以匹配
“100200”、
“100200200”、……
“(ab)”匹配“abcab”
“()”匹配任意字符串
“a()b”匹配“12ab12”
“a)b”匹配“za)bc”
“a(b”为非法模式串
x|y
匹配x或y。
“100|200”匹配“100”或者
“200”
“1(2|3)4”匹配“124”或者
“134”，而不匹配“1234”、
“14”、“1224”、“1334”
[xyz]
匹配正则表达式中的任意一个字
符。不可同时匹配多个字符，也
不可匹配同一个字符多次。
“[123]”匹配“255”中的
“2”
“[abc]”匹配字符“a”、
“b”、“c”
[^xyz]
匹配字符串中非“x”、“y”、
“z”的字符。只要字符串中有
非“x”、“y”、“z”的字
符，就能匹配到。
“[^123]”匹配除“1”、
“2”、“3”之外的任何字符
“[^abc]”匹配除“a”、
“b”、“c”之外的任何字符
[a-z]
匹配正则表达式指定范围内的任
意一个字符。不可同时匹配多个
字符，也不可匹配同一个字符多
次。
“[0-9]”匹配指定范围内的任意
数字
“[a-z]”匹配指定范围内的任意
字母
“[z-a]”为非法模式串
[^a-d]
匹配字符串中除“a”、“b”、
“c”、“d”以外的其他字符。
只要字符串中有a～d范围外的字
符，就能匹配到。
“[^0-9]”匹配所有非数字字符
“[^a-z]”匹配除字母以外的其
他任意字符
“[^z-a]”为非法模式串
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
22
特殊字符
功能
举例
{x,y}
匹配前面的子正则表达式x~y
次。此处的x、y应小于1000，否
则编译报错。x和y数值越大，模
式串编译的速度越慢。
如果仅有单个大括号（左或
右），则单个大括号作为普通字
符。
如果大括号中仅有一个数字，则
该数字表示前面的子正则表达式
精确匹配的次数。
如果左右大括号中间的逗号左边
没有数字，右边有数字，则左右
大括号及其包含的字符都作为普
通字符。
如果左右大括号中间的逗号左边
有数字，右边没有数字，则该数
字表示前面的子正则表达式匹配
的最少次数，没有上限。
“a{3,5}”匹配“aaa”、
“aaaa”、“aaaaa”
“a{3”匹配“za{3bc”
“a3}”匹配“za3}bc”
“ab{3}”匹配“abbbc”
“(ab){3}”匹配“abababc”
“a{,3}bc”匹配“za{,3}bc”
“a{3,}bc”匹配"aaabc”、
"aaaaabc”、……
 
说明
●
除非特别说明，上表中涉及到的字符指的是可以打印的字符，包括字母、数字、空格及
特殊符号等。
字符的使用
●
特殊字符的退化
某些特殊字符如果处在如下的正则表达式的特殊位置时，会引起退化，成为普通
字符。
–
特殊字符处在转义符号‘\’之后，则发生转义，变为匹配该字符本身。
–
特殊字符“*”、“+”、“?”，处于正则表达式的第一个字符位置。例如：
+45匹配+45，abc(*def)匹配abc*def。
–
特殊字符“^”，不在正则表达式的第一个字符位置。例如：abc^匹配
abc^。
–
特殊字符“$”，不在正则表达式的最后一个字符位置。例如：12$2匹配
12$2。
–
右括号“)”或者“]”没有对应的左括号“(”或“[”。例：abc)匹配abc)，
0-9]匹配0-9]。
说明
除非特别说明，以上正则表达式包括括号“（）”内包含的子正则表达式。
●
普通字符与特殊字符的组合使用
实际应用中，往往不是一个普通字符加上一个特殊字符配合使用，而是由多个普
通字符和特殊字符组合，匹配某些特征的字符串。
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
23
正则表达式的过滤条件
NE40E支持通过在命令中指定过滤条件来应用正则表达式，目前所有的display命令均
支持正则表达式。按过滤条件进行查询时，显示内容的第一行信息中，以包含该字符
串的整条信息作为起始，而非过滤字符串作为起始。
系统支持使用| count显示使用过滤条件后输出的结果的行数，使用| no-more表示显
示输出不分屏，这些过滤均既可以继续与下面的三种过滤方式配合使用，也可以单独
使用。
系统也支持使用| ignore-case表示过滤字符串时不区分大小写，使用| section显示使
用过滤条件后输出的结果段信息，此种方式必须与下面的三种过滤方式配合使用，不
可以单独使用。
在支持正则表达式的命令中，有三种过滤方式可供选择：
●
| begin regular-expression
输出以匹配指定正则表达式的行开始的所有行。即，过滤掉所有待输出字符串，
直到出现指定的字符串（此字符串区分大小写）为止，其后的所有字符串都会显
示到界面上。
●
| exclude regular-expression
输出不匹配指定正则表达式的所有行。即，待输出的字符串中没有包含指定的字
符串（此字符串区分大小写），则会显示到界面上；否则过滤不显示。
●
| include regular-expression
只输出匹配指定正则表达式的所有行。即，待输出的字符串中如果包含指定的字
符串（此字符串区分大小写），则会显示到界面上；否则过滤不显示。
说明
支持多级管道符对命令行进行筛选过滤。按照用户输入的先后顺序，上一级管道处理的输出作为
下一级管道处理的输入，最多支持32级过滤。例如，使用如下命令可以过滤出包含“ip”，且不
包含“address 10.1”以及“description”的结果段配置信息：
display current-configuration | section include ip | exclude address 10.1 | exclude description
按过滤条件进行查询时，有以下注意事项：
●
显示内容的第一行信息中，并不是以过滤字符串作为开始，而是以包含该字符串
的整条信息开始的。
●
对于某些配置命令，虽然用户已经配置，但如果这些配置的功能没有生效，则命
令行界面不予显示。
NE40E还支持将display命令显示的结果重定向到指定的文件。有两种重定向方式可供
选择：
●
> filename
将display命令显示的结果输出到指定的文件。如果目标文件已经存在，则覆盖该
文件的原有内容。
●
>> filename
将display命令显示的结果追加到指定文件的末尾，原文件的内容仍保留。
系统支持使用| refresh周期刷新查询结果。使用| refresh设备每隔一段时间显示一次
查询结果，默认查询间隔是5秒。
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
24
说明
●
命令刷新周期间隔过短，会引起CPU使用率上升，请尽量大的配置查询周期间隔。
●
如果设备剩余的VTY通道数小于4，则无法新增执行| refresh周期查询命令，已执行的周期查
询命令不受影响。
●
只有以display开头的查询命令支持使用| refresh周期查询功能。
HUAWEI NetEngine40E
配置指南
1 基础配置



#### 1.3.4.4 查询配置信息
完成一组配置之后，可以查看当前生效的参数来验证配置是否正确。
背景信息
已完成基本配置。
操作步骤
●
执行命令display current-configuration [ configuration [ configuration-type
[ configuration-instance ] | config-type-no-inst ] | all | inactive ] [ include-
default ]，显示当前配置信息。
●
执行命令display this，显示当前视图的运行配置信息。
对于某些正在生效的配置参数，如果与缺省工作参数相同，则不显示；对于某些
参数，虽然用户已经配置，但如果这些参数所在的功能没有生效，则不予显示。
----结束



#### 1.3.4.5 查询诊断信息
当系统出现故障时，如果难以判断产生故障的模块，可使用本命令收集信息，用于故
障定位。
操作步骤
步骤1 执行命令display diagnostic-information，查询当前系统的诊断信息。
本命令集合了多条常用display命令的输出信息，包括display clock、display
version、display current-configuration等等，可以看作是对系统常用display命令的
批量执行。
----结束



#### 1.3.4.6 命令行结果显示
所有的命令行有共同的显示特性，并且可以根据需求，灵活构建显示方式。
显示特性
在一次显示信息超过一屏时，提供暂停功能，在暂停显示时用户可以有三种选择，如
表1-5所示。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
25
表1-5 显示功能表
功能键
功能
键入空格键
继续显示下一屏信息。
键入回车键
继续显示下一行信息。
键入+ regular-expression
功能等同于管道符| include regular-
expression功能。
键入- regular-expression
功能等同于管道符| exclude regular-
expression功能。
键入/ regular-expression
功能等同于管道符| begin regular-
expression功能。
键入<Ctrl_C>和其他非以上键
停止显示和命令执行。
 
HUAWEI NetEngine40E
配置指南
1 基础配置



#### 1.3.4.7 命令行的错误信息
所有用户键入的命令，如果通过语法检查，则正确执行，否则系统将会向用户报告错
误信息。
常见错误信息参见表1-6。
表1-6 命令行常见错误信息表
英文错误信息
错误原因
Unrecognized command
没有查找到命令
没有查找到关键字
Wrong parameter
参数类型错
参数值越界
Incomplete command
输入命令不完整
Too many parameters
输入参数太多
Ambiguous command
输入命令不明确



#### 1.3.4.8 命令行别名
命令行别名功能可以将设备中的命令行别名为用户执行的字符串，方便用户使用。命
令行别名功能只能在人机模式下使用，机机模式下命令行别名功能不生效。
背景信息
用户可以通过命令terminal command alias打开当前会话的别名特性开关，也可以通
过命令undo terminal command alias关闭当前会话的别名特性开关。关闭当前会话
的别名特性，仅影响当前会话的别名配置功能，并不清除系统中存在的别名配置信
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
26
息。当继续执行打开当前会话的别名特性开关后，配置文件中的别名配置信息继续生
效。用户可以通过命令display terminal command alias查看命令别名特性的开关状
态。
操作步骤
步骤1 执行命令system-view，进入系统视图。
步骤2 执行命令command alias，进入命令行别名视图。
步骤3 执行命令alias alias-string [ parameter parameter & <1-32> ] command
command，进行命令别名配置。
----结束
后续处理
●
用户完成命令别名配置后，可以执行命令display command alias查看别名配置
信息。
HUAWEI NetEngine40E
配置指南
1 基础配置



#### 1.3.4.9 命令行智能回退
命令行具有智能回退功能，即在当前视图下可执行其他视图下的命令。
背景信息
命令行支持智能回退，即在当前视图下执行某条命令，如果命令行匹配失败，会自动
退到上一级视图进行匹配，如果仍然失败则继续退到上一级视图匹配，直到退到系统
视图为止。如果能够匹配到此命令，则会在当前视图下执行此命令并进入相应视图。
用户在配置业务时，为完成所需配置，需进入待配置命令所在视图，才能配置成功，
这样就需要重复执行quit命令退出当前视图，进入所需视图，给用户配置带来很多重复
工作。智能回退功能，可以在当前视图下执行其他视图的命令，减少重复工作，为用
户配置提供便利。
说明
如果在当前视图下由于模糊匹配发生歧义导致匹配失败时，不进行智能回退。
命令行匹配失败时不进行智能回退。
undo命令不支持智能回退。
任务示例
1.
执行命令terminal command forward matched upper-view使能命令行智能回
退功能。
2.
执行命令system-view，进入系统视图。
3.
执行命令interface GigabitEthernet1/0/0.1，创建子接口并进入子接口视图。
4.
无需退出当前视图，可直接执行命令interface LoopBack 1，创建LoopBack接口
并进入LoopBack接口视图。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
27
须知
智能回退功能可能会出现命令行在非预期视图执行，可能会影响业务运行，配置命令
行前请仔细确认本视图下是否存在即将配置的命令行，如果不存在请在正确的视图执
行该命令行。
如果需要关闭命令行智能回退功能，可执行命令undo terminal command forward
matched upper-view关闭。
HUAWEI NetEngine40E
配置指南
1 基础配置



#### 1.3.4.10 启用二次认证功能
二次认证功能可以防止用户误操作造成的业务中断。
背景信息
设备上有些命令，如果用户误操作会关联删除相关特性的配置，导致业务中断，造成
用户网络中断。为了防止误操作，用户可以启用二次认证功能。
当二次认证功能启用后，执行这些命令时，需要先输入登录密码进行二次认证后该命
令才能生效，命令范围包含：reboot、reset saved-configuration、rollback
configuration、undo mpls、undo mpls te、undo mpls rsvp、undo mpls ldp、
undo mpls l2vpn、undo multicast ipv6 routing-enable、undo multicast
routing-enable、undo pim、undo igmp、undo bgp、undo stp enable。
说明
为防止误操作导致某些业务不可用，建议使能二次认证功能。
任务示例
1.
执行命令system-view，进入系统视图。
2.
执行命令configuration re-authentication enable，启用二次认证功能。
3.
执行命令commit，提交配置。



### 1.3.5 如何获得命令帮助
输入命令行或进行配置业务时，命令帮助可以提供配置手册之外的实时帮助。
NE40E的命令行接口提供如下两种在线帮助。
完全帮助
命令行的完全帮助可以通过以下三种方式获取。
●
在任一命令视图下，键入“?”获取该命令视图下所有的命令及其简单描述。
<HUAWEI> ?
●
键入一条命令，后接以空格分隔的“?”，如果该位置为关键字，则列出全部关键
字及其简单描述。举例如下：
<HUAWEI> terminal ?
  debugging  Enable/disable debug information to terminal  
  logging    Enable/disable log information to terminal
其中“debugging”、“logging”是关键字，后面语句是对关键字的描述。
●
键入一条命令，后接以空格分隔的“?”，如果该位置为参数，则列出参数取值的
说明和参数作用的描述。举例如下：
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
28
[*HUAWEI] ftp timeout ?
  INTEGER<1-35791>  The value of FTP timeout, the default value is 10 minutes
[*HUAWEI] ftp timeout 35 ?
<cr>                    
其中，“INTEGER<1-35791>”是参数取值的说明，“The value of FTP timeout,
the default value is 10 minutes”是对参数作用的简单描述；<cr>表示该位置无
参数，直接键入回车即可执行。
部分帮助
命令行的部分帮助可以通过以下三种方式获取。
●
键入一字符串，其后紧接输入“?”，列出以该字符串开头的所有关键字。
<HUAWEI> d?
  debugging                               delete
  dir                                     display
●
键入一条命令，关键字不唯一时紧接输入“?”，则列出以该字符串开头的所有关
键字。
<HUAWEI> display c?
  car                                     clock
  configuration                           control-flap
  cpu-defend                              cpu-monitor
  cpu-usage                               current-configuration
●
输入命令的某个关键字的前几个字母，按下<tab>键，可以显示完整的关键字。如
果关键字不唯一，可以连续按下<tab>键，则会出现不同的关键字，用户可以从中
选择需要的关键字。
HUAWEI NetEngine40E
配置指南
1 基础配置



### 1.3.6 如何使用快捷键
可以通过使用系统快捷键或者自定义的快捷键，完成对应命令的输入，简化操作。
应用环境
在使用命令行配置业务时，如果某些命令经常使用，用户可以定义快捷键实现快速键
入。
前置任务
在使用快捷键之前，需要完成以下任务：
●
路由器安装完毕并加电启动正常。
●
已经通过客户端登录到路由器上。



#### 1.3.6.1 快捷键的分类
在使用快捷键时，会接触到两类快捷键，自定义的快捷键和系统快捷键。了解了分类
后，可以更快速和准确的使用快捷键。
系统中的快捷键分成两类：
●
提供给用户、可以自由定义的快捷键：包括CTRL_G、CTRL_L、CTRL_O和
CTRL_U。用户可以根据自己的需要将这几个快捷键与任意命令进行关联，当使用
快捷键时，系统自动执行它所对应的命令。定义此类快捷键的方法请参见1.3.6.2
定义快捷键。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
29
●
系统快捷键：是系统中固定的。这种快捷键不由用户自定义，代表固定功能。系
统包括的主要快捷键如表1-7所示。
说明
由于不同的终端软件对于某些键的解释不同，具体终端上实际可用的快捷键与本节所列举的按键
组合可能略有差异。
表1-7 系统快捷键
功能键
功能
CTRL_A
移动光标到当前行的开头。
CTRL_B
将光标向左移动一个字符。
CTRL_C
停止当前正在执行的功能。
CTRL_D
删除当前光标所在位置的字符。
CTRL_E
移动光标到当前行的末尾。
CTRL_F
将光标向右移动一个字符。
CTRL_H
删除光标左边的一个字符。
CTRL_I
功能等同于TAB键。
CTRL_J
功能等同于回车键。
CTRL_K
在连接建立阶段终止呼出的连接。
CTRL_M
功能等同于回车键。
CTRL_N
显示历史命令缓冲区中的后一条命令。
CTRL_P
显示历史命令缓冲区中的前一条命令。
CTRL_R
重新显示当前行。
CTRL_T
终止呼出的连接。
CTRL_V
粘贴剪贴板的内容。
CTRL_W
删除光标左侧的字。
CTRL_X
删除光标左侧所有字符。
CTRL_Y
删除光标右侧所有字符。
CTRL_Z
返回到用户视图。
CTRL_]
终止呼入的连接或重定向连接。
ESC_B
将光标向左移动一个字。
ESC_D
删除光标右侧的字。
ESC_F
将光标向右移动一个字。
ESC_N
移动光标到下一行。
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
30
功能键
功能
ESC_P
移动光标到上一行。
ESC_<
将光标所在位置指定为剪贴板的开始位置。
ESC_>
将光标所在位置指定为剪贴板的结束位置。
 
HUAWEI NetEngine40E
配置指南
1 基础配置



#### 1.3.6.2 定义快捷键
只有管理级用户有定义快捷键的权限。
操作步骤
步骤1 执行命令system-view，进入系统视图。
步骤2 执行命令hotkey { CTRL_G | CTRL_L | CTRL_O | CTRL_U } command-text，定义快
捷键。
步骤3 执行命令commit，提交配置。
----结束



#### 1.3.6.3 查看快捷键配置列表
在任何允许输入命令的地方都可以键入快捷键，系统执行时，会将该快捷键对应的命
令显示在屏幕上，如同输入了完整的命令一样。
背景信息
如果用户已经输入了命令的一部分，但是还没有键入回车以确认，此时键入快捷键将
会把以前输入的字符全部清空，并将该快捷键对应的命令显示在屏幕上，效果与用户
删除所有的输入，然后重新敲入完整的命令一样。
快捷键的执行与命令一样，也会将命令原形记录在命令缓冲区和日志中以备问题定位
和查询。
操作步骤
步骤1 执行命令display hotkey，查看快捷键的配置列表。
说明
快捷键的功能可能受用户所用的终端影响，例如用户终端本身自定义的快捷键与路由器系统中的
快捷键功能发生冲突，此时如果用户键入快捷键将会被终端程序截获而不能执行它所对应的命令
行。
----结束



### 1.3.7 配置会话日志功能开关
全局会话日志功能默认关闭，单个连接的会话日志功能默认打开，用户可根据需要选
择执行以下步骤配置会话日志功能开关。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
31
背景信息
全局会话日志功能默认关闭，用户需要将输入、设备屏幕输出以及设备执行用户输入
命令的时间保存到根目录下sessionlog文件夹中时，可打开全局会话日志功能。
记录单个连接的会话日志功能默认打开，用户使能全局会话日志功能后，会将设备上
所有连接的输入、设备屏幕输出等信息记录到日志文件中，如果某些连接不需要生成
会话日志，可执行相应命令关闭指定连接的会话日志功能。
操作步骤
●
打开全局会话日志功能。
a.
执行命令system-view，进入系统视图。
b.
执行命令undo info-center session log disable，打开全局会话日志功能。
c.
执行命令commit，提交配置。
●
关闭当前连接的会话日志功能。
执行命令display info-center session log status，查看设备上的全局会话日志功
能开关及所有在线连接的会话日志功能状态。
–
如果全局会话日志功能打开，则执行如下步骤：
i.
执行命令system-view，进入系统视图。
ii.
执行命令terminal session-log disable，关闭当前连接的会话日志功
能。
iii.
执行命令commit，提交配置。
–
如果全局会话日志功能关闭，则无需执行操作。
----结束
检查配置结果
执行命令display info-center session log status，查看设备上的全局会话日志功能开
关及所有在线连接的会话日志功能开关状态。
HUAWEI NetEngine40E
配置指南
1 基础配置



### 1.3.8 配置举例
通过配置举例来描述命令行的使用方法。
******* 使用Tab 键示例
输入Tab键可以提示所有相关的关键字，或者检查关键字是否正确。
组网需求
网络中的任意一台路由器。
配置注意事项
无
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
32



#### ******* 使用Tab键示例
#### ******* 定义快捷键示例
如果登录的路由器上定义了快捷键，那么所有的用户均可以使用，不区分用户级别。
组网需求
网络中的任意一台路由器。
配置注意事项
如果用户没有快捷键里面定义的命令的使用权限，那么执行此快捷键后对应的命令不
生效。
配置思路
采用如下思路配置定义快捷键的示例：
1.
定义快捷键CTRL_U，与命令display ip routing-table进行关联。
2.
在提示符[~HUAWEI]下键入快捷键“Ctrl+U”即显示。
数据准备
为完成此配置例，需准备如下的数据：
●
快捷键名
●
需要关联快捷键的命令名
操作步骤
步骤1 定义快捷键CTRL_U，与命令display ip routing-table进行关联，并执行。
<HUAWEI> system-view
[~HUAWEI] hotkey ctrl_u "display ip routing-table"
[*HUAWEI] commit
步骤2 在提示符[~HUAWEI]下键入快捷键“Ctrl+U”即显示。
[~HUAWEI] display ip routing-table
Route Flags: R - relay, D - download to fib, T - to vpn-instance, B - black hole route
------------------------------------------------------------------------------
Routing Table: Public
         Destinations : 8        Routes : 8
Destination/Mask    Proto  Pre  Cost     Flags NextHop         Interface
     ********/32    Direct 0    0           D  127.0.0.1       InLoopBack0
      ********/16   Direct 0    0           D  ***********     GigabitEthernet0/0/0
   ***********/32   Direct 0    0           D  127.0.0.1       InLoopBack0
  ************/32   Direct 0    0           D  127.0.0.1       InLoopBack0
      *********/8   Direct 0    0           D  127.0.0.1       InLoopBack0
      127.0.0.1/32  Direct 0    0           D  127.0.0.1       InLoopBack0
***************/32  Direct 0    0           D  127.0.0.1       InLoopBack0
***************/32  Direct 0    0           D  127.0.0.1       InLoopBack0
----结束
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
34
配置文件
无
HUAWEI NetEngine40E
配置指南
1 基础配置



## 1.4 用户界面配置
当用户通过Console口、Telnet或SSH方式登录路由器时，系统会分配相应的用户界
面，用来管理、监控设备和当前用户之间的会话。



### 1.4.1 用户界面概述
系统支持的用户界面有Console用户界面和VTY用户界面。
在配置了用户界面、用户管理和终端服务后，用户才能登录到设备对本地或远端的网
络设备进行配置、监控和维护。用户界面提供登录场所，用户管理确保登录安全，终
端服务则提供登录协议支持。
每个用户界面有对应的用户界面视图（User-interface view），在用户界面视图下网络
管理员可以配置一系列参数，如用户登录时是否需要认证、用户登录后的级别等。当
用户使用该用户界面登录时，将受到这些参数限制，从而达到统一管理各种用户会话
连接的目的。
目前系统支持的用户界面：
●
Console用户界面：用来管理和监控通过Console口登录的用户。
Console口端口类型为EIA/TIA-232 DCE。
●
VTY（Virtual Type Terminal，虚拟类型终端）用户界面：用来管理和监控通过
VTY方式登录的用户。
VTY口用于对设备进行Telnet或SSH访问，最多支持21个用户同时通过VTY方式访
问。
说明
同一用户登录的方式不同，分配的用户界面不同；同一用户登录的时间不同，分配的用户界面也
可能不同。
用户界面的编号
用户登录设备时，系统会根据此用户的登录方式自动分配一个当前空闲且编号最小的
相应类型的用户界面，整个登录过程将受到该用户界面视图下的配置约束。
用户界面的编号有两种方式：相对编号方式和绝对编号方式。
●
相对编号方式
相对编号方式只能唯一指定某种类型的用户界面中的一个或一组，而不能跨类型
操作。
相对编号方式的形式是：用户界面类型＋编号，遵守的规则如下：
–
Console口的编号：CON 0。
–
VTY的编号：第一个为VTY 0，第二个为VTY 1，依此类推。
●
绝对编号方式
绝对编号方式可以唯一的指定一个用户界面或一组用户界面。绝对编号的起始编
号是0，每次增长1，并按照Console、VTY的顺序依次分配。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
35
Console口有1个，VTY类型的用户界面有21个，可以在系统视图下使用命令user-
interface maximum-vty设置最大用户界面个数。
表1-8 用户界面的相对、绝对编号说明
用户界面
说明
绝对编号
相对编号
Console用
户界面
用来管理和监控通过
Console口登录的用户。
该用户界面仅在Admin-
VS支持。
0
0
TTY（True
Type
Terminal）
用户界面
说明
当前未支持
TTY用户界
面。
用来管理和监控通过异
步串口登录的用户。
1～32
第一个为TTY 0，第二
个为TTY 1，依此类
推。
绝对编号1～32对应相
对编号TTY 0～TTY
31。
AUX用户界
面
用来管理和监控通过
AUX口登录的用户。
通常用于通过Modem进
行拨号访问。
33
0
VTY
（Virtual
Type
Terminal）
用户界面
用来管理和监控通过
Telnet或SSH方式登录的
用户。
34～54
第一个为VTY 0，第二
个为VTY 1，依此类
推。
绝对编号34～54对应
相对编号VTY 0～VTY
20。
 
用户界面的用户验证
配置用户界面的用户验证方式后，用户登录设备时，系统对用户的身份进行验证。对
用户的验证方式如下：
●
Password验证：需要进行密码验证，只有密码验证成功，用户才能成功登录。
●
AAA验证：需要进行用户名和密码验证，用户名或密码错误，均会导致登录失
败。对Telnet用户一般采用AAA验证。
用户界面的用户级别
系统支持对登录用户进行分级管理。用户所能访问命令的级别由用户的级别决定。
●
如果对用户采用password验证，登录到设备的用户所能访问的命令级别由登录时
的用户界面级别决定。
●
如果对用户采用AAA验证，登录到设备的用户所能访问的命令级别由AAA配置信
息中本地用户的级别决定。
用户级别与命令级别的对应关系如表1-9所示。
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
36
表1-9 用户级别与命令级别对应关系
用户
级别
（0～
3）
用户级
别（0
～
15）
命令级
别
级别名
称
说明
0
0
0
参观级
网络诊断工具命令（ping、tracert）、从本设
备出发访问外部设备的命令（Telnet客户端）
等。
1
1～9
0、1
监控级
用于系统维护，包括display等命令。
说明
并不是所有display命令都是监控级，比如管理配置
文件中的display current-configuration命令是3级
管理级。各命令的级别请参见《HUAWEI
NetEngine40E-命令参考》手册。
2
10～
14
0、
1、2
配置级
业务配置命令。
3
15
0、
1、
2、3
管理级
用于系统基本运行的命令，对业务提供支撑作
用，包括文件系统、FTP、TFTP、配置文件切
换命令、备板控制命令、用户管理命令、命令
级别设置命令，设备重启reboot命令、用于业
务故障诊断的debugging命令等。
 
HUAWEI NetEngine40E
配置指南
1 基础配置



### 1.4.2 用户界面配置注意事项
特性限制
无
1.4.3 配置Console 用户界面
Console用户界面用来管理和监控通过Console口登录的用户。
应用环境
当用户需要通过Console口登录设备对设备进行本地维护时，需要配置相应的Console
用户界面，包括Console用户界面的物理属性、终端属性、用户级别以及用户验证方式
等。用户可以根据使用需求以及出于对设备安全性的考虑配置相应的参数。
前置任务
在配置Console用户界面之前，需要完成以下任务：
●
成功通过Console口登录路由器
1.4.3.1 配置Console 用户界面的物理属性
Console用户界面的物理属性包括Console口的传输速率、流控方式、校验位、停止位
和数据位。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
37



### 1.4.3 配置Console用户界面
#### 1.4.3.1 配置Console用户界面的物理属性
#### 1.4.3.2 配置Console用户界面的终端属性
#### 1.4.3.3 配置Console用户界面的用户级别
#### 1.4.3.4 配置Console用户界面的用户验证
#### 1.4.3.5 配置Console用户界面去使能
#### 1.4.3.6 检查配置结果
Console用户界面配置成功后，可以查看到用户界面的使用信息、物理属性和配置、本
地用户列表和在线用户等信息。
前提条件
已完成Console用户界面的相关配置。
操作步骤
●
使用display users [ all ]命令，查看用户界面的用户登录信息。
●
使用display user-interface console 0命令，查看用户界面信息。
VS模式下，该命令仅在Admin VS支持。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
41
●
使用display local-user命令，查看本地用户的属性信息。
●
使用display access-user命令，查看通过AAA认证的用户信息。
----结束
1.4.4 配置VTY 用户界面
VTY用户界面用来管理和监控通过VTY方式登录的用户。
应用环境
当用户需要通过Telnet或SSH方式进行本地或远程配置和管理设备时可以配置相应的
VTY用户界面，包括VTY用户界面的最大个数、呼入呼出限制、终端属性、用户级别以
及用户验证方式等。用户可以根据使用需求以及出于对设备安全性的考虑配置相应的
参数。
前置任务
在配置VTY用户界面之前，需要完成以下任务：
●
成功通过Console口登录路由器。
1.4.4.1 配置VTY 用户界面的最大个数
用户可以配置同时登录设备的VTY类型用户界面的最大个数，实现对登录用户量的限
制。
背景信息
VTY用户界面的最大个数是当前登录设备的Telnet用户和SSH用户的总和。
须知
当配置VTY用户界面最大个数为0时，任何用户（包括网管用户）都无法通过VTY登录
设备。
操作步骤
步骤1 执行命令system-view，进入系统视图。
步骤2 执行命令user-interface maximum-vty number，配置可以同时登录到设备的VTY类
型用户界面的最大个数。
●
如果配置的VTY用户接口的最大数小于当前的最大数量，不会影响当前在线用
户，也不需要其它配置。
●
如果配置的VTY用户接口的最大数量大于当前的最大数量，需要为新增加的用户
接口配置验证方式和密码。因为对于新增用户接口系统默认为使用密码验证。
例如：当前允许最多5个VTY用户同时在线，现在配置允许18个VTY用户同时在
线，那么VTY用户接口5～17就需要使用authentication-mode命令配置验证方式
和密码，配置如下：
<HUAWEI> system-view
[~HUAWEI] user-interface maximum-vty 18
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
42



### 1.4.4 配置VTY用户界面
#### 1.4.4.1 配置VTY用户界面的最大个数
#### 1.4.4.2 配置VTY用户界面的呼入呼出限制
#### 1.4.4.3 配置VTY用户界面的终端属性
#### 1.4.4.4 配置VTY用户界面的用户级别
#### 1.4.4.5 配置VTY用户界面的用户验证
#### 1.4.4.6 配置可用VTY通道数的超限告警阈值
#### 1.4.4.7 使能VTY用户界面的安全策略
#### 1.4.4.8 检查配置结果
VTY用户界面配置成功后，可以查看到用户界面的使用信息、VTY类型用户界面的最大
个数以及物理属性和配置等信息。
前提条件
已完成VTY用户界面的所有配置。
操作步骤
●
使用display users [ all ]命令，查看用户界面的用户登录信息。
●
使用display user-interface maximum-vty命令，查看VTY类型用户界面的最大
个数。
●
使用display user-interface vty ui-number命令，查看物理属性和用户界面配置
信息。
●
使用display local-user命令，查看本地用户的属性信息。
●
使用display access-user命令，查看通过AAA认证的用户信息。
●
使用display vty mode命令，查看VTY模式。
----结束



### 1.4.5 配置举例
配置Console用户界面、VTY用户界面的示例，配置示例中包括组网需求、配置注意事
项和配置思路等。
1.4.5.1 配置Console 用户界面示例
在本示例中，通过配置Console用户界面的物理属性、终端属性、用户级别、验证方式
和验证密码，实现通过Console口使用Password方式登录设备。
组网需求
在初始化空配置设备或本地维护设备时，用户需要通过Console用户界面登录并进行配
置。设备管理员可以根据使用需求或对设备安全性的考虑，可配置Console用户界面的
相关属性。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
49



#### 1.4.5.1 配置Console用户界面示例
#### 1.4.5.2 配置VTY用户界面示例
## 1.5 用户登录配置
用户可以通过Console口、Telnet或SSH（STelnet）方式登录设备，实现对设备的本地
或远程维护。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
53
背景信息
可以配置允许多个用户同时使用Console服务器登录设备，但存在以下安全风险：
●
若用户使用Console服务器登录设备时需要进行认证，则设备仅对第一个登录的用
户进行认证，而对后续登录的用户不会进行认证。
●
多个用户使用Console服务器同时登录设备时，所有用户的输入信息，会在其他用
户的命令行终端上同步显示。例如，若某个用户使用命令修改密码，则其显示信
息会被其他用户所感知。
因此，请谨慎使用Console服务器登录方式。若用户需要使用此登录方式，不推荐配置
允许多个用户同时登录。
HUAWEI NetEngine40E
配置指南
1 基础配置



### 1.5.1 用户登录概述
用户只有成功登录到设备，才能实现对设备的管理与维护。用户登录设备的方式有：
Console口、Telnet或STelnet。
配置了用户界面、用户管理和终端服务后，用户才能登录到设备，对本地或远端的设
备进行配置、监控和维护。
用户界面提供登录入口，用户管理确保登录安全，终端服务则提供登录协议支持。
用户可通过如表1-10所示几种方式登录设备，对设备进行配置和管理。
表1-10 用户登录方式
登录方式
应用场景
说明
配置用户通过
Console口登录系
统
使用终端通过连接设备的
Console口登录设备，进行第
一次上电和配置。
●当用户无法进行远程访问
设备时，可通过Console进
行本地登录。
●当设备系统无法启动时，
可通过Console口进行诊断
或进入BootRom进行系统
升级。
缺省情况下，用户可以直接通
过Console口本地登录设备。
说明
如果用户登录的是备板Console
口，此时用户只有查询权限，无
配置管理权限。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
54
登录方式
应用场景
说明
配置用户通过
Telnet登录系统
通过终端连接到网络上，使用
Telnet方式登录设备，进行本
地或远程的配置，目标设备根
据配置的登录参数对用户进行
验证。
常用于本地或远程维护设备，
特别是远程维护方式，为用户
带来了极大的方便，但安全性
不高。
缺省情况下，用户不能通过
Telnet方式直接登录设备。如
果需要通过Telnet方式登录设
备，必须先通过Console口本
地登录设备，并完成以下配
置：
●配置设备管理网口的IP地
址，确保终端和登录的设备
之间路由可达（缺省情况
下，设备管理网口的IP地址
为***********/24）。
●配置VTY用户界面的用户认
证方式。
●配置VTY用户界面的用户级
别。
●使能Telnet服务器功能。
配置用户通过
STelnet登录系统
通过终端连接到网络上，如果
网络安全性不高，SSH
（Secure Shell）可提供安全
的信息保障和强大认证功能，
保护设备系统不受IP欺骗等攻
击。
常用于需要安全登录设备，实
现安全的本地维护或远程维
护。
缺省情况下，用户不能通过
STelnet方式直接登录设备。如
果需要通过STelnet方式登录设
备，必须先通过Console口本
地登录设备，并完成以下配
置：
●配置设备管理网口的IP地
址，确保终端和登录的设备
之间路由可达（缺省情况
下，设备上没有配置IP地
址）。
●配置VTY用户界面的用户认
证方式。
●配置VTY用户界面的用户级
别。
●配置VTY用户界面支持SSH
协议。
●配置SSH用户并指定服务方
式包含STelnet。
●使能STelnet服务器功能。
配置用户通过AUX
口登录系统
当用户终端和设备之间无可达
路由时，用户可以通过设备的
AUX口登录设备，实现远程配
置和管理，也可以通过AUX口
进行本地维护。
通过AUX口登录设备后无法直
接实现管理和维护设备，为了
实现远程管理和维护设备，建
议通过AUX口登录设备前，先
通过Console口本地登录设
备，更改AUX用户界面的用户
优先级。
 
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
55
当用户通过以上方式登录设备时，设备会自动执行一个系统中预设好的批处理文件，
并将此操作信息记录在系统日志文件中。
Console 口概述
Console口概述请参见首次登录系统概述。
Telnet 概述
Telnet协议在TCP/IP协议族中属于应用层协议，通过网络提供远程登录和虚拟终端功
能。NE40E提供的Telnet服务包括：
●
Telnet server：用户在PC上运行Telnet客户端程序登录到设备，对设备进行配置管
理。此时，设备提供Telnet server服务。
●
Telnet client：用户在PC上通过终端仿真程序或Telnet客户端程序建立与设备的连
接后，再执行telnet命令登录到其它设备，对其进行配置管理。如图1-7所示，CE
此时既作为Telnet server，同时也提供Telnet client服务。
图1-7 Telnet server 提供Telnet client 服务示意图
●
中断Telnet服务
图1-8 Telnet 快捷键使用示意图
在Telnet连接过程中，可以使用两种快捷键来中断连接。如图1-8所示，P1通过
Telnet登录到P2，再Telnet连接到P3，形成级联结构。P1是P2的Telnet客户端，
P2是P3的Telnet客户端，以此结构说明两种快捷键的用法。
–
<Ctrl_]>快捷键——通知服务器端断开连接
在网络畅通的情况下，键入<Ctrl_]>将通知Telnet服务器中断本次Telnet登
录，即，Telnet服务器端主动断开连接。
例如，在P3上键入<Ctrl_]>，将退回到P2的提示符。
<P3> Select Ctrl_] to return to the prompt of P2
The connection was closed by the remote host.
<P2>
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
56
此时键入<Ctrl_]>，将退回到P1的提示符。
<P2> Ctrl_]
The connection was closed by the remote host.
<P1>
说明
如果由于某些原因网络连接断开，快捷键的指令无法到达Telnet服务器端，输入无
效。
–
<Ctrl_K>快捷键——客户端主动断开连接
当服务器端故障且客户端无法感知时，客户端输入任何指令服务器均无响
应，这种情况下键入<Ctrl_K>快捷键，则Telnet客户端主动中断并退出Telnet
连接。
例如，在P3上键入<Ctrl_K>，将直接中断并退出Telnet连接。
<P3> Ctrl_K
<P1>
须知
当远端用户登录数达到VTY类型用户界面的最大个数时，系统会提示所有的
用户接口都在使用，不允许Telnet。
STelnet 概述
说明
目前，设备作为SSH服务器时，支持SSH2和SSH1两个版本。设备作为SSH客户端时，只支持
SSH2版本。STelnet基于SSH2协议，客户端和服务器之间经过协商，建立安全连接，客户端可
以像操作Telnet一样登录服务器。
Telnet缺少安全的认证方式，而且传输过程采用TCP进行明文传输，存在很大的安全隐
患。单纯提供Telnet服务容易招致DoS（Denial of Service）、主机IP地址欺骗、路由
欺骗等恶意攻击。
相对于Telnet，SSH（Secure Shell）通过以下措施实现在不安全网络上提供安全的远
程访问：
●
支持密钥验证，根据非对称加密体系的加密原则，通过生成公钥和私钥，实现密
钥的安全交换，最终实现安全的会话全过程。
●
支持对传输的数据进行加密。
●
SSH客户端与服务器端通讯时，用户名及口令均进行加密，有效防止对口令的窃
听。
设备支持SSH服务器功能，可以接受多个SSH客户端的连接。同时，设备还支持SSH客
户端功能，允许用户与支持SSH服务器功能的设备建立SSH连接，从而实现从本地设备
通过SSH登录到远程设备。
●
本地连接
如图1-9所示，可以建立SSH通道进行本地连接。
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
57
图1-9 在局域网内建立SSH 通道
 
●
广域网连接
如图1-10所示，可以建立SSH通道进行广域网连接。
图1-10 通过广域网建立SSH 通道
 
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
58
HUAWEI NetEngine40E
配置指南
1 基础配置



### 1.5.2 登录设备命令行界面配置注意事项
特性限制
表1-11 本特性的使用限制
特性限制
系列
涉及产品
Telnet源地址只能配置主IP，不能配置虚IP。若有场景
需要将报文按照虚IP来进行限制，可以采用配置源地址
+ACL的方式来进行限制。
NE40E
NE40E-
X16C/
NE40E-
X8C/
NE40E-
X8A/
NE40E-
X3A/
NE40E-
X16A/
NetEngine
40E-X8AK
 
1.5.3 配置用户通过Console 口登录系统
当用户需要配置第一次上电的设备或在本地维护设备时，可以通过Console口登录。
应用环境
用户可以在本地通过Console口登录设备，尤其是设备第一次上电时只能采用此方式登
录。
●
当用户无法进行远程访问设备时，可通过Console进行本地登录。
●
当设备系统无法启动时，可通过Console口进行诊断或进入BootRom进行系统升
级。
前置任务
在配置用户通过Console口登录系统之前，需要完成以下任务：
●
PC已安装终端仿真程序（如PuTTY.exe软件）
●
准备好串口配置电缆
******* 用户通过Console 口登录系统
用户通过Console口连接终端与设备，实现从用户终端登录设备。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
59



### 1.5.3 配置用户通过Console口登录系统
#### ******* 用户通过Console口登录系统
#### 1.5.3.2 （可选）配置Console用户界面
#### 1.5.3.3 检查配置结果
用户通过Console口登录系统配置成功后，可以查看到用户界面的使用信息、物理属性
和配置、本地用户列表和在线用户等内容。
前提条件
已完成用户通过Console口登录系统的相关配置。
操作步骤
●
使用display users [ all ]命令，查看用户界面的用户登录信息。
●
使用display user-interface console 0命令，查看用户界面信息。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
62
VS模式下，该命令仅在Admin VS支持。
●
使用display local-user命令，查看本地用户的属性信息。
●
使用display access-user命令，查看通过AAA认证的用户信息。
----结束
1.5.4 配置用户通过Telnet 登录系统
Telnet可使用户远程登录设备进行管理与维护。
应用环境
如果网络中有一台或多台设备需要配置和管理，用户无需为每一台设备连接用户终端
进行本地维护。如果已知待登录设备的IP地址，且非首次登录设备，用户可以通过
Telnet方式从用户终端登录设备，对设备进行远程配置。用户可以通过此方式在一台用
户终端上维护网络中的多台设备，极大地方便了用户的操作。
说明
设备的IP地址需要通过Console口预先配置。
此协议不安全，建议使用STelnet。
前置任务
在配置用户通过Telnet登录设备之前，必须通过Console口登录设备，更改设备的缺省
配置，以便用户能够通过Telnet方式远程登录设备并实现管理和维护。更改的缺省配置
如下：
●
配置设备管理网口的IP地址，确保终端和登录的设备之间路由可达。
●
配置VTY用户界面的用户级别和验证方式，实现远程管理和维护设备。
●
使能Telnet服务器功能，以便用户能够通过Telnet方式远程登录设备。
******* 配置VTY 用户界面的用户级别和验证方式
为了实现通过Telnet方式远程登录设备维护和管理设备，必须先通过Console口登录设
备，更改用户级别和配置用户验证方式。
背景信息
VTY用户界面的其他属性在设备上都有缺省值，用户一般不需要另外配置。但是可以根
据用户使用需求，配置相关属性。具体配置请参见配置VTY用户界面。
操作步骤
●
配置VTY用户界面的用户级别
a.
执行命令system-view，进入系统视图。
b.
执行命令user-interface vty first-ui-number [ last-ui-number ]，进入VTY
用户界面视图。
c.
执行命令user privilege level level，设置用户级别，VTY用户界面的用户级
别和命令级别对应关系如表1-12所示。
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
63



### 1.5.4 配置用户通过Telnet登录系统
#### ******* 配置VTY用户界面的用户级别和验证方式
#### ******* 使能Telnet服务器功能
#### ******* 用户通过终端Telnet登录到系统
#### ******* 配置Telnet服务器参数
#### ******* （可选）配置Telnet协议的白名单Session-CAR
#### ******* （可选）配置Telnet访问控制
#### ******* （可选）使能Telnet连接的IP地址阻止功能
#### ******* （可选）配置通过Telnet登录服务器失败次数的告警门限
#### ******* （可选）配置单个IP地址通过Telnet登录服务器的最大连接数
#### ******** 检查配置结果
用户通过Telnet登录系统配置成功后，可以查看到当前用户界面连接情况、每个用户界
面连接情况、以及当前建立的所有TCP连接情况等内容。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
71
前提条件
已完成用户通过Telnet登录系统的所有配置。
操作步骤
●
使用display users [ all ]命令，查看用户界面连接情况。
●
使用display tcp status命令，查看当前建立的所有TCP连接情况。
●
使用display telnet server status命令，查看Telnet服务器的状态和配置信息。
●
Telnet协议的白名单Session-CAR功能配置成功以后，可以按照如下指导检查配置
结果：
–
基于IPv4：使用命令display cpu-defend whitelist session-car telnet
statistics slot slot-id，查看指定接口板上的Telnet协议白名单Session-CAR
的统计信息。
如果需要查看某一段时间的统计信息，可以使用命令reset cpu-defend
whitelist session-car telnet statistics slot slot-id先清除指定接口板上的
Telnet协议白名单Session-CAR的统计信息，再使用命令display cpu-defend
whitelist session-car telnet statistics slot slot-id。
–
基于IPv6：使用命令display cpu-defend whitelist-v6 session-car
telnetv6 statistics slot slot-id，查看指定接口板上的Telnet协议白名单
Session-CAR的统计信息。
如果需要查看某一段时间的统计信息，可以使用命令reset cpu-defend
whitelist-v6 session-car telnetv6 statistics slot slot-id先清除指定接口板
上的Telnet协议白名单Session-CAR的统计信息，再使用命令display cpu-
defend whitelist-v6 session-car telnetv6 statistics slot slot-id。
●
使用display vty ip-block all命令，查看所有认证失败的IP地址。
●
使用display vty ip-block list命令，查看因为认证失败而被阻止的IP地址列表。
----结束
1.5.5 配置用户通过STelnet 登录系统
STelnet基于SSH2协议，在不安全网络上提供安全的远程访问。
应用环境
网络中有大量设备需要管理与维护，用户不可能为每台设备连接用户终端，特别是终
端与需要管理的设备之间无可达路由时，用户可以使用Telnet方式从当前设备登录到网
络上另一台设备，从而实现对远程设备的管理与维护。但是Telnet缺少安全的认证方
式，而且传输过程采用TCP进行简单方式传输，存在很大的安全隐患。
STelnet是一种安全的Telnet服务，建立在SSH连接的基础之上。SSH可以利用加密和强
大的认证功能提供安全保障，保护设备不受诸如IP地址欺诈等攻击。
SSH客户端使用Stelnet方式登录设备时，如果使用的认证、加密、密钥交换算法为弱
安全算法，设备将提示用户这些算法不安全，建议用户使用更安全的算法或升级客户
端。
用户可执行命令display ssh client session查看SSH客户端使用的认证加密算法，或者
执行命令display security risk feature ssh_client查看SSH客户端存在的风险信息及
修复建议。
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
72



### 1.5.5 配置用户通过STelnet登录系统
#### ******* 配置VTY用户界面的用户级别和验证方式
#### ******* 配置VTY用户界面支持SSH协议
#### ******* 配置SSH用户和认证方式
#### ******* 使能STelnet服务器功能
#### ******* 用户通过终端STelnet到服务器
#### ******* 配置STelnet服务器参数
#### ******* （可选）配置SSH协议的白名单Session-CAR
#### ******* （可选）配置通过SSH登录服务器失败次数的告警门限
#### ******* （可选）配置单个IP地址通过SSH登录服务器的最大连接数
#### *******0 （可选）配置SSH服务器的本地端口转发服务
#### *******1 配置SSH服务器的键盘交互认证方式
#### *******2 配置SSH会话密钥重协商条件
#### *******3 检查配置结果
用户通过STelnet登录系统配置成功后，可以查看到SSH服务器的全局配置信息等内
容。
前提条件
已完成用户通过STelnet登录系统的所有配置。
操作步骤
●
使用display ssh user-information username命令，在SSH服务器端查看SSH用
户信息。
●
使用display ssh server status命令，查看SSH服务器的全局配置信息。
●
使用display ssh server session命令，在SSH服务器端查看与SSH客户端连接的
会话信息。
●
SSH协议的白名单Session-CAR功能配置成功以后，可以按照如下指导检查配置结
果：
–
基于IPv4：使用命令display cpu-defend whitelist session-car ssh
statistics slot slot-id，查看指定接口板上的SSH协议白名单Session-CAR的
统计信息。
如果需要查看某一段时间的统计信息，可以使用命令reset cpu-defend
whitelist session-car ssh statistics slot slot-id先清除指定接口板上的SSH
协议白名单Session-CAR的统计信息，再使用命令display cpu-defend
whitelist session-car ssh statistics slot slot-id。
–
基于IPv6：使用命令display cpu-defend whitelist-v6 session-car sshv6
statistics slot slot-id，查看指定接口板上的SSH协议白名单Session-CAR的
统计信息。
如果需要查看某一段时间的统计信息，可以使用命令reset cpu-defend
whitelist-v6 session-car sshv6 statistics slot slot-id先清除指定接口板上
的SSH协议白名单Session-CAR的统计信息，再使用命令display cpu-defend
whitelist-v6 session-car sshv6 statistics slot slot-id。
----结束
1.5.6 配置用户通过AUX 口登录系统
当用户终端和设备之间无可达路由时，用户可以通过设备的AUX口登录设备，实现远
程配置和管理，也可以通过AUX口进行本地维护。
******* 用户通过AUX 口登录系统
用户可以通过AUX口配置终端与设备的连接。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
100



### 1.5.6 配置用户通过AUX口登录系统
#### ******* 用户通过AUX口登录系统
#### 1.5.6.2 （可选）配置AUX用户界面
#### 1.5.6.3 检查配置结果
用户通过AUX口登录系统配置成功后，可以查看到用户界面的使用信息、物理属性和
配置、本地用户列表和在线用户等内容。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
103
前提条件
已完成用户通过AUX口登录系统的相关配置。
操作步骤
●
执行display users [ all ]命令查看用户登录信息。
●
执行display user-interface aux ui-number1命令查看AUX类型用户界面信息。
●
执行display local-user命令查看本地用户列表。
----结束
1.5.7 通过Telnet 重定向连接其他设备
当用户需要管理远程设备，但终端与远程设备之间无IP网络连接，此时可以通过Telnet
重定向服务来实现。
说明
使用Telnet协议存在安全风险，建议用户使用STelnet。
******* 使能Telnet 重定向功能
设备上使能重定向功能后，用户可以在Telnet客户端通过指定的端口号登录其他远程设
备，并对其进行管理和维护。
背景信息
Telnet重定向功能只在某些产品上支持，并且只能对AUX口配置。
请在设备上进行如下的配置。
说明
该配置过程仅在Admin-VS支持。
操作步骤
步骤1 执行命令system-view，进入系统视图。
步骤2 执行命令user-interface aux first-ui-number，进入AUX类型用户界面。
步骤3 执行命令undo shell，禁止在用户界面上启动终端服务。
步骤4 执行命令redirect，使能该用户界面的Telnet重定向功能。
说明
●
使能Telnet重定向后，指定的端口号就会被分配。AUX0的编号为33，指定的端口号则为
2033或4033。2033端口只透传数据报文，4033端口透传所有Telnet报文。
●
此时可以在Telnet客户端通过指定的端口号登录到远程需要管理的设备。
----结束
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
104



### 1.5.7 通过Telnet重定向连接其他设备
#### ******* 使能Telnet重定向功能
#### 1.5.7.2 使用Telnet重定向连接其他设备
#### 1.5.7.3 检查配置结果
Telnet重定向登录其他设备配置成功后，可以查看当前的TCP状态信息。
前提条件
已完成Telnet重定向登录其他设备的所有配置。
背景信息
●
执行display tcp status命令查看当前建立的TCP状态信息。



### 1.5.8 配置用户登录示例
配置用户通过Console口、Telnet、STelnet登录系统的示例，本示例中包括组网需求、
配置注意事项和配置思路等。
******* 配置用户通过Console 口登录系统示例
在本示例中，在PC端进行登录的设置，实现通过Console口登录系统。
组网需求
如果用户修改了设备的Console用户界面配置参数的默认值，则用户下次通过Console
口登录设备时，必须在用户终端进行相应参数的设置。
图1-24 配置通过Console 口登录组网图
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
105



#### ******* 配置用户通过Console口登录系统示例
#### ******* 配置用户通过Telnet登录系统示例
#### ******* 配置IPv6用户通过Telnet登录系统示例
#### ******* 配置用户通过STelnet登录系统示例
#### ******* 配置IPv6用户通过STelnet登录系统示例
#### ******* 配置网管设备上的SSH用户与设备通过VPN网络通信示例
## 1.6 文件系统配置
文件系统实现对存储设备中的文件、目录的管理。



### 1.6.1 文件系统概述
用户可以通过Console口、Telnet、STelnet、FTP（File Transfer Protocol）、TFTP
（Trivial File Transfer Protocol）或SFTP（Secure File Transfer Protocol）等方式进行
文件操作，实现对文件、目录的管理，包括查看、创建、重命名和删除目录，拷贝、
移动、重命名和删除文件等。
设备运行过程中所需要的文件（如：系统软件、配置文件等）保存在设备的存储设备
中，为了方便用户对存储设备进行有效的管理，设备以文件系统方式对这些文件进行
管理。
文件系统操作包括：目录操作、文件操作等。
用户可通过如下方式对目录、文件进行管理：
●
通过Console口、Telnet或STelnet方式登录设备后，对目录和文件进行管理。
通过Console口、Telnet或STelnet方式登录设备详细描述，请参见配置用户登录。
●
通过FTP方式对目录和文件进行管理。
●
通过TFTP方式对目录和文件进行管理。
●
通过SFTP方式对目录和文件进行管理。
FTP 概述
FTP协议是一种基于TCP/IP协议族的Internet标准应用协议，用于在远端服务器和本地
客户端之间传输文件。FTP采用两条TCP连接将一个文件从一个系统复制到另一个系
统，连接通常是以客户－服务器的方式建立，这两条TCP连接分别是控制连接（服务器
端为21号端口）和数据连接（服务器端为20号端口）。
●
控制连接
控制连接建立在客户端与服务器之间。
控制连接始终等待客户端和服务器之间的通信。 并且将相关命令从客户端传送给
服务器，同时将服务器的应答传送给客户端。
●
数据连接
服务器的数据连接端使用端口20。服务器执行主动打开数据连接，通常也执行主
动关闭数据连接。但是，当客户端向服务器发送流形式的文件时，则需要客户端
关闭数据连接。 
FTP中传输方式是流方式，并且文件结尾以关闭数据连接为标志，所以对每一个文
件传输或目录列表来说，都要建立一个全新的数据连接。因此，当一个文件在客
户端与服务器之间传输时，一个数据连接就建立起来了。
在网络中传输数据时，FTP支持以下两种类型的数据传输：
●
二进制文件类型：以二进制模式将程序文件（比如后缀名为.app、.bin和.btm的文
件，此类文件如果使用ASCII码模式，可能会显示一堆乱码）在数据连接中传输，
不对数据进行任何处理，不需要转换或格式化就可以传输字符，二进制模式比
ASCII码模式更快，并且可以传输所有ASCII值。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
139
●
ASCII码文件类型：以ASCII码模式传输文本格式的文件（比如后缀名为.txt、.bat
和.cfg的文件，此类文件如果使用二进制模式，可能导致文件执行出错），将回
车、换行转换为本机的回车字符。
TFTP 概述
TFTP协议是一种基于UDP的文件传输应用协议，使用UDP端口69在远端服务器和本地
主机之间传输文件。相对于FTP，TFTP没有复杂的交互存取接口和认证控制，适用于
客户端和服务器之间不需要复杂交互的环境。
TFTP支持以下传输模式：
●
二进制模式：用于传输程序文件。
●
ASCII码模式：用于传输文本文件。
说明
目前，设备实现情况是只能作为TFTP客户端，且只能使用二进制模式传输文件。
TFTP传输请求是由客户端发起的：
●
当TFTP客户端需要从服务器下载文件时，由客户端向TFTP服务器发送读请求包，
然后从服务器接收数据，并向服务器发送确认。
●
当TFTP客户端需要向服务器上传文件时，由客户端向TFTP服务器发送写请求包，
然后向服务器发送数据，并接收服务器的确认。
SFTP 概述
SFTP利用SSH协议提供的安全通道，使得远程用户可以安全地登录设备进行文件管理
和文件传送等操作，为数据传输提供了更高的安全保障。同时，由于设备支持作为客
户端的功能，用户可以从本地设备安全登录到远程设备上，进行文件的安全传输。
当SFTP服务器端或是与客户端的连接存在故障时，客户端需要及时了解故障的存在，
并主动断开连接。为了实现上述目标，客户端以SFTP方式登录服务器时，配置无数据
接收时发送Keepalive报文的间隔时间和服务器端的无应答限制次数：
●
如果在指定时间间隔内未收到数据，客户端将发送Keepalive报文至服务器端。
●
如果服务端的无应答次数超过配置的次数，客户端将主动断开连接。
文件系统管理方式的应用场景
表1-20 文件系统管理方式应用场景
文件系统管
理方式
优点
缺点
应用场景
通过Console
口、Telnet
或STelnet方
式
用户可直接登录设备，对目录、文件进行管理。
通过Console口、Telnet或STelnet方式登录设备详细描述，请参见配
置用户登录。
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
140
文件系统管
理方式
优点
缺点
应用场景
FTP
●基于TCP，具有
TCP所有特点。
●具有授权和认证
功能。
●FTP可以在两个不
同文件系统主机
之间传输文件。
●FTP的命令复杂、
繁多。
●FTP所占的内存要
比TFTP大。
●明文传输数据，甚
至是明文传输用户
名和口令，存在安
全隐患
FTP适用于存在一定
时延、丢包和抖动的
网络。
应用在设备的版本升
级、以及各种文件传
输场景中。
TFTP
●基于UDP连接。
●TFTP所占的内存
要比FTP小。
●TFTP只支持文件
传输，不支持交
互。
●TFTP不允许用户
列出目录内容或者
与服务器协商来决
定那些可得到的文
件名。
●TFTP没有授权和
认证，且是明文传
输数据，存在安全
隐患，易于黑客和
网络病毒传输。
在网络条件良好的实
验室局域网中，可以
使用TFTP进行版本的
在线加载和升级。
适用于客户机和服务
器之间不需要复杂交
互的环境
详细配置，请参见通
过TFTP访问其他设
备的文件。
SFTP
数据进行了严格加密
和完整性保护，安全
性高。
●数据传输效率低。
●用户终端需要安装
支持SFTP客户端
的第三方软件。
适用于网络安全性要
求高的场景。
 
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
141
HUAWEI NetEngine40E
配置指南
1 基础配置



### 1.6.2 文件系统管理配置注意事项
特性限制
表1-21 本特性的使用限制
特性限制
系列
涉及产品
SCP不支持和WinSCP工具对接。
NE40E
NE40E-
X16C/
NE40E-
X8C/
NE40E-
X8A/
NE40E-
X3A/
NE40E-
X16A/
NetEngine
40E-X8AK
不支持大于等于4G大小的文件操作，比如dir无法正确
显示文件大小。
NE40E
NE40E-
X16C/
NE40E-
X8C/
NE40E-
X8A/
NE40E-
X3A/
NE40E-
X16A/
NetEngine
40E-X8AK
文件名区分大小写。
NE40E
NE40E-
X16C/
NE40E-
X8C/
NE40E-
X8A/
NE40E-
X3A/
NE40E-
X16A/
NetEngine
40E-X8AK
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
142
特性限制
系列
涉及产品
文件系统中目录和文件名的总长度不超过128位，目录
可以是1-128位，文件名也可以是1-128位，创建了最
大长度的目录后，不能往目录里放文件，创建了最大长
度的文件后，不能放到子目录里面。
NE40E
NE40E-
X16C/
NE40E-
X8C/
NE40E-
X8A/
NE40E-
X3A/
NE40E-
X16A/
NetEngine
40E-X8AK
设备存储器的类型和规格请参见《硬件描述》的“技术
指标”。
NE40E
NE40E-
X16C/
NE40E-
X8C/
NE40E-
X8A/
NE40E-
X3A/
NE40E-
X16A/
NetEngine
40E-X8AK
文件系统实际可用的空间容量小于物理存储器最大规
格，实际可用空间可执行命令dir查看。
NE40E
NE40E-
X16C/
NE40E-
X8C/
NE40E-
X8A/
NE40E-
X3A/
NE40E-
X16A/
NetEngine
40E-X8AK
文件系统在Admin-VS操作，各VS之间文件系统无法做
到隔离。
NE40E
NE40E-
X16C/
NE40E-
X8C/
NE40E-
X8A/
NE40E-
X3A/
NE40E-
X16A/
NetEngine
40E-X8AK
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
143
特性限制
系列
涉及产品
当设备进行端口扩展操作后，需要重新建立ftp链接，
以保证后续文件操作正常执行。
NE40E
NE40E-
X16C/
NE40E-
X8C/
NE40E-
X8A/
NE40E-
X3A/
NE40E-
X16A/
NetEngine
40E-X8AK
 
HUAWEI NetEngine40E
配置指南
1 基础配置



### 1.6.3 通过登录系统进行文件操作
用户可以通过登录系统进行文件操作，包括管理存储设备、管理目录和管理文件。
应用环境
当设备无法正常进行信息的存取，需要对异常的存储设备进行修复或用户需要对设备
上的文件或目录进行操作管理时，可以通过直接登录系统的方式实现以上需求。
特别是对存储设备的操作需要通过此种方式。
前置任务
在通过登录系统进行文件操作之前，需要完成以下任务：
●
成功配置用户登录。



#### 1.6.3.1 管理目录
用户可以通过管理目录在逻辑上将文件分级存放。
背景信息
对目录的管理包括：改变当前目录、显示当前目录、显示目录中的文件和子目录列表
以及创建和删除目录。
操作步骤
●
执行命令cd directory，改变当前所处的目录。
●
执行命令pwd，查看当前所处的目录。
●
执行命令dir [ /all ] [ filename ]，显示目录中的文件和子目录的列表。
所有路径都支持绝对路径或相对于当前工作路径的相对路径。
该命令显示包含的文件信息详见下表：
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
144
说明
显示的文件和系统的软件版本和业务配置有关，此处仅列出常见文件。
支持使用通配符“ * ”匹配。
表1-22 文件信息描述表
文件名
文件含义
$_checkpoint
保存配置回退点信息的文件。
**.cc
软件版本文件。
$_install_hpg
存放随版补丁的目录。
$_install_mod
存放MOD（动态模块包）的目录。
$_license
用户激活过的license文件备份的目
录。
$_security_info
保存AAA用户历史数据的目录。
$_system
Linux系统自带的目录，存放系统使用
的脚本。
backup_bkp_elb.txt
背板电子标签备份文件。
backupelb.txt
备份的某个单板的电子标签的文件，
最近一次自动读某个单板的电子标签
生成该文件。
backupelable.txt
备份的某个单板的电子标签的文件，
最近一次通过命令行读取某个单板的
电子标签后生成该文件。
dbupgrade.log
DB升级过程日志文件。
device.sys
系统硬件配置文件。
lcsbox
保存当前激活的GTL license的文件
名、文件内容等的文件。
lpustat
接口板定时采集接口板芯片丢弃计
数，上送主控板后保存在该目录下。
pmdata
保存业务性能生成的数据文件的目
录。
security
保存SSL证书的目录。
logfile
日志信息文件，占用独立的存储空
间。
KPISTAT
存放KPI指标采集数据。
said
存放SAID节点进行故障检测、诊断和
恢复过程中产生的信息。
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
145
文件名
文件含义
lost+found
异常重启时，系统恢复文件管理模块
遭破坏的信息文件。
**.zip/**.cfg/**.dat
系统配置文件。具体说明请参见
save。
压缩后的日志文件也以.zip为后缀，其
中：
●log_槽位号_时间.log.zip：达到设
定大小的普通日志文件。
●diaglog_槽位号_时间.log.zip：达到
设定大小的诊断日志文件。
日志文件大小通过info-center logfile
size命令来设置。
*.pat
补丁文件。
lpustat.dat
转发引擎故障丢包采集日志文件。
转发引擎故障丢包数据会自动记录，
数据以结构化的16进制文件存储，保
存在主控板CF卡lpustat目录下，文件
名是lpustat.dat。因为lpustat.dat文件
采用16进制存储，因此无法直接打开
阅读，可以联系华为工程师将此文件
解析成lpustat.csv文档，基于时间、槽
号、模块、芯片、故障ID、故障信息
进行显示。
当设备CF卡剩余空间小于等于110M的
时候，设备不记录丢包采集的日志。
insegdroplog.log
MPLS转发丢包采集日志文件。公网标
签转发会自动记录因标签表查不中而
丢包的相关信息，该信息保存在主控
板CF卡insegdroplog目录下，文件名
是insegdroplog.log。当日志文件
insegdroplog.log大于8M时，自动压
缩为insegdroplog_时间戳.rar，并删除
insegdroplog.log。新的丢包产生时，
重新生成insegdroplog.log文件，继续
记录。insegdroplog整个目录下所有
文件最多占用10M。当日志文件查过
10M时，会删除最老日志文件，记录
最新日志信息。
 
●
执行命令mkdir directory，创建目录。
●
执行命令rename source-filename destination-filename，重新命名目录。
●
执行命令rmdir directory，删除目录。
----结束
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
146
HUAWEI NetEngine40E
配置指南
1 基础配置



#### 1.6.3.2 管理文件
如果用户需要查看、删除、重命名设备上的文件时，可以通过文件系统对文件进行相
应的操作。
背景信息
●
对文件的管理包括：显示文件的内容、拷贝文件、移动文件、重命名文件、压缩
文件、删除文件以及恢复删除的文件、彻底删除回收站中的文件、运行批处理文
件和配置文件系统提示方式。
●
当用户需要对某个文件进行操作时，可以执行cd directory命令，改变当前目录到
文件所处的目录。
操作步骤
●
执行命令more file-name，显示文件的内容。
●
执行命令copy source-filename destination-filename，拷贝文件。
●
执行命令move source-filename destination-filename，移动文件。
●
执行命令rename source-filename destination-filename，重新命名文件。
●
执行命令zip source-filename destination-filename，压缩文件或目录。
●
执行命令unzip source-filename destination-filename，解压缩文件。
说明
zip命令支持压缩文件或者目录，unzip命令仅支持解压缩文件。
●
执行命令delete [ /unreserved ] filename [ all ]，删除文件。
如果使用参数[ /unreserved ]，则删除后的文件不可恢复。
VS模式下，该命令仅在Admin VS支持。
●
执行命令undelete filename，恢复删除的文件。
说明
执行命令dir /all可以查看所有的文件信息，其中被删除到回收站的文件用“ [ ] ”括起来
表示。
对于执行删除命令行时使用命令delete中参数/unreserved后的文件不可恢复。
VS模式下，该命令仅在Admin VS支持。
●
执行命令reset recycle-bin [ /f | filename ]，彻底删除回收站中的文件。
当需要永久删除某一已丢弃的文件时，可以进行彻底删除回收站中的文件的操
作。命令关键字/f表示删除回收站中的所有文件，删除时不需要用户再逐个文件
确认是否删除。
●
执行命令tail file-name，显示指定文件的最后指定行内容信息。
●
运行批处理文件或VSL（VRP Shell Languages）脚本文件
当需要对文件一次进行多项处理时，可以进行运行批处理文件的操作。编辑好的
批处理文件要预先保存在设备的存储设备中。
如果已经建立好批处理文件，那么可以执行该文件，以实现执行固定任务的自动
化。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
147
a.
执行命令system-view，进入系统视图。
b.
执行命令execute filename [ parameter &<1-8> ]，运行批处理文件或VSL
（VRP Shell Languages）脚本文件。
须知
为防止用户信息泄露，指定的批处理文件或VSL脚本文件中，请尽量不要携带
用户敏感数据信息（如明文密码、明文密钥等）。
●
配置文件系统提示方式
当在设备上进行操作时，系统可以给予提示或警示信息（特别是对于可能导致数
据丢失或破坏的操作）。如果需要修改系统对文件操作的提醒方式时，可以进行
配置文件系统提示方式的操作。
a.
执行system-view命令，进入到系统视图。
b.
执行file prompt { alert | quiet }命令，配置文件系统提示方式。
须知
如果将文件操作的提醒方式设置为quiet，则对由于用户误操作（比如删除文
件操作）而导致数据丢失的情况不作提示，请慎用。
c.
执行命令commit，提交配置。
----结束
1.6.4 通过FTP 进行文件操作
FTP用于在远端服务器和本地客户端之间传输文件。
应用环境
随着设备的逐步稳定，应用范围在不断扩大，设备升级的范围也越来越大，设备的可
维护性要求也越来越高，特别是设备在线远程加载升级。这不仅可以丰富设备升级维
护手段，方便远程在线升级，还可以节约升级成本，减少客户等待升级的时间，在一
定程度上提高了客户的满意度。但是，实际网络上的丢包、时延、抖动影响了数据传
输。为了可靠的保证在线升级、数据传输，可利用基于TCP的FTP进行在线升级、传输
文件。
说明
此协议不安全，建议使用SFTP。
前置任务
在通过FTP进行文件操作之前，需要完成以下任务：
●
终端与设备之间三层路由可达。
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
148



### 1.6.4 通过FTP进行文件操作
#### ******* 配置FTP类型的本地用户
#### ******* （可选）指定FTP服务器端口号
#### ******* 使能FTP服务器功能
#### ******* 配置FTP服务器参数
#### ******* （可选）配置FTP协议的白名单Session-CAR
#### ******* （可选）配置FTP访问控制
#### ******* （可选）配置IP地址锁定功能
#### ******* 用户通过FTP软件访问系统
#### ******* 用户使用FTP命令进行文件操作
#### ******** 检查配置结果
通过FTP进行文件操作配置成功后，可以查看到FTP服务器的配置和状态信息、登录的
FTP用户信息等内容。
前提条件
已完成通过FTP进行文件操作的所有配置。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
157
操作步骤
●
使用display ftp-server 命令查看FTP服务器的配置和状态信息。
●
使用display ftp-users命令查看登录的FTP用户信息。
●
FTP协议的白名单Session-CAR功能配置成功以后，可以按照如下指导检查配置结
果：
–
基于IPv4：使用命令display cpu-defend whitelist session-car ftp
statistics slot slot-id，查看指定接口板上的FTP协议白名单Session-CAR的
统计信息。
如果需要查看某一段时间的统计信息，可以使用命令reset cpu-defend
whitelist session-car ftp statistics slot slot-id先清除指定接口板上的FTP
协议白名单Session-CAR的统计信息，再使用命令display cpu-defend
whitelist session-car ftp statistics slot slot-id。
–
基于IPv6：使用命令display cpu-defend whitelist-v6 session-car ftpv6
statistics slot slot-id，查看指定接口板上的FTP协议白名单Session-CAR的
统计信息。
如果需要查看某一段时间的统计信息，可以使用命令reset cpu-defend
whitelist-v6 session-car ftpv6 statistics slot slot-id先清除指定接口板上
的FTP协议白名单Session-CAR的统计信息，再使用命令display cpu-defend
whitelist-v6 session-car ftpv6 statistics slot slot-id。
----结束
1.6.5 通过SFTP 进行文件操作
SFTP使得用户可以从远端安全的登录设备进行文件管理，增加了数据传输的安全性。
应用环境
随着设备的逐步稳定，应用范围在不断扩大，设备升级的范围也越来越大，设备的可
维护性要求也越来越高，特别是设备在线远程加载升级。这不仅可以丰富设备升级维
护手段，方便远程在线升级，还可以节约升级成本，减少客户等待升级的时间，在一
定程度上提高了客户的满意度。最通用的在线升级、数据传输就是FTP。但是FTP是明
文传输，甚至是明文传输用户名和密码，存在安全隐患。
而SFTP使得用户可以从远端安全的登入设备进行文件管理，增加了数据传输的安全
性。同时，由于提供了Client功能，可以在本设备上SFTP到远程设备，进行文件的安
全传输、在线升级。
前置任务
在通过SFTP进行文件操作之前，需要完成以下任务：
●
终端与设备之间三层路由可达。
******* 配置SSH 用户并指定服务方式
通过SFTP方式访问设备时，必须要配置SSH用户、产生本地密钥对、设置用户验证方
式以及指定SSH用户的服务方式。
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
158



### 1.6.5 通过SFTP进行文件操作
#### ******* 配置SSH用户并指定服务方式
#### 1.6.5.2 使能SFTP服务器功能
#### 1.6.5.3 配置SFTP服务器参数
#### 1.6.5.4 （可选）配置SSH协议的白名单Session-CAR
#### 1.6.5.5 配置SFTP服务授权目录
#### 1.6.5.6 用户通过SFTP协议访问系统
#### 1.6.5.7 用户使用SFTP命令进行文件操作
#### 1.6.5.8 检查配置结果
通过SFTP进行文件操作配置成功后，可以查看到SSH用户信息和SSH服务器的全局配
置信息等内容。
前提条件
已完成SSH用户的所有配置。
操作步骤
●
使用display ssh user-information username命令在SSH服务器端查看SSH用户
信息。
●
使用display ssh server status命令查看SSH服务器的全局配置信息。
●
使用display ssh server session命令在SSH服务器端查看SSH客户端连接会话信
息。
●
SSH协议的白名单Session-CAR功能配置成功以后，可以按照如下指导检查配置结
果：
–
基于IPv4：使用命令display cpu-defend whitelist session-car ssh
statistics slot slot-id，查看指定接口板上的SSH协议白名单Session-CAR的
统计信息。
如果需要查看某一段时间的统计信息，可以使用命令reset cpu-defend
whitelist session-car ssh statistics slot slot-id先清除指定接口板上的SSH
协议白名单Session-CAR的统计信息，再使用命令display cpu-defend
whitelist session-car ssh statistics slot slot-id。
–
基于IPv6：使用命令display cpu-defend whitelist-v6 session-car sshv6
statistics slot slot-id，查看指定接口板上的SSH协议白名单Session-CAR的
统计信息。
如果需要查看某一段时间的统计信息，可以使用命令reset cpu-defend
whitelist-v6 session-car sshv6 statistics slot slot-id先清除指定接口板上
的SSH协议白名单Session-CAR的统计信息，再使用命令display cpu-defend
whitelist-v6 session-car sshv6 statistics slot slot-id。
----结束
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
175
1.6.6 通过HTTPS 下载文件
用户可以通过HTTPS进行版本包下载操作。
前提条件
已通过HTTPS方式登录HTTPS服务器，具体可参考1.8.12 通过HTTP登录其他设备。
背景信息
当设备作为客户端时，可以通过HTTPS方式从远端HTTPS服务器获取版本包保存到本
地，用于版本升级。
说明
仅支持HTTPS方式，不支持普通HTTP。
操作步骤
步骤1 执行命令download file-url [ save-as file-path | [ ssl-policy policy-name [ ssl-
verify peer [ verify-dns ] ] | verify-dns ] | vpn-instance vpn-name ] | source-ip
ip-address ]*，下载指定URL的版本包到设备的对应路径。
说明
只支持下载以.cc结尾的版本包。
source-ip可以支持IPv4/IPv6地址。
----结束
1.6.7 文件系统管理配置举例
文件系统管理的基本使用示例。配置示例中包括组网需求、配置注意事项、配置思路
等。
******* 通过登录系统进行目录管理示例
在本示例中，通过登录设备方式，进行查看目录、拷贝等操作。
组网需求
用户可通过Console口、Telnet或STelnet方式登录设备，对设备上的目录进行操作。
配置注意事项
●
如果用户通过Console口登录设备，必要已经准备好终端和RS-232电缆。
●
如果用户通过Telnet或STelnet方式登录设备，Telnet或STelnet服务器相关配置已
经成功配置，且服务器和终端之间三层路由可达。
Telnet或STelnet服务器相关配置，请参见配置用户登录。
说明
本示例采取通过Console口登录设备进行目录管理。
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
176



### 1.6.6 通过HTTPS下载文件
### 1.6.7 文件系统管理配置举例
文件系统管理的基本使用示例。配置示例中包括组网需求、配置注意事项、配置思路
等。



#### ******* 通过登录系统进行目录管理示例
在本示例中，通过登录设备方式，进行查看目录、拷贝等操作。
组网需求
用户可通过Console口、Telnet或STelnet方式登录设备，对设备上的目录进行操作。
配置注意事项
●
如果用户通过Console口登录设备，必要已经准备好终端和RS-232电缆。
●
如果用户通过Telnet或STelnet方式登录设备，Telnet或STelnet服务器相关配置已
经成功配置，且服务器和终端之间三层路由可达。
Telnet或STelnet服务器相关配置，请参见配置用户登录。
说明
本示例采取通过Console口登录设备进行目录管理。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
176
配置思路
采用如下的思路配置:
1.
配置用户通过Console口登录设备。
2.
查看当前目录信息。
3.
创建一个新的目录。
4.
查看该目录，可看到新的目录已经成功创建。
数据准备
为完成此配置举例，需准备如下的数据:
●
进行创建的目标目录名
操作步骤
步骤1 配置用户通过Console口登录设备。
配置用户通过Console口登录设备详细配置，请参考******* 配置用户通过Console口登
录系统示例。
步骤2 显示当前目录信息。
<HUAWEI> dir
Directory of cfcard:/
  Idx  Attr     Size(Byte)  Date        Time(LMT)  FileName
    0  -rw-          1,235  Dec 17 2009 17:10:53   vrpcfg.cfg
    1  -rw-        524,575  Jan 25 2010 10:03:33   private-data.txt
    2  drw-              -  Sep 09 2009 09:42:52   src
    3  drw-              -  Sep 09 2009 09:42:53   logfile
    4  -rw-            280  Sep 09 2009 09:42:53   $_patch_rollback_state
    5  -rw-         11,772  Nov 25 2009 16:56:55   $_patchstate_a
    6  -rw-              4  Jan 19 2010 03:09:32   snmpnotilog.txt
    7  drw-              -  Sep 09 2009 09:43:00   lam
    8  -rw-          2,584  Jan 21 2010 12:02:18   vrpcfg.cfg
    9  drw-              -  Jan 21 2010 11:09:21   logfilelogfile
180,862 KB total (305,358 KB free)
步骤3 在根目录下创建一个新的目录。
<HUAWEI> mkdir abc
Info:Create directory cfcard:/abc/......Done.
步骤4 显示当前目录信息，可以看到新的目录已经成功创建。
<HUAWEI> dir
Directory of cfcard:/
  Idx  Attr     Size(Byte)  Date        Time(LMT)  FileName
    0  -rw-          1,235  Dec 17 2009 17:10:53   vrpcfg.cfg
    1  -rw-        524,575  Jan 25 2010 10:03:33   private-data.txt
    2  drw-              -  Sep 09 2009 09:42:52   src
    3  drw-              -  Sep 09 2009 09:42:53   logfile
    4  -rw-            280  Sep 09 2009 09:42:53   $_patch_rollback_state
    5  -rw-         11,772  Nov 25 2009 16:56:55   $_patchstate_a
    6  -rw-              4  Jan 19 2010 03:09:32   snmpnotilog.txt
    7  drw-              -  Sep 09 2009 09:43:00   lam
    8  -rw-          2,584  Jan 21 2010 12:02:18   vrpcfg.cfg
    9  drw-              -  Jan 21 2010 11:09:21   logfilelogfile
    10  drw-             -  Jan 23 2010 11:10:42   abc
180,862 KB total (305,358 KB free)
----结束
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
177
HUAWEI NetEngine40E
配置指南
1 基础配置



#### 1.6.7.2 通过登录系统进行文件操作示例
在本示例中，通过登录设备方式，进行查看文件、拷贝等操作。
组网需求
用户可通过Console口、Telnet或STelnet方式登录设备，对设备上的文件进行操作。
文件在存储设备中的路径一定要正确，如果不指定目标文件名，则目标文件名默认为
源文件名，即目标文件和源文件同名。
配置注意事项
●
如果用户通过Console口登录设备，必要已经准备好终端和RS-232电缆。
●
如果用户通过Telnet或STelnet方式登录设备，Telnet或STelnet服务器相关配置已
经成功配置，且服务器和终端之间三层路由可达。
Telnet或STelnet服务器相关配置，请参见配置用户登录。
说明
本示例采取通过Console口登录设备进行文件管理。
配置思路
采用如下的思路配置:
1.
配置用户通过Console口登录设备。
2.
查看某目录下有哪些文件。
3.
拷贝文件到该目录下。
4.
查看该目录，可看到文件已经成功拷贝到指定目录。
数据准备
为完成此配置举例，需准备如下的数据:
●
源文件名和目标文件名
●
源文件路径和目标文件路径
操作步骤
步骤1 配置用户通过Console口登录设备。
配置用户通过Console口登录设备详细配置，请参考******* 配置用户通过Console口登
录系统示例。
步骤2 显示当前目录下的文件信息。
<HUAWEI> dir
Directory of cfcard:/
  Idx  Attr     Size(Byte)  Date        Time(LMT)  FileName
    0  -rw-          1,235  Dec 17 2009 17:10:53   vrpcfg.cfg
    1  -rw-        524,575  Jan 25 2010 10:03:33   private-data.txt
    2  drw-              -  Sep 09 2009 09:42:52   src
    3  drw-              -  Sep 09 2009 09:42:53   logfile
    4  -rw-            280  Sep 09 2009 09:42:53   $_patch_rollback_state
    5  -rw-         11,772  Nov 25 2009 16:56:55   $_patchstate_a
    6  -rw-              4  Jan 19 2010 03:09:32   snmpnotilog.txt
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
178
7  drw-              -  Sep 09 2009 09:43:00   lam
    8  -rw-          2,584  Jan 21 2010 12:02:18   vrpcfg.cfg
    9  drw-              -  Jan 21 2010 11:09:21   logfilelogfile
180,862 KB total (305,358 KB free)
步骤3 拷贝文件从slave#cfcard:/sample.txt到cfcard:/sample1.txt。
<HUAWEI> copy slave#cfcard:/sample.txt cfcard:/sample1.txt
Copy slave#cfcard:/sample.txt to cfcard:/sample1.txt?[Y/N]: y
.100%  complete
Info:Copied file slave#cfcard:/sample.txt to cfcard:/sample1.txt...Done.
步骤4 显示当前目录下的文件信息，可以看到文件已经被拷贝至指定目录下。
<HUAWEI> dir
Directory of cfcard:/
  Idx  Attr     Size(Byte)  Date        Time(LMT)  FileName
    0  -rw-          1,235  Dec 17 2009 17:10:53   vrpcfg.cfg
    1  -rw-        524,575  Jan 25 2010 10:03:33   private-data.txt
    2  drw-              -  Sep 09 2009 09:42:52   src
    3  drw-              -  Sep 09 2009 09:42:53   logfile
    4  -rw-            280  Sep 09 2009 09:42:53   $_patch_rollback_state
    5  -rw-         11,772  Nov 25 2009 16:56:55   $_patchstate_a
    6  -rw-              4  Jan 19 2010 03:09:32   snmpnotilog.txt
    7  drw-              -  Sep 09 2009 09:43:00   lam
    8  -rw-          2,584  Jan 21 2010 12:02:18   vrpcfg.cfg
    9  drw-              -  Jan 21 2010 11:09:21   logfilelogfile
   10  drw-          1,605  Jan 23 2010 14:30:32   sample1.txt
180,864 KB total (305,356 KB free)
----结束
******* 通过FTP 进行文件操作示例
在本示例中，通过使用正确的用户名和密码从用户终端以FTP方式登录到FTP服务器，
实现文件的上传和下载。
组网需求
随着设备的逐步稳定，应用范围在不断扩大，设备升级的范围也越来越大，设备的可
维护性要求也越来越高，特别是设备在线远程加载升级。这不仅可以丰富设备升级维
护手段，方便远程在线升级，还可以节约升级成本，减少客户等待升级的时间，在一
定程度上提高了客户的满意度。但是，实际网络上的丢包、时延、抖动影响了数据传
输。为了可靠的保证在线升级、数据传输，可利用基于TCP的FTP进行在线升级、传输
文件。
如图1-49所示，在作为FTP服务器的设备上使能FTP服务器功能后，从终端仿真程序登
录到FTP服务器，实现文件的上传和下载。
图1-49 通过FTP 进行文件操作组网图
说明
本例中interface1代表GigabitEthernet0/0/0。
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
179



#### ******* 通过FTP进行文件操作示例
#### ******* 通过SFTP进行文件操作示例
#### ******* 通过IPv6 SFTP进行文件操作示例
## 1.7 配置管理配置
为了保障用户配置的可靠性，系统支持两种配置生效模式。
背景信息
在业务日益丰富的今天，对设备的要求越来越高，包括配置业务时能够使业务整体生
效，在业务配置有问题时能够快速丢弃无效配置，并且要求配置过程对现有业务的影
响降到最低。
为了保障用户配置的可靠性，配置提交技术支持用户进行两阶段生效模式：第一阶段
只进行语法语义检查，第二阶段配置生效到业务。



### 1.7.1 配置管理概述
系统支持两种配置生效模式：立即生效模式和两阶段生效模式。缺省为两阶段生效模
式。系统还支持单板离线方式对接口进行配置。
配置生效模式
系统支持两种配置生效模式：立即生效模式和两阶段生效模式。缺省为两阶段生效模
式。
●
立即生效模式是传统的生效模式。
此模式使用命令system-view immediately进入系统视图。用户在输入命令行并
输入回车键后，系统执行语法检查，如果语法检查通过则配置立即生效。
说明
立即生效模式下，不支持配置回退功能。
●
两阶段生效模式将系统配置分为两个阶段。
此模式使用命令system-view进入系统视图。第一阶段用户输入配置命令，系统
在候选数据集执行命令语法和语义检查，对于有错误的配置语句，系统通过命令
行终端提醒用户配置错误及错误原因。用户完成系列配置命令的输入后，输入
commit命令提交配置，系统进入第二阶段，即配置的提交阶段。此时系统将候选
数据集上的配置下发到业务，如果业务生效则将候选数据集的配置合并到当前系
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
186
统的运行数据集。在配置的提交阶段，系统会进行检查，发现配置相同时会产生
提示信息。
从下表可以看出立即生效模式和两阶段生效模式的优缺点：
表1-32 立即生效模式和两阶段生效模式的优缺点
生效模式
优点
缺点
立即生效模式
配置产生的业务影响能够
立即反映在系统上。
由于配置是立即生效的，
用户在配置错误时会直接
对运行业务产生影响，且
不能将业务作为一个整体
直接丢弃，用户需要逐条
撤销配置。
两阶段生效模式
●用户要求对业务的配置
能够整体生效。
●用户可以预览候选数据
集里面的配置。
●用户在预览配置后发现
业务配置产生错误或配
置不符合用户预期时，
能够立即清除未生效的
配置。
●用户要求把配置过程对
现有业务的影响降到最
低。
需要输入commit命令配
置才可以生效。
说明
在两阶段生效模式中，如果
用户存在未提交的配置时，
在对应的视图中用*标识。如
果用户所有配置均已提交
时，在对应的视图中用~标
识。此种情况，用户视图除
外。
 
离线配置
当前系统支持离线配置。即当在接口上配置了命令后，拔出单板，此时单板配置不丢
失，并且仍然能够对接口进行配置。
此时当在单板槽位插入其他单板时，对原有配置的影响如下：
●
同一槽位插入和之前类型相同的单板
系统会自动恢复该单板上所有接口的配置。此时用户又可以看到该单板上的接
口，也能对它们进行配置。
●
同一槽位插入和之前接口类型不同的单板
系统会先删除原来单板的接口配置。即使用户不对现在的单板进行配置，就再拔
出，然后再插入原来拔出的单板，这些配置也不会再被恢复。
例如：单板A的接口类型为P，单板A上有配置信息。
a.
先把单板A拔出后，插入接口类型为E的单板C。
b.
不对单板C进行任何配置，再拔出单板C。在单板C的接口视图下通过display
this命令，不能看到原来在单板A上的配置信息了。
c.
再插入单板A或接口类型为P的其他单板。在单板A或接口类型为P的其他单板
的接口视图下通过display this命令，也不能看到原来在单板A上的配置信
息。
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
187
●
插入接口数目不同但接口类型相同的单板
–
如果插入的单板上的接口数目比之前拔出的单板上的接口数目多，则系统会
进行如下处理：
▪
与之前单板相同的接口：直接恢复之前保留的接口配置信息。
▪
其余接口：接口下只保留默认配置信息。
–
如果插入的单板上的接口数目比之前拔出的单板上的接口数目少，则系统会
进行如下处理：
▪
与之前单板相同的接口：直接恢复之前保留的接口配置信息。
▪
其余接口：全部删除接口及配置信息。
通过display current-configuration inactive或display current-configuration all
命令可以查看到设备中的离线配置信息。
HUAWEI NetEngine40E
配置指南
1 基础配置



### 1.7.2 配置文件管理配置注意事项
特性限制
表1-33 本特性的使用限制
特性限制
系列
涉及产品
系统处于配置提交、回退过程中，可能出现新启动的单
板较长时间无法注册。
NE40E
NE40E-
X16C/
NE40E-
X8C/
NE40E-
X8A/
NE40E-
X3A/
NE40E-
X16A/
NetEngine
40E-X8AK
配置差异比较命令display configuration changes执行
时，如果同一个视图中存在重复命令，则当前命令执行
失败。
NE40E
NE40E-
X16C/
NE40E-
X8C/
NE40E-
X8A/
NE40E-
X3A/
NE40E-
X16A/
NetEngine
40E-X8AK
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
188
特性限制
系列
涉及产品
网管向设备端发起全量同步过程中，不允许发生配置变
更。
NE40E
NE40E-
X16C/
NE40E-
X8C/
NE40E-
X8A/
NE40E-
X3A/
NE40E-
X16A/
NetEngine
40E-X8AK
用户执行试运行命令使设备进入试运行态后，仅当前试
运行用户可以修改配置，其他用户在试运行期间无法修
改配置。
NE40E
NE40E-
X16C/
NE40E-
X8C/
NE40E-
X8A/
NE40E-
X3A/
NE40E-
X16A/
NetEngine
40E-X8AK
多个用户同时提交配置时，可能会因为冲突提示系统
忙，需要重新提交。
NE40E
NE40E-
X16C/
NE40E-
X8C/
NE40E-
X8A/
NE40E-
X3A/
NE40E-
X16A/
NetEngine
40E-X8AK
二阶段配置模式下，单次未提交配置数量受限，不能无
限配置。
NE40E
NE40E-
X16C/
NE40E-
X8C/
NE40E-
X8A/
NE40E-
X3A/
NE40E-
X16A/
NetEngine
40E-X8AK
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
189
特性限制
系列
涉及产品
配置编辑时，如果当前设备已使用内存超过90%，本条
配置命令执行失败。
NE40E
NE40E-
X16C/
NE40E-
X8C/
NE40E-
X8A/
NE40E-
X3A/
NE40E-
X16A/
NetEngine
40E-X8AK
设备刚启动时配置数据恢复期间不能保存配置。
NE40E
NE40E-
X16C/
NE40E-
X8C/
NE40E-
X8A/
NE40E-
X3A/
NE40E-
X16A/
NetEngine
40E-X8AK
设备刚启动时配置数据恢复期间不能提交配置。
NE40E
NE40E-
X16C/
NE40E-
X8C/
NE40E-
X8A/
NE40E-
X3A/
NE40E-
X16A/
NetEngine
40E-X8AK
设备刚启动时配置数据恢复期间不能查询设备当前运行
配置。
NE40E
NE40E-
X16C/
NE40E-
X8C/
NE40E-
X8A/
NE40E-
X3A/
NE40E-
X16A/
NetEngine
40E-X8AK
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
190
HUAWEI NetEngine40E
配置指南
1 基础配置



### 1.7.3 选择配置生效模式
用户可以根据可靠性的不同要求选择立即生效和两阶段生效配置模式。
应用环境
用户在进行配置前必须先进入配置视图。进入配置视图后，系统根据用户选择的配置
模式启动相应的配置事务。如果希望配置能立即生效，可以使用立即生效模式；如果
希望配置完成后再生效，可以使用两阶段生效模式。
前置任务
在进行配置之前，需完成以下任务：
●
用户成功登录设备并进入用户视图。



#### ******* 配置立即生效模式
用户如果希望配置能立即生效，可以配置立即生效模式。
背景信息
用户在进行配置前必须先进入系统视图。进入系统视图后，系统根据用户选择的配置
模式启动相应的配置事务。在立即生效模式下，用户在输入命令行并输入回车键后，
系统执行语法检查，如果语法检查通过则配置立即生效。
操作步骤
步骤1 （可选）在用户视图下执行命令configuration exclusive，锁定配置。
当用户希望锁定配置，禁止其他用户同一时间进行配置和提交时，可通过锁定配置来
达到独占当前运行数据集的目的。如果配置被其他用户锁定，则首先需要联系该用户
解除配置锁定。
可以执行display configuration-occupied user命令，查看当前锁定配置集用户的信
息。
须知
用户锁定配置后，其他用户可以对当前运行数据库进行配置，但是不能提交配置。
如果其他用户需要对运行数据库进行配置，则首先需要由锁定配置的用户进行解锁。
步骤2 执行命令system-view immediately，进入立即生效模式。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
191
说明
为了保护某些业务，在业务进程刚开始启动时，会进行配置锁定，所以在此期间用户操作时会出
现配置失败。到启动结束时会自动解除锁定，在此期间无法进行配置，但可以执行查询操作。
如果配置失败，建议等待30秒后再进行配置，如果再次失败，说明配置被某个用户锁定。
立即生效模式下，用户的提示符如下：
<HUAWEI> system-view immediately
[HUAWEI] 
步骤3 （可选）如果用户前面已经锁定配置，则执行命令quit退回用户视图后，执行命令
undo configuration exclusive，解除配置锁定。
须知
用户锁定配置然后配置完成必须解除锁定，否则其他用户的任何配置都不会生效。
步骤4 执行命令configuration-occupied timeout timeout-value，设置自行解锁时间间
隔。
----结束
HUAWEI NetEngine40E
配置指南
1 基础配置



#### 1.7.3.2 配置两阶段生效模式
用户如果希望配置完成后再生效，可以配置两阶段生效模式。
背景信息
两阶段生效模式，可以提高配置的安全性和可靠性，将用户配置对业务的异常影响降
到最低。对于已经生效的业务，如果用户发现配置不符合预期，可以回退之前提交的
配置。
操作步骤
步骤1 （可选）在用户视图下执行命令configuration exclusive，锁定配置。
当用户希望锁定配置，禁止其他用户同一时间进行配置和提交时，可通过锁定配置来
达到独占当前运行数据集的目的。如果配置被其他用户锁定，则首先需要联系该用户
解除配置锁定。
须知
用户锁定配置后，其他用户可以对当前运行数据库进行配置，但是不能提交配置。
如果其他用户需要对运行数据库进行配置，则首先需要由锁定配置的用户进行解锁。
步骤2 执行命令system-view，进入两阶段生效模式，并且编辑配置。
说明
两阶段生效模式下，用户的提示符如下：
<HUAWEI> system-view
[~HUAWEI] 
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
192
步骤3 （可选）执行命令display configuration candidate merge，预览当前配置事务中候
选数据集里面的配置，包括用户未提交的配置和原系统的配置。
如果发现配置错误或者不需要所编辑的配置时，可以使用clear configuration
candidate命令，清除未提交的所有配置。
步骤4 （可选）执行命令commit trial [ time ] [ persist persistId ]，配置试运行功能。
试运行过程中，用户可以通过abort trial [ { session session-id | persist persistId } ]
命令取消配置试运行，系统配置回退至试运行前的配置状态。用户还可以通过命令
display configuration trial status查看系统中配置试运行的状态。
步骤5 完成配置后，执行命令commit，提交配置。
说明
为了保护某些业务，在业务进程刚开始启动时，会进行配置锁定，所以在此期间用户提交会出现
提交失败。到启动结束时会自动解除锁定，在此期间无法提交配置，但可以执行查询操作。
如果提交配置失败，建议等待30秒后再提交配置，如果再次失败，说明配置被某个用户锁定。
步骤6 （可选）如果用户前面已经锁定配置，则执行命令quit退回用户视图后，执行命令
undo configuration exclusive，解除锁定配置。
须知
用户锁定配置然后配置完成必须解除锁定，否则其他用户的任何配置都不会生效。
----结束
HUAWEI NetEngine40E
配置指南
1 基础配置



### 1.7.4 关闭二次确认功能
设备上有些命令，如果用户误操作会引发比较严重的后果。为防止用户误操作，设备
默认需要进行二次确认。
背景信息
设备上有些undo命令，如果用户误操作会关联删除相关特性的配置，导致业务中断，
造成用户网络中断。缺省情况下，为了防止用户误操作，执行这些undo命令时，需要
用户进行二次交互确认，命令范围包含：undo mpls、undo mpls te、undo mpls
rsvp、undo mpls ldp、undo mpls l2vpn、undo multicast ipv6 routing-
enable、undo multicast routing-enable、undo pim、undo igmp、undo bfd、
undo stp enable。
为防止误操作导致某些业务不可用，建议使能二次交互确认功能。
操作步骤
步骤1 执行命令system-view，进入系统视图。
步骤2 执行命令configuration prevent-misoperation disable，关闭二次确认功能。
步骤3 执行命令commit，提交配置。
----结束
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
193
HUAWEI NetEngine40E
配置指南
1 基础配置



### 1.7.5 配置回退
配置回退能够快速、方便地将配置一次性批量回退到用户指定的历史配置状态，本节
介绍系统回退到指定配置状态的操作步骤。



#### ******* 配置/清除定时配置回退点自动生成时间
在设备正常运行过程中，通过配置定时配置回退点的生成时间，可以把设备上的配置
保存在配置回退点文件中。
操作步骤
步骤1 执行命令system-view，进入系统视图。
步骤2 执行命令set save-configuration checkpoint daily time time，设置定时生成配置
回退点的生成时间。
步骤3 执行命令commit，提交配置。
步骤4 执行命令quit，返回用户视图。
步骤5 （可选）执行命令clear configuration commit label label-name，删除指定用户标
签的配置回退点。
----结束



#### 1.7.5.2 配置回退
配置回退能够快速、方便地将配置一次性批量回退到用户指定的历史配置状态，本节
介绍系统回退到指定配置状态的操作步骤。
背景信息
用户提交配置后，如果发现配置错误、配置产生故障导致此配置对网络产生了超出预
期的结果时，可以使用此功能将系统的配置批量回退至历史状态。
操作步骤
步骤1 （可选）选择相应的配置生效模式，并进行配置编辑和提交。
说明
两种生效模式的特点如下：
●
立即生效模式：用户执行命令并回车后，系统就会检测当前配置和历史配置是否产生差
异。如果有差异，系统就会提交用户的配置操作。立即生效模式不会生成配置回退点。
●
两阶段生效模式：用户执行一系列配置命令后，必须输入commit [ description
description ]命令，系统才会检查当前配置和历史配置的差异，并且生成配置回退点，这
样就可以实现对某个业务一系列命令配置的整体生效。而且，如果用户希望能够快速找到
要回退的配置点，可以使用参数description description为此次配置添加简单的描述，便于
以后跟踪查看。推荐使用此模式进行配置编辑和提交。
●
执行命令system-view immediately，进入立即生效模式的系统视图，然后执行
所需的配置命令进行配置编辑，配置会立即生效。
●
执行命令system-view，进入两阶段生效模式的系统视图。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
194
a.
（可选）执行命令undo configuration checkpoint auto-save disable，使
能自动生成配置回退点功能。
b.
进行配置编辑。
c.
执行命令commit [ description description | label label ] 提交配置使得配
置生效。
步骤2 查看配置回退点的列表以及配置差异。
1.
执行命令display configuration commit list [ verbose ] [ number-of-commits
| label ]查看所有配置回退点列表，确认配置回退点是否生成、以及各回退点的相
关信息。
如果希望查看最近一个或者多个配置回退点的相关信息，可以使用number-of-
commits参数指定个数。
2.
执行命令display configuration commit changes [ at commit-id | since
commit-id | last number-of-commits ]查看配置变更。由此可以分析如果执行回
退，可能变化的配置有哪些，从而确定是否执行配置回退，以及如果配置回退会
对系统产生造成哪些影响。
如果希望查看所有配置回退点的配置变更，在命令中不指定任何参数。
如果希望查看某个配置回退点的配置变更，可以使用at commit-id参数指定配置
回退点。
如果希望查看从某个配置回退点之前到当前状态的配置变更，可以使用since
commit-id参数指定配置回退点。
如果希望查看最近一次或者多次配置变更，可以使用last number-of-commits参
数指定次数。
步骤3 根据用户所需的历史配置状态进行配置回退。
●
如果某个配置回退点对应的历史配置能够满足用户需要，则执行如下步骤：
a.
执行命令return退回到用户视图，避免回退前用户还有数据未提交。
b.
执行命令rollback configuration { to commit-id commit-id | last
number-of-commits | to label label | to file file-name }选择配置回退点或
者回退的配置次数进行配置回退，将系统回退至所需的历史配置状态。配置
的回退包括：创建的配置会被删除、删除的配置会被重新创建、修改的配置
会被改回原值。
如果希望系统回退到某个配置回退点生成时的历史配置状态，可以使用to
commit-id commit-id参数指定配置回退点。
如果希望系统回退掉最近一个或者多个配置回退点之前的历史配置状态，可
以使用last number-of-commits参数指定个数。
说明
配置回退结束后，可以执行命令display configuration rollback result查看最近一
次的回退结果。
●
如果当前配置有错误，或者与当前配置相比，配置回退点对应的配置更能满足用
户需要，则用户可执行如下步骤，加载对应配置回退点对应的配置，然后在编辑
成需要的内容：
a.
执行命令return退回到用户视图，避免回退前用户还有数据未提交。
b.
执行命令system-view，进入系统视图。
c.
执行命令load configuration rollback changes { at commit-id at-
commit-id | to commit-id commit-id | last number-of-commits | to label
user-label }，加载指定配置回退点或者用户标签所在的系统配置。
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
195
说明
配置回退结束后，可以执行命令display configuration rollback changes load-
result查看最近一次的回退结果。
----结束
检查配置结果
●
执行命令display configuration commit list [ verbose ] [ number-of-commits
| label ]，查看配置回退点列表。
●
执行命令display configuration commit changes [ commit-id | since commit-
id | last number-of-commits ]，查看配置变更。
●
执行命令display configuration rollback result，查看最近一次配置回退的失败
信息和配置命令在回退过程中的提示信息。
●
执行命令display configuration rollback changes load-result，查看加载指定
配置回退点或者用户标签所在配置时的具体失败命令和原因。
HUAWEI NetEngine40E
配置指南
1 基础配置



### 1.7.6 管理配置文件
用户可以设置下次启动的配置文件、保存配置文件等。
应用环境
用户保存当前系统运行的配置到配置文件，在重启时可以执行配置恢复。
前置任务
在进行配置之前，需完成以下任务：
●
路由器安装完毕并加电启动正常。
●
配置了用户账号以及正确的登录验证方式。
●
终端与路由器之间有可达路由。
●
用户成功登录设备。



#### ******* 缺省配置文件
设备出厂时带有缺省配置文件default.cfg，内容如下：
●
路由器模式
对于X3A设备：
#
sysname HUAWEI
#
crypto weak-algorithm disable
#
lldp enable
#
undo telnet server enable
undo telnet ipv6 server enable
#
undo icmp name timestamp-reply send
#
ip vpn-instance __LOCAL_OAM_VPN__
 ipv4-family
 ipv6-family
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
196
#
interface GigabitEthernet0/0/0
 ip binding vpn-instance __LOCAL_OAM_VPN__
 ip address *********** *************
#
security password
 #
 rule admin
  forbidden word ******
#
undo ssh server compatible-ssh1x enable
stelnet server enable
snetconf server enable
ssh server cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr
ssh server hmac sha2_512 sha2_256
ssh server key-exchange dh_group_exchange_sha256
#
ssh server publickey rsa_sha2_256 rsa_sha2_512
#
ssh server dh-exchange min-len 3072
ssh server rsa-key min-length 3072
#
ssh client publickey rsa_sha2_256 rsa_sha2_512
#
ssh client cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr
ssh client hmac sha2_512 sha2_256
ssh client key-exchange dh_group_exchange_sha256
#
user-interface vty 0 4
 authentication-mode aaa
 idle-timeout 10 0
 protocol inbound ssh
#
mpls ldp-srbe convergence enhance
#
undo snmp-agent protocol source all-interface
undo ssh server-source all-interface
undo ssh ipv6 server-source all-interface
ssh server-source -i GigabitEthernet0/0/0
#
return
对于X8A/X16A设备：
#
sysname HUAWEI
#
crypto weak-algorithm disable
#
lldp enable
#
undo telnet server enable
undo telnet ipv6 server enable
#
undo icmp name timestamp-reply send
#
security password
 #
 rule admin
  forbidden word ******
#
undo ssh server compatible-ssh1x enable
stelnet server enable
snetconf server enable
ssh server cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr
ssh server hmac sha2_512 sha2_256
ssh server key-exchange dh_group_exchange_sha256
#
ssh server publickey rsa_sha2_256 rsa_sha2_512
#
ssh server dh-exchange min-len 3072
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
197
ssh server rsa-key min-length 3072
#
ssh client publickey rsa_sha2_256 rsa_sha2_512
#
ssh client cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr
ssh client hmac sha2_512 sha2_256
ssh client key-exchange dh_group_exchange_sha256
#
user-interface vty 0 4
 authentication-mode aaa
 idle-timeout 10 0
 protocol inbound ssh
#
mpls ldp-srbe convergence enhance
#
undo snmp-agent protocol source all-interface
undo ssh server-source all-interface
undo ssh ipv6 server-source all-interface
#
return
●
传输模式
对于X3A设备：
#
 sysname HUAWEI
#
crypto weak-algorithm disable
#
undo telnet server enable
undo telnet ipv6 server enable
#
undo icmp name timestamp-reply send
#
fan speed auto
#
 undo radius enable
#
snmp-agent trap type entity-trap
#
dot1x-template 1
#
 dhcpv6 disable
#
ip vpn-instance __LOCAL_OAM_VPN__
 ipv4-family
 ipv6-family
#
interface GigabitEthernet0/0/0
 ip binding vpn-instance __LOCAL_OAM_VPN__
 ip address *********** *************
#
security password
 #
 rule admin
  forbidden word ******
#
aaa
 authentication-scheme default0
 authentication-scheme default1
 authentication-scheme default
  authentication-mode local radius
 #
 authorization-scheme default
 #
 accounting-scheme default0
 accounting-scheme default1
 #
 domain default0
 domain default1
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
198
domain default_admin
 #
#
multicastbandwidth
#
interface GigabitEthernet0/0/0
 speed auto
 duplex auto
 undo shutdown
 ip address *********** ***********
#
interface NULL0
#
l2tp-group default-lns
#
undo ssh server compatible-ssh1x enable
stelnet server enable
snetconf server enable
ssh server cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr
ssh server hmac sha2_512 sha2_256
ssh server key-exchange dh_group_exchange_sha256
#
ssh server publickey rsa_sha2_256 rsa_sha2_512
#
ssh server dh-exchange min-len 3072
ssh server rsa-key min-length 3072
#
ssh client publickey rsa_sha2_256 rsa_sha2_512
#
ssh client cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr
ssh client hmac sha2_512 sha2_256
ssh client key-exchange dh_group_exchange_sha256
#
user-interface maximum-vty 15
user-interface con 0
user-interface aux 0
user-interface vty 0 14
 authentication-mode aaa
 protocol inbound ssh
 idle-timeout 10 0
#
multicast shaping
#
mpls
mpls te
mpls rsvp-te
mpls rsvp-te hello
mpls rsvp-te hello support-peer-gr
mpls te cspf
mpls oam based-itu
#
mpls l2vpn
#
mpls ldp
remote-peer pwe3
#
local-aaa-server
#
mpls ldp-srbe convergence enhance
#
dtls policy qx_dtls_client
#
dcn security-mode enable
#
undo snmp-agent protocol source all-interface
undo ssh server-source all-interface
undo ssh ipv6 server-source all-interface
ssh server-source -i GigabitEthernet0/0/0
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
199
#
return
对于X8A/X16A设备：
#
 sysname HUAWEI
#
crypto weak-algorithm disable
#
undo telnet server enable
undo telnet ipv6 server enable
#
undo icmp name timestamp-reply send
#
fan speed auto
#
 undo radius enable
#
snmp-agent trap type entity-trap
#
dot1x-template 1
#
 dhcpv6 disable
#
security password
 #
 rule admin
  forbidden word ******
#
aaa
 authentication-scheme default0
 authentication-scheme default1
 authentication-scheme default
  authentication-mode local radius
 #
 authorization-scheme default
 #
 accounting-scheme default0
 accounting-scheme default1
 #
 domain default0
 domain default1
 domain default_admin
 #
#
multicastbandwidth
#
interface GigabitEthernet0/0/0
 speed auto
 duplex auto
 undo shutdown
 ip address *********** ***********
#
interface NULL0
#
l2tp-group default-lns
#
undo ssh server compatible-ssh1x enable
stelnet server enable
snetconf server enable
ssh server cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr
ssh server hmac sha2_512 sha2_256
ssh server key-exchange dh_group_exchange_sha256
#
ssh server publickey rsa_sha2_256 rsa_sha2_512
#
ssh server dh-exchange min-len 3072
ssh server rsa-key min-length 3072
#
ssh client publickey rsa_sha2_256 rsa_sha2_512
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
200
#
ssh client cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr
ssh client hmac sha2_512 sha2_256
ssh client key-exchange dh_group_exchange_sha256
#
user-interface maximum-vty 15
user-interface con 0
user-interface aux 0
user-interface vty 0 14
 authentication-mode aaa
 protocol inbound ssh
 idle-timeout 10 0
#
multicast shaping
#
mpls
mpls te
mpls rsvp-te
mpls rsvp-te hello
mpls rsvp-te hello support-peer-gr
mpls te cspf
mpls oam based-itu
#
mpls l2vpn
#
mpls ldp
remote-peer pwe3
#
local-aaa-server
#
mpls ldp-srbe convergence enhance
#
dtls policy qx_dtls_client
#
dcn security-mode enable
#
undo snmp-agent protocol source all-interface
undo ssh server-source all-interface
undo ssh ipv6 server-source all-interface
ssh server-source -i GigabitEthernet0/0/0
#
return
配置文件只有管理级别的用户才可以保存和修改，是在运行过程中对配置文件作为系
统文件的保护机制（不允许删除和修改）。
HUAWEI NetEngine40E
配置指南
1 基础配置



#### ******* 空配置启动下的默认配置文件
当设备升级到当前版本并空配置启动时，需要工程师进行现场配置，设备的运维管理
很不方便。如果希望设备空配置启动后实现即插即用，可以对设备预置一个默认配置
文件，具体请参考******* 配置下次启动时加载的配置文件，默认配置文件中携带满足
即插即用的一些配置，设备下次空配置启动后就会使用预置的默认配置文件进行配置
恢复。default-custom.defcfg文件是设备自带的默认配置文件，只用于设备首次上
线，用户可以根据需要自行修改，具体内容如下：
!Router function begin
#
aaa
 local-user root password irreversible-cipher $1c$]f(3Q<j7uS$!0!)8@e`\+lj]vQx\2l&y-$M(|\n_ERFU_BF$!6X$
 local-user root service-type ssh
 local-user root user-group manage-ug
 local-user root expire 2000-01-01
 user-password password-force-change disable
#
snmp-agent protocol source all-interface
#
stelnet server enable
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
201
snetconf server enable
ssh user root
ssh user root authentication-type password
ssh user root service-type stelnet snetconf
ssh server-source all-interface
ssh ipv6 server-source all-interface
ssh server key-exchange dh_group_exchange_sha256 dh_group_exchange_sha1 dh_group14_sha1 
ecdh_sha2_nistp256 ecdh_sha2_nistp384 ecdh_sha2_nistp521
#
ssh server publickey ecc rsa rsa_sha2_256 rsa_sha2_512
#
ssh client publickey ecc rsa rsa_sha2_256 rsa_sha2_512
#
ssh client key-exchange dh_group_exchange_sha256 dh_group_exchange_sha1 dh_group14_sha1 
ecdh_sha2_nistp256 ecdh_sha2_nistp384 ecdh_sha2_nistp521
#
return
!Router function end
!Transport function begin
#
undo dcn security-mode enable
#
undo dtls policy qx_dtls_client
#
snmp-agent protocol source all-interface
#
aaa
 local-user root password irreversible-cipher $1c$]f(3Q<j7uS$!0!)8@e`\+lj]vQx\2l&y-$M(|\n_ERFU_BF$!6X$
 local-user root service-type ssh mml
 local-user root user-group manage-ug
 local-user root expire 2000-01-01
 user-password password-force-change disable
#
stelnet server enable
snetconf server enable
ssh user root
ssh user root authentication-type password
ssh user root service-type stelnet snetconf
#
ssh server-source all-interface
ssh ipv6 server-source all-interface
ssh server key-exchange dh_group_exchange_sha256 dh_group_exchange_sha1 dh_group14_sha1 
ecdh_sha2_nistp256 ecdh_sha2_nistp384 ecdh_sha2_nistp521
#
ssh server publickey ecc rsa rsa_sha2_256 rsa_sha2_512
#
ssh client publickey ecc rsa rsa_sha2_256 rsa_sha2_512
#
ssh client key-exchange dh_group_exchange_sha256 dh_group_exchange_sha1 dh_group14_sha1 
ecdh_sha2_nistp256 ecdh_sha2_nistp384 ecdh_sha2_nistp521
#
return
!Transport function end
说明
文件中的密码支持设置为用户自定义的明文类型，为了提高安全性，设置的密码建议包含多种类
型字符，包括大写字母、小写字母、数字及特殊字符。
HUAWEI NetEngine40E
配置指南
1 基础配置



#### ******* 保存配置文件
用户可以保存配置到配置文件，支持自动保存和手工保存两种方式。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
202
背景信息
为了防止设备掉电或者意外重启导致配置信息丢失，系统同时提供手动和自动保存配
置功能。
自动保存配置时，可将配置文件保存到本设备，也可以根据需要将配置文件保存到备
份服务器中。当本地数据库空间不足或损坏时，可将配置文件保存到备份服务器中，
提高设备数据库的安全性。
要保存配置文件，需要在设备上进行如下配置。
操作步骤
●
自动保存配置。
a.
执行命令system-view，进入系统视图。
b.
执行命令set save-configuration [ interval interval | cpu-limit cpu-usage
| delay delay-interval ] *，配置系统定时保存配置。
▪
如果需要指定定时保存配置时间间隔，则指定参数interval interval。
▪
为了防止自动保存影响系统性能，可以指定参数cpu-limit cpu-usage配
置自动保存时系统CPU使用率上限。在自动保存定时器触发时，检测到
系统的CPU占用率高于配置的值，系统将取消本次自动保存。
▪
若需要指定系统自动备份配置的延时时间，则选择参数delay delay-
interval，系统将在到达配置发生变更后的指定延时时间后自动保存配
置。
▪
如果同时配置参数interval interval和delay delay-interval，则哪个参数
配置的时间先到，则由哪个参数触发配置保存操作，另一个参数配置的
时间到的时候会再次检查配置，发现没有变更则不执行保存操作。
配置系统定时自动保存功能后，不管是否有手动执行过save操作，只要当前
运行配置和下次启动文件中的配置有差异，interval interval参数配置的时间
到了之后会触发保存配置到下次启动配置文件操作。
c.
（可选）执行命令set save-configuration backup-to-server server
[ ipv6 ] server-ip [ vpn-instance vpn-instance-name ] transport-type
{ tftp | { ftp | sftp } [ port port-value ] user user-name password
password } [ path folder ]，配置将当前配置文件自动保存到指定的服务
器。
说明
●
使用TFTP传输方式保存配置文件时，可执行命令tftp client-source配置路由器的
Loopback接口作为客户端源地址。
●
系统最多支持配置5个文件服务器。各服务器相互独立，互不影响。如果某个服务
器路径上传失败，则系统将向网管上报告警，并在设备上记录日志。
●
容灾备份场景下，可通过多次执行set save-configuration backup-to-server命
令配置多个文件服务器。在配置此命令前，请先配置set save-configuration命
令使能自动保存功能，并在服务器上开启传输协议服务（FTP、SFTP或TFTP）。
●
设备支持配置IP地址相同，VPN实例不同的服务器。即对于同一个IP地址的服务
器，如果该服务器配置了不同的VPN实例，则设备可以向其中任意一个VPN实例
中发送配置文件。
●
手动保存配置。
执行命令save [ configuration-file ]，保存当前配置。
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
203
配置文件必须以“.dat”、“.zip”或“.cfg”作为扩展名。
说明
在第一次保存配置文件时，如果不指定可选参数configuration-file，则路由器将提示是否将文
件名保存为“vrpcfg.zip”。“vrpcfg.zip”是系统缺省的配置文件，初始状态是空配置。
----结束
HUAWEI NetEngine40E
配置指南
1 基础配置



#### ******* 比较配置文件
用户可以比较当前的配置文件和下次启动的配置文件或者指定的配置文件是否一致。
背景信息
说明
所比较的配置文件必须以“.cfg” 或“.zip”作为扩展名。
操作步骤
步骤1 执行命令compare configuration [ configuration-file ]，比较当前的配置与下次启动
的配置文件或者指定的配置文件的内容是否一致。
当用户完成一系列操作后，可通过本命令从当前配置的首行开始，比较当前配置与下
次启动的配置文件或指定的配置文件内容是否一致，以便用户决定是否需要将当前的
配置保存，设置为下次启动的配置文件。
该命令在比较出不同之处时，将从不同处所在的行开始，分别对当前配置和下次启动
的配置文件或指定的配置文件，显示9行文件内容，如果该不同之处到文件末尾不足9
行，将显示到文件尾为止。
----结束



#### ******* 加载配置文件
在设备正常运行过程中，通过加载配置文件可以批量执行命令行，进行功能配置。
背景信息
用户需要将远端服务器或者本地的配置文件加载至当前正在运行的配置数据库中时，
可按如下步骤进行操作。
此功能只能在两阶段生效模式下生效。
操作步骤
步骤1 执行命令system-view，进入系统视图。
步骤2 根据配置文件所在位置，选择执行如下命令之一：
●
执行命令load configuration file file-name merge [ relative ]，加载本地配置
文件并下发配置。
●
执行命令load configuration server ip-address [ vpn-instance vpn-instance-
name ] transport-type { ftp | sftp } username user-name password
password-value file file-name merge [ relative ]，加载远端IPv4服务器上的配
置文件，并将配置下发至当前设备。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
204
●
执行命令load configuration server ipv6 ipv6-address [ vpn-instance vpn-
instance-name ] transport-type { ftp | sftp } username user-name password
password-value file file-name merge [ relative ]，加载远端IPv6服务器上的配
置文件，并将配置下发至当前设备。
●
执行命令load configuration server http url url-address [ vpn-instance vpn-
instance-name ] merge [ relative ]，根据URL获取远端服务器上的配置文件，
并将配置下发至当前设备。
说明
不建议手工构造配置文件。如果构造配置文件格式错误，可能导致配置恢复失败或者配置恢复发
生错误。
指定的待加载配置文件必须存在，且必须满足以下条件：
●
配置文件中只能包含配置命令、视图切换命令、#，其他类型（如display查询命令、reset/
save/ping等维护命令、quit、commit、return、升级兼容命令、一阶段命令等）命令执行
时设备会报错，并继续加载后续命令。
●
配置文件中的交互类型命令仅支持Y/N自动交互。
●
配置文件中命令的缩进要正确。系统视图下的命令需顶格，系统视图下的一级视图需顶
格，一级视图下的命令需缩进一个空格，多级视图依次缩进一格。
●
#号如果顶格，表示回退到系统视图；非顶格，则只是用来隔离命令块，但缩进需正确，
和其下方的命令块对齐。如果使用错误，可能会导致配置丢失或者命令在非预期视图执
行。
●
配置文件必须以*.zip、*.cfg、*.txt、*.dat、*.bat作为扩展名，或者不带扩展名。FTP或者
SFTP模式下，支持带服务器目录文件名。文件名不包括特殊字符“~”“?”“*”“/”
“\”“:” “"” “|”“<”“>”“[”“]”。其中，
–
*.cfg和*.txt为纯文本格式，可直接查看其内容。指定为待加载的配置文件后，替换时
系统对里面的命令逐条进行恢复。
–
*.zip是*.cfg格式文件的压缩，占用空间较小。指定为待加载的配置文件后，系统先将
文件解压成.cfg格式，然后逐条恢复。*.cfg文件名必须和*.zip文件名一致，否则会导
致配置文件加载失败。
–
*.dat是压缩文件格式，可以是二进制格式或者文本格式文件。只能执行从华为设备导
出的*.dat文件，且不能手工修改，否则会导致配置文件加载失败。
–
*.bat是批处理文件，为纯文本格式文件，可以手工修改。
步骤3 执行命令commit，提交配置。
----结束
HUAWEI NetEngine40E
配置指南
1 基础配置



#### ******* 配置下次启动时加载的配置文件
用户可以设定需要的配置文件作为系统下次启动时加载的文件。
背景信息
系统重新启动后使用指定的配置文件进行配置恢复，用户可以根据需要设定此配置文
件。
操作步骤
●
执行命令startup saved-configuration configuration-file，设定系统下次启动时
使用的配置文件。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
205
说明
不建议手工构造配置文件。如果构造配置文件格式错误，可能导致配置恢复失败或者配置
恢复发生错误。
系统下次启动时使用的配置文件必须存在，且必须满足以下条件：
●
配置文件必须以“.dat”、“.zip”或“.cfg”作为扩展名，而且系统启动配置文件必须
存放在存储设备的根目录下。
●
配置文件中只能包含配置命令、视图切换命令、#，其他类型（如display查询命令、
reset/save/ping等维护命令、quit、commit、return、升级兼容命令、一阶段命令等）
命令执行时设备会报错，并继续加载后续命令。
●
配置文件中的交互类型命令仅支持Y/N自动交互。
●
配置文件中命令的缩进要正确。系统视图下的命令需顶格，系统视图下的一级视图需顶
格，一级视图下的命令需缩进一个空格，多级视图依次缩进一格。
●
#号如果顶格，则表示退出到系统视图；非顶格，则只是用来隔离命令块，但缩进需正
确，和其下方的命令块对齐。
如果配置数据量较大，设备启动时加载配置的时间会更长，请耐心等待。
●
执行命令startup default-configuration configuration-file，配置或者更新系统
的默认配置文件。
当系统没有设定下次启动使用的配置文件时，系统启动时会使用该默认配置文
件。
说明
●
配置文件必须以*.defcfg作为扩展名，而且默认配置文件必须在设置前预先存放在存储
设备的根目录下。
●
设置的默认配置文件大小有限制，最大不能超过50KB。
●
慎重执行该命令，建议在技术支持人员指导下使用。
----结束
HUAWEI NetEngine40E
配置指南
1 基础配置



#### 1.7.6.7 清除配置文件
用户可以清除本次启动时加载的配置文件。用户可以清除当前设备上加载的配置文
件，也可以一键式清除接口下配置信息。
背景信息
在以下情况下需要清除配置文件：
●
路由器软件升级之后，配置文件与软件不匹配。
●
配置文件遭到破坏，或加载了错误的配置文件。
操作步骤
●
清除当前加载的配置文件
执行命令reset saved-configuration，清除设备当前加载的配置文件。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
206
说明
系统在清除路由器配置文件前会比较当前启动与下次启动的配置文件：
●
如果一致，执行该命令将同时清除这两个配置文件。此时可以在路由器上设定下次启动
文件，否则下次启动时配置文件为空。
●
如果不一致，执行该命令将清除下次启动的配置文件。
●
如果路由器当前启动的配置文件为空，执行该命令后，系统将提示配置文件不存在。
慎重执行该命令，建议在技术支持人员指导下使用。
●
一键式清除指定接口下配置信息
用户可根据表1-34选择操作一键式清除指定接口下配置信息。
表1-34 一键式清除指定接口下配置信息
视图
操作
说明
注意事项
系统
视图
clear configuration
interface interface-
type interface-
number
选择该操作将清除指
定接口下配置信息，
用户需要从当前接口
视图返回到系统视
图，并记住需要清除
的接口类型和编号。
无论是在系统视图下
还是在接口视图下执
行命令清除指定接口
下配置信息，使用时
请慎重。
接口
视图
clear configuration
this
选择该操作将清除接
口下配置信息，用户
可直接在当前接口视
图下执行该操作，简
化用户操作。
说明
该操作不支持在
Controller类型接口视
图下执行。
 
●
清除默认配置文件
执行命令reset default-configuration，清除设备中的默认配置文件。
说明
慎重执行该命令，建议在技术支持人员指导下使用。
●
清除指定的不注册框或者不在位单板的非激活配置信息
a.
执行命令system-view，进入系统视图。
b.
执行命令clear inactive-configuration { chassis chassis-id | slot slot-id
[ card card-number ] | all }，清除指定的非激活配置信息。
----结束
HUAWEI NetEngine40E
配置指南
1 基础配置



#### 1.7.6.8 使能自动核查数据库信息功能
背景信息
设备的数据存储分为中心数据库存储和业务进程数据库存储，各业务进程数据库需要
同步中心数据库的数据。如果出现业务数据库和中心数据库里的数据不一致的情况，
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
207
可能造成主机行为与操作者的预期不符，导致业务功能异常，因此用户需要使能自动
核查数据库信息功能，让系统定时检查业务数据库和中心数据库中数据的一致性。如
果出现数据不一致，设备立即上报告警，提示用户及时分析这类差异给业务造成的影
响，这时用户可以尝试重启单板或设备，进行修复。
说明
该配置过程仅在Admin-VS支持。
操作步骤
步骤1 执行命令system-view，进入系统视图。
步骤2 执行命令set configuration appdata auto-check enable，使能自动检查业务进程数
据库和中心数据库中的数据是否一致功能。
步骤3 执行命令commit，提交配置。
----结束
HUAWEI NetEngine40E
配置指南
1 基础配置



#### 1.7.6.9 使能自动检查主用主控和备用主控配置数据一致性功能
使能自动检查主用主控和备用主控配置数据一致性功能，让系统定时检查配置数据的
一致性，出现数据不一致，设备立即上报告警，提示用户及时分析这类差异给设备造
成的影响。
背景信息
主用主控和备用主控上均有配置数据的存储，正常情况下两者的配置数据一致。如果
出现主用主控和备用主控中配置数据不一致，此时若发生主备倒换，会导致主用主控
上原有配置丢失。因此需要使能自动检查主用主控和备用主控配置数据一致性功能，
让系统定时检查配置数据的一致性。如果出现数据不一致，设备立即上报告警，提示
用户及时分析配置数据不一致给设备造成的影响，这时用户可以尝试保存配置后重启
设备进行修复。
说明
该配置过程仅在Admin-VS支持。
操作步骤
步骤1 执行命令system-view，进入系统视图。
步骤2 执行命令configuration inconsistent slave detect enable，使能自动检查主用主控
和备用主控上的配置数据是否一致功能。
步骤3 执行命令commit，提交配置。
----结束



#### *******0 锁定系统配置数据
多用户共同管理场景下，为避免转发器和控制器配置不一致，可按照如下步骤执行锁
定配置操作。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
208
背景信息
设备允许多用户接入，对设备进行管理。这些用户可以是控制器，也可以其他类型用
户，如果设备的配置被其他类型用户修改，可能会导致控制器和转发器配置不一致。
执行以下步骤指定控制器锁定转发器系统配置，可避免转发器与控制器配置不一致。
操作步骤
步骤1 执行命令system-view，进入系统视图。
步骤2 执行命令configuration exclusive by-user-name user-name，使用指定用户名锁定
系统配置。
步骤3 执行命令commit，提交配置。
----结束
后续处理
执行命令display configuration exclusive by-user-name，查看当前系统中基于用户
名锁定或解锁系统配置的锁定信息。
HUAWEI NetEngine40E
配置指南
1 基础配置



#### *******1 使用配置模板下发配置
设备不同的视图下的配置，可能会出现大量的重复情况。此时，可以通过创建一个配
置模板，将重复的配置添加到配置模板中，在设备上应用该配置模板即可。
背景信息
设备不同的视图下的配置，可能会出现大量的重复情况。此时，可以通过创建一个配
置模板，将重复的配置添加到配置模板中，相应视图下应用该配置模板即可，使配置
看起来更简洁。
操作步骤
步骤1 执行命令system-view，进入系统视图。
步骤2 执行命令command group group-name，创建配置模板并进入命令模板视图。
步骤3 根据需要配置的业务视图实例，执行相应业务视图实例命令进入业务视图实例中。以
接口视图为例，执行命令interface interface-group-name，将Loopback接口与当前
配置模板相关联。
说明
配置模板视图下执行业务视图实例命令，可使用正则表达式。例如执行命令interface
<Loopback.>，将设备上所有的Loopback接口与当前配置模板相关联。
表1-35 正则表达式语法意义描述
特殊字符
功能
举例
\
转义字符。将下一个字符（特殊字
符或者普通字符）标记为普通字
符。
\*匹配*
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
209
特殊字符
功能
举例
^
匹配行首的位置。
^10匹配10.10.10.1，不匹配
2.2.2.2
$
匹配行尾的位置。
1$匹配10.10.10.1，不匹配
10.10.10.2
*
匹配前面的子正则表达式零次或多
次。
10*可以匹配1、10、100、1000、
……
(10)*可以匹配空、10、1010、
101010、……
+
匹配前面的子正则表达式一次或多
次。
10+可以匹配10、100、1000、
……
(10)+可以匹配10、1010、
101010、……
?
匹配前面的子正则表达式零次或一
次。
说明
当前，在华为公司数据通信设备上通
过命令行运用正则表达式输入？时，
系统显示为命令行帮助功能。华为公
司数据通信设备不支持正则表达式输
入？特殊字符。
10?可以匹配1或者10
(10)?可以匹配空或者10
.
匹配任意单个字符。
a.b匹配任何一个以a开头，以b结
尾含有三个字符的字符串
0.0可以匹配0x0、020、……
.oo.可以匹配book、look、tool、
……
()
一对圆括号内的正则表达式作为一
个子正则表达式，匹配子表达式并
获取这一匹配。
如果圆括号中内容为空，则等价于
空串。
如果模式串只有()，则可匹配任意
字符串。
如果模式串中的右括号没有匹配的
左括号，则右括号就作为普通字
符。
如果模式串中的左括号没有匹配的
右括号，则为非法模式串。
100(200)+可以匹配100200、
100200200、……
(ab)匹配abcab
()匹配任意字符串
a()b匹配12ab12
a)b匹配za)bc
a(b为非法模式串
_
匹配一个符号，包括逗号、左大括
号、右大括号、左括号、右括号和
空格，在表达式的开头或结尾时还
可作起始符、结束符（同^ ，
$）。
_65001_可以匹配20 65001 30、
20 65001、65001 30、65001、
……
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
210
特殊字符
功能
举例
x|y
匹配x或y。
100|200匹配100或者200
1(2|3)4匹配124或者134，而不匹
配1234、14、1224、1334
[xyz]
匹配正则表达式中的任意一个字
符。不可同时匹配多个字符，也不
可匹配同一个字符多次。
[123]匹配255中的2
[abc]匹配字符“a”、“b”、
“c”
[^xyz]
匹配字符串中非“x”、“y”、
“z”的字符。只要字符串中有非
“x”、“y”、“z”的字符，就
能匹配到。
[^123]匹配除123之外的任何字符
[^abc]匹配除“a”、“b”、
“c”之外的任何字符
[a-z]
匹配正则表达式指定范围内的任意
一个字符。不可同时匹配多个字
符，也不可匹配同一个字符多次。
[0-9]匹配指定范围内的任意数字
[a-z]匹配指定范围内的任意字母
[z-a]为非法模式串
[^a-d]
匹配字符串中除“a”、“b”、
“c”、“d”以外的其他字符。只
要字符串中有a～d范围外的字符，
就能匹配到。
[^0-9]匹配所有非数字字符
[^a-z]匹配除字母以外的其他任意
字符
[^z-a]为非法模式串
 
说明
●
除非特别说明，上表中涉及到的字符指的是可以打印的字符，包括字母、数字、空格及特殊
符号等。
步骤4 配置需要下发的数据。以使能IPv6功能为例，执行命令ipv6 enable，使能IPv6功能。
步骤5 （可选）执行命令display this command group candidate merge，查看配置模板
中的配置信息。
步骤6 （可选）执行命令display this command group candidate，查看配置模板中发生变
更的配置信息。
步骤7 执行命令end-group，结束并提交配置，退出至系统视图。
如果不需要使用当前的配置模板，可以执行命令abort，放弃当前正在配置的模板，并
退出配置模板视图。
步骤8 执行命令interface interface-type interface-number，创建Loopback接口。
步骤9 进入对应接口视图，执行命令apply-command-group group-name & < 1-8 >，应用
配置模板。
说明
●
指定业务视图下执行此命令，配置将下发至指定业务视图实例中。
●
同一业务视图实例中可应用多个配置模板。
●
可同时应用多个配置模板。
●
NULL接口视图不支持应用配置模板下的配置。
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
211
如果某个业务视图实例无需应用配置模板中的配置，可在配置模板视图下执行命令
undo command-string，删除指定业务视图实例。
如果业务视图实例中有多余或者错误的配置，可在配置模板视图下进入相应的业务视
图实例，执行此命令undo command-string，删除多余或者错误的配置。
说明
undo command-string命令中的参数取值只能是配置模板中关联的业务视图实例及其下面已配
置的命令，如果删除的是业务视图实例，则实例下应用此模板中的配置会一起删除。
----结束
后续处理
●
执行命令display current-configuration [ inheritance [ no-comment ] ]，查
看从模板继承的配置信息。
●
进入相应视图，执行命令display this [ inheritance [ no-comment ] ]，查看系
统当前视图从模板继承的配置信息。
●
执行命令display configuration apply-command-group fail-result，查看最近
五次应用配置模板失败的原因。
●
执行命令display command-group groupName applied，查看配置模板应用的
视图数量及视图名称。
HUAWEI NetEngine40E
配置指南
1 基础配置



#### *******2 查看待提交配置和当前运行配置的差异
用户完成一组配置，在提交之前，可以查看修改的配置和当前运行的配置之间的差
异，如果查看差异时有冲突，需先解决配置冲突后再查看。
背景信息
用户完成一组配置，在提交之前，可以查看修改的配置和当前运行的配置之间的差
异。在用户修改配置到查看配置差异期间，如果设备上正在运行的配置发生变更，就
会发生配置冲突，用户执行查看配置差异命令时，会提示错误，此时需要先解决配置
冲突，才能查看到配置差异信息。
说明
该操作仅支持在两阶段生效模式下配置。
操作步骤
步骤1 执行命令system-view，进入系统视图。
步骤2 执行命令display configuration candidate changes，查看当前用户未提交的配置和
当前运行配置之间的差异。
执行该步骤时，如果提示设备当前运行配置有变更，需执行步骤3解决配置冲突，然后
再执行该步骤查看配置差异。
步骤3 （可选）执行命令refresh configuration candidate，更新当前用户未提交的配置，
解决配置冲突。
当发生配置冲突时，执行该步骤解决配置冲突。
----结束
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
212
HUAWEI NetEngine40E
配置指南
1 基础配置



#### *******3 检查配置结果
用户可以查看启动文件列表、某个启动文件的配置信息、当前运行的配置文件。
前提条件
系统加载了下次启动文件。
操作步骤
●
执行命令display configuration configuration-file，查看指定配置文件的配置信
息。
●
执行命令display saved-configuration last，查看本次启动时使用的配置文件。
●
执行命令display saved-configuration configuration，查看系统下次启动时所
用的配置文件。
●
执行命令display startup，查看本次及下次启动相关的系统软件、配置文件名。
----结束



#### *******4 配置替换




##### *******4.1 配置文件替换
多台设备同源管理场景下，使用此功能批量替换设备上的配置，实现多台设备配置一
致。
应用环境
管理服务器管理节点设备场景下，管理服务器上存放了节点设备需要的配置，如果管
理服务器的配置发生变更，则相应节点设备的配置也需要同步修改，此时可以加载管
理服务器上的配置文件，替换当前设备上的配置，使节点设备上的配置与管理服务器
上配置保持一致。
配置相同的多台设备，如果其中一台设备的配置发生变更，为保持配置一致，其他设
备的配置需同步变更，此时可以加载配置发生变更的服务器上的配置，替换其他设备
上的配置文件，使所有设备上的配置保持一致。
此功能可替换当前设备上的整个配置文件，也可以只替换某个视图下的配置，这取决
于加载的源配置文件。如果加载的源文件中包含整个设备的配置，则替换当前设备上
的所有配置，如果加载的源文件是某个视图下保存的配置（视图下保存的配置自动携
带<replace/>标签），则替换相应视图下的配置。
此功能只能在两阶段生效模式下生效。
操作步骤
步骤1 保存配置文件。
●
如果加载本地设备配置文件替换当前正在运行的配置，则在本地设备上执行以下
命令：
–
用户视图下执行命令save [ configuration-file ]，保存整个设备的配置文件。
–
业务视图下执行命令save configuration-file，保存相应视图下的配置文件。
执行完成后退出到用户视图。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
213
说明
加载本地配置文件替换当前设备配置时，本地配置文件必须存放在设备的根目录下。save
命令默认保存配置文件至根目录。
●
如果加载远端设备配置文件替换当前设备正在运行的配置，则在指定的远端设备
上执行以下命令：
–
用户视图下执行命令save [ configuration-file ]，保存整个设备的配置文件。
–
业务视图下执行命令save configuration-file，保存相应视图下的配置文件。
执行完成后退出到用户视图。
说明
非用户视图下执行本命令，必须指定configuration-file名称，且文件的扩展名只能是*.zip或者
*.cfg。
步骤2 加载配置文件，替换当前运行配置。
1.
执行命令system-view，进入系统视图。
2.
根据待加载的源配置文件所在位置不同，选择执行以下命令：
–
执行命令load configuration file filename replace [ relative ]，加载本地
设备上的配置文件，替换当前设备上正在运行的配置。
–
执行命令load configuration { server ip-address | server ipv6 ipv6-
address } [ vpn-instance vpn-instance-name ] transport-type { ftp |
sftp } username user-name password password file filename replace
[ relative ]，加载指定的远端服务器上的配置文件，替换当前设备上正在运
行的配置。
–
执行命令load configuration server http url url-address [ vpn-instance
vpn-instance-name ] [ file filename ] replace [ relative ]，根据URL获取
远端服务器上的配置文件，替换当前设备上正在运行的配置。
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
214
说明
指定的待加载配置文件必须存在，且必须满足以下条件：
–
配置文件中只能包含配置命令、视图切换命令、#，其他类型（如display查询命令、
reset/save/ping等维护命令、quit、commit、return、升级兼容命令、一阶段命令
等）命令执行时设备会报错，并继续加载后续命令。
–
配置文件中的交互类型命令仅支持Y/N自动交互。
–
配置文件中命令的缩进要正确。系统视图下的命令需顶格，系统视图下的一级视图需
顶格，一级视图下的命令需缩进一个空格，多级视图依次缩进一格。
–
#号如果顶格，表示回退到系统视图；非顶格，则只是用来隔离命令块，但缩进需正
确，和其下方的命令块对齐。如果使用错误，可能会导致配置丢失或者命令在非预期
视图执行。
–
配置文件必须以*.zip、*.cfg、*.txt、*.dat、*.bat作为扩展名，或者不带扩展名。FTP
或者SFTP模式下，支持带服务器目录文件名。文件名不包括特殊字符“~”“?”
“*”“/”“\”“:” “"” “|”“<”“>”“[”“]”。其中，
▪
*.cfg和*.txt为纯文本格式，可直接查看其内容。指定为待加载的配置文件后，替
换时系统对里面的命令逐条进行恢复。
▪
*.zip是*.cfg格式文件的压缩，占用空间较小。指定为待加载的配置文件后，系统
先将文件解压成.cfg格式，然后逐条恢复。*.cfg文件名必须和*.zip文件名一致，
否则会导致配置文件加载失败。
▪
*.dat是压缩文件格式，可以是二进制格式或者文本格式文件。只能执行从华为
设备导出的*.dat文件，且不能手工修改，否则会导致配置文件加载失败。
▪
*.bat是批处理文件，为纯文本格式文件，可以手工修改。
3.
（可选）执行命令display configuration candidate，查看替换后的配置是否符
合预期：
–
如果符合预期，请继续执行步骤d。
–
如果不符合预期，请执行命令clear configuration candidate清除替换后的
内容，重新执行步骤b加载正确的配置文件。
4.
执行命令commit，提交配置。
----结束
检查配置结果
●
使用命令display configuration replace failed，查看配置替换失败信息。
●
使用命令display configuration replace file，查看配置替换后和目标配置的差
异内容。
HUAWEI NetEngine40E
配置指南
1 基础配置



##### *******4.2 差异配置粘贴
多台设备同源管理场景下，如果不同设备上的配置出现不一致情况，可执行此功能将
差异配置粘贴至其他设备，实现多台设备配置一致。
应用环境
配置相同的多台设备，如果其中一台设备的配置发生变更，为保持配置一致，其他设
备的配置需同步变更，此时用户可使用此功能查询出有差异的配置，将差异配置粘贴
至其他设备，使多台设备配置保持同步。
此功能只能在两阶段生效模式下执行。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
215
前置条件
用户已经对系统进行了配置操作，并且系统生成配置回退点。可执行命令display
configuration commit list [ verbose ] [ number-of-commits | label ]查看配置回
退点列表。
操作步骤
步骤1 查看配置差异。
●
查看设备上的配置文件中保存的配置和当前运行配置之间携带标签的差异信息
–
执行命令display configuration changes running file file-name with-
tag，查看当前运行配置与指定的配置文件之间的差异。
–
执行命令display configuration changes file file-name running with-
tag，查看指定配置文件与当前运行配置之间的差异。
–
执行命令display configuration changes running label label with-tag，
查看当前运行配置与指定用户标签的配置之间的差异。
–
执行命令display configuration changes label label running with-tag，
查看指定用户标签的配置与当前运行配置之间的差异。
●
查看配置回退点记录的携带标签的配置变更信息
–
执行命令display configuration commit changes [ at commit-id | since
commit-id | last number-of-commits ] with-tag，查看配置回退点记录的
携带标签的配置变更信息。
步骤2 复制差异配置，粘贴至本地设备，替换当前设备运行配置。
1.
执行命令system-view，进入系统视图。
2.
执行命令load configuration terminal，进入差异配置粘贴视图。
在配置差异粘贴视图下粘贴差异配置需要以下两个步骤：
a.
进入配置差异粘贴视图后，将查询到的差异配置直接复制粘贴至当前设备。
b.
粘贴结束符：end-diff或者abort。
▪
end-diff：结束差异配置粘贴，并退出差异配置粘贴视图。
▪
abort：取消差异配置粘贴，并退出差异配置粘贴视图。
3.
（可选）执行命令display configuration candidate，查看替换后的配置是否符
合预期：
–
如果符合预期，请继续执行步骤d。
–
如果不符合预期，请执行命令clear configuration candidate清除替换后的
内容，重新执行步骤b粘贴差异配置。
4.
执行命令commit，提交配置。
----结束
HUAWEI NetEngine40E
配置指南
1 基础配置



##### *******4.3 字符替换
设备上的某个字符串需要替换成新的字符串时，可执行该功能进行批量替换。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
216
应用环境
如果设备上的某个字符串或者某类字符串不满足用户需求，可以按如下步骤执行此功
能对这类字符进行批量替换。该功能仅替换当前视图下的字符串。
此功能只能在两阶段生效模式下生效。
操作步骤
步骤1 执行命令system-view，进入系统视图。
步骤2 执行命令replace configuration pattern src-string with target-string，将源字符串
替换成目的字符串。
说明
源字符串src-string支持正则表达式。可使用正则表达式指定一类源字符串。
表1-36 正则表达式语法意义描述
特殊字符
功能
举例
\
转义字符。将下一个字符（特殊字
符或者普通字符）标记为普通字
符。
\*匹配*
^
匹配行首的位置。
^10匹配10.10.10.1，不匹配
2.2.2.2
$
匹配行尾的位置。
1$匹配10.10.10.1，不匹配
10.10.10.2
*
匹配前面的子正则表达式零次或多
次。
10*可以匹配1、10、100、1000、
……
(10)*可以匹配空、10、1010、
101010、……
+
匹配前面的子正则表达式一次或多
次。
10+可以匹配10、100、1000、
……
(10)+可以匹配10、1010、
101010、……
?
匹配前面的子正则表达式零次或一
次。
说明
当前，在华为公司数据通信设备上通
过命令行运用正则表达式输入？时，
系统显示为命令行帮助功能。华为公
司数据通信设备不支持正则表达式输
入？特殊字符。
10?可以匹配1或者10
(10)?可以匹配空或者10
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
217
特殊字符
功能
举例
.
匹配任意单个字符。
a.b匹配任何一个以a开头，以b结
尾含有三个字符的字符串
0.0可以匹配0x0、020、……
.oo.可以匹配book、look、tool、
……
()
一对圆括号内的正则表达式作为一
个子正则表达式，匹配子表达式并
获取这一匹配。
如果圆括号中内容为空，则等价于
空串。
如果模式串只有()，则可匹配任意
字符串。
如果模式串中的右括号没有匹配的
左括号，则右括号就作为普通字
符。
如果模式串中的左括号没有匹配的
右括号，则为非法模式串。
100(200)+可以匹配100200、
100200200、……
(ab)匹配abcab
()匹配任意字符串
a()b匹配12ab12
a)b匹配za)bc
a(b为非法模式串
_
匹配一个符号，包括逗号、左大括
号、右大括号、左括号、右括号和
空格，在表达式的开头或结尾时还
可作起始符、结束符（同^ ，
$）。
_65001_可以匹配20 65001 30、
20 65001、65001 30、65001、
……
x|y
匹配x或y。
100|200匹配100或者200
1(2|3)4匹配124或者134，而不匹
配1234、14、1224、1334
[xyz]
匹配正则表达式中的任意一个字
符。不可同时匹配多个字符，也不
可匹配同一个字符多次。
[123]匹配255中的2
[abc]匹配字符“a”、“b”、
“c”
[^xyz]
匹配字符串中非“x”、“y”、
“z”的字符。只要字符串中有非
“x”、“y”、“z”的字符，就
能匹配到。
[^123]匹配除123之外的任何字符
[^abc]匹配除“a”、“b”、
“c”之外的任何字符
[a-z]
匹配正则表达式指定范围内的任意
一个字符。不可同时匹配多个字
符，也不可匹配同一个字符多次。
[0-9]匹配指定范围内的任意数字
[a-z]匹配指定范围内的任意字母
[z-a]为非法模式串
[^a-d]
匹配字符串中除“a”、“b”、
“c”、“d”以外的其他字符。只
要字符串中有a～d范围外的字符，
就能匹配到。
[^0-9]匹配所有非数字字符
[^a-z]匹配除字母以外的其他任意
字符
[^z-a]为非法模式串
 
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
218
说明
●
除非特别说明，上表中涉及到的字符指的是可以打印的字符，包括字母、数字、空格及特殊
符号等。
步骤3 （可选）执行命令display configuration candidate，查看替换后的配置是否符合预
期：
●
如果符合预期，请继续执行步骤4。
●
如果不符合预期，请执行命令clear configuration candidate清除替换后的内
容，重新执行步骤2替换字符。
步骤4 执行命令commit，提交配置。
----结束
HUAWEI NetEngine40E
配置指南
1 基础配置



### 1.7.7 配置管理配置举例
配置管理的组网举例。配置示例中包括组网需求、配置注意事项和配置思路等。



#### 1.7.7.1 立即生效模式下用户配置示例
介绍用户如何在路由器上使用立即生效模式配置业务的例子。
组网需求
如图1-52所示，用户登录路由器。
图1-52 立即生效模式下用户配置组网图
 
如果用户希望所进行的配置能立即生效，可以配置立即生效模式。
用户在输入命令行并输入回车键后，系统执行语法检查，如果语法检查通过则配置立
即生效。
配置思路
采用如下的思路提交配置：
1.
用户选择立即生效模式。
2.
用户进行配置。
数据准备
接口的IP地址
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
219
操作步骤
步骤1 用户选择立即生效模式。
<HUAWEI> system-view immediately
步骤2 用户进行配置。
#用户配置路由器GE接口1/0/4的IP地址为********。
[HUAWEI] interface GigabitEthernet 1/0/4
[HUAWEI-GigabitEthernet1/0/4] ip address ******** 24
----结束
配置文件
# 
sysname HUAWEI 
# 
interface GigabitEthernet1/0/4
 undo shutdown
 ip address ******** *************
#    
HUAWEI NetEngine40E
配置指南
1 基础配置



#### ******* 两阶段生效模式下用户锁定配置示例
介绍在多个用户在一台路由器上进行同时配置时，某个用户锁定配置后其他用户进行
配置时的举例。
组网需求
如图1-53所示，UserA和UserB同时登录路由器。在UserA锁定配置的情况下，UserB
在路由器上进行配置。
图1-53 两阶段生效模式下用户锁定配置组网图
 
当用户希望锁定配置，禁止其他用户同一时间进行配置和提交时，可通过锁定配置来
达到独占当前运行数据集的目的。此时其他用户进行配置的时候，系统会显示配置被
其他用户锁定的相关提示信息。如果其他用户需要对运行数据库进行配置，则首先需
要由锁定配置的用户解锁。
配置思路
采用如下的思路提交配置：
1.
UserA锁定配置。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
220
2.
UserB进行配置，系统提示失败，显示配置被其他用户锁定。
数据准备
接口的IP地址
操作步骤
步骤1 UserA锁定配置。
<HUAWEI> configuration exclusive
步骤2 UserB进行配置。
<HUAWEI> system-view
[~HUAWEI] interface GigabitEthernet 1/0/4
[~HUAWEI-GigabitEthernet1/0/4] ip address ******** 24
[*HUAWEI-GigabitEthernet1/0/4] commit
Error: The configuration is locked by other user. [Session ID = 407]
----结束
配置文件
# 
sysname HUAWEI 
# 
interface GigabitEthernet1/0/4
 undo shutdown
#    
HUAWEI NetEngine40E
配置指南
1 基础配置



#### ******* 两阶段生效模式下多用户对同一业务进行相同配置示例
多个用户在一台路由器上对同一业务进行相同配置时系统回应提示信息的例子。
组网需求
如图1-54所示，UserA和UserB同时登录路由器。在UserA对某业务进行配置后，
UserB在路由器上进行相同的配置。
图1-54 两阶段生效模式下多用户对同一业务进行相同配置组网图
 
UserB提交的配置与UserA配置重复，系统会提示用户有冲突的配置。
配置思路
采用如下的思路提交配置：
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
221
1.
UserA和UserB先后进行配置，且配置的业务重复。
2.
UserA提交配置。
3.
UserB提交配置。
数据准备
接口的IP地址
操作步骤
步骤1 UserA和UserB同时进行配置，配置的业务重复。
●
UserA配置路由器接口1/0/4的IP地址为********。
<HUAWEI> system-view
[~HUAWEI] interface GigabitEthernet 1/0/4
[~HUAWEI-GigabitEthernet1/0/4] ip address ******** 24
●
UserB配置路由器接口1/0/4的IP地址为********。
<HUAWEI> system-view
[~HUAWEI] interface GigabitEthernet 1/0/4
[~HUAWEI-GigabitEthernet1/0/4] ip address ******** 24
步骤2 UserA提交配置。
[*HUAWEI-GigabitEthernet1/0/4] commit
步骤3 UserB提交配置。
可以看到，在UserB提交配置时，系统提示用户重复配置。
[*HUAWEI-GigabitEthernet1/0/4] commit
ip address ******** 24
Error: The address already exists.
Commit canceled, the configuration conflicted with other user, you can modify 
the configuration and commit again.
----结束
配置文件
# 
sysname HUAWEI 
# 
interface GigabitEthernet1/0/4
 undo shutdown
 ip address ******** *************
#    
HUAWEI NetEngine40E
配置指南
1 基础配置



#### ******* 两阶段生效模式下多用户对同一业务进行不同配置示例
多个用户在一台路由器上对同一业务进行不同配置时的举例。
组网需求
如图1-55所示，UserA和UserB同时登录路由器。在UserA对某业务进行配置后，
UserB在路由器上进行同一业务不同的配置。比如，不同的用户在相同的接口下配置IP
地址，且IP地址不同。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
222
图1-55 两阶段生效模式下多用户对同一业务进行不同配置组网图
 
UserB提交与UserA相同的业务下的不同配置，系统会进行配置覆盖。
配置思路
采用如下的思路提交配置：
1.
UserA和UserB进行同一业务的不同配置。
2.
UserA提交配置。
3.
UserB提交配置。
数据准备
接口的IP地址
操作步骤
步骤1 UserA和UserB进行同一业务的不同配置。
●
UserA配置路由器接口1/0/4的IP地址为********。
<HUAWEI> system-view
[~HUAWEI] interface GigabitEthernet 1/0/4
[~HUAWEI-GigabitEthernet1/0/4] ip address ******** 24
●
UserB配置路由器接口1/0/4的IP地址为********。
<HUAWEI> system-view
[~HUAWEI] interface GigabitEthernet 1/0/4
[~HUAWEI-GigabitEthernet1/0/4] ip address ******** 24
步骤2 UserA提交配置。
[*HUAWEI-GigabitEthernet1/0/4] commit
步骤3 UserB提交配置。
[*HUAWEI-GigabitEthernet1/0/4] commit
可以看到，在UserB提交配置时，系统进行配置覆盖。
[~HUAWEI-GigabitEthernet1/0/4] display this
#
interface GigabitEthernet1/0/4
 undo shutdown
 ip address ******** *************
return 
----结束
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
223
配置文件
# 
sysname HUAWEI 
# 
interface GigabitEthernet1/0/4
 undo shutdown
 ip address ******** *************
#    
HUAWEI NetEngine40E
配置指南
1 基础配置



#### ******* 两阶段生效模式下多用户对不同业务进行配置示例
多个用户在一台路由器上对不同业务进行配置时系统回应提示信息的例子。
组网需求
如图1-56所示，UserA和UserB同时登录路由器。在UserA对某业务进行配置后，
UserB在路由器上进行不同业务的配置。
图1-56 两阶段生效模式下多用户对不同业务进行配置组网图
 
UserB提交与UserA不同业务的配置，系统会进行配置叠加。
配置思路
采用如下的思路提交配置：
1.
UserA和UserB进行不同业务的配置。
2.
UserA提交配置。
3.
UserB提交配置。
数据准备
接口的IP地址
操作步骤
步骤1 UserA和UserB进行不同业务的配置。
●
UserA配置路由器接口1/0/4的IP地址为********。
<HUAWEI> system-view
[~HUAWEI] interface GigabitEthernet 1/0/4
[~HUAWEI-GigabitEthernet1/0/4] ip address ******** 24
●
UserB使能SFTP服务。
<HUAWEI> system-view
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
224
[~HUAWEI] sftp server enable
步骤2 UserA提交配置。
[*HUAWEI-GigabitEthernet1/0/4] commit
步骤3 UserB提交配置。
[*HUAWEI-GigabitEthernet1/0/4] commit
可以看到，在UserB提交配置时，系统进行配置叠加。
<HUAWEI> display current-configuration
#
sftp server enable
#
interface GigabitEthernet1/0/4
 undo shutdown
 ip address ******** *************
----结束
配置文件
# 
sysname HUAWEI
#
interface GigabitEthernet1/0/4
 undo shutdown
 ip address ******** *************
#    
sftp server enable
#
return
HUAWEI NetEngine40E
配置指南
1 基础配置



#### ******* 配置回退示例
介绍用户在发现IP地址配置错误后，希望系统一次性恢复到未配置IP地址的状态时的操
作过程。
组网需求
如图1-57所示，用户登录路由器，对路由器的各个接口进行IP地址的配置。用户在配
置完设备相应接口的IP地址之后，发现IP地址规划错误，所以需要重新配置接口的IP地
址。如果采用传统方法，必须进入每个接口删除相应的IP地址，再重新配置。
为了解决上述问题，可以使用配置回退功能，使系统的配置统一恢复至所有接口都尚
未配置IP地址的配置状态，这样大大降低了配置恢复的复杂度。
图1-57 配置回退组网图
说明
本例中的Interface1，Interface2，Interface3，Interface4分别代表接口1/0/0、GE1/0/1、GE1/0/2
和GE1/0/3。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
225
配置注意事项
无
配置思路
采用如下思路进行配置回退：
1.
进行配置操作后，发现IP地址规划错误。
2.
查看配置回退点的信息以及最近若干次配置提交的配置变更。
3.
选择配置回退点或者回退的配置次数进行配置回退。
数据准备
为完成此配置示例，需准备如下数据：
●
设备的接口名称GE1/0/0、GE1/0/1、GE1/0/2和GE1/0/3
●
接口所需要的IP地址分别为********/30、********/30、********/30和********/30
操作步骤
步骤1 配置设备路由器的GE1/0/0、GE1/0/1、GE1/0/2和GE1/0/3的IP地址
<HUAWEI> system-view
[~HUAWEI] interface gigabitethernet 1/0/0
[~HUAWEI-GigabitEthernet1/0/0] ip address ******** 30
[*HUAWEI-GigabitEthernet1/0/0] quit
[*HUAWEI] interface gigabitethernet 1/0/1
[*HUAWEI-GigabitEthernet1/0/1] ip address ******** 30
[*HUAWEI-GigabitEthernet1/0/1] quit
[*HUAWEI] interface gigabitethernet 1/0/2
[*HUAWEI-GigabitEthernet1/0/2] ip address ******** 30
[*HUAWEI-GigabitEthernet1/0/2] quit
[*HUAWEI] interface gigabitethernet 1/0/3
[*HUAWEI-GigabitEthernet1/0/3] ip address ******** 30
[*HUAWEI-GigabitEthernet1/0/3] quit
[*HUAWEI] commit description IP address
[~HUAWEI] quit
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
226
步骤2 查看配置回退点的信息以及历史配置与当前配置的差异
# 查看配置回退点的信息。
<HUAWEI> display configuration commit list verbose
1) CommitId: 1000000006
        Label: -
        User: root
        User-Intf: VTY 1
        Type: CLI
        TimeStamp: 2012-06-29 15:55:20
        Description: IP address 
2) CommitId: 1000000005
        Label: -
        User: root
        User-Intf: VTY 0
        Type: CLI
        TimeStamp: 2012-06-29 11:04:05
        Description: 
3) CommitId: 1000000004
        Label: -
        User: root
        User-Intf: VTY 0
        Type: CLI
        TimeStamp: 2012-06-29 09:57:34
        Description: 
4) CommitId: 1000000003
        Label: -
        User: root
        User-Intf: VTY 0
        Type: CLI
        TimeStamp: 2012-06-29 09:57:21
        Description: 
5) CommitId: 1000000002
        Label: -
        User: anonymous
        User-Intf: CON 1023
        Type: CLI
        TimeStamp: 2012-06-28 16:31:48
        Description: 
6) CommitId: 1000000001
        Label: -
        User: anonymous
        User-Intf: CON 1023
        Type: CLI
        TimeStamp: 2012-06-28 16:31:48
        Description:
# 查看最近一次配置提交的配置变更。
<HUAWEI> display configuration commit changes last 1
Building configuration
  #
  interface GigabitEthernet1/0/0
+  ip address ******** ***************
  #
  interface GigabitEthernet1/0/1
+  ip address ******** ***************
  #
  interface GigabitEthernet1/0/2
+  ip address ******** ***************
  #
  interface GigabitEthernet1/0/3
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
227
+  ip address ******** ***************
  #
步骤3 配置回退
# 执行配置回退，将系统回退到最近一个配置回退点之前的历史配置状态。
说明
在本示例中，rollback configuration last 1命令等同于rollback configuration to commit-id
1000000005命令，均是表示系统回退至配置回退点6生成之前的历史配置状态。
<HUAWEI> rollback configuration last 1
# 配置回退完成后，查看配置回退点是否生成。
<HUAWEI> display configuration commit list verbose
1) CommitId: 1000000007
        Label: -
        User: root
        User-Intf: VTY 1
        Type: ROLLBACK
        TimeStamp: 2012-06-29 15:58:22
        Description: 
2) CommitId: 1000000006
        Label: -
        User: root
        User-Intf: VTY 1
        Type: CLI
        TimeStamp: 2012-06-29 15:55:20
        Description: IP address 
3) CommitId: 1000000005
        Label: -
        User: root
        User-Intf: VTY 0
        Type: CLI
        TimeStamp: 2012-06-29 11:04:05
        Description: 
4) CommitId: 1000000004
        Label: -
        User: root
        User-Intf: VTY 0
        Type: CLI
        TimeStamp: 2012-06-29 09:57:34
        Description: 
5) CommitId: 1000000003
        Label: -
        User: root
        User-Intf: VTY 0
        Type: CLI
        TimeStamp: 2012-06-29 09:57:21
        Description: 
6) CommitId: 1000000002
        Label: -
        User: anonymous
        User-Intf: CON 1023
        Type: CLI
        TimeStamp: 2012-06-28 16:31:48
        Description: 
7) CommitId: 1000000001
        Label: -
        User: anonymous
        User-Intf: CON 1023
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
228
Type: CLI
        TimeStamp: 2012-06-28 16:31:48
        Description: 
步骤4 验证配置结果
# 通过查看配置文件确定回退已经成功。
<HUAWEI> display current-configuration interface
#
interface GigabitEthernet1/0/0
 undo shutdown
#
interface GigabitEthernet1/0/1
 undo shutdown
#
interface GigabitEthernet1/0/2
 undo shutdown
#
interface GigabitEthernet1/0/3
 undo shutdown
#
----结束
配置文件
无
HUAWEI NetEngine40E
配置指南
1 基础配置



#### ******* 管理配置文件示例
本示例中，通过演示说明了保存配置文件、配置系统下次启动时使用的配置文件等基
本操作。
组网需求
如图1-58所示，用户登录设备。
图1-58 管理配置文件组网图
 
配置注意事项
无
配置思路
采用如下的思路进行配置：
1.
修改配置。
2.
保存配置文件。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
229
3.
配置系统下次启动时使用的配置文件。
4.
系统升级后，比较运行配置和下次启动配置文件是否一致，以确认升级后配置是
否丢失。
操作步骤
步骤1 修改配置。
例如，使能SFTP服务。
<HUAWEI> system-view
[~HUAWEI] sftp server enable
[*HUAWEI] commit
[~HUAWEI] quit
步骤2 保存配置到vrpcfg.cfg文件。
<HUAWEI> save vrpcfg.cfg
Warning: Are you sure to save the configuration to vrpcfg.cfg? [Y/N]: y
步骤3 配置系统下次启动时使用的配置文件。
<HUAWEI> startup saved-configuration vrpcfg.cfg
步骤4 系统升级后，比较运行配置和下次启动配置文件是否一致，以确认升级后配置是否丢
失。
<HUAWEI> compare configuration
The current configuration is the same as the next startup configuration file.
----结束
配置文件
# 
sysname HUAWEI 
# 
sftp server enable
HUAWEI NetEngine40E
配置指南
1 基础配置



## 1.8 访问其他设备配置
设备可以作为客户端访问网络上的其他设备。
背景信息
当用户需要对网络中其他设备进行管理配置或者进行文件操作时，可以在当前设备通
过Telnet、FTP、TFTP、STelnet、SCP或SFTP访问网络上的其他设备。



### 1.8.1 访问其他设备概述
分别介绍通过Telnet、FTP、TFTP、STelnet、SCP或SFTP方式访问其他设备。
如图1-59所示，用户在PC上通过终端仿真程序或Telnet程序建立与路由器的连接后，
仍可以将当前路由器作为客户端，通过Telnet、FTP、TFTP、STelnet、SCP或SFTP访
问网络上的其他设备。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
230
图1-59 访问其他设备示意图
Telnet 概述
Telnet协议在TCP/IP协议族中属于应用层协议，通过网络提供远程登录和虚拟终端功
能。NE40E提供的Telnet服务包括：
●
Telnet server：用户在PC上运行Telnet客户端程序登录到设备，对设备进行配置管
理。此时，设备提供Telnet server服务。
●
Telnet client：用户在PC上通过终端仿真程序或Telnet客户端程序建立与设备的连
接后，再执行telnet命令登录到其它设备，对其进行配置管理。如图1-60所示，
CE此时既作为Telnet server，同时也提供Telnet client服务。
图1-60 Telnet server 提供Telnet client 服务示意图
●
中断Telnet服务
图1-61 Telnet 快捷键使用示意图
在Telnet连接过程中，可以使用两种快捷键来中断连接。如图1-61所示，P1通过
Telnet登录到P2，再Telnet连接到P3，形成级联结构。P1是P2的Telnet客户端，
P2是P3的Telnet客户端，以此结构说明两种快捷键的用法。
–
<Ctrl_]>快捷键——通知服务器端断开连接
在网络畅通的情况下，键入<Ctrl_]>将通知Telnet服务器中断本次Telnet登
录，即，Telnet服务器端主动断开连接。
例如，在P3上键入<Ctrl_]>，将退回到P2的提示符。
<P3> Select Ctrl_] to return to the prompt of P2
The connection was closed by the remote host.
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
231
<P2>
此时键入<Ctrl_]>，将退回到P1的提示符。
<P2> Ctrl_]
The connection was closed by the remote host.
<P1>
说明
如果由于某些原因网络连接断开，快捷键的指令无法到达Telnet服务器端，输入无
效。
–
<Ctrl_K>快捷键——客户端主动断开连接
当服务器端故障且客户端无法感知时，客户端输入任何指令服务器均无响
应，这种情况下键入<Ctrl_K>快捷键，则Telnet客户端主动中断并退出Telnet
连接。
例如，在P3上键入<Ctrl_K>，将直接中断并退出Telnet连接。
<P3> Ctrl_K
<P1>
须知
当远端用户登录数达到VTY类型用户界面的最大个数时，系统会提示所有的
用户接口都在使用，不允许Telnet。
FTP 概述
FTP协议是一种基于TCP/IP协议族的Internet标准应用协议，用于在远端服务器和本地
客户端之间传输文件。FTP采用两条TCP连接将一个文件从一个系统复制到另一个系
统，连接通常是以客户－服务器的方式建立，这两条TCP连接分别是控制连接（服务器
端为21号端口）和数据连接（服务器端为20号端口）。
●
控制连接：将命令从客户端传送到服务器端，并传回服务器的应答，IP对控制连
接的服务特点是最大限度地减少延迟。
●
数据连接：在客户和服务器之间传输数据，因此IP对数据连接的服务特点是最大
限度地提高吞吐量。
FTP有两种文件传输模式：
●
二进制模式，用于传输程序文件（比如后缀名为.app、.bin和.btm的文件）。
●
ASCII码模式，用于传输文本格式的文件（比如后缀名为.txt、.bat和.cfg的文
件）。
设备提供的FTP功能包括：
●
设备作为FTP客户端：用户在PC上通过终端仿真程序或Telnet程序连接到设备，执
行ftp命令建立设备与远程FTP服务器的连接并访问远程主机上的文件，对远程主
机上的文件进行操作。
●
设备作为FTP服务器：用户运行FTP客户端程序，登录设备并进行文件操作。
用户登录前，网络管理员需要事先配置好FTP服务器的IP地址。
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
232
TFTP 概述
TFTP协议是一种基于UDP的文件传输应用协议，使用UDP端口69在远端服务器和本地
主机之间传输文件。相对于FTP，TFTP没有复杂的交互存取接口和认证控制，适用于
客户端和服务器之间不需要复杂交互的环境。
TFTP文件传输模式与FTP一样，支持二进制和ASCII码两种模式。
说明
●
目前，HUAWEI NetEngine40E支持的TFTP只能使用二进制模式传输文件。
●
目前，HUAWEI NetEngine40E只能作为TFTP客户端，不支持作为TFTP服务器。
TFTP传输请求是由客户端发起的：
●
当TFTP客户端需要从服务器下载文件时，由客户端向TFTP服务器发送读请求包，
然后从服务器接收数据，并向服务器发送确认。
●
当TFTP客户端需要向服务器上传文件时，由客户端向TFTP服务器发送写请求包，
然后向服务器发送数据，并接收服务器的确认。
STelnet 概述
STelnet基于SSH2.0协议，客户端和服务器之间经过协商，建立安全连接，客户端可以
像操作Telnet一样登录服务器。
●
设备支持STelnet客户端、STelnet服务器功能
为了方便用户的使用，设备不仅提供STelnet服务器功能，同时也可以作为STelnet
客户端访问其他STelnet服务器。
●
支持使能/去使能STelnet服务器功能
在不需要STelnet服务器情况下可以将其去使能，该功能在全局模式下配置。
SCP 概述
SCP是基于SSH2.0的安全协议。合法用户通过客户端登录时，输入正确的用户名以及
对应的密码和私钥。通过服务器的验证后，用户可以实现对网络文件的远程传输管
理，而系统会对用户的数据采用协商出来的会话密钥对数据加密。
攻击者没有正确的私钥和密码，无法通过服务器的认证。并且攻击者无法获得其他用
户和服务器之间的会话密钥，因此后续服务器和指定客户端的通讯报文只有指定客户
端和服务器才能解密。即使攻击者窃听到通讯报文，也不能解密，实现了网络数据传
输的安全性。
●
支持SCP客户端、服务器功能
为了方便用户的使用，设备既支持SCP客户端功能，同时也支持SCP服务器功能。
即设备既可以作为SCP服务器也可以作为SCP客户端接入SCP服务器。
●
支持使能/去使能SCP服务器功能（默认关闭）。
在不需要SCP服务器情况下，可以将其去使能。该功能在全局模式下配置。
●
支持客户端与服务器之间通过透明文件系统进行的运转。即对于所有的文件操作
来说，一个标准的文件系统可以用来访问远端单板上的文件。
●
支持文件目录中多文件的递归传输。
例如，文件目录directory下包含多个文件以及子目录，SCP可实现将整个directory
目录下的文件进行传输，并保持原有的文件目录格式。
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
233
SFTP 概述
SFTP利用SSH协议提供的安全通道，使得远程用户可以安全地登录设备进行文件管理
和文件传送等操作，为数据传输提供了更高的安全保障。同时，由于设备支持作为客
户端的功能，用户可以从本地设备安全登录到远程设备上，进行文件的安全传输。
当SFTP服务器端或是与客户端的连接存在故障时，客户端需要及时了解故障的存在，
并主动断开连接。为了实现上述目标，客户端以SFTP方式登录服务器时，配置无数据
接收时发送Keepalive报文的间隔时间和服务器端的无应答限制次数：
●
如果在指定时间间隔内未收到数据，客户端将发送Keepalive报文至服务器端。
●
如果服务端的无应答次数超过配置的次数，客户端将主动断开连接。
HUAWEI NetEngine40E
配置指南
1 基础配置



### 1.8.2 访问其他设备配置注意事项
特性限制
表1-37 本特性的使用限制
特性限制
系列
涉及产品
SSL加载证书文件（身份证书、CA、吊销列表）存在文
件大小限制，文件大小不能超过（包含）50K。
NE40E
NE40E-
X16C/
NE40E-
X8C/
NE40E-
X8A/
NE40E-
X3A/
NE40E-
X16A/
NetEngine
40E-X8AK
配置使用SSL证书时，单个SSL策略最多加载1本证书、
4本信任证书、2个证书吊销列表；
配置使用PKI域下证书时，单个SSL策略最多使用1本证
书、64本信任证书、64个证书吊销列表。
NE40E
NE40E-
X16C/
NE40E-
X8C/
NE40E-
X8A/
NE40E-
X3A/
NE40E-
X16A/
NetEngine
40E-X8AK
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
234
特性限制
系列
涉及产品
出于安全性考虑，不建议使用该特性提供的弱安全算法
或弱安全协议。
如果需要使用弱安全算法，需执行undo crypto weak-
algorithm disable命令使能弱安全算法功能后才能使
用。
NE40E
NE40E-
X16C/
NE40E-
X8C/
NE40E-
X8A/
NE40E-
X3A/
NE40E-
X16A/
NetEngine
40E-X8AK
创建SSL策略，DH模数默认值为3072，可配置2048、
3072、4096；签名算法默认开启ed25519，ed448，
rsa-pss-pss-sha256，rsa-pss-pss-sha384，rsa-pss-
pss-sha512，rsa-pss-rsae-sha256，rsa-pss-rsae-
sha384，rsa-pss-rsae-sha512，可单独配置，增加安
全性。
创建SSL策略后，如果是签名算法不匹配或者DH模数长
度过长导致的SSL握手失败，可以通过diffie-hellman
modulus命令调整DH模数长度；通过signature
algorithm-list命令调整签名算法。
NE40E
NE40E-
X16C/
NE40E-
X8C/
NE40E-
X8A/
NE40E-
X3A/
NE40E-
X16A/
NetEngine
40E-X8AK
HTTP请求报文的最大长度是10M - 1字节。
NE40E
NE40E-
X16C/
NE40E-
X8C/
NE40E-
X8A/
NE40E-
X3A/
NE40E-
X16A/
NetEngine
40E-X8AK
HTTP请求行的最大长度为8192字节。
HTTP请求行（HTTP request line）的最大长度为8192
bytes;
NE40E
NE40E-
X16C/
NE40E-
X8C/
NE40E-
X8A/
NE40E-
X3A/
NE40E-
X16A/
NetEngine
40E-X8AK
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
235
特性限制
系列
涉及产品
HTTP请求报文头总大小为4096字节。
NE40E
NE40E-
X16C/
NE40E-
X8C/
NE40E-
X8A/
NE40E-
X3A/
NE40E-
X16A/
NetEngine
40E-X8AK
 
1.8.3 通过Telnet 登录其他设备
Telnet是客户-服务器应用程序，他使用户能够登录到远程设备上，从而使用户能够对
远程设备进行管理与维护。
应用环境
网络中有大量设备需要管理与维护，用户不可能为每台设备连接用户终端，特别是终
端与需要管理的设备之间无可达路由时，用户可以使用Telnet方式从当前设备登录到网
络上另一台设备，从而实现对远程设备的管理与维护。
如图1-62所示，用户可以在PC上通过Telnet登录到Telnet client，但是由于PC与Telnet
server之间无可达路由，用户无法远程管理Telnet server，此时用户可以在Telnet
client上通过Telnet方式登录到Telnet server，实现对Telnet server的远程管理。
图1-62 当前设备访问其他设备示意图
前置任务
在通过Telnet登录其他设备之前，需要完成以下任务：
●
成功配置用户通过Telnet登录系统。
●
Telnet客户端与Telnet服务器之间有可达路由。
1.8.3.1 （可选）配置Telnet 客户端源地址
用户可以配置Telnet客户端的源地址信息，从指定的客户端源地址和路由建立Telnet连
接，保证安全性。
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
236



### 1.8.3 通过Telnet登录其他设备
#### 1.8.3.1 （可选）配置Telnet客户端源地址
#### 1.8.3.2 使用Telnet命令登录其他设备
#### 1.8.3.3 检查配置结果
用户通过Telnet方式从当前路由器成功登录另一台路由器后，可以查看到当前建立的
TCP连接情况等信息。
前提条件
已完成通过Telnet登录其他设备的所有配置。
操作步骤
●
使用display tcp status命令查看TCP连接状态。
----结束
1.8.4 通过STelnet 登录其他设备
STelnet是一种安全的Telnet服务，用户可以通过STelnet方式从当前设备登录到另一台
设备，对其进行远程管理。
应用环境
网络中有大量设备需要管理与维护，用户不可能为每台设备连接用户终端，特别是终
端与需要管理的设备之间无可达路由时，用户可以使用Telnet方式从当前设备登录到网
络上另一台设备，从而实现对远程设备的管理与维护。但是Telnet缺少安全的认证方
式，而且传输过程采用TCP进行简单方式传输，存在很大的安全隐患。
而STelnet是一种安全的Telnet服务，建立在SSH连接的基础之上。SSH可以利用加密和
强大的认证功能提供安全保障，保护设备不受诸如IP地址欺诈等攻击。如图1-63所
示，设备支持SSH功能，用户可以通过SSH方式登录到远端设备上，对设备进行远程管
理和维护。此时，当前的设备是客户端，待登录的设备是SSH服务器。
图1-63 通过STelnet 登录其他设备示意图
前置任务
在通过STelnet登录其他设备之前，需要完成以下任务：
●
成功配置用户通过STelnet登录系统。
******* 配置用户首次登录其他设备（使能SSH 客户端首次认证功能方式）
使能SSH客户端首次认证功能后，当STelnet客户端第一次登录SSH服务器时，不对SSH
服务器的RSA、DSA、ECC、SM2公钥进行有效性检查。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
238



### 1.8.4 通过STelnet登录其他设备
#### ******* 配置用户首次登录其他设备（使能SSH客户端首次认证功能方式）
#### ******* 配置用户首次登录其他设备（SSH客户端为SSH服务器分配公钥）
#### 1.8.4.3 （可选）配置SSH客户端支持Keepalive特性
#### 1.8.4.4 使用STelnet命令登录其他设备
#### 1.8.4.5 检查配置结果
通过STelnet登录其他设备功能配置成功后，可以查看到SSH客户端所有SSH服务器与
RSA或ECC公钥之间的对应关系、SSH服务器的全局配置信息以及SSH服务器与客户端
连接的会话信息。
前提条件
已完成通过STelnet登录其他设备功能的所有配置。
操作步骤
●
使用display ssh server-info命令在SSH客户端查看所有SSH服务器与RSA公钥之
间的对应关系。
----结束
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
242
1.8.5 通过TFTP 访问其他设备的文件
TFTP也是用于在远端服务器和本地主机之间传输文件，相对于FTP，TFTP没有复杂的
交互存取接口和认证控制，适用于客户端和服务器之间不需要复杂交互的环境。
应用环境
FTP是TCP/IP协议族中最常用的文件传送协议，但是终端和服务器之间交互复杂，对于
没有先进操作系统的终端而言实现文件传输相当困难。因此，TFTP应运而生，它提供
不复杂、开销不大的服务，是专为终端和服务器间不需要复杂交互而设计。但是TFTP
只限于简单文件传送操作，不提供存取授权。
说明
目前，HUAWEI NetEngine40E只能作为TFTP客户端，不支持作为TFTP服务器。
前置任务
在通过TFTP访问其他设备的文件之前，需要完成以下任务：
●
成功配置用户登录。
******* （可选）配置TFTP 客户端源地址
用户可以配置TFTP客户端的源地址信息，从指定的客户端源地址建立TFTP连接，保证
安全性。
背景信息
用户可以在设备上指定某一接口，为此接口配置IP地址，然后使用该IP地址作为TFTP
连接的源IP地址，从而达到进行安全校验的目的。
请在作为TFTP客户端的路由器上进行如下的配置。
操作步骤
步骤1 执行命令system-view，进入系统视图。
步骤2 执行命令tftp client-source { -a ip-address | -i { interface-type interface-number |
interface-name } }或tftp ipv6 client-source -a ipv6-address [ -vpn-instance ipv6-
vpn-instance-name ]，配置TFTP客户端的源地址信息。
说明
参数interface-type指定的接口类型必须是Loopback接口。
配置了TFTP客户端源地址信息后，在服务器端显示的TFTP客户端的源地址信息与该步骤中的配
置一致。
步骤3 执行命令commit，提交配置。
----结束
******* 配置TFTP 访问限制
通过ACL规则配置客户端登录TFTP服务器的访问限制，实现允许当前设备以TFTP方式
可以访问哪些TFTP服务器。
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
243



### 1.8.5 通过TFTP访问其他设备的文件
#### ******* （可选）配置TFTP客户端源地址
#### ******* 配置TFTP访问限制
#### ******* 使用TFTP命令下载其他设备的文件
#### ******* 使用TFTP命令向其他设备上传文件
#### ******* 检查配置结果
通过TFTP访问其他设备的文件配置成功后，可以查看客户端的源地址和配置的访问控
制列表的规则。
前提条件
已完成通过TFTP访问其他设备的文件的所有配置。
操作步骤
●
使用display tftp-client命令查看设备作为TFTP客户端时的源地址。
●
使用display acl {acl-number | all }命令查看TFTP客户端配置的访问控制列表的
规则。
----结束
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
245
1.8.6 通过FTP 访问其他设备的文件
配置设备作为FTP客户端，登录网络上的FTP服务器，实现文件的上传和下载等操作。
应用环境
当用户需要与远程FTP服务器进行文件传输、目录管理等操作时，可以配置当前设备为
FTP客户端，通过FTP方式访问远程FTP服务器，以实现远程管理和维护。
前置任务
在通过FTP访问其他设备的文件之前，需要完成以下任务：
●
成功配置FTP服务器。详细配置如下：
a.
配置FTP类型的本地用户
b.
（可选）指定FTP服务器端口号
c.
使能FTP服务器功能
d.
（可选）配置FTP服务器参数
e.
（可选）配置FTP访问控制
******* （可选）配置FTP 客户端源地址
用户可以配置FTP客户端的源地址信息，从指定的客户端源地址建立FTP连接，保证安
全性。
背景信息
用户可以在路由器上指定某一接口，为此接口配置IP地址，然后使用该IP地址作为FTP
连接的源IP地址，从而达到进行安全校验的目的。
请在作为FTP客户端的路由器上进行如下的配置。
操作步骤
步骤1 执行命令system-view，进入系统视图。
步骤2 执行命令ftp client-source { -a ip-address | -i { interface-type interface-number |
interface-name } }或ftp ipv6 client-source -a ipv6-address [ -vpn-instance ipv6-
vpn-instance-name ]，配置FTP客户端的源地址。
配置了FTP客户端源地址信息后，在FTP服务器端执行display ftp-users命令时，显示
的FTP客户端的源地址信息与该步骤中配置的一致。
步骤3 执行命令commit，提交配置。
----结束
******* 使用FTP 命令连接其他设备
用户可以使用FTP命令从作为FTP客户端的设备登录到其他设备。
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
246



### 1.8.6 通过FTP访问其他设备的文件
#### ******* （可选）配置FTP客户端源地址
#### ******* 使用FTP命令连接其他设备
#### ******* 通过FTP文件操作命令进行文件操作
#### ******* （可选）更改登录用户
更改登录用户主要用于不同权限用户之间的切换。
背景信息
当设备作为FTP客户端，与FTP服务器连接建立成功后，可以更改当前登录的用户，以
实现不同权限用户之间的切换。不同用户成功切换后，不会影响当前的FTP连接，即
FTP控制连接、数据连接及连接状态都不会变。
更改登录用户时，输入的用户名/密码错误，则会断开当前连接，用户必须重新登录才
能继续访问设备。
说明
HUAWEI NetEngine40E可以在不退出FTP客户端视图的情况下，以其他的用户名登录到FTP服务
器。所建立的FTP连接，与执行ftp命令建立的FTP连接完全相同。
操作步骤
步骤1 根据服务器端IP地址类型不同，进行如下操作：
●
执行命令ftp [ [ -a source-ip-address | -i { interface-type interface-number |
interface-name } ] host-ip [ port-number ] [ vpn-instance vpn-instance-
name ] | public-net ]，设备使用IPv4地址与FTP服务器建立连接，进入FTP客户
端视图。
●
执行命令ftp ipv6 [ -a source-ip6 ] host-ipv6-address [ [ vpn-instance ipv6-
vpn-instance-name ] | public-net ] [ -oi { interface-type interface-number |
interface-name } ] [ port-number ]，设备使用IPv6地址与FTP服务器建立连接，
进入FTP客户端视图。
步骤2 执行命令user username，更改当前的登录用户，重新登录FTP服务器。
更改当前的登录用户后，原用户与FTP服务器的连接将断开。
说明
只有3级及3级以上的FTP用户才能通过本命令更改用户身份，登录FTP服务器。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
251
步骤3 执行命令commit，提交配置。
----结束
******* 断开与FTP 服务器的连接
为了节约系统资源，及保证合法用户能够成功登录FTP服务器，需要及时断开与FTP服
务器连接。
背景信息
登录FTP服务器的用户数达到最大值后，其他合法的用户将无法成功登录。为了保证合
法用户能够成功登录FTP服务器，需要及时断开与FTP服务器连接。
操作步骤
步骤1 根据服务器端IP地址类型不同，进行如下操作：
●
执行命令ftp [ [ -a source-ip-address | -i { interface-type interface-number |
interface-name } ] host-ip [ port-number ] [ vpn-instance vpn-instance-
name ] | public-net ]，设备使用IPv4地址与FTP服务器建立连接，进入FTP客户
端视图。
●
执行命令ftp ipv6 [ -a source-ip6 ] host-ipv6-address [ [ vpn-instance ipv6-
vpn-instance-name ] | public-net ] [ -oi { interface-type interface-number |
interface-name } ] [ port-number ]，设备使用IPv6地址与FTP服务器建立连接，
进入FTP客户端视图。
步骤2 断开FTP连接，根据需要，以下步骤任选其一：
●
执行命令bye/quit终止与FTP服务器的连接，并退回到用户视图。
●
执行命令close/disconnect终止与FTP服务器的连接，并终止FTP会话，仍保持在
FTP客户端视图。
----结束
******* 检查配置结果
通过FTP访问其他设备的文件配置成功后，可以查看到FTP客户端配置的源参数。
前提条件
已完成通过FTP访问其他设备的文件的所有配置。
操作步骤
●
使用display ftp-client命令查看设备作为FTP客户端时的源参数。
●
使用display ftp server ip auth-fail information命令查看所有认证失败的客户
端IP地址。
●
使用display ftp server ip-block list命令查看因认证失败而被锁定的客户端IP地
址。
----结束
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
252



#### ******* 断开与FTP服务器的连接
#### ******* 检查配置结果
通过FTP访问其他设备的文件配置成功后，可以查看到FTP客户端配置的源参数。
前提条件
已完成通过FTP访问其他设备的文件的所有配置。
操作步骤
●
使用display ftp-client命令查看设备作为FTP客户端时的源参数。
●
使用display ftp server ip auth-fail information命令查看所有认证失败的客户
端IP地址。
●
使用display ftp server ip-block list命令查看因认证失败而被锁定的客户端IP地
址。
----结束
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
252
1.8.7 通过SFTP 访问其他设备的文件
SFTP是一种安全的FTP服务。配置设备作为SFTP客户端，服务器通过对客户端的认证
及双向的数据加密，为网络文件传输提供了安全的服务。
应用环境
SFTP是Secure File Transfer Protocol的简称，建立在SSH连接的基础之上，远程用户
可以安全地登录设备，进行文件管理和文件传送等操作，为数据传输提供了更高的安
全保障。同时，由于设备提供了SFTP客户端功能，可以从本设备安全登录到远程SSH
服务器上，进行文件的安全传输。
前置任务
在配置通过SFTP访问其他设备文件之前，需完成以下任务：
●
成功配置SFTP服务器。详细配置如下：
a.
配置SSH用户并指定服务方式
b.
使能SFTP服务器功能
c.
（可选）配置SFTP服务器参数
******* （可选）配置SFTP 客户端源地址
用户可以配置SFTP客户端的源地址信息，从指定的客户端源地址建立SFTP连接，保证
安全性。
背景信息
用户可以在设备上指定某一接口，为此接口配置IP地址，然后使用该IP地址作为SFTP
连接的源IP地址，从而达到进行安全校验的目的。
客户端源地址可以配置为源接口或源IP。
请在作为SFTP客户端的设备上进行如下的配置。
操作步骤
步骤1 执行命令system-view，进入系统视图。
步骤2 执行以下命令配置SFTP的源地址或源接口信息。
●
IPv4场景下：执行命令sftp client-source -a source-ip-address { public-net | -
vpn-instance ipv6-vpn-instance-name } }或sftp client-source -i { interface-
type interface-number | interface-name }
●
IPv6场景下：执行命令sftp ipv6 client-source -a source-ipv6-address [ -vpn-
instance ipv6-vpn-instance-name ]
步骤3 执行命令commit，提交配置。
----结束
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
253



### 1.8.7 通过SFTP访问其他设备的文件
#### ******* （可选）配置SFTP客户端源地址
#### ******* 配置用户首次登录其他设备（使能SSH客户端首次认证功能方式）
#### ******* 配置用户首次登录其他设备（SSH客户端为SSH服务器分配公钥）
#### ******* 使用SFTP命令连接其他设备（SSH服务器）
#### ******* 通过SFTP文件操作命令进行文件操作
#### ******* 检查配置结果
通过SFTP访问其他设备的文件配置成功后，可以查看到SSH客户端源地址、客户端所
有的SSH服务器与RSA或ECC公钥之间的对应关系、SSH服务器的全局配置信息以及与
客户端连接的会话信息。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
258
前提条件
已完成通过SFTP访问其他设备的文件的所有配置。
操作步骤
●
使用display sftp-client命令，在SSH客户端查看客户端源地址。
●
使用display ssh server-info命令，在SSH客户端查看SSH服务器与RSA公钥之间
的对应关系。
----结束
1.8.8 配置SFTP 客户端通过一键式文件操作命令进行文件操作
设备作为SFTP客户端，使用一键式文件操作命令，从本地上传文件到SFTP服务器，或
者从SFTP服务器下载文件至本地。
前提条件
已完成SFTP服务器端的配置，且SFTP客户端和服务器端的路由相通。
操作步骤
步骤1 执行命令system-view，进入系统视图。
步骤2 执行命令ssh client first-time enable，使能SSH客户端首次认证功能。
步骤3 执行命令commit提交配置。
步骤4 基于网络协议执行如下操作步骤：
●
基于IPv4建立SFTP连接
执行命令sftp client-transfile { get | put } [ -a source-address | -i { interface-
type interface-number | interface-name } ] host-ip host-ipv4 [ port ]
[ [ public-net | -vpn-instance vpn-instance-name ] | [ prefer_kex
{ prefer_kex } ] | [ identity-key identity-key-type ] | [ prefer_ctos_cipher
prefer_ctos_cipher ] | [ prefer_stoc_cipher prefer_stoc_cipher ] |
[ prefer_ctos_hmac prefer_ctos_hmac ] | [ prefer_stoc_hmac
prefer_stoc_hmac ] | [ -ki interval ] | [ -kc count ] ] * username user-name
password password sourcefile destination [ destination source-file ]，通过
IPv4方式连接SFTP服务器，并从服务器上下载文件至SFTP客户端或者从SFTP客户
端上传文件至服务器。
●
基于IPv6建立SFTP连接
执行命令sftp client-transfile { put | get } ipv6 [ -a source-ipv6-address ]
host-ip host-ipv6 [ -oi { interface-type interface-number | interface-name } ]
[ port ] [ [ public-net | -vpn-instance vpn-instance-name ] | [ prefer_kex
{ prefer_kex } ] | [ identity-key identity-key-type ] | [ prefer_ctos_cipher
prefer_ctos_cipher ] | [ prefer_stoc_cipher prefer_stoc_cipher ] |
[ prefer_ctos_hmac prefer_ctos_hmac ] | [ prefer_stoc_hmac
prefer_stoc_hmac ] | [ -ki interval ] | [ -kc count ] ] *username user-name
password password sourcefile source-file [ destination destination ]，通过
IPv6方式连接SFTP服务器，并从服务器上下载文件至SFTP客户端或者从SFTP客户
端上传文件至服务器。
----结束
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
259



### 1.8.8 配置SFTP客户端通过一键式文件操作命令进行文件操作
### 1.8.9 通过SCP访问其他设备的文件
#### ******* 配置SCP服务器端
#### ******* 配置SCP客户端
#### ******* 检查配置结果
通过SCP访问其他设备的文件成功配置后，可以查看到SCP客户端的源地址。
前提条件
已完成通过SCP访问其他设备的文件的所有配置。
操作步骤
●
使用display scp-client命令在SCP客户端查看源配置信息。
●
使用display ssh server-info命令，在SSH客户端查看SSH服务器与RSA公钥之间
的对应关系。
----结束
1.8.10 配置SSL 策略加密算法套
客户端与服务端之间进行认证时，会为SSL算法协商提供加密算法列表。本文介绍如何
配置加密算法套允许使用的加密算法，使用安全的算法可增强系统安全性。
背景信息
客户端与服务端之间进行认证时，会为SSL算法协商提供加密算法列表。对于安全性要
求较高的系统，使用更安全的加密算法可增强系统安全性。
操作步骤
步骤1 执行命令system-view，进入系统视图。
步骤2 执行命令ssl cipher-suite-list customization-policy-name，创建SSL策略加密算法套
并进入SSL算法套定制视图。
步骤3 执行命令set cipher-suite { tls1_ck_rsa_with_aes_256_sha |
tls1_ck_rsa_with_aes_128_sha | tls1_ck_dhe_rsa_with_aes_256_sha |
tls1_ck_dhe_dss_with_aes_256_sha | tls1_ck_dhe_rsa_with_aes_128_sha |
tls1_ck_dhe_dss_with_aes_128_sha | tls12_ck_rsa_aes_128_cbc_sha |
tls12_ck_rsa_aes_256_cbc_sha | tls12_ck_rsa_aes_128_cbc_sha256 |
tls12_ck_rsa_aes_256_cbc_sha256 | tls12_ck_dhe_dss_aes_128_cbc_sha |
tls12_ck_dhe_rsa_aes_128_cbc_sha | tls12_ck_dhe_dss_aes_256_cbc_sha |
tls12_ck_dhe_rsa_aes_256_cbc_sha | tls12_ck_dhe_dss_aes_128_cbc_sha256 |
tls12_ck_dhe_rsa_aes_128_cbc_sha256 | tls12_ck_dhe_dss_aes_256_cbc_sha256 |
tls12_ck_dhe_rsa_aes_256_cbc_sha256 |
tls12_ck_rsa_with_aes_128_gcm_sha256 |
tls12_ck_rsa_with_aes_256_gcm_sha384 |
tls12_ck_dhe_rsa_with_aes_128_gcm_sha256 |
tls12_ck_dhe_rsa_with_aes_256_gcm_sha384 |
tls12_ck_dhe_dss_with_aes_128_gcm_sha256 |
tls12_ck_dhe_dss_with_aes_256_gcm_sha384 |
tls12_ck_ecdhe_rsa_with_aes_128_gcm_sha256 |
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
262



### 1.8.10 配置SSL策略加密算法套
### 1.8.11 配置绑定SSL Policy
部署SSL策略可防止传输的数据被篡改，提高系统安全性。
背景信息
传统的一些协议（如Syslog）不具备安全机制，采用明文形式传输数据，不能验证通
信双方的身份，无法防止传输的数据被篡改，安全性很低。安全协议SSL利用数据加
密、身份验证和消息完整性验证机制，为基于TCP可靠连接的应用层协议提供了安全性
保证。
操作步骤
在SSL策略视图下部署SSL策略：
1.
执行命令system-view，进入系统视图。
2.
执行命令ssl policy policy-name，配置SSL策略并进入SSL策略视图。
3.
（可选）执行命令ecdh group { nist | curve | brainpool | ffdhe }* ，配置
ECDHE算法的椭圆曲线参数。
4.
执行命令ssl minimum version { tls1.1 | tls1.2 | tls1.3 }，配置当前SSL策略所采
用的最低版本。
说明
已经配置了TLS1.0的版本升级到不支持配置TLS1.0的新版本后，SSL策略支持的最低版本为
TLS1.0，可以通过ssl minimum version { tls1.1 | tls1.2 | tls1.3 }命令重新配置当前SSL
策略所采用的最低版本，该过程不可逆。
在未配置ssl minimum version命令的情况下升级到新版本后，升级后SSL策略支持的最低
版本为TLS1.1，缺省情况下支持的最低版本为TLS1.2，可以通过ssl minimum version
{ tls1.1 | tls1.2 | tls1.3 }命令重新配置当前SSL策略所采用的最低版本。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
263
5.
（可选）执行命令ssl forbidden tls13-use-brainpoolr1，禁止TLS1.3使用
brainpoolr1曲线。
6.
（可选）执行命令ssl renegotiation enable，使能SSL重协商功能。
7.
（可选）执行命令ssl verify { basic-constrain | key-usage | version { cert-
version3 | crl-version2 } | certificate-signature-algorithm } enable，使能证
书校验功能。
8.
（可选）执行命令ssl verify certificate-chain minimum-path-length path-
length，配置数字证书链的最小路径长度。
9.
（可选）执行命令signature algorithm-list { ecdsa-secp256r1-sha256 |
ecdsa-secp384r1-sha384 | ecdsa-secp521r1-sha512 | ed25519 | ed448 | rsa-
pss-pss-sha256 | rsa-pss-pss-sha384 | rsa-pss-pss-sha512 | rsa-pss-rsae-
sha256 | rsa-pss-rsae-sha384 | rsa-pss-rsae-sha512 | rsa-pkcs1-sha256 |
rsa-pkcs1-sha384 | rsa-pkcs1-sha512 | ecdsa-sha1 | ecdsa-sha224 | rsa-sha1
| rsa-sha224 | dsa-sha1 | dsa-sha224 | dsa-sha256 | dsa-sha384 | dsa-
sha512 } *，设置SSL握手过程支持的签名算法。
说明
如果支持的签名算法减少，可能由于签名算法不匹配导致SSL握手失败，可以通过执行该命
令调整支持的签名算法。
10. （可选）执行pki-domain pki-domain，为SSL策略绑定PKI域。
说明
●
绑定PKI域后，SSL策略使用PKI域下的证书和证书撤销列表。
●
除了通过PKI加载和撤销证书以外，还支持通过下面两个步骤进行手工操作。
11. （可选）执行命令certificate load，加载数字证书。当前支持PEM格式证书、
PFX格式证书和PEM格式的证书链，请根据需要加载一个证书或者证书链。
–
执行命令certificate load pem-cert certFile key-pair { dsa | rsa } key-file
keyFile auth-code [ cipher authCode ]，为SSL策略加载PEM格式的证书。
–
执行命令certificate load pfx-cert certFile key-pair { dsa | rsa } mac或
certificate load pfx-cert certFile key-pair { dsa | rsa } { mac cipher
mac-code | key-file keyFile } auth-code cipher authCode，为SSL策略加
载PFX格式的证书。
–
执行命令certificate load pem-chain certFile key-pair { dsa | rsa } key-
file keyFile auth-code [ cipher authCode ]，为SSL策略加载PEM格式的证
书链。
说明
对于设备，加载数字证书是可选步骤，但是对于网管，加载数字证书是必选步骤。
12. （可选）执行命令crl load crlType crlFile，加载数字证书撤销列表。
一个SSL策略最多可以同时加载2个CRL（Certificate Revocation List）文件。
13. 执行命令trusted-ca load，加载信任证书机构文件。一个SSL策略最多可以同时
加载4个信任证书机构文件。
–
执行命令trusted-ca load pem-ca caFile，为SSL策略加载PEM格式信任证
书机构文件。
–
执行命令trusted-ca load asn1-ca caFile，为SSL策略加载ASN1格式信任证
书机构文件。
–
执行命令trusted-ca load pfx-ca caFile auth-code [ cipher authCode ]，
为SSL策略加载PFX格式信任证书机构文件。
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
264
14. （可选）执行命令binding cipher-suite-customization customization-name，
为SSL策略绑定加密算法套。 绑定加密算法套之前需已完成SSL策略加密算法套的
配置，配置过程请参考1.8.10 配置SSL策略加密算法套。
15. （可选）执行命令cipher-suite exclude key-exchange rsa，排除SSL策略加密
算法套中RSA密钥交换算法。
说明
由于RSA安全性不高，对安全性要求较高的网络，不建议使用RSA密钥交换算法。
16. （可选）执行命令cipher-suite exclude key-exchange dhe，排除SSL策略加密
算法套中DHE密钥交换算法。
说明
由于DHE安全性不高，对安全性要求较高的网络，不建议使用DHE密钥交换算法。
17. （可选）执行命令cipher-suite exclude cipher mode cbc，排除SSL策略加密算
法套中CBC加密模式算法。
说明
由于CBC安全性不高，对安全性要求较高的网络，不建议使用CBC加密模式算法。
18. （可选）执行命令cipher-suite exclude hmac sha1，排除SSL策略加密算法套中
SHA1摘要算法。
说明
由于SHA1安全性不高，对安全性要求较高的网络，不建议使用SHA1摘要算法。
19. （可选）执行命令diffie-hellman modulus modulus-val，设置Diffie-Hellman
密钥交换算法的模数。
说明
如果Diffie-Hellman密钥交换算法的模数长度增加，可能由于模数过长导致SSL握手失败，
可以通过执行该命令调整模数长度。
20. 执行命令quit，退回系统视图。
21. （可选）执行命令ssl certificate alarm-threshold early-alarm time check-
interval check-period，设置证书过期的告警阈值和检查间隔。
22. 根据不同业务，在相应的业务视图下执行bind ssl-policy命令绑定SSL策略。
a.
双机备份业务
执行命令remote-backup-service service-name，进入远端备份服务视图。
执行命令bind ssl-policy ssl-policy-name，绑定SSL策略。
说明
VS模式下，该命令仅在Admin VS支持。
双机备份业务的详细配置可以参考建立双机备份平台。
b.
DCN业务
执行命令dcn，进入DCN视图。
执行命令bind ssl-policy ssl-policy-name，绑定SSL策略。
DCN业务的详细配置可以参考在GNE上配置SSL认证。
23. （可选）执行命令ssl renegotiation enable，使能SSL重协商功能，可以在不中
断连接的情况下定期更新密钥和算法，以提高通信的安全性。
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
265
24. 执行命令commit，提交配置。
在DTLS策略视图下部署SSL策略：
1.
执行命令system-view，进入系统视图。
2.
执行命令dtls policy policyName，配置DTLS策略并进入DTLS策略视图。
3.
（可选）执行命令signature algorithm-list { ecdsa-secp256r1-sha256 |
ecdsa-secp384r1-sha384 | ecdsa-secp521r1-sha512 | ed25519 | ed448 | rsa-
pss-pss-sha256 | rsa-pss-pss-sha384 | rsa-pss-pss-sha512 | rsa-pss-rsae-
sha256 | rsa-pss-rsae-sha384 | rsa-pss-rsae-sha512 | rsa-pkcs1-sha256 |
rsa-pkcs1-sha384 | rsa-pkcs1-sha512 | ecdsa-sha1 | ecdsa-sha224 | rsa-sha1
| rsa-sha224 | dsa-sha1 | dsa-sha224 | dsa-sha256 | dsa-sha384 | dsa-
sha512 } *，设置SSL握手过程支持的签名算法。
说明
如果支持的签名算法减少，可能由于签名算法不匹配导致SSL握手失败，可以通过执行该命
令调整支持的签名算法。
4.
（可选）执行命令ssl verify { basic-constrain | key-usage | certificate-
signature-algorithm } enable，使能证书校验功能。
5.
（可选）执行命令ssl verify certificate-chain minimum-path-length path-
length，配置数字证书链的最小路径长度。
6.
（可选）执行命令binding cipher-suite-customization customization-name，
为SSL策略绑定加密算法套。 绑定加密算法套之前需已完成SSL策略加密算法套的
配置，配置过程请参考1.8.10 配置SSL策略加密算法套。
7.
（可选）执行命令diffie-hellman modulus modulus-val，设置Diffie-Hellman
密钥交换算法的模数。
说明
如果Diffie-Hellman密钥交换算法的模数长度增加，可能由于模数过长导致SSL握手失败，
可以通过执行该命令调整模数长度。
8.
（可选）执行pki-domain pki-domain，为SSL策略绑定PKI域。
说明
绑定PKI域后，SSL策略使用PKI域下的证书和证书撤销列表。
9.
执行命令commit，提交配置。
1.8.12 通过HTTP 登录其他设备
HTTP（Hypertext Transfer Protocol）即超文本传输协议，是用于从WWW服务器传
输超文本到本地浏览器的传送协议。HTTP是一个应用层协议，由请求和响应构成，是
一个标准的客户端/服务器模型。
背景信息
当用户需要进行从HTTP服务器端下载证书，可以使用HTTP协议。HTTP是Hypertext
Transfer Protocol的简称，它用来在Internet上传递Web页面信息。
说明
使用HTTP协议存在安全风险。
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
266



### 1.8.12 通过HTTP登录其他设备
### 1.8.13 配置Telnet、SSH协议报文的DSCP值
### 1.8.14 访问其他设备配置举例
配置设备访问其他设备的示例，示例中包括组网需求、配置注意事项和配置思路等。
1.8.14.1 通过Telnet 登录其他设备配置示例
配置Telnet登录其他设备的示例。在本示例中，通过配置用户验证方式和密码，实现
Telnet登录。
组网需求
说明
使用Telnet协议存在安全风险，建议使用STelnet V2登录设备。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
268



#### 1.8.14.1 通过Telnet登录其他设备配置示例
#### 1.8.14.2 通过STelnet登录其他设备配置示例（RSA认证方式）
#### 1.8.14.3 通过STelnet登录其他设备配置示例（DSA认证方式）
#### 1.8.14.4 通过STelnet登录其他设备配置示例（ECC认证方式）
#### 1.8.14.5 通过STelnet登录其他设备配置示例（SM2认证方式）
#### 1.8.14.6 通过TFTP访问其他设备文件配置示例
#### 1.8.14.7 通过FTP访问其他设备文件配置示例
#### 1.8.14.8 通过SFTP访问其他设备文件配置示例（RSA认证方式）
#### 1.8.14.9 通过SFTP访问其他设备文件配置示例（DSA认证方式）
#### 1.8.14.10 通过SFTP访问其他设备文件配置示例（ECC认证方式）
#### 1.8.14.11 通过SFTP访问其他设备文件配置示例（SM2认证方式）
#### 1.8.14.12 配置SSH服务器支持其他端口号访问的示例
#### 1.8.14.13 配置公网SSH客户端访问私网SSH服务器的示例
#### 1.8.14.14 通过SCP访问其他设备文件配置示例
#### 1.8.14.15 通过HTTP访问其他设备配置示例
## 1.9 ZTP配置
### 1.9.1 ZTP概述
### 1.9.2 ZTP配置注意事项
### 1.9.3 配置通过DHCP实现ZTP自动部署
#### 1.9.3.1 编辑中间文件
操作步骤
步骤1 中间文件可以是ini文件、cfg文件或Python脚本文件。其中，ini文件和cfg文件的使用
要求低且配置简单，Python脚本文件对用户要求高，所以推荐首次使用ZTP的用户选
择ini文件或者cfg文件作为中间文件。文件格式请参见ini格式的中间文件、cfg格式的
中间文件或Python格式的中间文件。
----结束
******* 配置DHCPv4 Server 和DHCPv4 Relay
背景信息
需要运行ZTP的设备在上电之前，须先部署DHCPv4服务器，以确保作为DHCP客户端
的空配置设备能正常获取到IP地址、网关及中间文件服务器地址、中间文件名称等信
息。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
353



#### ******* 配置DHCPv4 Server和DHCPv4 Relay
#### ******* 配置DHCPv6 Server和DHCPv6 Relay
#### ******* 配置文件服务器
背景信息
文件服务器用于存放空配置设备需要下载的文件，包括中间文件、版本文件等。用户
可以将路由器配置为文件服务器，但由于文件服务器需要占用设备的存储资源，因此
在使用路由器作为文件服务器时，需要考虑存储空间的问题。所以在ZTP网络中，一般
需要部署第三方服务器，配置的具体方法请参见第三方服务器的操作指导。
用户可以将中间文件和版本文件部署在同一个文件服务器上。文件服务器可以是
TFTP/FTP/SFTP服务器。
说明
文件服务器与空配置设备的缺省网关之间必须路由可达。
后续处理
配置完文件服务器后，将中间文件、版本文件存放至文件服务器。
说明
为充分保证文件服务器的安全，建议配置的文件服务器用户名唯一，并将其权限设置为只读，防
止被非法修改。ZTP过程结束后，请关闭相应的文件服务器功能。



#### 1.9.3.5 上电启动设备
背景信息
上述配置步骤完成后，将待配置设备上电启动。设备将自动下载版本文件并重新启
动，完成自动部署。
操作步骤
步骤1 上电启动设备。
----结束
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
356
HUAWEI NetEngine40E
配置指南
1 基础配置



#### 1.9.3.6 （可选）加载预配置脚本
背景信息
当设备空配置启动时，在进入ZTP流程之前，如果需要对设备进行预配置命令下发，则
需要设置预配置脚本。
操作步骤
步骤1 根据文件类型和格式要求编辑预配置脚本，文件格式请参见预配置脚本。
步骤2 上传预配置脚本至主控板的存储介质中。
说明
本设备支持FTP，TFTP以及SFTP上传文件，请参考通过FTP访问其他设备的文件，通过TFTP访
问其他设备的文件以及1.8.7 通过SFTP访问其他设备的文件操作。请根据实际情况选择文件上传
方式上传到设备。
步骤3 执行命令set ztp pre-configuration file-name，加载预配置脚本。
若用户希望设备空配置启动时不执行ZTP预配置流程，可以执行命令reset ztp pre-
configuration，清空预配置脚本。
步骤4 执行命令display ztp status，可以查看当前设备预配置脚本的配置状态。
说明
在设备软件大包从低版本升级到当前版本时，若设置的启动配置文件为vrpcfg.zip，加载的预配
置脚本会被执行（若不想执行预配置脚本，可以通过执行命令reset ztp pre-configuration清空
预配置脚本）；当设置其它配置文件时，预配置脚本不会被执行。
----结束



#### ******* （可选）配置自动补丁修复
通过配置ZTP自动补丁修复，可以解决ZTP开局自动部署时遇到的非环境问题（环境问
题如设备初始不稳定、服务器未启动等）。
背景信息
在ZTP开局自动部署过程中，出现非环境问题（环境问题如设备初始不稳定、服务器未
启动等）时，不需要工程师前往站点维修或设备返厂维修，ZTP支持自动补丁修复机
制。通过人工定位问题根因，联系工程师制作修复补丁，ZTP运行过程中可以自主识
别、设置修复补丁，并重启设备使修复补丁生效。设备重启后ZTP会再次运行，由于此
时问题已被修复，ZTP可以顺利完成开局部署。
操作步骤
步骤1 将修复补丁信息配置在ZTP的中间文件中，配置详情见自动补丁修复机制。
步骤2 将修复补丁上传到文件服务器中，并将修改后的ZTP中间文件重新上传到文件服务器
中。
步骤3 空配置重启设备，ZTP重新运行，识别中间文件中的修复补丁信息，自动完成修复过
程。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
357
说明
●
修复过程中，在设置好修复补丁后，会立刻检测ZTP是否使能，如果此时ZTP去使能，则恢复
下次启动补丁为ZTP开局前设备自带的原始补丁。
●
修复完成后，ZTP设置了下次启动大包、补丁和配置文件，如果此时ZTP去使能，则下次启动
补丁回退到修复补丁，不会回退为开局前设备自带补丁。
----结束
1.9.3.8 开启ZTP 功能
背景信息
为了使设备空配置启动时能够自动执行ZTP流程，需要开启设备的ZTP功能。
操作步骤
步骤1 执行命令set ztp enable，配置设备下次空配置启动时执行ZTP流程。
若用户希望设备空配置启动时不执行ZTP流程，也可以使用命令set ztp disable关闭设
备的ZTP功能。
步骤2 执行命令display ztp status，可以查看设备下次空配置启动是否执行ZTP流程。
----结束
1.9.3.9 检查配置结果
操作步骤
步骤1 设备启动完成后，登录设备并通过命令display startup查看设备的启动文件是否与要
求的一致。
步骤2 执行命令display ztp status查看设备是否是通过ZTP完成部署。
步骤3 如果设备没有完成自动配置，可以通过设备上保存的ZTP日志查看出错原因。
设备执行ZTP流程的信息会被保存在cfcard:/ztp目录下，文件名为：ztp_年月日时分
秒.log。
----结束
1.9.4 配置举例
介绍通过DHCP实现自动部署的配置示例，示例中包括组网需求、配置注意事项和配置
思路等。
1.9.4.1 配置通过DHCP 实现ZTP 自动部署示例
组网需求
如图1-80所示，某网络中新增两台空配置设备RouterA和RouterB，连接到现网设备
RouterC上。RouterC作为RouterA和RouterB的出口网关。RouterC与DHCP服务器、
文件服务器之间路由可达。
HUAWEI NetEngine40E
配置指南
1 基础配置
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
358



#### 1.9.3.8 开启ZTP功能
#### 1.9.3.9 检查配置结果
操作步骤
步骤1 设备启动完成后，登录设备并通过命令display startup查看设备的启动文件是否与要
求的一致。
步骤2 执行命令display ztp status查看设备是否是通过ZTP完成部署。
步骤3 如果设备没有完成自动配置，可以通过设备上保存的ZTP日志查看出错原因。
设备执行ZTP流程的信息会被保存在cfcard:/ztp目录下，文件名为：ztp_年月日时分
秒.log。
----结束



### 1.9.4 配置举例
介绍通过DHCP实现自动部署的配置示例，示例中包括组网需求、配置注意事项和配置
思路等。
1.9.4.1 配置通过DHCP 实现ZTP 自动部署示例
组网需求
如图1-80所示，某网络中新增两台空配置设备RouterA和RouterB，连接到现网设备
RouterC上。RouterC作为RouterA和RouterB的出口网关。RouterC与DHCP服务器、
文件服务器之间路由可达。
文档版本 01 (2024-03-31)
版权所有 © 华为技术有限公司
358



#### 1.9.4.1 配置通过DHCP实现ZTP自动部署示例