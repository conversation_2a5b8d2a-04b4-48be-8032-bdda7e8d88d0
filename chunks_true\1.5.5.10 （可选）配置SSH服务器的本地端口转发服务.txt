

使能了 SSH 服务器的本地端口转发服务后，可以与指定地址和端口号建立转发通道。

##### 背景信息


只有当 SSH 服务器的本地端口转发服务使能后， SSH 服务器才能从 SSH 客户端接收转发
请求信息，建立与指定地址和端口号的 TCP 连接，即转发通道，它能够将从客户端接收
到的数据转发至指定主机。

##### 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **ssh server tcp forwarding enable** ，使能 SSH 服务器的本地端口转发服务。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 98


HUAWEI NetEngine40E
配置指南 1 基础配置


步骤 **3** 执行命令 **commit** ，提交配置。


**----**
结束
