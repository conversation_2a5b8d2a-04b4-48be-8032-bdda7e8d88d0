

部署 SSL 策略可防止传输的数据被篡改，提高系统安全性。

##### 背景信息


传统的一些协议（如 Syslog ）不具备安全机制，采用明文形式传输数据，不能验证通
信双方的身份，无法防止传输的数据被篡改，安全性很低。安全协议 SSL 利用数据加
密、身份验证和消息完整性验证机制，为基于 TCP 可靠连接的应用层协议提供了安全性
保证。

##### 操作步骤


在 SSL 策略视图下部署 SSL 策略：


1. 执行命令 **system-view** ，进入系统视图。

2. 执行命令 **ssl policy** policy-name ，配置 SSL 策略并进入 SSL 策略视图。

3. （可选）执行命令 **ecdh group** { **nist** | **curve** | **brainpool** | **ffdhe** } [*] ，配置
ECDHE 算法的椭圆曲线参数。


4. 执行命令 **ssl minimum version** { **tls1.1** | **tls1.2** | **tls1.3** } ，配置当前 SSL 策略所采
用的最低版本。


说明


已经配置了 TLS1.0 的版本升级到不支持配置 TLS1.0 的新版本后， SSL 策略支持的最低版本为
TLS1.0 ，可以通过 **ssl minimum version** { **tls1.1** | **tls1.2** | **tls1.3** } 命令重新配置当前 SSL
策略所采用的最低版本，该过程不可逆。


在未配置 **ssl minimum version** 命令的情况下升级到新版本后，升级后 SSL 策略支持的最低
版本为 TLS1.1 ，缺省情况下支持的最低版本为 TLS1.2 ，可以通过 **ssl minimum version**
{ **tls1.1** | **tls1.2** | **tls1.3** } 命令重新配置当前 SSL 策略所采用的最低版本。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 263


HUAWEI NetEngine40E
配置指南 1 基础配置


5. （可选）执行命令 **ssl forbidden tls13-use-brainpoolr1** ，禁止 TLS1.3 使用
brainpoolr1 曲线。

6. （可选）执行命令 **ssl renegotiation enable** ，使能 SSL 重协商功能。

7. （可选）执行命令 **ssl verify** { **basic-constrain** | **key-usage** | **version** { **cert-**
**version3** | **crl-version2** } | **certificate-signature-algorithm** } **enable** ，使能证
书校验功能。

8. （可选）执行命令 **ssl verify certificate-chain minimum-path-length** pathlength ，配置数字证书链的最小路径长度。

9. （可选）执行命令 **signature algorithm-list** { **ecdsa-secp256r1-sha256** |
**ecdsa-secp384r1-sha384** | **ecdsa-secp521r1-sha512** | **ed25519** | **ed448** | **rsa-**
**pss-pss-sha256** | **rsa-pss-pss-sha384** | **rsa-pss-pss-sha512** | **rsa-pss-rsae-**
**sha256** | **rsa-pss-rsae-sha384** | **rsa-pss-rsae-sha512** | **rsa-pkcs1-sha256** |
**rsa-pkcs1-sha384** | **rsa-pkcs1-sha512** | **ecdsa-sha1** | **ecdsa-sha224** | **rsa-sha1**
| **rsa-sha224** | **dsa-sha1** | **dsa-sha224** | **dsa-sha256** | **dsa-sha384** | **dsa-**
**sha512** } [*] ，设置 SSL 握手过程支持的签名算法。


说明


如果支持的签名算法减少，可能由于签名算法不匹配导致 SSL 握手失败，可以通过执行该命
令调整支持的签名算法。


10. （可选）执行 **pki-domain** pki-domain ，为 SSL 策略绑定 PKI 域。


说明


            - 绑定 PKI 域后， SSL 策略使用 PKI 域下的证书和证书撤销列表。


            - 除了通过 PKI 加载和撤销证书以外，还支持通过下面两个步骤进行手工操作。

11. （可选）执行命令 **certificate load** ，加载数字证书。当前支持 PEM 格式证书、
PFX 格式证书和 PEM 格式的证书链，请根据需要加载一个证书或者证书链。

– 执行命令 **certificate load** **pem-cert** certFile **key-pair** { **dsa** | **rsa** } **key-file**
keyFile **auth-code** [ **cipher** authCode ] ，为 SSL 策略加载 PEM 格式的证书。

– 执行命令 **certificate load** **pfx-cert** certFile **key-pair** { **dsa** | **rsa** } **mac** 或
**certificate load** **pfx-cert** certFile **key-pair** { **dsa** | **rsa** } { **mac** **cipher**
mac-code | **key-file** keyFile } **auth-code** **cipher** authCode ，为 SSL 策略加
载 PFX 格式的证书。

– 执行命令 **certificate load** **pem-chain** certFile **key-pair** { **dsa** | **rsa** } **key-**
**file** keyFile **auth-code** [ **cipher** authCode ] ，为 SSL 策略加载 PEM 格式的证
书链。


说明


对于设备，加载数字证书是可选步骤，但是对于网管，加载数字证书是必选步骤。


12. （可选）执行命令 **crl load** crlType crlFile ，加载数字证书撤销列表。

一个 SSL 策略最多可以同时加载 2 个 CRL （ Certificate Revocation List ）文件。

13. 执行命令 **trusted-ca load** ，加载信任证书机构文件。一个 SSL 策略最多可以同时
加载 4 个信任证书机构文件。


– 执行命令 **trusted-ca load** **pem-ca** caFile ，为 SSL 策略加载 PEM 格式信任证
书机构文件。


– 执行命令 **trusted-ca load** **asn1-ca** caFile ，为 SSL 策略加载 ASN1 格式信任证
书机构文件。


– 执行命令 **trusted-ca load** **pfx-ca** caFile **auth-code** [ **cipher** authCode ] ，
为 SSL 策略加载 PFX 格式信任证书机构文件。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 264


HUAWEI NetEngine40E
配置指南 1 基础配置


14. （可选）执行命令 **binding cipher-suite-customization** customization-name ，
为 SSL 策略绑定加密算法套。 绑定加密算法套之前需已完成 SSL 策略加密算法套的
配置，配置过程请参考 **1.8.10** 配置 **SSL** 策略加密算法套 。


15. （可选）执行命令 **cipher-suite exclude** **key-exchange rsa** ，排除 SSL 策略加密
算法套中 RSA 密钥交换算法。


说明


由于 RSA 安全性不高，对安全性要求较高的网络，不建议使用 RSA 密钥交换算法。


16. （可选）执行命令 **cipher-suite exclude** **key-exchange dhe** ，排除 SSL 策略加密
算法套中 DHE 密钥交换算法。


说明


由于 DHE 安全性不高，对安全性要求较高的网络，不建议使用 DHE 密钥交换算法。


17. （可选）执行命令 **cipher-suite exclude** **cipher** **mode** **cbc** ，排除 SSL 策略加密算
法套中 CBC 加密模式算法。


说明


由于 CBC 安全性不高，对安全性要求较高的网络，不建议使用 CBC 加密模式算法。


18. （可选）执行命令 **cipher-suite exclude** **hmac** **sha1** ，排除 SSL 策略加密算法套中
SHA1 摘要算法。


说明


由于 SHA1 安全性不高，对安全性要求较高的网络，不建议使用 SHA1 摘要算法。

19. （可选）执行命令 **diffie-hellman modulus** modulus-val ，设置 Diffie-Hellman

密钥交换算法的模数。


说明


如果 Diffie-Hellman 密钥交换算法的模数长度增加，可能由于模数过长导致 SSL 握手失败，
可以通过执行该命令调整模数长度。


20. 执行命令 **quit** ，退回系统视图。

21. （可选）执行命令 **ssl certificate alarm-threshold** **early-alarm** time **check-**
**interval** check-period ，设置证书过期的告警阈值和检查间隔。


22. 根据不同业务，在相应的业务视图下执行 **bind ssl-policy** 命令绑定 SSL 策略。


a. 双机备份业务


执行命令 **remote-backup-service** service-name ，进入远端备份服务视图。


执行命令 **bind ssl-policy** ssl-policy-name ，绑定 SSL 策略。


说明


VS 模式下，该命令仅在 Admin VS 支持。


双机备份业务的详细配置可以参考建立双机备份平台。


b. DCN 业务


执行命令 **dcn** ，进入 DCN 视图。


执行命令 **bind ssl-policy** ssl-policy-name ，绑定 SSL 策略。


DCN 业务的详细配置可以参考在 GNE 上配置 SSL 认证。


23. （可选）执行命令 **ssl renegotiation enable** ，使能 SSL 重协商功能，可以在不中
断连接的情况下定期更新密钥和算法，以提高通信的安全性。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 265


HUAWEI NetEngine40E
配置指南 1 基础配置


24. 执行命令 **commit** ，提交配置。


在 DTLS 策略视图下部署 SSL 策略：


1. 执行命令 **system-view** ，进入系统视图。


2. 执行命令 **dtls policy** policyName ，配置 DTLS 策略并进入 DTLS 策略视图。


3. （可选）执行命令 **signature algorithm-list** { **ecdsa-secp256r1-sha256** |
**ecdsa-secp384r1-sha384** | **ecdsa-secp521r1-sha512** | **ed25519** | **ed448** | **rsa-**
**pss-pss-sha256** | **rsa-pss-pss-sha384** | **rsa-pss-pss-sha512** | **rsa-pss-rsae-**
**sha256** | **rsa-pss-rsae-sha384** | **rsa-pss-rsae-sha512** | **rsa-pkcs1-sha256** |
**rsa-pkcs1-sha384** | **rsa-pkcs1-sha512** | **ecdsa-sha1** | **ecdsa-sha224** | **rsa-sha1**
| **rsa-sha224** | **dsa-sha1** | **dsa-sha224** | **dsa-sha256** | **dsa-sha384** | **dsa-**
**sha512** } [*] ，设置 SSL 握手过程支持的签名算法。


说明


如果支持的签名算法减少，可能由于签名算法不匹配导致 SSL 握手失败，可以通过执行该命
令调整支持的签名算法。

4. （可选）执行命令 **ssl verify** { **basic-constrain** | **key-usage** | **certificate-**
**signature-algorithm** } **enable** ，使能证书校验功能。

5. （可选）执行命令 **ssl verify certificate-chain minimum-path-length** pathlength ，配置数字证书链的最小路径长度。


6. （可选）执行命令 **binding cipher-suite-customization** customization-name ，
为 SSL 策略绑定加密算法套。 绑定加密算法套之前需已完成 SSL 策略加密算法套的
配置，配置过程请参考 **1.8.10** 配置 **SSL** 策略加密算法套 。

7. （可选）执行命令 **diffie-hellman modulus** modulus-val ，设置 Diffie-Hellman
密钥交换算法的模数。


说明


如果 Diffie-Hellman 密钥交换算法的模数长度增加，可能由于模数过长导致 SSL 握手失败，
可以通过执行该命令调整模数长度。


8. （可选）执行 **pki-domain** pki-domain ，为 SSL 策略绑定 PKI 域。


说明


绑定 PKI 域后， SSL 策略使用 PKI 域下的证书和证书撤销列表。


9. 执行命令 **commit** ，提交配置。
