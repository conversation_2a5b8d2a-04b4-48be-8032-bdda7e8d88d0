

需要运行 ZTP 的设备在上电之前，须先部署 DHCPv6 服务器，以确保作为 DHCP 客户端
的空配置设备能正常获取到 IP 地址、网关及中间文件服务器地址、中间文件名称等信
息。


运行 ZTP 的设备进入 DHCPv6 Solicit 阶段后，在发送 DHCPv6 Solicit 消息时会携带
DHCPv6 option 6 ， DHCPv6 option 6 用来携带客户端请求的选项代码。


DHCPv6 服务器上需配置的 Options 字段见 表 **1-44** 。


表 **1-44** Options 字段说明

|Option编号|是否可选|Option作用|
|---|---|---|
|5|必选|申请的IA地址，IPv6地址以及生存期。|
|59|必选|中间文件路径，中间文件的名称为*.ini、*.py或<br>*.cfg。中间文件名格式如下：<br>●tftp://hostname/path/flename<br>●ftp://[username[:password]@]hostname/<br>path/flename<br>●sftp://[username[:password]@]hostname/<br>path/flename|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 355


HUAWEI NetEngine40E
配置指南 1 基础配置


说明


ZTP 通过 DHCPv6 申请的 IPv6 地址租期至少为 1 小时且不支持续租。


当待配置设备与 DHCPv6 服务器不在同一网段时，需要配置 DHCPv6 中继以转发 DHCPv6 的交互报
文。

##### 操作步骤


步骤 **1** 在作为 DHCPv6 服务器的设备上配置地址池，地址池中需要配置 **Option 59** 。


步骤 **2** 配置地址池为接入用户分配地址。


步骤 **3** （可选）如果组网中存在 DHCPv6 中继，在作为 DHCPv6 中继的设备上进行配置，详细
配置请参见“配置 DHCPv6 Relay ”。


**----**
结束
