

设备出厂时带有缺省配置文件 default.cfg ，内容如下：


       - 路由器模式


对于 X3A 设备：

#

sysname HUAWEI
#
crypto weak-algorithm disable
#
lldp enable
#

undo telnet server enable
undo telnet ipv6 server enable
#
undo icmp name timestamp-reply send
#
ip vpn-instance __LOCAL_OAM_VPN__
ipv4-family
ipv6-family


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 196


HUAWEI NetEngine40E
配置指南 1 基础配置


#
interface GigabitEthernet0/0/0
ip binding vpn-instance __LOCAL_OAM_VPN__
ip address *********** *************
#
security password
#

rule admin

forbidden word ******

#
undo ssh server compatible-ssh1x enable
stelnet server enable

snetconf server enable
ssh server cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr
ssh server hmac sha2_512 sha2_256
ssh server key-exchange dh_group_exchange_sha256
#
ssh server publickey rsa_sha2_256 rsa_sha2_512
#
ssh server dh-exchange min-len 3072
ssh server rsa-key min-length 3072
#
ssh client publickey rsa_sha2_256 rsa_sha2_512
#
ssh client cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr
ssh client hmac sha2_512 sha2_256
ssh client key-exchange dh_group_exchange_sha256
#
user-interface vty 0 4
authentication-mode aaa

idle-timeout 10 0
protocol inbound ssh
#
mpls ldp-srbe convergence enhance
#
undo snmp-agent protocol source all-interface
undo ssh server-source all-interface
undo ssh ipv6 server-source all-interface
ssh server-source -i GigabitEthernet0/0/0
#

return

对于 X8A/X16A 设备：


#

sysname HUAWEI
#
crypto weak-algorithm disable
#
lldp enable
#

undo telnet server enable
undo telnet ipv6 server enable
#
undo icmp name timestamp-reply send
#
security password
#

rule admin

forbidden word ******

#
undo ssh server compatible-ssh1x enable
stelnet server enable

snetconf server enable
ssh server cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr
ssh server hmac sha2_512 sha2_256
ssh server key-exchange dh_group_exchange_sha256
#
ssh server publickey rsa_sha2_256 rsa_sha2_512
#
ssh server dh-exchange min-len 3072


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 197


HUAWEI NetEngine40E
配置指南 1 基础配置


ssh server rsa-key min-length 3072
#
ssh client publickey rsa_sha2_256 rsa_sha2_512
#
ssh client cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr
ssh client hmac sha2_512 sha2_256
ssh client key-exchange dh_group_exchange_sha256
#
user-interface vty 0 4
authentication-mode aaa

idle-timeout 10 0
protocol inbound ssh
#
mpls ldp-srbe convergence enhance
#
undo snmp-agent protocol source all-interface
undo ssh server-source all-interface
undo ssh ipv6 server-source all-interface
#

return


       - 传输模式


对于 X3A 设备：

#

sysname HUAWEI
#
crypto weak-algorithm disable
#

undo telnet server enable
undo telnet ipv6 server enable
#
undo icmp name timestamp-reply send
#
fan speed auto
#

undo radius enable

#
snmp-agent trap type entity-trap
#
dot1x-template 1
#
dhcpv6 disable
#
ip vpn-instance __LOCAL_OAM_VPN__
ipv4-family
ipv6-family
#
interface GigabitEthernet0/0/0
ip binding vpn-instance __LOCAL_OAM_VPN__
ip address *********** *************
#
security password
#

rule admin

forbidden word ******

#

aaa

authentication-scheme default0

authentication-scheme default1

authentication-scheme default

authentication-mode local radius

#

authorization-scheme default

#
accounting-scheme default0
accounting-scheme default1
#

domain default0

domain default1


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 198


HUAWEI NetEngine40E
配置指南 1 基础配置


domain default_admin
#

#

multicastbandwidth

#
interface GigabitEthernet0/0/0
speed auto
duplex auto
undo shutdown
ip address *********** ***********
#

interface NULL0

#
l2tp-group default-lns
#
undo ssh server compatible-ssh1x enable
stelnet server enable

snetconf server enable
ssh server cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr
ssh server hmac sha2_512 sha2_256
ssh server key-exchange dh_group_exchange_sha256
#
ssh server publickey rsa_sha2_256 rsa_sha2_512
#
ssh server dh-exchange min-len 3072
ssh server rsa-key min-length 3072
#
ssh client publickey rsa_sha2_256 rsa_sha2_512
#
ssh client cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr
ssh client hmac sha2_512 sha2_256
ssh client key-exchange dh_group_exchange_sha256
#
user-interface maximum-vty 15
user-interface con 0

user-interface aux 0
user-interface vty 0 14
authentication-mode aaa
protocol inbound ssh
idle-timeout 10 0

#
multicast shaping
#
mpls
mpls te
mpls rsvp-te
mpls rsvp-te hello
mpls rsvp-te hello support-peer-gr
mpls te cspf
mpls oam based-itu
#
mpls l2vpn
#
mpls ldp
remote-peer pwe3
#

local-aaa-server

#
mpls ldp-srbe convergence enhance
#
dtls policy qx_dtls_client
#
dcn security-mode enable
#
undo snmp-agent protocol source all-interface
undo ssh server-source all-interface
undo ssh ipv6 server-source all-interface
ssh server-source -i GigabitEthernet0/0/0


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 199


HUAWEI NetEngine40E
配置指南 1 基础配置


#

return

对于 X8A/X16A 设备：


#

sysname HUAWEI
#
crypto weak-algorithm disable
#

undo telnet server enable
undo telnet ipv6 server enable
#
undo icmp name timestamp-reply send
#
fan speed auto
#

undo radius enable

#
snmp-agent trap type entity-trap
#
dot1x-template 1
#
dhcpv6 disable
#
security password
#

rule admin

forbidden word ******

#

aaa

authentication-scheme default0

authentication-scheme default1

authentication-scheme default

authentication-mode local radius

#

authorization-scheme default

#
accounting-scheme default0
accounting-scheme default1
#

domain default0

domain default1
domain default_admin
#

#

multicastbandwidth

#
interface GigabitEthernet0/0/0
speed auto
duplex auto
undo shutdown
ip address *********** ***********
#

interface NULL0

#
l2tp-group default-lns
#
undo ssh server compatible-ssh1x enable
stelnet server enable

snetconf server enable
ssh server cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr
ssh server hmac sha2_512 sha2_256
ssh server key-exchange dh_group_exchange_sha256
#
ssh server publickey rsa_sha2_256 rsa_sha2_512
#
ssh server dh-exchange min-len 3072
ssh server rsa-key min-length 3072
#
ssh client publickey rsa_sha2_256 rsa_sha2_512


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 200


HUAWEI NetEngine40E
配置指南 1 基础配置


#
ssh client cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr
ssh client hmac sha2_512 sha2_256
ssh client key-exchange dh_group_exchange_sha256
#
user-interface maximum-vty 15
user-interface con 0

user-interface aux 0
user-interface vty 0 14
authentication-mode aaa
protocol inbound ssh
idle-timeout 10 0

#
multicast shaping
#
mpls
mpls te
mpls rsvp-te
mpls rsvp-te hello
mpls rsvp-te hello support-peer-gr
mpls te cspf
mpls oam based-itu
#
mpls l2vpn
#
mpls ldp
remote-peer pwe3
#

local-aaa-server

#
mpls ldp-srbe convergence enhance
#
dtls policy qx_dtls_client
#
dcn security-mode enable
#
undo snmp-agent protocol source all-interface
undo ssh server-source all-interface
undo ssh ipv6 server-source all-interface
ssh server-source -i GigabitEthernet0/0/0
#

return


配置文件只有管理级别的用户才可以保存和修改，是在运行过程中对配置文件作为系
统文件的保护机制（不允许删除和修改）。
