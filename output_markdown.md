# 目 录

1. 1 1. 2 1. 3 1. 4 1. 5 1. 6 通过IPv6 1. 7 1. 8 配置绑定SSL 1. 9 配置DHCPv4 Server和DHCPv4 配置DHCPv6 Server和DHCPv6 基础配置 1. 1 首次登录配置 1. 2 当用户需要为第一次上电的设备进行基本配置时，可以通过Console口登录设备，还可 以通过即插即用方式批量部署设备。 命令行接口配置 1. 3 用户对设备的日常操作，主要通过输入命令行来实现。

用户可以在命令视图下编辑和 配置命令行，并显示配置成功或错误的信息。 用户界面配置 1. 4 当用户通过Console口、Telnet或SSH方式登录路由器时，系统会分配相应的用户界 面，用来管理、监控设备和当前用户之间的会话。 用户登录配置 1. 5 用户可以通过Console口、Telnet或SSH（STelnet）方式登录设备，实现对设备的本地 或远程维护。 文件系统配置 1. 6 文件系统实现对存储设备中的文件、目录的管理。 配置管理配置 1.

7 为了保障用户配置的可靠性，系统支持两种配置生效模式。 访问其他设备配置 1. 8 设备可以作为客户端访问网络上的其他设备。 ZTP配置 1. 9 设备可以通过零配置自动部署ZTP（Zero Provisioning）实现空配置下的上电自 Touch 动部署。 1. 1 本文档介绍了基础配置的基本概念、在不同应用场景中的配置过程和配置举例。


# 1 基础配置

基础配置 1. 1 首次登录配置 1. 2 当用户需要为第一次上电的设备进行基本配置时，可以通过Console口登录设备，还可 以通过即插即用方式批量部署设备。 命令行接口配置 1. 3 用户对设备的日常操作，主要通过输入命令行来实现。 用户可以在命令视图下编辑和 配置命令行，并显示配置成功或错误的信息。 用户界面配置 1. 4 当用户通过Console口、Telnet或SSH方式登录路由器时，系统会分配相应的用户界 面，用来管理、监控设备和当前用户之间的会话。 用户登录配置 1.

5 用户可以通过Console口、Telnet或SSH（STelnet）方式登录设备，实现对设备的本地 或远程维护。 文件系统配置 1. 6 文件系统实现对存储设备中的文件、目录的管理。 配置管理配置 1. 7 为了保障用户配置的可靠性，系统支持两种配置生效模式。 访问其他设备配置 1. 8 设备可以作为客户端访问网络上的其他设备。 ZTP配置 1. 9 设备可以通过零配置自动部署ZTP（Zero Provisioning）实现空配置下的上电自 Touch 动部署。 1. 1


## 1.1 前 言

本文档介绍了基础配置的基本概念、在不同应用场景中的配置过程和配置举例。 License License的详细信息，请查阅License使用指南。 企业网用户： 使用指南：License使用指南 NE40E License 使用指南：License使用指南 NE40E-X8AK License 产品版本 与本文档相对应的产品版本如下所示。

产品名称 产品版本 HUAWEI NetEngine40E V800R023C10SPC500 iMaster NCE-IP V100R023C10SPC100 读者对象 本文档主要适用于以下工程师： 数据配置工程师 调测工程师 网络监控工程师 系统维护工程师 安全声明 受限公开声明 产品资料中主要介绍了您在使用华为设备时，在网络部署及维护时，需要使用的 命令。 对用于生产、装备、返厂检测维修的接口、命令，不在资料中说明。

对于部分仅用于工程实施、定位故障的高级命令以及升级兼容命令，如使用不 当，将可能导致设备异常或者业务中断，建议较高权限的工程师使用。 如您需 要，请向华为公司申请。 加密算法声明 使用加密算法时，DES/3DES/RSA（3072位以下\\)/MD5（数字签名场景和口令加 密）/SHA1（数字签名场景）加密算法安全性低，存在安全风险，在协议支持的 加密算法选择范围内，建议使用更安全的加密算法，例如AES/RSA（3072位及以 上）/SHA2/HMAC-SHA2。

出于安全性考虑，不建议使用不安全协议Telnet、FTP、TFTP；不建议使用特性 BGP、LDP、PCEP、MSDP、DCN、TCP-AO、MSTP、VRRP、E-trunk、AAA、 IPSEC、BFD、QX、端口扩展、SSH、SNMP、IS-IS、RIP、SSL、NTP、OSPF、 Keychain中的弱安全算法。 如果确实需要使用，请执行undo crypto weakdisable命令使能弱安全算法功能。 详细步骤请参见《配置指南》。

algorithm 出于安全性考虑，不建议使用该特性中的弱安全算法，若当前系统已关闭弱安全 算法功能，配置弱安全算法会提示Error信息。 如果确实需要使用弱安全算法，请 先执行undo disable命令使能弱安全算法功能。 crypto weak-algorithm 密码配置声明 密文（本设备可以解密的密文）时，在设备上查看配置文件时显示的是和配 置相同的密文，请不要采用该方式直接配置密码。 为保证设备安全，请定期修改密码。

MAC地址、公网IP地址使用的声明 出于特性介绍及配置示例的需要，产品资料中会使用真实设备的MAC地址、 公网的IP地址，如无特殊说明，出现的真实设备的MAC地址、公网的IP地址 均为示意，不指代任何实际意义。 因开源及第三方软件中自带公网地址（包括公网IP地址、公网URL地址/域 名、邮箱地址），本产品没有使用这些公网地址，这遵循业界实践，也符合 开源软件使用规范。 出于功能特性实现的需要，设备会使用如下公网地址 表1-1 公网地址列表 公网地址 华为官方网站地址 http://www.

huawei. com 华为企业用户服务邮箱 support\\_e@huawei. com 个人数据声明 您购买的产品、服务或特性在业务运营或故障定位的过程中将可能获取或使 用用户的某些个人数据，因此您有义务根据所适用国家的法律制定必要的用 户隐私政策并采取足够的措施以确保用户的个人数据受到充分的保护。 废弃、回收或者再利用设备时，请注意根据需要备份或清除设备中的数据， 避免数据泄露的安全风险。 如需支持，请联系售后技术支持人员。

预置证书使用声明 在生产阶段预置于华为设备的华为证书是华为设备必备的出厂身份凭证，对其使 用声明如下： 华为预置证书仅用于部署阶段为设备接入客户网络建立初始安全通道以及设 备对接，华为不对预置证书的安全性做承诺与保证。 对于将华为预置证书作为业务证书使用而导致的安全风险和安全事件，由客 户自行处置并承担后果。 华为预置证书有效期自2041年起开始过期，可以通过display pki cert\\_list default命令查看实际的有效期。

domain 预置证书过期后，使用预置证书的业务会中断。 华为建议客户通过部署PKI系统对现网设备、软件签发证书并做好证书的生命 周期管理（为保证安全性推荐使用短有效期的证书）。 华为产品中用于产品入网初始化配置和连接时使用的华为PKI根证书支持禁用 （当验证华为新网元入网时，可配置重启该证书）。 建议客户完成产品入网 配置并为产品配置客户CA签发的证书后，将该根证书禁用。 对于客户未禁用 华为PKI根证书而带来的安全风险和安全事件，由客户自行处置并承担后果。

产品生命周期政策 华为公司对产品生命周期的规定以“产品生命周期终止政策”为准，该政策的详 细内容请参见如下网址：https://support. huawei. com/ecolumnsweb/zh/ warranty-policy。 漏洞处理流程 华为公司对产品漏洞管理的规定以“漏洞处理流程”为准，该流程的详细内容请 参见如下网址：https://www. huawei.

com/cn/psirt/vul-response-process 如企业客户须获取漏洞信息，请参见如下网址：https:// securitybulletin. huawei. com/enterprise/cn/security-advisory 华为企业业务最终用户许可协议\\(EULA\\) 本最终用户许可协议是最终用户（个人、公司或其他任何实体）与华为公司就华 为软件的使用所缔结的协议。 最终用户对华为软件的使用受本协议约束，该协议 的详细内容请参见如下网址：https://e.

huawei. com/cn/about/eula。 产品资料生命周期策略 华为公司针对随产品版本发布的售后客户资料（产品资料），发布了“产品资料 生命周期策略”，该策略的详细内容请参见如下网址：https:// support. huawei. com/enterprise/zh/bulletins-website/ ENEWS2000017760。

华为预置证书权责说明 华为公司对随设备出厂的预置数字证书，发布了“华为设备预置数字证书权责说 明”，该说明的详细内容请参见如下网址：https://support. huawei. com/ enterprise/zh/bulletins-service/ENEWS2000015766。 设备升级、打补丁的声明 对设备进行升级或打补丁操作时，请使用软件数字签名（OpenPGP）验证工具验 证软件。 为避免软件被篡改或替换，防止给用户带来安全风险，建议用户进行此 项操作。

特性声明 NetStream功能，出于对网络流量的统计管理，可能涉及对最终用户的通信 内容分析，建议您在所适用法律法规允许的目的和范围内方可启用相应的功 能。 在采集、存储用户通信内容的过程中，您应采取足够的措施以确保用户 的通信内容受到严格保护。 镜像功能，可能基于运维目的需要对某些最终用户的通信内容进行分析，建 议您在所适用法律法规允许的目的和范围内方可启用相应的功能。 在采集、 存储用户通信内容的过程中，您应采取足够的措施以确保用户的通信内容受 到严格保护。

报文头获取功能，出于检测通信传输中的故障和错误的目的，可能涉及采集 或存储个人用户某些通信内容。 本公司无法单方采集或存储用户通信内容。 建议您只有在所适用法律法规允许的目的和范围内方可启用相应的功能。 在 采集、存储用户通信内容的过程中，您应采取足够的措施以确保用户的通信 内容受到严格保护。 可靠性设计声明 对于网络规划和站点设计，必须严格遵守可靠性设计原则，具备设备级和方案级 保护。 设备级保护包括双网双平面，双机、跨板双链路的规划原则，避免出现单 点，单链路故障。

方案级指FRR、VRRP等快速收敛保护机制。 在应用方案级保护 时，应避免保护方案的主备路径经过相同链路或者传输，以免方案级保护不生 特别声明 本文档中包含了NE40E支持的所有产品内容，如果需要了解在本国销售的设备或 单板等硬件相关信息，请查阅硬件描述章节。 本手册仅作为使用指导，其内容依据实验室设备信息编写。 手册提供的内容具有 一般性的指导意义，并不确保涵盖所有使用场景。 因版本升级、设备型号不同、 板卡限制不同、配置文件不同等原因，可能造成手册中提供的内容与用户使用的 设备界面不一致。

请以用户设备界面的信息为准，本手册不再针对前述情况造成 的差异一一说明。 本手册中提供的最大值是设备在实验室特定场景（例如被测试设备上只有某种类 型的单板，或者只配置了某一种协议）达到的最大值。 在现实网络中，由于设备 硬件配置不同、承载的业务不同等原因可能会使设备测试出的最大值与手册中提 供的数据不一致。 本手册中出现的接口编号仅作示例，并不代表设备上实际具有此编号的接口，实 际使用中请以设备上存在的接口编号为准。 本手册中的硬件照片仅供参考，具体请以发货的硬件实物为准。

本手册中体现设备支持的相关硬件板卡，存在特定客户定制的需求，实际支持以 售前销售界面为准。 出于特性介绍及配置示例的需要，产品资料中会使用公网IP地址，如无特殊说 明，资料里出现的公网IP地址均为示意，不指代任何实际意义。 本手册中配置指南出现的“XX配置注意事项”，请结合产品的实际特性支持情况 来使用。 本手册中的日志参考和告警参考，记录的是对应产品上注册的日志和告警信息。 实际应用中可触发的日志和告警，取决于当前产品所支持的业务功能。

本文档中描述的所有设备尺寸数据均为设计尺寸，不包含尺寸公差。 在部件制造 过程中，由于加工或测量等因素的影响，实际尺寸存在一定的偏差。 符号约定 在本文中可能出现下列标志，它们所代表的含义如下。 表示如不避免则将会导致死亡或严重伤害的具有高等 级风险的危害。 表示如不避免则可能导致死亡或严重伤害的具有中等 级风险的危害。 表示如不避免则可能导致轻微或中度伤害的具有低等 级风险的危害。 用于传递设备或环境安全警示信息。 如不避免则可能 会导致设备损坏、数据丢失、设备性能降低或其它不 可预知的结果。

“须知”不涉及人身伤害。 对正文中重点信息的补充说明。 “说明”不是安全警示信息，不涉及人身、设备及环 境伤害信息。 命令格式约定 命令行关键字（命令中保持不变、必须照输的部分）采用 加粗字体表示。 命令行参数（命令中必须由实际值进行替代的部分）采用 斜体表示。 表示用“\\[ \\]”括起来的部分在命令配置时是可选的。 表示从两个或多个选项中选取一个。 表示从两个或多个选项中选取一个或者不选。 表示从两个或多个选项中选取多个，最少选取一个，最多 选取所有选项。

表示从两个或多个选项中选取多个或者不选。 表示符号&前面的参数可以重复1～n次。 &<1-n> 由“\#”开始的行表示为注释行。 修订记录 修改记录累积了每次文档变更的说明。 最新版本的文档包含以前所有文档版本的更新 内容。 产品版本 文档版本 发布日期 V800R023C10SPC500 2024-06-30 V800R023C10SPC500 2024-03-30 首次登录配置 1.

2 当用户需要为第一次上电的设备进行基本配置时，可以通过Console口登录设备，还可 以通过即插即用方式批量部署设备。


## 1.2 首次登录配置

当用户需要为第一次上电的设备进行基本配置时，可以通过Console口登录设备，还可 以通过即插即用方式批量部署设备。 首次登录概述 设备第一次上电可以使用Console口或通过管理网口登录，使用Console口进行本地登 录是登录设备的最基本的方式，也是配置通过其他方式登录设备的基础。 Console口是一种通信串行端口，由设备的主控板提供。 一块主控板提供一个Console 口，端口类型为EIA/TIA-232 DCE。

用户终端的串行端口可以与设备的Console口直接 连接，实现对设备的本地配置。 Console口状态如下： Connected：Console口处于试连接状态。 Disconnected：与Console口连接断开。 首次登录，系统自动提示用户设置密码。 通过按CTRL+R恢复出厂设置，从而清除用户设置的密码。

在构建大型网络时，为了实现对设备的远程统一管理和控制，以提高部署效率和降低 运维成本，设备首次上电时还可以基于即插即用功能通过DCN或DHCP方式批量部署设 首次登录设备配置注意事项 特性限制 口登录设备 Console 通过Console口连接终端与设备，搭建配置环境。 应用环境 设备第一次上电，需要对此设备进行配置和管理时，可以通过Console口登录。 前置任务 在通过Console口登录设备之前，需要完成以下任务： PC已安装终端仿真程序（如PuTTY.

exe软件） 准备好串口配置电缆 如使用第三方的USB转RJ45的串口线缆，为保证线缆传输的信号质量的完整性， 建议线缆长度不大于两米。 建立物理连接 使用配置电缆将设备的Console口与终端COM口进行物理连接。


### 1.2.1 首次登录概述

设备第一次上电可以使用Console口或通过管理网口登录，使用Console口进行本地登 录是登录设备的最基本的方式，也是配置通过其他方式登录设备的基础。 Console口是一种通信串行端口，由设备的主控板提供。 一块主控板提供一个Console 口，端口类型为EIA/TIA-232 DCE。 用户终端的串行端口可以与设备的Console口直接 连接，实现对设备的本地配置。 Console口状态如下： Connected：Console口处于试连接状态。

Disconnected：与Console口连接断开。 首次登录，系统自动提示用户设置密码。 通过按CTRL+R恢复出厂设置，从而清除用户设置的密码。 在构建大型网络时，为了实现对设备的远程统一管理和控制，以提高部署效率和降低 运维成本，设备首次上电时还可以基于即插即用功能通过DCN或DHCP方式批量部署设 首次登录设备配置注意事项

