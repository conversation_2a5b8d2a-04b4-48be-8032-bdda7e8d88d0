

执行如下步骤配置 SSH 用户的 SFTP 服务授权目录。

##### 背景信息


执行 **ssh user** username **sftp-directory** directoryname 命令时，如果用户 username
不存在，则新建一个用户名为 username 的 SSH 用户， SFTP 服务授权目录为所配置的目
录；或者可以使用 **local-user** user-name **ftp-directory** directory 所配置的用户名进行
授权。如果所配置的授权目录不存在，且不存在默认授权目录，则 SFTP 客户端连接
SFTP 服务器失败。

##### 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 选择执行如下命令，配置 SSH 用户的 SFTP 服务授权目录。


       - 执行命令 **ssh user** user-name **sftp-directory** directoryname ，配置指定 SSH 用户
的 SFTP 服务授权目录。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 172


HUAWEI NetEngine40E
配置指南 1 基础配置


       - 执行命令 **sftp server default-directory** sftpdir ，配置 SFTP 服务器的默认授权目
录。


说明


          - **ssh user** username **sftp-directory** directoryname 命令优先级高，仅对指定 SSH 用户生效。


          - **sftp server default-directory** sftpdir 命令优先级低，对所有 SSH 用户生效。


步骤 **3** 执行命令 **commit** ，提交配置。


**----**
结束
