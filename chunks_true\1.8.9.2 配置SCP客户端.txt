

SCP 客户端和服务器之间经过协商，建立安全连接，客户端可以上传文件至服务器或从
服务器下载文件至本地。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 260


HUAWEI NetEngine40E
配置指南 1 基础配置

##### 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** （可选）执行命令 **scp client-source** { **-a** source-ip-address [ **public-net** | **-vpn-**
**instance** vpn-instance-name ] | **-i** { interface-type interface-number | interfacename } } 或 **scp ipv6 client-source** **-a** ipv6-address [ **-vpn-instance** ipv6-vpninstance-name ] ，配置 SCP 客户端的源地址。


步骤 **3** （可选）执行命令 **ssh client cipher** { **des_cbc** | **3des_cbc** | **aes128_cbc** |
**aes192_cbc** | **aes256_cbc** | **aes128_ctr** | **aes192_ctr** | **aes256_ctr** | **arcfour128** |
**arcfour256** | **aes128_gcm** | **aes256_gcm** | **sm4_cbc** | **sm4_gcm** } [*] ，配置 SSH 客户端
上的加密算法。


说明


为保证更好的安全性，建议使用以下安全性更高的加密算法： aes128_ctr 、 aes256_ctr 、
aes192_ctr 、 aes128_gcm 、 aes256_gcm 。


des_cbc 、 3des_cbc 、 aes128_cbc 、 aes256_cbc 、 arcfour128 、 arcfour256 、 aes192_cbc 和
sm4_cbc 算法为弱安全算法不建议使用，如需配置，需执行 **undo crypto weak-algorithm**
**disable** 命令使能弱安全算法功能。为避免安全风险，建议改用更安全的算法。


步骤 **4** （可选）执行命令 **ssh client hmac** { **md5** | **md5_96** | **sha1** | **sha1_96** | **sha2_256** |
**sha2_256_96** | **sha2_512** | **sm3** } [*] ，配置 SSH 客户端 HMAC 认证算法。


说明


为保证更好的安全性，建议使用以下安全性更高的 HMAC 算法： sha2_256 、 sha2_512 。


md5 、 md5_96 、 sha1 、 sha1_96 和 sha2_256_96 算法为弱安全算法不建议使用，如需配置，需
执行 **undo crypto weak-algorithm disable** 命令使能弱安全算法功能。为避免安全风险，建议
改用更安全的算法。


步骤 **5** 上传文件至远端 SCP 服务器或下载远端 SCP 服务器上的文件至本地。


       - 基于 IPv4 网络


执行命令 **scp** [ **-a** source-ip-address ] [ **-force-receive-pubkey** ] [ **-port** portnumber | **public-net** | **vpn-instance** vpn-instance-name | **identity-key**
identity-key-type | **user-identity-key** user-key | **-r** | **-c** | **-cipher** cipher | **-**
**prefer-kex** prefer-kex ] [*] source-filename destination-filename 或 **scp** { **-i**
interface-name | interface-type interface-number } [ **-force-receive-pubkey** ]

[ **-port** port-number | **identity-key** identity-key-type | **user-identity-key** userkey | **-r** | **-c** | **-cipher** cipher| **-prefer-kex** prefer-kex ] [*] source-filename
destination-filename


       - 基于 IPv6 网络


**-**
执行命令 **scp ipv6** [ [ **vpn-instance** vpn-instance-name ] | **public-net** ] [
**force-receive-pubkey** ] [ [ **-port** server-port ] | [ **identity-key** identity-keytype ] | [ **user-identity-key** user-key ] | [ [ **-a** source-ipv6-address ] | [ **-oi**
{ interface-name | interface-type interface-number } ] ] | **-r** | **-c** | [ **-cipher**
cipher ] | [ **-prefer-kex** { prefer-kex } ] ] * source-filename destinationfilename


说明


为了保证更好的安全性，建议不要使用小于 3072 位的 RSA 算法作为 SSH 用户的认证方式，建议您
使用更安全的 ECC 认证算法。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 261


HUAWEI NetEngine40E
配置指南 1 基础配置


步骤 **6** 执行命令 **commit** ，提交配置。


**----**
结束
