import fitz # PyMuPDF
import pdfplumber
import re
from typing import Any, Dict, List, Optional

def clean_text(text: str) -> str:
    """
    清理文本内容，去除无效字符和多余的空白。
    """
    if not text:
        return ""
    
    cleaned = re.sub(r'(\w+)-\n(\w+)', r'\1\2', text)
    cleaned = re.sub(r'\n', ' ', cleaned)
    cleaned = re.sub(r'\s+', ' ', cleaned).strip()
    
    return cleaned

def find_text_bbox(page: Any, text_to_find: str) -> Optional[Dict[str, Any]]:
    """
    在指定页面上查找给定文本的边界框（bbox），并返回第一个匹配项。
    为了增加匹配成功率，先使用原始文本搜索，失败则尝试去除空格后再次搜索。
    """
    # 尝试原始文本搜索
    results = page.search(text_to_find)
    if results:
        print(f"Debug: Found '{text_to_find}' with exact match.")
        return results[0]

    # 如果原始文本搜索失败，尝试去除所有空格后再次搜索
    sanitized_text = re.sub(r'\s+', '', text_to_find)
    if sanitized_text:
        results = page.search(sanitized_text)
        if results:
            print(f"Debug: Found '{text_to_find}' with sanitized text '{sanitized_text}'.")
            return results[0]

    return None

def extract_content_by_toc(pdf_path: str) -> List[Dict[str, Any]]:
    """
    根据PDF的目录（TOC）提取标题和对应的内容。
    """
    extracted_data = []

    try:
        fitz_doc = fitz.open(pdf_path)
        toc = fitz_doc.get_toc()

        if not toc:
            print(f"Warning: The PDF '{pdf_path}' does not have a table of contents.")
            return []

        with pdfplumber.open(pdf_path) as plumber_doc:
            num_titles = len(toc)
            for i, (level, title_text, page_num) in enumerate(toc):
                print(f"\n--- Processing title: {title_text} (Page: {page_num}) ---")
                current_page_idx = page_num - 1
                
                # 获取当前标题的边界框
                # 由于目录可能不准确，这里需要处理页面索引超出范围的情况
                try:
                    current_title_bbox = find_text_bbox(plumber_doc.pages[current_page_idx], title_text)
                except IndexError:
                    print(f"Warning: Page index {current_page_idx} is out of range. Skipping.")
                    continue

                if not current_title_bbox:
                    print(f"Warning: Could not find title '{title_text}' on page {page_num}. Skipping.")
                    continue

                # 确定下一个标题的页码和边界框（如果存在）
                next_title_text = None
                next_page_idx = -1
                next_title_bbox = None
                if i + 1 < num_titles:
                    next_title_text = toc[i + 1][1]
                    next_page_idx = toc[i + 1][2] - 1
                    
                    try:
                        next_title_bbox = find_text_bbox(plumber_doc.pages[next_page_idx], next_title_text)
                    except IndexError:
                        # 如果下一个标题的页码不正确，也跳过
                        pass

                content_parts = []
                current_loop_page_idx = current_page_idx
                while True:
                    if current_loop_page_idx >= len(plumber_doc.pages):
                        break # 防止页码超出范围

                    current_page = plumber_doc.pages[current_loop_page_idx]
                    
                    start_y = current_title_bbox["bottom"] if current_loop_page_idx == current_page_idx else 0
                    
                    page_texts = current_page.extract_words()
                    
                    current_page_content_words = []
                    for word in page_texts:
                        if word["top"] < start_y:
                            continue

                        if next_title_bbox and current_loop_page_idx == next_page_idx:
                            # 严格判断，如果文本块的顶部坐标大于或等于下一个标题的顶部坐标，则停止
                            if word["top"] >= next_title_bbox["top"]:
                                break
                        
                        current_page_content_words.append(word["text"])

                    content_parts.append(" ".join(current_page_content_words))

                    if next_title_bbox and current_loop_page_idx >= next_page_idx:
                        break
                    if i + 1 >= num_titles:
                        break
                    
                    current_loop_page_idx += 1
                
                full_content = " ".join(content_parts)
                cleaned_content = clean_text(full_content)
                
                extracted_data.append({
                    'title': title_text,
                    'page': page_num,
                    'content': cleaned_content
                })

    except fitz.FileNotFoundError:
        print(f"Error: The file '{pdf_path}' was not found.")
        return []
    except Exception as e:
        # 为了调试，暂时不捕获所有异常，而是打印出来
        # print(f"An unexpected error occurred: {e}")
        # print(f"Traceback: {traceback.format_exc()}")
        raise # 重新抛出异常以显示详细的错误栈
    finally:
        if 'fitz_doc' in locals():
            fitz_doc.close()
            
    return extracted_data

if __name__ == "__main__":
    pdf_file_path = "2.pdf" # 替换为您的PDF文件路径
    
    print(f"Starting extraction from: {pdf_file_path}")
    
    extracted_results = extract_content_by_toc(pdf_file_path)

    if extracted_results:
        out = open("output.txt", "wb") # create a text output
        print("\n--- Extraction Complete ---")
        for item in extracted_results:
            out.write(f"\n--- Title: {item['title']} (Page: {item['page']}) ---\n".encode('utf-8'))
            out.write(item['content'].encode('utf-8'))
            print("\n------------------------------------")
    else:
        print("Extraction failed or no content was found.")
        