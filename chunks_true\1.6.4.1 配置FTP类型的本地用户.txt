

用户可以配置 FTP 用户的验证信息、授权方式和授权目录，保证安全性，无权限的用户
将不能访问某些目录。

##### 背景信息


当用户通过 FTP 进行文件操作时，需要在作为 FTP 服务器的设备上配置本地用户名及口
令，指定用户的服务类型以及可以访问的目录。否则用户将无法通过 FTP 访问设备。


请在作为 FTP 服务器的设备上进行如下的配置。

##### 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** （可选）执行命令 **crypto password irreversible-algorithm hmac-sha256** ，设置用
户密文口令加密算法为 hmac-sha256 。


步骤 **3** 执行命令 **aaa** ，进入 AAA 视图。


步骤 **4** 执行命令 **local-user** user-name **password** [ **cipher** password | **irreversible-cipher**
irreversible-cipher-password ] ，配置本地用户名和口令。


       - 不选择 **cipher** 或 **irreversible-cipher** 关键字时，密码以交互式输入，系统不回显密
码。


输入的密码为字符串形式，区分大小写，开启用户账户安全策略时，取值范围是 8
～ 128 。关闭用户账户安全策略时，长度范围是 1 ～ 128 。开启用户账户安全策略
时，密码不能与用户名及其用户名反向字符串相同，且密码必须包括大写字母、
小写字母、数字及特殊字符。


说明


特殊字符不包括“？”和空格。但是，当输入的密码两端使用双引号时，可在密码中间输
入空格。


–
如果使用双引号设置带空格密码，双引号之间不能再使用双引号。


–
如果使用双引号没有设置带空格密码，双引号之间可以再使用双引号。


例如， "Aa 123"45"" 为不合法密码， "Aa123"45"" 为合法密码。


       - 选择 **cipher** 关键字时，密码可以以简单形式输入，也可以以密文形式输入。


密码以简单形式输入，要求与不选择 **cipher** 关键字时一样。密码以简单形式输
入，系统会回显简单形式的密码，存在安全风险，因此建议使用交互式输入密
码。


无论是简单输入还是密文输入，配置文件中都以密文形式体现。


       - 选择 **irreversible-cipher** 关键字时，密码可以以简单形式输入，也可以以不可逆密
文形式输入。


密码以简单形式输入，要求与不选择 **irreversible-cipher** 关键字时一样。


无论是简单输入还是不可逆密文输入，配置文件中都以密文形式体现。


步骤 **5** 执行命令 **local-user** user-name **service-type ftp** ，配置 FTP 服务类型。


步骤 **6** 执行命令 **local-user** user-name **ftp-directory** directory [ **access-permission** { **read-**
**only** | **read-write** } ] ，配置 FTP 用户的授权目录以及进入目录后的操作权限。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 149


HUAWEI NetEngine40E
配置指南 1 基础配置


说明


根据认证方式的不同， FTP 服务器用户分为 AAA 本地认证用户和远程认证（ RADIUS 、
HWTACACS ）用户。


          - 对于本地认证用户，必须使用 **local-user ftp-directory** 命令指定本地用户的 FTP 工作目录。
如果没有指定，则登录失败。


          - 对于远程认证用户，可以通过 HWTACACS 服务器为远程用户指定 FTP 工作目录。如果没有指
定，则可使用 **set default ftp-directory** 命令指定 FTP 用户的缺省工作目录。


          - 对于远程认证用户，需要在 AAA 服务器上配置用户组或用户级别。具体的配置方法请参见对
应服务器厂商的配置指南。


步骤 **7** 执行命令 **local-user** user-name **level** level 配置本地用户的优先级。


本地用户的优先级必须设置为 3 级及以上级别的权限才能访问 FTP 服务器。


步骤 **8** 执行命令 **commit** ，提交配置。


**----**
结束
