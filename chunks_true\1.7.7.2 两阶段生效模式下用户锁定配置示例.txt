

介绍在多个用户在一台路由器上进行同时配置时，某个用户锁定配置后其他用户进行
配置时的举例。

##### 组网需求


如 图 **1-53** 所示， UserA 和 UserB 同时登录路由器。在 UserA 锁定配置的情况下， UserB
在路由器上进行配置。


图 **1-53** 两阶段生效模式下用户锁定配置组网图


当用户希望锁定配置，禁止其他用户同一时间进行配置和提交时，可通过锁定配置来
达到独占当前运行数据集的目的。此时其他用户进行配置的时候，系统会显示配置被
其他用户锁定的相关提示信息。如果其他用户需要对运行数据库进行配置，则首先需
要由锁定配置的用户解锁。

##### 配置思路


采用如下的思路提交配置：


1. UserA 锁定配置。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 220


HUAWEI NetEngine40E
配置指南 1 基础配置


2. UserB 进行配置，系统提示失败，显示配置被其他用户锁定。

##### 数据准备


接口的 IP 地址

##### 操作步骤


步骤 **1** UserA 锁定配置。

<HUAWEI> **configuration exclusive**


步骤 **2** UserB 进行配置。


<HUAWEI> **system-view**

[ ~ HUAWEI] **interface GigabitEthernet 1/0/4**

[ ~ HUAWEI-GigabitEthernet1/0/4] **ip address ******** 24**

[*HUAWEI-GigabitEthernet1/0/4] **commit**
Error: The configuration is locked by other user. [Session ID = 407]


**----**
结束

##### 配置文件


#

sysname HUAWEI
#
interface GigabitEthernet1/0/4
undo shutdown

#
