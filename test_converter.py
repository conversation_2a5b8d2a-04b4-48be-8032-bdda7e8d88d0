#!/usr/bin/env python3
"""
PDF转Markdown转换器测试脚本
"""

import os
import sys
from main2 import convert_pdf_to_markdown, PDFToMarkdownConverter

def test_basic_conversion():
    """测试基本转换功能"""
    print("=== 测试基本转换功能 ===")
    
    pdf_file = "2.pdf"
    if not os.path.exists(pdf_file):
        print(f"错误: 找不到测试文件 {pdf_file}")
        return False
    
    try:
        # 测试转换
        output_file = "test_output.md"
        markdown_content = convert_pdf_to_markdown(pdf_file, output_file)
        
        # 检查结果
        if not markdown_content:
            print("错误: 转换结果为空")
            return False
        
        if not os.path.exists(output_file):
            print("错误: 输出文件未生成")
            return False
        
        # 统计信息
        lines = markdown_content.split('\n')
        title_lines = [line for line in lines if line.strip().startswith('#')]
        
        print(f"✅ 转换成功")
        print(f"   - 总字符数: {len(markdown_content)}")
        print(f"   - 总行数: {len(lines)}")
        print(f"   - 标题数量: {len(title_lines)}")
        print(f"   - 输出文件: {output_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        return False

def test_title_extraction():
    """测试标题提取功能"""
    print("\n=== 测试标题提取功能 ===")
    
    pdf_file = "2.pdf"
    if not os.path.exists(pdf_file):
        print(f"错误: 找不到测试文件 {pdf_file}")
        return False
    
    try:
        with PDFToMarkdownConverter(pdf_file) as converter:
            # 测试目录提取
            titles = converter.extract_titles_from_toc()
            
            if not titles:
                print("⚠️  未从目录提取到标题，尝试字体分析")
                titles = converter.extract_titles_by_font_analysis()
            
            if titles:
                print(f"✅ 成功提取 {len(titles)} 个标题")
                
                # 显示前10个标题
                print("   前10个标题:")
                for i, title in enumerate(titles[:10]):
                    print(f"   {i+1}. [{title.level}] {title.text}")
                
                # 统计各级标题数量
                level_counts = {}
                for title in titles:
                    level_counts[title.level] = level_counts.get(title.level, 0) + 1
                
                print("   标题级别分布:")
                for level in sorted(level_counts.keys()):
                    print(f"   - 级别 {level}: {level_counts[level]} 个")
                
                return True
            else:
                print("❌ 未能提取到任何标题")
                return False
                
    except Exception as e:
        print(f"❌ 标题提取失败: {e}")
        return False

def test_markdown_syntax():
    """测试Markdown语法保护"""
    print("\n=== 测试Markdown语法保护 ===")
    
    try:
        with PDFToMarkdownConverter("2.pdf") as converter:
            # 测试特殊字符转义
            test_cases = [
                "这是一个#标题",
                "代码`code`示例",
                "链接[text](url)",
                "强调*text*示例",
                "下划线_text_示例",
                "反斜杠\\示例"
            ]
            
            print("特殊字符转义测试:")
            for test_text in test_cases:
                escaped = converter.escape_markdown_chars(test_text)
                print(f"   原文: {test_text}")
                print(f"   转义: {escaped}")
                print()
            
            return True
            
    except Exception as e:
        print(f"❌ 语法保护测试失败: {e}")
        return False

def test_content_filtering():
    """测试内容过滤功能"""
    print("\n=== 测试内容过滤功能 ===")
    
    try:
        with PDFToMarkdownConverter("2.pdf") as converter:
            # 测试页眉页脚识别
            test_cases = [
                ("1", 100, 10),  # 页码在顶部
                ("第1页", 100, 90),  # 页码在底部
                ("Copyright 2023", 100, 5),  # 版权信息在顶部
                ("正常内容", 100, 50),  # 正常内容在中间
            ]
            
            print("页眉页脚识别测试:")
            for text, page_height, y_pos in test_cases:
                is_header_footer = converter.is_header_footer(text, page_height, y_pos)
                status = "页眉页脚" if is_header_footer else "正常内容"
                print(f"   '{text}' (y={y_pos}): {status}")
            
            # 测试目录内容识别
            toc_test_cases = [
                "1.1 概述...........................1",
                "第一章 基础配置",
                "1.2.3 配置示例......................25",
                "这是正常的文本内容",
            ]
            
            print("\n目录内容识别测试:")
            for text in toc_test_cases:
                is_toc = converter.is_table_of_contents(text)
                status = "目录内容" if is_toc else "正常内容"
                print(f"   '{text}': {status}")
            
            return True
            
    except Exception as e:
        print(f"❌ 内容过滤测试失败: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("开始运行PDF转Markdown转换器测试\n")
    
    tests = [
        test_basic_conversion,
        test_title_extraction,
        test_markdown_syntax,
        test_content_filtering,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 异常: {e}")
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过！")
        return True
    else:
        print("⚠️  部分测试失败")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
