#!/usr/bin/env python3
"""
分析内容丢失的原因
"""

import re
from typing import List, Dict, Set

def extract_all_titles(markdown_content: str) -> List[str]:
    """提取所有标题"""
    titles = []
    lines = markdown_content.split('\n')
    
    for line in lines:
        if re.match(r'^#{1,6}\s+', line):
            titles.append(line.strip())
    
    return titles

def normalize_title(title: str) -> str:
    """标准化标题，去除markdown标记和多余空格"""
    # 去除markdown标记
    title = re.sub(r'^#{1,6}\s+', '', title)
    # 去除多余空格
    title = re.sub(r'\s+', ' ', title)
    return title.strip()

def find_missing_titles(baseline_titles: List[str], our_titles: List[str]) -> Dict[str, List[str]]:
    """找出缺失的标题"""
    # 标准化标题
    baseline_normalized = {normalize_title(t): t for t in baseline_titles}
    our_normalized = {normalize_title(t): t for t in our_titles}
    
    # 找出缺失的标题
    missing_normalized = set(baseline_normalized.keys()) - set(our_normalized.keys())
    extra_normalized = set(our_normalized.keys()) - set(baseline_normalized.keys())
    
    missing_titles = [baseline_normalized[t] for t in missing_normalized]
    extra_titles = [our_normalized[t] for t in extra_normalized]
    
    return {
        'missing': missing_titles,
        'extra': extra_titles,
        'baseline_count': len(baseline_titles),
        'our_count': len(our_titles),
        'missing_count': len(missing_titles),
        'extra_count': len(extra_titles)
    }

def analyze_title_patterns(titles: List[str]) -> Dict[str, any]:
    """分析标题模式"""
    patterns = {
        'by_level': {},
        'by_chapter': {},
        'special_patterns': []
    }
    
    for title in titles:
        # 计算标题级别
        level = len(re.match(r'^(#{1,6})', title).group(1)) if re.match(r'^#{1,6}', title) else 0
        
        if level not in patterns['by_level']:
            patterns['by_level'][level] = []
        patterns['by_level'][level].append(title)
        
        # 分析章节模式
        normalized = normalize_title(title)
        if re.match(r'^\d+\.', normalized):  # 以数字开头的章节
            chapter = re.match(r'^(\d+(?:\.\d+)*)', normalized).group(1)
            if chapter not in patterns['by_chapter']:
                patterns['by_chapter'][chapter] = []
            patterns['by_chapter'][chapter].append(title)
    
    return patterns

def check_toc_extraction_issues():
    """检查TOC提取是否有问题"""
    print("=== 检查TOC提取问题 ===")
    
    from main2 import PDFToMarkdownConverter
    
    with PDFToMarkdownConverter("2.pdf") as converter:
        # 获取原始TOC
        toc = converter.fitz_doc.get_toc()
        print(f"原始TOC条目数: {len(toc)}")
        
        # 显示前20个TOC条目
        print("前20个TOC条目:")
        for i, (level, title, page) in enumerate(toc[:20]):
            print(f"  {i+1:3d}. [级别{level}] {title} (页码{page})")
        
        # 检查是否有重复或异常的条目
        title_counts = {}
        for level, title, page in toc:
            clean_title = title.strip()
            if clean_title in title_counts:
                title_counts[clean_title] += 1
            else:
                title_counts[clean_title] = 1
        
        duplicates = {title: count for title, count in title_counts.items() if count > 1}
        if duplicates:
            print(f"\n发现重复标题: {len(duplicates)} 个")
            for title, count in list(duplicates.items())[:5]:
                print(f"  '{title}': {count} 次")
        
        # 检查页码分布
        pages = [page for level, title, page in toc]
        print(f"\n页码范围: {min(pages)} - {max(pages)}")
        print(f"总页数: {len(converter.fitz_doc)}")
        
        # 检查级别分布
        levels = [level for level, title, page in toc]
        level_counts = {}
        for level in levels:
            level_counts[level] = level_counts.get(level, 0) + 1
        
        print(f"\n标题级别分布:")
        for level in sorted(level_counts.keys()):
            print(f"  级别 {level}: {level_counts[level]} 个")

def main():
    print("=== 内容丢失原因分析 ===")
    
    # 读取两个文档
    try:
        with open('baseline_pymupdf4llm.md', 'r', encoding='utf-8') as f:
            baseline_content = f.read()
        
        with open('fixed_output.md', 'r', encoding='utf-8') as f:
            our_content = f.read()
    except FileNotFoundError as e:
        print(f"文件未找到: {e}")
        return
    
    # 提取所有标题
    baseline_titles = extract_all_titles(baseline_content)
    our_titles = extract_all_titles(our_content)
    
    print(f"基准文档标题数: {len(baseline_titles)}")
    print(f"我们的文档标题数: {len(our_titles)}")
    
    # 分析缺失的标题
    missing_analysis = find_missing_titles(baseline_titles, our_titles)
    
    print(f"\n=== 标题对比结果 ===")
    print(f"缺失标题数: {missing_analysis['missing_count']}")
    print(f"多余标题数: {missing_analysis['extra_count']}")
    print(f"标题覆盖率: {(missing_analysis['our_count'] / missing_analysis['baseline_count'] * 100):.1f}%")
    
    # 分析缺失标题的模式
    print(f"\n=== 缺失标题样本 (前20个) ===")
    for i, title in enumerate(missing_analysis['missing'][:20]):
        normalized = normalize_title(title)
        print(f"  {i+1:2d}. {title}")
        print(f"      标准化: {normalized}")
    
    # 分析基准文档的标题模式
    print(f"\n=== 基准文档标题模式分析 ===")
    baseline_patterns = analyze_title_patterns(baseline_titles)
    
    print("标题级别分布:")
    for level in sorted(baseline_patterns['by_level'].keys()):
        count = len(baseline_patterns['by_level'][level])
        print(f"  级别 {level}: {count} 个")
        if level <= 3:  # 显示前3级的样本
            for title in baseline_patterns['by_level'][level][:3]:
                print(f"    - {title}")
    
    # 分析我们文档的标题模式
    print(f"\n=== 我们文档标题模式分析 ===")
    our_patterns = analyze_title_patterns(our_titles)
    
    print("标题级别分布:")
    for level in sorted(our_patterns['by_level'].keys()):
        count = len(our_patterns['by_level'][level])
        print(f"  级别 {level}: {count} 个")
    
    # 检查TOC提取问题
    check_toc_extraction_issues()
    
    # 分析可能的原因
    print(f"\n=== 可能的内容丢失原因 ===")
    
    # 1. 检查是否只提取了部分章节
    our_chapters = set()
    baseline_chapters = set()
    
    for title in our_titles:
        normalized = normalize_title(title)
        match = re.match(r'^(\d+(?:\.\d+)*)', normalized)
        if match:
            our_chapters.add(match.group(1))
    
    for title in baseline_titles:
        normalized = normalize_title(title)
        match = re.match(r'^(\d+(?:\.\d+)*)', normalized)
        if match:
            baseline_chapters.add(match.group(1))
    
    missing_chapters = baseline_chapters - our_chapters
    print(f"缺失的章节编号: {sorted(missing_chapters)[:10]}")
    
    # 2. 检查是否存在标题匹配问题
    print(f"\n检查标题匹配问题...")
    
    # 找出基准文档中的1.1前言
    baseline_1_1 = None
    for title in baseline_titles:
        if "1.1" in normalize_title(title) and "前" in normalize_title(title):
            baseline_1_1 = title
            break
    
    our_1_1 = None
    for title in our_titles:
        if "1.1" in normalize_title(title) and "前" in normalize_title(title):
            our_1_1 = title
            break
    
    if baseline_1_1 and our_1_1:
        print(f"基准1.1前言: {baseline_1_1}")
        print(f"我们的1.1前言: {our_1_1}")
        print(f"标题匹配: {normalize_title(baseline_1_1) == normalize_title(our_1_1)}")

if __name__ == "__main__":
    main()
