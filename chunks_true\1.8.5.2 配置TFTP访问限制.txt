

通过 ACL 规则配置客户端登录 TFTP 服务器的访问限制，实现允许当前设备以 TFTP 方式
可以访问哪些 TFTP 服务器。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 243


HUAWEI NetEngine40E
配置指南 1 基础配置

##### 背景信息


ACL （ Access Control List ）是一系列有顺序的规则组的集合，这些规则根据数据包的
源地址、目的地址、端口号等来描述。 ACL 通过规则对数据包进行分类，这些规则应用
到路由设备，路由设备根据这些规则判断哪些数据包可以接收，哪些数据包需要拒
绝。


每个 ACL 中可以定义多个规则，根据规则的功能分为接口 ACL 规则、基本 ACL 规则和高
级 ACL 规则。


请在作为 TFTP 客户端的路由器进行如下的配置。

##### 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **acl** acl-number 或命令 acl-number ，进入基本 ACL 视图。


步骤 **3** 执行命令 **rule** [ rule-id ] [ **name** rule-name ] { **deny** | **permit** } [ [ **fragment** |
**fragment-type** fragment-type-name ] | **source** { source-ip-address sourcewildcard | **any** } | **time-range** time-name | **vpn-instance** vpn-instance-name ] [*] ，
配置基本 ACL 规则。


步骤 **4** 执行命令 **quit** ，退回到系统视图。


步骤 **5** 根据 TFTP 服务器的地址类型，选择执行如下 2 个配置之一。


       - IPv4 类型地址


执行命令 **tftp-server acl** { acl-number | acl-name } ，使用访问控制列表限制本
设备端对 TFTP 服务器的访问。


       - IPv6 类型地址


执行命令 **tftp-server ipv6 acl** { acl-number | acl-name } ，使用访问控制列表限
制本设备端对 TFTP 服务器的访问。


步骤 **6** 执行命令 **commit** ，提交配置。


**----**
结束
