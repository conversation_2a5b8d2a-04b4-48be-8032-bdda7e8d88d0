#!/usr/bin/env python3
"""
全面排查标题匹配问题
"""

import fitz
import re
from main3 import PDFToMarkdownConverter

def comprehensive_debug():
    """全面调试分析"""
    
    with PDFToMarkdownConverter("2.pdf", {"debug_mode": True}) as converter:
        raw_markdown = converter.generate_raw_markdown()
        toc_titles = converter.extract_toc_titles()
        
        print("=== 全面问题分析 ===")
        
        # 1. 分析raw_markdown的结构
        lines = raw_markdown.split('\n')
        print(f"Raw markdown总行数: {len(lines)}")
        
        # 2. 分析TOC标题格式
        print(f"\nTOC标题总数: {len(toc_titles)}")
        print("前10个TOC标题:")
        for i, title in enumerate(toc_titles[:10]):
            print(f"  {i+1}. '{title.text}' (级别: {title.level})")
        
        # 3. 分析raw_markdown中的标题格式
        print("\n=== Raw markdown中的标题格式分析 ===")
        
        # 查找所有可能的标题行
        potential_titles = []
        for i, line in enumerate(lines):
            line_clean = line.strip()
            if not line_clean:
                continue
            
            # 检查是否包含数字编号
            if re.match(r'^\d+(\.\d+)*\s+', line_clean):
                potential_titles.append((i, line_clean))
        
        print(f"找到 {len(potential_titles)} 个潜在标题行")
        print("前20个潜在标题:")
        for i, (line_num, line) in enumerate(potential_titles[:20]):
            print(f"  行{line_num}: {line[:80]}...")
        
        # 4. 测试具体的匹配问题
        print("\n=== 具体匹配测试 ===")
        
        test_cases = [
            "1.2.3 通过Console口登录设备",
            "******* 使用Tab键示例",
            "1.4.3 配置Console用户界面"
        ]
        
        for test_title in test_cases:
            print(f"\n--- 测试标题: {test_title} ---")
            
            # 找到对应的TOC标题
            toc_title = None
            for title in toc_titles:
                if test_title == title.text.strip():
                    toc_title = title
                    break
            
            if not toc_title:
                print(f"❌ 在TOC中未找到完全匹配的标题")
                # 查找相似的
                for title in toc_titles:
                    if test_title in title.text or title.text in test_title:
                        print(f"   相似标题: '{title.text}'")
                continue
            
            print(f"✅ TOC标题: '{toc_title.text}'")
            
            # 在raw_markdown中查找匹配
            matches = []
            
            # 标准化标题
            title_normalized = re.sub(r'\s+', ' ', toc_title.text.strip())
            
            for i, line in enumerate(lines):
                line_clean = line.strip()
                if not line_clean:
                    continue
                
                # 标准化行
                line_normalized = re.sub(r'\s+', ' ', line_clean)
                
                # 精确匹配
                if title_normalized == line_normalized:
                    matches.append(("精确匹配", i, line_clean))
                    continue
                
                # 去除点号后匹配
                line_no_dots = re.sub(r'\.+\s*\d*$', '', line_clean).strip()
                line_no_dots_normalized = re.sub(r'\s+', ' ', line_no_dots)
                if title_normalized == line_no_dots_normalized:
                    matches.append(("去点号匹配", i, line_clean))
                    continue
                
                # 模糊匹配
                if title_normalized in line_normalized:
                    matches.append(("包含匹配", i, line_clean))
            
            print(f"找到 {len(matches)} 个匹配:")
            for match_type, line_num, line_content in matches:
                print(f"  {match_type} - 行{line_num}: {line_content[:60]}...")

            if matches:
                # 选择最后一个匹配（从尾部往前的逻辑）
                best_match = matches[-1]
                print(f"选择: {best_match[0]} - 行{best_match[1]}")
            else:
                # 如果没有匹配，显示详细的字符对比
                print("❌ 没有匹配，进行详细分析:")
                print(f"  TOC标题: '{title_normalized}'")
                print(f"  TOC标题长度: {len(title_normalized)}")
                print(f"  TOC标题字符: {[c for c in title_normalized]}")

                # 查找最相似的行
                for i, line in enumerate(lines):
                    line_clean = line.strip()
                    if test_title[:10] in line_clean:  # 查找包含前10个字符的行
                        line_normalized = re.sub(r'\s+', ' ', line_clean)
                        print(f"  相似行{i}: '{line_normalized}'")
                        print(f"  相似行长度: {len(line_normalized)}")
                        print(f"  相似行字符: {[c for c in line_normalized[:len(title_normalized)+5]]}")
                        break

if __name__ == "__main__":
    comprehensive_debug()
