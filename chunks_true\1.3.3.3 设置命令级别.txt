

如果用户没有对某条命令单独调整过命令级别，命令级别批量提升后，原注册的所有
命令行按以下原则自动调整：


       - 0 级和 1 级命令保持级别不变。


       - 2 级命令提升到 10 级， 3 级命令提升到 15 级。


       - 2 ～ 9 级和 11 ～ 14 级这些命令级别中没有命令行。用户可以单独调整需要的命令行
到这些级别中，以实现权限的精细化管理。


须知


不建议随意修改缺省的命令级别。否则会影响其他用户对命令的使用。

##### 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **command-privilege level rearrange** ，批量提升命令的级别。


步骤 **3** 执行命令 **command-privilege level** level **view** view-name command-key ，设置指
定视图内命令的级别。


所有的命令都有默认的视图和优先级，一般不需要用户进行重新设置。


命令级别分为参观、监控、配置、管理 4 个级别，分别对应标识 0 、 1 、 2 、 3 ，如 表 **1-2**
所示。


表 **1-2** 命令级别简介













|用户<br>级别<br>（0～<br>3）|用户级<br>别（0<br>～<br>15）|命令级<br>别|级别名<br>称|说明|
|---|---|---|---|---|
|0|0|0|参观级|网络诊断工具命令（ping、tracert）、从本设<br>备出发访问外部设备的命令（Telnet客户端）<br>等。|


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 16


HUAWEI NetEngine40E
配置指南 1 基础配置






















|用户<br>级别<br>（0～<br>3）|用户级<br>别（0<br>～<br>15）|命令级<br>别|级别名<br>称|说明|
|---|---|---|---|---|
|1|1～9|0、1|监控级|用于系统维护，包括display等命令。<br>说明<br>并不是所有display命令都是监控级，比如管理配置<br>文件中的**display current-confguration**命令是3级<br>管理级。|
|2|10～<br>14|0、<br>1、2|配置级|业务配置命令。|
|3|15|0、<br>1、<br>2、3|管理级|用于系统基本运行的命令，对业务提供支撑作<br>用，包括文件系统、FTP、TFTP、配置文件切<br>换命令、备板控制命令、用户管理命令、命令<br>级别设置命令，设备重启reboot命令、用于业<br>务故障诊断的debugging命令等。|



步骤 **4** 执行命令 **commit** ，提交配置。


**----**
结束
