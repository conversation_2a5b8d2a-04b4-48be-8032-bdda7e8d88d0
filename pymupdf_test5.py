import fitz  # PyMuPDF
import os
import re
import collections

class PdfExtractor:
    def __init__(self, pdf_path):
        """初始化，打开PDF并执行预处理来识别页眉页脚。"""
        if not os.path.exists(pdf_path):
            raise FileNotFoundError(f"错误：文件 '{pdf_path}' 不存在。")
        
        try:
            self.doc = fitz.open(pdf_path)
            self.toc = self.doc.get_toc()
        except Exception as e:
            raise IOError(f"错误：无法打开或处理PDF文件 '{pdf_path}'. 错误信息: {e}")

        if not self.toc:
            print("警告：此PDF没有目录（书签），无法按章节提取。")

        # 初始化页眉和页脚的边界
        self.header_y = 0
        self.footer_y = self.doc[0].rect.height if self.doc.page_count > 0 else 0
        
        # 执行预处理
        self._identify_header_footer_regions()

    def _identify_header_footer_regions(self, sample_size=10, margin_percent=0.15):
        """
        通过分析示例文本块来自动识别页眉和页脚的垂直边界。
        """
        if self.doc.page_count < 5: # 如果文档太短，则不进行此操作
            return
        
        print("\n正在预处理，尝试识别页眉和页脚...")

        # 为了避免封面和封底的特殊格式，我们从文档中间取样
        start_page = self.doc.page_count // 4
        end_page = min(start_page + sample_size, self.doc.page_count)
        
        potential_hf_blocks = collections.defaultdict(int)
        
        page_height = self.doc[0].rect.height
        header_zone_limit = page_height * margin_percent
        footer_zone_limit = page_height * (1 - margin_percent)

        for page_num in range(start_page, end_page):
            page = self.doc.load_page(page_num)
            blocks = page.get_text("blocks")
            for block in blocks:
                block_rect = fitz.Rect(block[:4])
                block_text = block[4].strip()
                
                # 忽略数字，以处理变化的页码
                normalized_text = re.sub(r'\d+', '', block_text)
                if not normalized_text:
                    continue

                # 如果文本块位于顶部或底部区域，则将其视为潜在的页眉/页脚
                if block_rect.y1 < header_zone_limit or block_rect.y0 > footer_zone_limit:
                    # 使用文本内容和大致的垂直位置作为键
                    key = (normalized_text, round(block_rect.y0 / 10))
                    potential_hf_blocks[key] += 1
        
        # 筛选出在多页中出现的文本块
        # 一个好的候选者至少在超过一半的样本页面中出现
        frequent_blocks = {k: v for k, v in potential_hf_blocks.items() if v > (end_page - start_page) // 2}
        
        max_header_y = 0
        min_footer_y = page_height

        for (text, y_approx), count in frequent_blocks.items():
            # 重新遍历以获取精确的y坐标
            for page_num in range(start_page, end_page):
                page = self.doc.load_page(page_num)
                blocks = page.get_text("blocks")
                for block in blocks:
                    if self._normalize_text(re.sub(r'\d+', '', block[4].strip())) == self._normalize_text(text):
                        block_rect = fitz.Rect(block[:4])
                        if block_rect.y1 < header_zone_limit: # 是页眉
                            max_header_y = max(max_header_y, block_rect.y1)
                        elif block_rect.y0 > footer_zone_limit: # 是页脚
                            min_footer_y = min(min_footer_y, block_rect.y0)
        
        # 增加一点点边距，确保安全
        if max_header_y > 0:
            self.header_y = max_header_y + 5
            print(f"  > 预处理: 已识别页眉区域 (y < {self.header_y:.2f})")
        if min_footer_y < page_height:
            self.footer_y = min_footer_y - 5
            print(f"  > 预处理: 已识别页脚区域 (y > {self.footer_y:.2f})")

    def _normalize_text(self, text):
        """标准化文本，用于模糊匹配。"""
        cleaned = re.sub(r'[^a-zA-Z0-9\s]', '', text)
        return cleaned.lower().replace(" ", "")

    def find_heading_bbox_flexible(self, page_num, text_to_find):
        """在指定页面上灵活地搜索标题文本。"""
        page = self.doc.load_page(page_num)
        rects = page.search_for(text_to_find.strip())
        if rects:
            return rects[0]

        target_norm = self._normalize_text(text_to_find)
        if not target_norm:
             return None

        blocks = page.get_text("blocks")
        for block in blocks:
            # 只在正文区域内搜索标题
            if self.header_y < block[1] < self.footer_y:
                block_text = block[4]
                block_norm = self._normalize_text(block_text)
                if target_norm in block_norm or block_norm in target_norm:
                    print(f"  > 模糊匹配成功: '{text_to_find.strip()}' -> '{block_text.strip()}'")
                    return fitz.Rect(block[:4])
        return None

    def extract_content(self):
        """核心提取逻辑：现在会忽略已识别的页眉页脚区域。"""
        if not self.toc:
            return None

        extracted_data = []
        print(f"\n成功读取到 {len(self.toc)} 个目录项。开始精确提取内容...")

        for i, item in enumerate(self.toc):
            level, title, start_page_num = item
            print(f"\n处理章节: '{title.strip()}' (页码 {start_page_num})")
            start_bbox = self.find_heading_bbox_flexible(start_page_num - 1, title)

            if not start_bbox:
                print(f"  [!!] 警告: 无法在第 {start_page_num} 页找到标题 '{title.strip()}'。跳过此章节。")
                continue

            end_page_num = self.doc.page_count
            end_bbox = None
            if i + 1 < len(self.toc):
                _, next_title, next_page_num = self.toc[i+1]
                end_page_num = next_page_num
                end_bbox = self.find_heading_bbox_flexible(next_page_num - 1, next_title)
            
            content = ""
            for page_idx in range(start_page_num - 1, end_page_num):
                current_page = self.doc.load_page(page_idx)
                blocks = current_page.get_text("blocks")
                blocks.sort(key=lambda b: b[1])

                for block in blocks:
                    block_rect = fitz.Rect(block[:4])
                    # 关键步骤：只处理在正文区域内的文本块
                    if block_rect.y1 < self.header_y or block_rect.y0 > self.footer_y:
                        continue

                    is_after_start = (page_idx > start_page_num - 1) or (block_rect.y1 > start_bbox.y1)
                    is_before_end = True
                    if end_bbox and page_idx == end_page_num - 1:
                        is_before_end = (block_rect.y0 < end_bbox.y0)
                    
                    if is_after_start and is_before_end:
                        content += block[4]

            extracted_data.append({'level': level, 'title': title.strip(), 'content': content})

        return extracted_data
    
    def write_to_markdown(self, data, md_path):
        """将提取的数据写入Markdown文件"""
        if not data:
            print("没有提取到任何内容，不生成Markdown文件。")
            return
        with open(md_path, 'w', encoding='utf-8') as md_file:
            for item in data:
                md_file.write(f"{'#' * item['level']} {item['title']}\n\n")
                md_file.write(item['content'].strip() + "\n\n")
        print(f"\n处理完成！内容已成功写入 '{md_path}'")

    def close(self):
        self.doc.close()


def main():
    """主函数"""
    pdf_file_path = '2.pdf' # <--- 请在这里修改您的PDF文件名

    try:
        extractor = PdfExtractor(pdf_file_path)
        extracted_data = extractor.extract_content()
        md_file_path = os.path.splitext(pdf_file_path)[0] + '.md'
        extractor.write_to_markdown(extracted_data, md_file_path)
    except (FileNotFoundError, IOError) as e:
        print(e)
    finally:
        if 'extractor' in locals() and extractor.doc:
            extractor.close()

if __name__ == "__main__":
    # main()
    import pymupdf4llm
    md_text = pymupdf4llm.to_markdown("2.pdf")
    new_text = re.sub(r'\n# \D', '/#', md_text)
    import pathlib
    pathlib.Path("2.5.md").write_bytes(new_text.encode())