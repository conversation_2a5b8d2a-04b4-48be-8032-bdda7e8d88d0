

STelnet 基于 SSH2 协议，在不安全网络上提供安全的远程访问。

##### 应用环境


网络中有大量设备需要管理与维护，用户不可能为每台设备连接用户终端，特别是终
端与需要管理的设备之间无可达路由时，用户可以使用 Telnet 方式从当前设备登录到网
络上另一台设备，从而实现对远程设备的管理与维护。但是 Telnet 缺少安全的认证方
式，而且传输过程采用 TCP 进行简单方式传输，存在很大的安全隐患。


STelnet 是一种安全的 Telnet 服务，建立在 SSH 连接的基础之上。 SSH 可以利用加密和强
大的认证功能提供安全保障，保护设备不受诸如 IP 地址欺诈等攻击。


SSH 客户端使用 Stelnet 方式登录设备时，如果使用的认证、加密、密钥交换算法为弱
安全算法，设备将提示用户这些算法不安全，建议用户使用更安全的算法或升级客户
端。


用户可执行命令 **display ssh client session** 查看 SSH 客户端使用的认证加密算法，或者
执行命令 **display security risk** **feature ssh_client** 查看 SSH 客户端存在的风险信息及
修复建议。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 72


HUAWEI NetEngine40E
配置指南 1 基础配置

##### 前置任务


在配置用户通过 STelnet 登录系统之前，必须通过 Console 口登录设备，更改设备的缺
省配置，以便用户能够通过 STelnet 方式远程登录设备并实现管理和维护。更改的缺省
配置如下：


       - 配置设备管理网口的 IP 地址，确保终端和登录的设备之间路由可达。


       - 配置 **VTY** 用户界面的用户级别和验证方式 ，实现远程管理和维护设备。


       - 配置 **VTY** 用户界面支持 **SSH** 协议 、 配置 **SSH** 用户并指定服务方式 、 使能 **STelnet** 服
务器功能 ，以便用户能够通过 STelnet 方式远程登录设备。
