

Telnet 是客户          - 服务器应用协议，用户从 Telnet 客户端以 Telnet 方式登录到远端设备，并
对远端设备进行管理与维护。

##### 背景信息


Telnet 提供了一种通过终端远程登录到服务器的方式，呈现一个交互式操作界面。用户
可以先登录到一台设备，再通过 Telnet 的方式远程登录到网络上的其他设备上，对设备
进行配置和管理，而不需要为每一台设备都连接一个硬件终端。


用户可以在设备上指定某一接口，为此接口配置 IP 地址，然后使用该 IP 地址作为 Telnet
连接的源 IP 地址，从而达到安全校验的目的。


说明


Telnet 缺少安全的认证方式，而且传输过程采用 TCP 进行明文传输，存在安全隐患。对于安全性
较高的网络，建议采用 STelnet 方式。

##### 操作步骤


       - 根据 IP 源地址类型，选择执行如下命令：


– 源地址为 IPv4 类型。


执行命令 **telnet** [ **-i** { interface-type interface-number | interface-name } |

[ **vpn-instance** vpn-instance-name ] [ **-a** source-ip-address ] ] host-ipaddress [ port-number ] ，使用 Telnet 协议通过 IPv4 地址登录到 Telnet 服务
器。


– 源地址为 IPv6 类型。


执行命令 **telnet** **ipv6** [ **-a** source-ip6 ] [ **public-net** | **vpn-instance** ipv6vpn-name ] ipv6-address [ **-oi** { interface-type interface-number |
interface-name } ] [ port-number ] ，使用 Telnet 协议通过 IPv6 地址登录到
Telnet 服务器。


**----**
结束


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 237


HUAWEI NetEngine40E
配置指南 1 基础配置
