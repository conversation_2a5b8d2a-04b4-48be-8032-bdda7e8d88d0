

介绍用户在发现 IP 地址配置错误后，希望系统一次性恢复到未配置 IP 地址的状态时的操
作过程。

##### 组网需求


如 图 **1-57** 所示，用户登录路由器，对路由器的各个接口进行 IP 地址的配置。用户在配
置完设备相应接口的 IP 地址之后，发现 IP 地址规划错误，所以需要重新配置接口的 IP 地
址。如果采用传统方法，必须进入每个接口删除相应的 IP 地址，再重新配置。


为了解决上述问题，可以使用配置回退功能，使系统的配置统一恢复至所有接口都尚
未配置 IP 地址的配置状态，这样大大降低了配置恢复的复杂度。


图 **1-57** 配置回退组网图


说明


本例中的 Interface1 ， Interface2 ， Interface3 ， Interface4 分别代表接口 1/0/0 、 GE1/0/1 、 GE1/0/2
和 GE1/0/3 。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 225


HUAWEI NetEngine40E
配置指南 1 基础配置

##### 配置注意事项


无

##### 配置思路


采用如下思路进行配置回退：


1. 进行配置操作后，发现 IP 地址规划错误。


2. 查看配置回退点的信息以及最近若干次配置提交的配置变更。


3. 选择配置回退点或者回退的配置次数进行配置回退。

##### 数据准备


为完成此配置示例，需准备如下数据：


       - 设备的接口名称 GE1/0/0 、 GE1/0/1 、 GE1/0/2 和 GE1/0/3


       - 接口所需要的 IP 地址分别为 ********/30 、 ********/30 、 ********/30 和 ********/30

##### 操作步骤


步骤 **1** 配置设备路由器的 GE1/0/0 、 GE1/0/1 、 GE1/0/2 和 GE1/0/3 的 IP 地址


<HUAWEI> **system-view**

[ ~ HUAWEI] **interface gigabitethernet 1/0/0**

[ ~ HUAWEI-GigabitEthernet1/0/0] **ip address ******** 30**

[*HUAWEI-GigabitEthernet1/0/0] **quit**

[*HUAWEI] **interface gigabitethernet 1/0/1**

[*HUAWEI-GigabitEthernet1/0/1] **ip address ******** 30**

[*HUAWEI-GigabitEthernet1/0/1] **quit**

[*HUAWEI] **interface gigabitethernet 1/0/2**

[*HUAWEI-GigabitEthernet1/0/2] **ip address ******** 30**

[*HUAWEI-GigabitEthernet1/0/2] **quit**

[*HUAWEI] **interface gigabitethernet 1/0/3**

[*HUAWEI-GigabitEthernet1/0/3] **ip address ******** 30**

[*HUAWEI-GigabitEthernet1/0/3] **quit**

[*HUAWEI] **commit description IP address**

[ ~ HUAWEI] **quit**


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 226


HUAWEI NetEngine40E
配置指南 1 基础配置


步骤 **2** 查看配置回退点的信息以及历史配置与当前配置的差异


# 查看配置回退点的信息。


<HUAWEI> **display configuration commit list verbose**
1) CommitId: 1000000006
Label: 
User: root

User-Intf: VTY 1

Type: CLI
TimeStamp: 2012-06-29 15:55:20
Description: IP address


2) CommitId: 1000000005
Label: 
User: root

User-Intf: VTY 0

Type: CLI
TimeStamp: 2012-06-29 11:04:05
Description:


3) CommitId: 1000000004
Label: 
User: root

User-Intf: VTY 0

Type: CLI
TimeStamp: 2012-06-29 09:57:34
Description:


4) CommitId: 1000000003
Label: 
User: root

User-Intf: VTY 0

Type: CLI
TimeStamp: 2012-06-29 09:57:21
Description:


5) CommitId: 1000000002
Label: 
User: anonymous
User-Intf: CON 1023

Type: CLI
TimeStamp: 2012-06-28 16:31:48
Description:


6) CommitId: 1000000001
Label: 
User: anonymous
User-Intf: CON 1023

Type: CLI
TimeStamp: 2012-06-28 16:31:48
Description:


# 查看最近一次配置提交的配置变更。


<HUAWEI> **display configuration commit changes last 1**
Building configuration


#
interface GigabitEthernet1/0/0
+ ip address ******** ***************
#
interface GigabitEthernet1/0/1
+ ip address ******** ***************
#
interface GigabitEthernet1/0/2
+ ip address ******** ***************
#
interface GigabitEthernet1/0/3


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 227


HUAWEI NetEngine40E
配置指南 1 基础配置


+ ip address ******** ***************
#


步骤 **3** 配置回退


# 执行配置回退，将系统回退到最近一个配置回退点之前的历史配置状态。


说明


在本示例中， **rollback configuration last 1** 命令等同于 **rollback configuration to commit-id**
**1000000005** 命令，均是表示系统回退至配置回退点 6 生成之前的历史配置状态。

<HUAWEI> **rollback configuration last 1**


# 配置回退完成后，查看配置回退点是否生成。


<HUAWEI> **display configuration commit list verbose**
1) CommitId: 1000000007
Label: 
User: root

User-Intf: VTY 1

Type: ROLLBACK
TimeStamp: 2012-06-29 15:58:22
Description:


2) CommitId: 1000000006
Label: 
User: root

User-Intf: VTY 1

Type: CLI
TimeStamp: 2012-06-29 15:55:20
Description: IP address


3) CommitId: 1000000005
Label: 
User: root

User-Intf: VTY 0

Type: CLI
TimeStamp: 2012-06-29 11:04:05
Description:


4) CommitId: 1000000004
Label: 
User: root

User-Intf: VTY 0

Type: CLI
TimeStamp: 2012-06-29 09:57:34
Description:


5) CommitId: 1000000003
Label: 
User: root

User-Intf: VTY 0

Type: CLI
TimeStamp: 2012-06-29 09:57:21
Description:


6) CommitId: 1000000002
Label: 
User: anonymous
User-Intf: CON 1023

Type: CLI
TimeStamp: 2012-06-28 16:31:48
Description:


7) CommitId: 1000000001
Label: 
User: anonymous
User-Intf: CON 1023


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 228


HUAWEI NetEngine40E
配置指南 1 基础配置


Type: CLI
TimeStamp: 2012-06-28 16:31:48
Description:


步骤 **4** 验证配置结果


# 通过查看配置文件确定回退已经成功。


<HUAWEI> **display current-configuration interface**
#
interface GigabitEthernet1/0/0
undo shutdown

#
interface GigabitEthernet1/0/1
undo shutdown

#
interface GigabitEthernet1/0/2
undo shutdown

#
interface GigabitEthernet1/0/3
undo shutdown

#


**----**
结束

##### 配置文件


无
