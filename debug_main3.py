#!/usr/bin/env python3
"""
调试main3.py的标题匹配问题
"""

import fitz
import re
from main3 import PDFToMarkdownConverter

def debug_title_matching():
    """调试标题匹配问题"""

    with PDFToMarkdownConverter("2.pdf", {"debug_mode": True}) as converter:
        # 生成raw markdown
        raw_markdown = converter.generate_raw_markdown()

        # 保存raw markdown到文件以便检查
        with open("raw_markdown_debug.txt", "w", encoding="utf-8") as f:
            f.write(raw_markdown)

        print("=== 检查raw markdown结构 ===")
        lines = raw_markdown.split('\n')
        print(f"总行数: {len(lines)}")

        # 查找包含"1.1 前 言"的行
        for i, line in enumerate(lines):
            if "1.1" in line and "前" in line:
                print(f"行 {i}: {line}")
                # 显示前后几行
                start = max(0, i-5)
                end = min(len(lines), i+10)
                print("上下文:")
                for j in range(start, end):
                    marker = ">>> " if j == i else "    "
                    print(f"{marker}{j}: {lines[j]}")
                break

        # 提取TOC标题
        toc_titles = converter.extract_toc_titles()

        print(f"\n=== TOC标题信息 ===")
        for i, title in enumerate(toc_titles[:5]):
            print(f"{i+1}. {title.text} (页码: {title.page_num})")

        # 测试"1.1 前 言"的匹配
        title_11 = None
        for title in toc_titles:
            if "1.1" in title.text and "前" in title.text:
                title_11 = title
                break

        if title_11:
            print(f"\n=== 测试标题匹配: {title_11.text} ===")
            start_pos, end_pos = converter.find_title_in_markdown(title_11)
            print(f"匹配位置: {start_pos}-{end_pos}")

            if start_pos != -1:
                # 显示匹配的文本
                matched_text = raw_markdown[start_pos:end_pos]
                print(f"匹配的文本: '{matched_text}'")

                # 显示后续的一些文本
                following_text = raw_markdown[end_pos:end_pos+500]
                print(f"后续文本: '{following_text}'")
        else:
            print("未找到'1.1 前 言'标题")

if __name__ == "__main__":
    debug_title_matching()
