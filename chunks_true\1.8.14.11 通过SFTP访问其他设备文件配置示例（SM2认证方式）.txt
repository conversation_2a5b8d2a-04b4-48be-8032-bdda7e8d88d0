

在本示例中，通过在 SFTP 客户端和 SSH 服务器端生成本地密钥对，在 SSH 服务器端生
成 SM2 公钥、并为用户绑定该 SM2 公钥，实现 SFTP 客户端连接 SSH 服务器。

##### 组网需求


SFTP 建立在 SSH 连接的基础之上，远程用户可以安全地登录设备，进行文件管理和文
件传送等操作，为数据传输提供了更高的安全保障。同时，由于设备提供了 SFTP 客户
端功能，可以从本设备安全登录到远程 SSH 服务器上，进行文件的安全传输。


如 图 **1-75** 所示， SSH 服务器端 SFTP 服务使能后， SFTP 客户端可以通过 RSA 、 DSA 、
ECC 、 SM2 、 x509v3-ssh-rsa 、 password 、 password-rsa 、 password-ecc 、 passworddsa 、 password-sm2 、 password-x509v3-rsa 和 all 认证方式登录到 SSH 服务器端进行文
件的访问。


图 **1-75** 通过 SFTP 访问其他设备文件组网图


说明


本例中的 Interface1 代表接口 GigabitEthernet0/0/0 。

##### 配置思路


采用如下思路配置 SFTP 客户端连接 SSH 服务器的示例：


1. 在 SSH 服务器上配置用户 client001 和 client002 ，分别使用不同的认证方式登录
SSH 服务器。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 324


HUAWEI NetEngine40E
配置指南 1 基础配置


2. 分别在 SFTP 客户端 Client002 和 SSH 服务器端生成本地密钥对，并为用户 client002
绑定 SSH 客户端的 SM2 公钥，实现客户端登录服务器端时，对客户端进行验证。


3. SSH 服务器端 SFTP 服务使能。


4. 配置 SSH 用户的服务方式和授权目录。


5. 用户 client001 和 client002 分别以 SFTP 方式登录 SSH 服务器，实现访问服务器上的
文件。

##### 数据准备


为完成此配置举例，需准备如下的数据：


       - 用户 Client001 ，登录验证方式为 password 。


       - 用户 Client002 ，验证方式为 SM2 ，并为其分配公钥 sm2key001 。


       - SSH 服务器的 IP 地址为 ********

##### 操作步骤


步骤 **1** 在服务器端生成本地密钥对


<HUAWEI> **system-view**

[*HUAWEI] **sysname SSH Server**

[*HUAWEI] **commit**

[ ~ SSH Server] **ssh server publickey sm2**


步骤 **2** 在服务器端创建 SSH 用户


说明


SSH 用户主要有 Password 、 RSA 、 password-rsa 、 ECC 、 password-ecc 、 DSA 、 password-dsa 、
SM2 、 password-sm2 或 all 这几种认证方式：


          - 如果 SSH 用户的认证方式为 password 、 password-rsa 、 password-dsa 、 password-sm2 和
password-ecc 时，必须配置同名的 local-user 用户。


          - 如果 SSH 用户的认证方式为 RSA 、 password-rsa 、 DSA 、 password-dsa 、 SM2 、 passwordsm2 、 ECC 、 password-ecc 和 all ，服务器端应保存 SSH 客户端的 RSA 、 DSA 、 SM2 或 ECC 公
钥。


# 配置 VTY 用户界面。


[*SSH Server] **user-interface vty 0 4**

[*SSH Server-ui-vty0-4] **authentication-mode aaa**

[*SSH Server-ui-vty0-4] **protocol inbound ssh**

[*SSH Server-ui-vty0-4] **commit**

[ ~ SSH Server-ui-vty0-4] **quit**


       - 创建 SSH 用户 Client001 。


# 新建用户名为 Client001 的 SSH 用户，且认证方式为 password 。


[ ~ SSH Server] **ssh user client001**

[*SSH Server] **ssh user client001 authentication-type password**

[*SSH Server] **ssh server cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr**

[*SSH Server] **ssh server hmac sha2_512 sha2_256**

[*SSH Server] **ssh server key-exchange dh_group_exchange_sha256**

[*SSH Server] **ssh server publickey rsa_sha2_256 rsa_sha2_512**

[*SSH Server] **ssh server dh-exchange min-len 3072**

[*SSH Server] **ssh client publickey rsa_sha2_256 rsa_sha2_512**

[*SSH Server] **ssh client cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr**

[*SSH Server] **ssh client hmac sha2_512 sha2_256**

[*SSH Server] **ssh client key-exchange dh_group_exchange_sha256**

[*SSH Server] **commit**


# 为 SSH 用户 Client001 配置密码。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 325


HUAWEI NetEngine40E
配置指南 1 基础配置


[ ~ SSH Server] **aaa**

[*SSH Server-aaa] **local-user client001 password**
Please configure the password (8-128)
Enter Password:
Confirm Password:


说明


设置的密码必须满足以下要求：


–
密码采取交互式输入，系统不回显输入的密码。


– 输入的密码为字符串形式，区分大小写，长度范围是 8 ～ 16 。输入的密码至少包含两种
类型字符，包括大写字母、小写字母、数字及特殊字符。


–
特殊字符不包括“？”和空格。但是，当输入的密码两端使用双引号时，可在密码中间
输入空格。

#### ▪ 如果使用双引号设置带空格密码，双引号之间不能再使用双引号。 ▪ 如果使用双引号没有设置带空格密码，双引号之间可以再使用双引号。


例如， "Aa 123"45"" 为不合法密码， "Aa123"45"" 为合法密码。


配置文件中将以密文形式体现设置的密码。


[*SSH Server-aaa] **local-user client001 service-type ssh**

[*SSH Server-aaa] **local-user client001 level 3**

[*SSH Server-aaa] **commit**

[ ~ SSH Server-aaa] **quit**


       - 创建 SSH 用户 Client002 。


# 新建用户名为 Client002 的 SSH 用户，且认证方式为 SM2 。


[ ~ SSH Server] **ssh user client002**

[*SSH Server] **ssh user client002 authentication-type sm2**

[*SSH Server] **ssh server cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr**

[*SSH Server] **ssh server hmac sha2_512 sha2_256**

[*SSH Server] **ssh server key-exchange dh_group_exchange_sha256**

[*SSH Server] **ssh server publickey rsa_sha2_256 rsa_sha2_512**

[*SSH Server] **ssh server dh-exchange min-len 3072**

[*SSH Server] **ssh client publickey rsa_sha2_256 rsa_sha2_512**

[*SSH Server] **ssh client cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr**

[*SSH Server] **ssh client hmac sha2_512 sha2_256**

[*SSH Server] **ssh client key-exchange dh_group_exchange_sha256**

[*SSH Server] **ssh authorization-type default root**

[*SSH Server] **commit**


步骤 **3** 配置服务器端 SM2 公钥


# 客户端生成客户端的本地密钥对


<HUAWEI> **system-view**

[ ~ HUAWEI] **sysname client002**

[*HUAWEI] **commit**

[ ~ client002] **ssh client publickey sm2**

[ ~ client002] **sm2 key-pair label sm2key001**
Info: Key pair generation will take a short while. Please wait...
Info: Creating the key pair succeeded.

[ ~ client002] **ssh client assign sm2-host-key sm2key001**

[*client002] **commit**


# 查看客户端上生成 SM2 公钥。


[ ~ client002] **display sm2 key-pair**

=====================================

Label Name: sm2key001
Modulus: 521
Time of Key pair created: 2018-06-19 15:39:45

=====================================

Key :


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 326


HUAWEI NetEngine40E
配置指南 1 基础配置


0474F110 F90F131B B6F6D929 9A23A41E F1AB1666 AC4BE4EE EF2CD876

2B633F80 DD5CF42F 147A722F DE527F39 247F3744 C23296BE FE3BE502

EEF7D9EC BC28A576 7E

=====================================


# 将客户端上产生的 SM2 公钥传送到服务器端。


[ ~ SSH Server] **sm2 peer-public-key sm2key001**
Enter "SM2 public key" view, return system view with "peer-public-key end".

[*SSH Server-sm2-public-key] **public-key-code begin**
Enter "SM2 key code" view, return last view with "public-key-code end".

[*SSH Server-sm2-public-key-sm2-key-code] **0474F110 F90F131B B6F6D929 9A23A41E F1AB1666**

[*SSH Server-sm2-public-key-sm2-key-code] **AC4BE4EE EF2CD876 2B633F80 DD5CF42F 147A722F**

[*SSH Server-sm2-public-key-sm2-key-code] **DE527F39 247F3744 C23296BE FE3BE502 EEF7D9EC**

[*SSH Server-sm2-public-key-sm2-key-code] **BC28A576 7E**

[*SSH Server-sm2-public-key-sm2-key-code] **public-key-code end**

[*SSH Server-sm2-public-key] **peer-public-key end**

[*SSH Server] **commit**


步骤 **4** 为 SSH 用户 Client002 绑定 SSH 客户端的 SM2 公钥。


[ ~ SSH Server] **ssh user client002 assign sm2-key sm2key001**

[*SSH Server] **commit**


步骤 **5** SSH 服务器端 SFTP 服务使能


# 使能 SFTP 服务功能


[ ~ SSH Server] **interface LoopBack 0**

[ ~ SSH Server-LoopBack0] **ip address ******** *****************

[*SSH Server-LoopBack0] **quit**

[*SSH Server] **sftp server enable**

[*SSH Server] **ssh server-source** **-i loopback 0**

[*SSH Server] **commit**


步骤 **6** 配置 SSH 用户的服务方式和授权目录


目前 SSH 服务器端已配置了两个 SSH 用户： Client001 和 Client002 ， Client001 的认证方
式是 password ， Client002 的认证方式是 SM2 。


[*SSH Server] **ssh user client001 service-type sftp**

[*SSH Server] **ssh user client001 sftp-directory cfcard** ：

[*SSH Server] **ssh user client002 service-type sftp**

[*SSH Server] **ssh user client002 sftp-directory cfcard** ：


步骤 **7** SFTP 客户端连接 SSH 服务器


# 第一次登录，需要使能 SSH 客户端首次认证功能。


使能客户端 Client001 首次认证功能。


<HUAWEI> **system-view**

[ ~ HUAWEI] **sysname client001**

[*HUAWEI] **commit**

[ ~ client001] **ssh client first-time enable**

[*client001] **commit**


使能客户端 Client002 首次认证功能


[ ~ client002] **ssh client first-time enable**

[*client002] **commit**


# SFTP 客户端 Client001 用 password 认证方式连接 SSH 服务器。


[ ~ client001] **sftp **********
Trying ******** ...
Press CTRL+K to abort

Connected to ******** ...
Please input the username:client001
Enter password:


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 327


HUAWEI NetEngine40E
配置指南 1 基础配置


# SFTP 客户端 Client002 用 SM2 认证方式连接 SSH 服务器。


[ ~ client002] **sftp **********
Trying ******** ...
Press CTRL+K to abort

Connected to ******** ...
The server is not authenticated. Continue to access it? [Y/N] :y
Save the server's public key? [Y/N] :y
The server's public key will be saved with the name ********. Please wait.
Please input the username: client002


步骤 **8** 检查配置结果


# 查看 SSH 状态信息。


[ ~ SSH Server] **display ssh server status**
SSH Version                : 2.0
SSH authentication timeout (Seconds)    : 60
SSH authentication retries (Times)     : 3
SSH server key generating interval (Hours) : 0
SSH version 1.x compatibility       : Enable
SSH server keepalive            : Disable
SFTP IPv4 server              : Enable

SFTP IPv6 server              : Enable

STELNET IPv4 server            : Enable

STELNET IPv6 server            : Enable

SNETCONF IPv4 server            : Enable

SNETCONF IPv6 server            : Enable
SNETCONF IPv4 server port(830)       : Disable
SNETCONF IPv6 server port(830)       : Disable
SCP IPv4 server              : Enable

SCP IPv6 server              : Enable
SSH port forwarding            : Disable
SSH IPv4 server port            : 22
SSH IPv6 server port            : 22
ACL name                  :

ACL number                 :

ACL6 name                 :

ACL6 number                :
SSH server ip-block            : Enable


# 查看 SSH 用户信息。


[ ~ SSH Server] **display ssh user-information**

---------------------------------------------------
Username        : client001
Authentication-type   : password
User-public-key-name  :
User-public-key-type  : Sftp-directory     : cfcard:
Service-type      : sftp


Username        : client002
Authentication-type   : sm2
User-public-key-name  : sm2key001
User-public-key-type  : SM2
Sftp-directory     : cfcard:
Service-type      : sftp

---------------------------------------------------
Total 2, 2 printed


**----**
结束

##### 配置文件


       - SSH 服务器上的配置文件


#

sysname SSH Server


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 328


HUAWEI NetEngine40E
配置指南 1 基础配置


#
interface GigabitEthernet0/0/0
undo shutdown
ip address ******** ***********
#
sm2 peer-public-key sm2key001
public-key-code begin
0474F110 F90F131B B6F6D929 9A23A41E F1AB1666

AC4BE4EE EF2CD876 2B633F80 DD5CF42F 147A722F

DE527F39 247F3744 C23296BE FE3BE502 EEF7D9EC

BC28A576 7E

#
interface loopback 0
ip address ******** ***************
sftp server enable
ssh server-source -i loopback 0
ssh user client001
ssh user client001 authentication-type password
ssh user client001 sftp-directory cfcard:
ssh user client001 service-type sftp
ssh user client002
ssh user client002 assign sm2-key sm2key001
ssh user client002 authentication-type sm2
ssh authorization-type default root
ssh user client002 sftp-directory cfcard:
ssh user client002 service-type sftp
#
ssh server cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr
ssh server hmac sha2_512 sha2_256
ssh server key-exchange dh_group_exchange_sha256
#
ssh server publickey rsa_sha2_256 rsa_sha2_512
#
ssh server dh-exchange min-len 3072
#
ssh client publickey dsa ecc rsa rsa_sha2_256 rsa_sha2_512
#
ssh client cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr
ssh client hmac sha2_512 sha2_256
ssh client key-exchange dh_group_exchange_sha256
#

aaa
local-user client001 password cipher @%@%UyQs4,KTtSwJo(4QmW#K,LC:@%@%
local-user client001 level 3
local-user client001 service-type ssh
#
user-interface vty 0 4
authentication-mode aaa
protocol inbound ssh
#

return

       - SSH 客户端 Client001 的配置文件


#
sysname client001
#
interface GigabitEthernet0/0/0
undo shutdown
ip address ******** ***********
#
ssh client first-time enable
#

return

       - SSH 客户端 Client002 的配置文件


#
sysname client002
#
interface GigabitEthernet0/0/0
undo shutdown


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 329


HUAWEI NetEngine40E
配置指南 1 基础配置


ip address ******** ***********
#
ssh client first-time enable
ssh client assign sm2-host-key sm2key001
#
ssh client publickey sm2
#

return
