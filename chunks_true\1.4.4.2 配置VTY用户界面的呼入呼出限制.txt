

用户可以通过访问控制列表（ ACL ），实现对 VTY 用户界面的呼入呼出进行限制。

##### 背景信息


确定了对 Telnet 的控制策略，包括对哪些源 IP 、目的 IP 进行控制，控制的动作是允许访
问还是拒绝访问。


       - 通过基本 ACL （ 2000 ～ 2999 ）控制列表实现通过源 IP 地址对 Telnet 进行限制。


       - 通过高级 ACL （ 3000 ～ 3999 ）控制列表实现通过源 IP 地址和目的 IP 地址对 Telnet 进
行限制。

##### 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 相比基本 ACL ，高级 ACL 支持的过滤规则更加丰富。除了像基本 ACL ，可以针对报文源
地址进行过滤，高级 ACL 还可以针对报文目的地址、报文优先级进行过滤。请根据情况
不同，执行如下命令：


       - 对于基本 ACL ：


执行命令 **acl** { **name** basic-acl-name { **basic** | [ **basic** ] **number** basic-aclnumber } | [ **number** ] basic-acl-number } [ **match-order** { **config** | **auto** } ] 或
**acl ipv6** { **name** basic-acl6-name **basic** | [ **number** ] basic-acl6-number }

[ **match-order** { **config** | **auto** } ] ，进入 ACL 或 ACL6 视图。


       - 对于高级 ACL ：


执行命令 **acl** { **name** advance-acl-name [ **advance** | [ **advance** ] **number**
advance-acl-number ] | [ **number** ] advance-acl-number } [ **match-order**
{ **config** | **auto** } ] 或 **acl ipv6** { **name** advance-acl6-name [ **advance** |


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 43


HUAWEI NetEngine40E
配置指南 1 基础配置


[ **advance** ] **number** advance-acl6-number ] | [ **number** ] advance-acl6number } [ **match-order** { **config** | **auto** } ] ，进入 ACL 或 ACL6 视图。


～ ～
用户界面支持基本访问控制列表（ 2000 2999 ）和高级访问控制列表（ 3000
3999 ）。


步骤 **3** 根据情况不同，执行如下命令：


       - 对于基本访问控制列表：


执行命令 **rule** [ rule-id ] [ **name** rule-name ] { **deny** | **permit** } [ **fragment-**
**type** { **fragment** | **non-fragment** | **non-subseq** | **fragment-subseq** |
**fragment-spe-first** } | **source** { source-ip-address { source-wildcard | **0** | srcnetmask } | **any** } | **time-range** time-name | [ **vpn-instance** vpn-instancename | **vpn-instance-any** ] ] [*] 或 **rule** [ rule-id ] [ **name** rule-name ] { **deny** |
**permit** } [ **fragment** | **source** { source-ipv6-address { prefix-length | sourcewildcard } | source-ipv6-address/prefix-length | **any** } | **time-range** time-name
| [ **vpn-instance** vpn-instance-name | **vpn-instance-any** ] ] [*] ，配置基本 ACL 或
基本 ACL6 规则。


       - 对于高级访问控制列表：


执行命令 **rule** [ rule-id ] [ **name** rule-name ] { **deny** | **permit** } { protocol | **gre**
| **ip** | **ipinip** | **igmp** | **ospf** } [ [ **dscp** dscp | [ **precedence** precedence | **tos** tos ]
                   - ] | { **destination** { destination-ip-address { destination-wildcard | **0** | desnetmask } | **any** } | **destination-pool** destination-pool-name } | **fragment-**
**type** { **fragment** | **non-fragment** | **non-subseq** | **fragment-subseq** |
**fragment-spe-first** } | { **source** { source-ip-address { source-wildcard | **0** | srcnetmask } | **any** } | **source-pool** source-pool-name } | **time-range** time-name
| [ **vpn-instance** vpn-instance-name | **vpn-instance-any** ] | **ttl** ttl-operation
ttl-value | **packet-length** length-operation length-value ] [*] 或 **rule** [ rule-id ]

[ **name** rule-name ] { **permit** | **deny** } { **hoport** [ **option-code** option-value ] |
**1** | **5** | protocol | **gre** | **ipv6** | **ipv6-frag** | **ipv6-ah** | **ipv6-esp** | **ospf** | 7-16 |
18-42 | { 43 | **ipv6-routing** } [ **routing-type** routing-number ] | 44-57 | 59 |
{ 60 | **ipv6-destination** } [ **option-code** option-value ] | 61-255 }

[ **destination** { destination-ipv6-address { prefix-length | destinationwildcard } | dest-ipv6-addr-prefix | **any** } | **fragment** | { **source** { source-ipv6address { prefix-length | source-wildcard } | src-ipv6-addr-prefix | **any** } |
**source-pool** source-pool-name } | **time-range** time-name | [ **dscp** dscp |

[ **precedence** { precedence | **critical** | **flash** | **flash-override** | **immediate** |
**internet** | **network** | **priority** | **routine** } | **tos** { tos | **max-reliability** | **max-**
**throughput** | **min-delay** | **min-monetary-cost** | **normal** } ] [*] ] | [ **vpn-**
**instance** vpn-instance-name | **vpn-instance-any** ] ] [*] ，配置高级 ACL 或高级
ACL6 规则。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 44


HUAWEI NetEngine40E
配置指南 1 基础配置


说明


          - 如果登录用户匹配上了 ACL ，缺省对所有登录用户执行 deny 动作。即：如果登录用户匹配上
的 ACL 规则的动作是 permit ，那么用户可以登录设备，其他用户则被禁止；如果登录用户匹
配上的 ACL 规则的动作是 deny ，那么该用户被禁止登录。


例如：如果需要在限制某用户的同时，让其他用户正常登录，那么还需要在 ACL 规则中配置
permit 操作。例如，配置限制源 IP 为 ********* 的用户登录，那么 ACL 中应当定义两条规则：


–
**rule deny source ********* 0**


–
**rule permit source any**


如果没有定义 **rule permit source any** ，其他源 IP 地址不是 ********* 的用户也会被限制。


          - 如果登录用户不在 ACL 规则指定的允许范围内，那么此用户被禁止登录。


          - 如果 VTY 引用的 ACL 中不存在规则或者引用的 ACL 不存在，那么用户登录时不受 ACL 的控制，
用户可以登录设备。


步骤 **4** 执行命令 **quit** ，返回系统视图。


步骤 **5** 执行命令 **user-interface** **vty** first-ui-number [ last-ui-number ] ，进入 VTY 用户界面
视图。


步骤 **6** 执行命令 **acl** [ **ipv6** ] { acl-number | acl-name } { **inbound** | **outbound** } ，配置 VTY
类型用户界面的呼入呼出限制。


       - 当需要限制某个地址或地址段的用户登录到设备时，选择参数 **inbound** 。


       - 当需要限制已经登录的用户登录到其它设备时，选择参数 **outbound** 。


步骤 **7** 执行命令 **commit** ，提交配置。


**----**
结束
