

FTP 服务器的侦听端口号变更后，只有知道当前侦听端口号的用户才能访问设备，确保
安全性。

##### 背景信息


如果使用标准的侦听端口号，可能会有攻击者不断访问此端口，导致带宽和服务器性
能的下降，造成其他正常用户无法访问。所以可以重新配置 FTP 服务器的侦听端口号，
使攻击者无法获知更改后的 FTP 服务器侦听端口号，有效的防止了攻击者对 FTP 服务标
准端口的访问。


说明


如果变更端口号前 FTP 服务已经启动， FTP 服务将重新启动。


请在作为 FTP 服务器的设备上进行如下的配置。

##### 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **ftp** [ **ipv6** ] **server port** port-number ，变更 FTP 服务器端口号。


如果配置了新的侦听端口号， FTP 服务器端会先断开当前已经建立的所有 FTP 连接，然
后使用新的端口号开始侦听。


步骤 **3** 执行命令 **commit** ，提交配置。


**----**
结束
