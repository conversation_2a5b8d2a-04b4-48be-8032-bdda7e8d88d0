

为了节约系统资源，及保证合法用户能够成功登录 FTP 服务器，需要及时断开与 FTP 服
务器连接。

##### 背景信息


登录 FTP 服务器的用户数达到最大值后，其他合法的用户将无法成功登录。为了保证合
法用户能够成功登录 FTP 服务器，需要及时断开与 FTP 服务器连接。

##### 操作步骤


步骤 **1** 根据服务器端 IP 地址类型不同，进行如下操作：


       - 执行命令 **ftp** [ [ **-a** source-ip-address | **-i** { interface-type interface-number |
interface-name } ] host-ip [ port-number ] [ **vpn-instance** vpn-instancename ] | **public-net** ] ，设备使用 IPv4 地址与 FTP 服务器建立连接，进入 FTP 客户
端视图。


       - 执行命令 **ftp** **ipv6** [ **-a** source-ip6 ] host-ipv6-address [ [ **vpn-instance** ipv6vpn-instance-name ] | **public-net** ] [ **-oi** { interface-type interface-number |
interface-name } ] [ port-number ] ，设备使用 IPv6 地址与 FTP 服务器建立连接，
进入 FTP 客户端视图。


步骤 **2** 断开 FTP 连接，根据需要，以下步骤任选其一：


       - 执行命令 **bye/quit** 终止与 FTP 服务器的连接，并退回到用户视图。


       - 执行命令 **close/disconnect** 终止与 FTP 服务器的连接，并终止 FTP 会话，仍保持在
FTP 客户端视图。


**----**
结束
