

系统提供 AAA 验证和密码验证方式，配置用户验证方式可以提高设备的安全性。

##### 背景信息


说明


**password** 验证方式不安全，强烈建议用户使用 **aaa** 验证方式。

##### 操作步骤


       - 设置 AAA 验证


a. 执行命令 **system-view** ，进入系统视图。

b. 执行命令 **user-interface** **vty** first-ui-number [ last-ui-number ] ，进入 VTY
用户界面视图。


c. 执行命令 **authentication-mode** **aaa** ，设置用户验证方式为 AAA 验证。


d. 执行命令 **commit** ，提交配置。


e. 执行命令 **quit** ，退出 VTY 用户界面视图。


f. 执行命令 **aaa** ，进入 AAA 视图。


g. 执行命令 **local-user** user-name **password** [ **cipher** password |
**irreversible-cipher** irreversible-cipher-password ] ，配置本地用户名和密
码。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 46


HUAWEI NetEngine40E
配置指南 1 基础配置

#### ▪ 不选择 cipher 或 irreversible-cipher 关键字时，密码以交互式输入，系统

不回显密码。


输入的密码为字符串形式，区分大小写，开启用户账户安全策略时，取
值范围是 8 ～ 128 。关闭用户账户安全策略时，长度范围是 1 ～ 128 。开启
用户账户安全策略时，密码不能与用户名及其用户名反向字符串相同，
且密码必须包括大写字母、小写字母、数字及特殊字符。


说明


特殊字符不包括“？”和空格。但是，当输入的密码两端使用双引号时，可在
密码中间输入空格。


                - 如果使用双引号设置带空格密码，双引号之间不能再使用双引号。


                - 如果使用双引号没有设置带空格密码，双引号之间可以再使用双引号。


例如， "Aa 123"45"" 为不合法密码， "Aa123"45"" 为合法密码。

#### ▪ 选择 cipher 关键字时，密码可以以简单形式输入，也可以以密文形式输

入。


密码以简单形式输入，要求与不选择 **cipher** 关键字时一样。密码以简单
形式输入，系统会回显简单形式的密码，存在安全风险，因此建议使用
交互式输入密码。


无论是简单输入还是密文输入，配置文件中都以密文形式体现。

#### ▪ 选择 irreversible-cipher 关键字时，密码可以以简单形式输入，也可以

以不可逆密文形式输入。


密码以简单形式输入，要求与不选择 **irreversible-cipher** 关键字时一
样。


无论是简单输入还是不可逆密文输入，配置文件中都以密文形式体现。


h. 执行命令 **commit** ，提交配置。


       - 设置密码验证


a. 执行命令 **system-view** ，进入系统视图。

b. 执行命令 **user-interface** **vty** first-ui-number [ last-ui-number ] ，进入 VTY
用户界面视图。


c. 执行命令 **authentication-mode** **password** ，设置用户验证方式为密码验
证。


d. 执行命令 **set authentication password** [ **cipher** password ] ，更改本地验
证密码。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 47


HUAWEI NetEngine40E
配置指南 1 基础配置


说明


              - 不选择 **cipher** 关键字时，密码以交互式输入，系统不回显密码。


              - 输入的密码为字符串形式，区分大小写，长度范围是 8 ～ 16 。输入的密码至少包含
两种类型字符，包括大写字母、小写字母、数字及特殊字符。


              - 特殊字符不包括“？”和空格。但是，当输入的密码两端使用双引号时，可在密
码中间输入空格。


               - 如果使用双引号设置带空格密码，双引号之间不能再使用双引号。


               - 如果使用双引号没有设置带空格密码，双引号之间可以再使用双引号。


例如， "Aa 123"45"" 为不合法密码， "Aa123"45"" 为合法密码。


              - 选择 **cipher** 关键字时，密码可以以简单形式输入，也可以以密文形式输入。


               - 密码以简单形式输入，要求与不选择 **cipher** 关键字时一样。密码以简单形式
输入，系统会回显简单形式的密码，存在安全风险，因此建议使用交互式输
入密码。


               - 无论是简单输入还是密文输入，配置文件中都以密文形式体现。


如果用户通过命令 **undo authentication-mode** ，取消了配置登录用户界面的验证模
式，那么将无法通过命令 **set authentication password** [ **cipher** password ] 更改验
证的密码。


e. 执行命令 **commit** ，提交配置。


**----**
结束
