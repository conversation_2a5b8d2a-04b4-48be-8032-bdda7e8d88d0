

用户可以使用 FTP 命令从作为 FTP 客户端的设备登录到其他设备。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 246


HUAWEI NetEngine40E
配置指南 1 基础配置

##### 背景信息


在用户视图和 FTP 客户端视图下，用户均可以使用相应命令与远程 FTP 服务器建立连
接。


说明


          - 在用户视图下执行命令 **ftp** 建立与远程 FTP 服务器建立控制连接时，如果不选择任何参数，则
仅进入 FTP 客户端视图，并未与任何 FTP 服务器建立连接。


          - 无论是在用户视图下执行命令 **ftp** 还是在 FTP 客户端视图下执行命令 **open** 建立与远程 FTP 服务
器建立控制连接时，如果 FTP 服务器的侦听端口是缺省端口号，则不需要设置端口号，否则
需要重新指定端口号。


          - 在登录 FTP 服务器之前，可以执行命令 **set net-manager vpn-instance** ，设置默认的 VPN 实
例。执行该命令后，进行 FTP 操作时所使用的 VPN 实例即用户配置的默认 VPN 实例。


请根据服务器端 IP 地址类型不同，在作为客户端的设备上任选如下其中一种配置。

##### 操作步骤


       - 基于服务器端是 IPv4 地址，使用 FTP 命令连接其他设备如 表 **1-38** 所示。


表 **1-38** 使用 FTP 命令连接其他设备

|视图|操作|
|---|---|
|用户视图|执行命令**ftp** [**-a** source-ip-address | {**-i** interface-type<br>interface-number | interface-name } ] host-ip [ port-number ]<br>[ **public-net |** **vpn-instance** vpn-instance-name ]，与FTP服务<br>器建立连接。|
|FTP视图|执行命令**open** [**-a** source-ip | {**-i** interface-type interface-<br>number | interface-name } ] host-ip-address [ port-number ]<br>[ **public-net |** **vpn-instance** vpn-instance-name ]，与FTP服务<br>器建立连接。|



       - 基于服务器端是 IPv6 地址，使用 FTP 命令连接其他设备如 表 **1-39** 所示。


表 **1-39** 使用 FTP 命令连接其他设备


**----**

|视图|操作|
|---|---|
|用户视图|执行命令**ftp** **ipv6** [**-a** source-ip6 ] host-ipv6-address [ [**vpn-**<br>**instance** ipv6-vpn-instance-name ] |**public-net** ] [**-oi**<br>{ interface-type interface-number | interface-name } ] [ port-<br>number ]，与FTP服务器建立连接。|
|FTP视图|执行命令**open** **ipv6** [**-a** source-ip6 ] host-ipv6-address [**-oi**<br>{ **-i** interface-type interface-number | interface-name } ]<br>[ port-number ] [**vpn-instance** vpn-instance |**public-net** ]，<br>与FTP服务器建立连接。|

结束


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 247


HUAWEI NetEngine40E
配置指南 1 基础配置
