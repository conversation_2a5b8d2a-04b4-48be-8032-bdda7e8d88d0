

设备首次上电时，还可以基于即插即用功能通过 DCN 或 DHCP 方式批量部署设备。

##### 背景信息


在构建大型网络时，为了实现对设备的远程统一管理和控制，以提高部署效率和降低
运维成本，设备首次上电时还可以基于即插即用功能批量部署设备，主要通过以下两
种方式：


       - DCN ：设备上电后，所有网元由网元 ID （即 NEID ）自动生成 IP 地址（即 NEIP ），
并通过 OSPF 扩散 LSA 形成一张由 NEIP 和 NEID 组成的核心路由表。网管通过网关网
元的 IP 地址和网关网元上送的目的网元 ID 进行访问，并通过网关网元远程对网络
中的所有网元进行管理。


       - DHCP ：通过 DHCP 方式实现的即插即用功能主要表现为 ZTP （ Zero Touch
Provisioning ，零配置自动部署），它是一种空配置设备上电启动时可以从文件服
务器获取版本文件并自动加载的功能。


说明


设备如果在没有加载带账号密码的默认配置文件的情况下上电启动，则会进入 first-login 流程
（即需要新创建用户并设置密码），导致通过即插即用功能上线失败。在上述过程中，提示信息
和交互确认信息通过 SSH 标准协议中的 SSH_MSG_CHANNEL_DATA 字段在设备和网管间交互传
递。报文结构的定义如下：


byte   SSH_MSG_CHANNEL_DATA
uint32  recipient channel
string  data

##### 操作步骤


步骤 **1** 上电启动设备。


步骤 **2** 设备根据实际状态进入相应的即插即用流程：


       - 在实现 DCN 配置的情况下，网管与设备间建立通信网络，网管可以直接对网元进
行远程管理。


       - 在 **1.9.3** 配置通过 **DHCP** 实现 **ZTP** 自动部署 的情况下，设备可以从文件服务器获取
版本文件并自动加载。


**----**
结束


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 13


HUAWEI NetEngine40E
配置指南 1 基础配置
