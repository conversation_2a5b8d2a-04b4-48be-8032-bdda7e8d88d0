

设备作为 SFTP 客户端，使用一键式文件操作命令，从本地上传文件到 SFTP 服务器，或
者从 SFTP 服务器下载文件至本地。

##### 前提条件


已完成 SFTP 服务器端的配置，且 SFTP 客户端和服务器端的路由相通。

##### 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。

步骤 **2** 执行命令 **ssh client first-time enable** ，使能 SSH 客户端首次认证功能。


步骤 **3** 执行命令 **commit** 提交配置。


步骤 **4** 基于网络协议执行如下操作步骤：


       - 基于 IPv4 建立 SFTP 连接

执行命令 **sftp client-transfile** { **get** | **put** } [ **-a** source-address | **-i** { interfacetype interface-number | interface-name } ] **host-ip** host-ipv4 [ port ]

[ [ **public-net** | **-vpn-instance** vpn-instance-name ] | [ **prefer_kex**
{ prefer_kex } ] | [ **identity-key** identity-key-type ] | [ **prefer_ctos_cipher**
prefer_ctos_cipher ] | [ **prefer_stoc_cipher** prefer_stoc_cipher ] |

[ **prefer_ctos_hmac** prefer_ctos_hmac ] | [ **prefer_stoc_hmac**
prefer_stoc_hmac ] | [ **-ki** interval ] | [ **-kc** count ] ] [*] **username** user-name
**password** password **sourcefile** destination [ **destination** source-file ] ，通过
IPv4 方式连接 SFTP 服务器，并从服务器上下载文件至 SFTP 客户端或者从 SFTP 客户
端上传文件至服务器。


       - 基于 IPv6 建立 SFTP 连接

执行命令 **sftp client-transfile** { **put** | **get** } **ipv6** [ **-a** source-ipv6-address ]
**host-ip** host-ipv6 [ **-oi** { interface-type interface-number | interface-name } ]

[ port ] [ [ **public-net** | **-vpn-instance** vpn-instance-name ] | [ **prefer_kex**
{ prefer_kex } ] | [ **identity-key** identity-key-type ] | [ **prefer_ctos_cipher**
prefer_ctos_cipher ] | [ **prefer_stoc_cipher** prefer_stoc_cipher ] |

[ **prefer_ctos_hmac** prefer_ctos_hmac ] | [ **prefer_stoc_hmac**
prefer_stoc_hmac ] | [ **-ki** interval ] | [ **-kc** count ] ] [*] **username** user-name
**password** password **sourcefile** source-file [ **destination** destination ] ，通过
IPv6 方式连接 SFTP 服务器，并从服务器上下载文件至 SFTP 客户端或者从 SFTP 客户
端上传文件至服务器。


**----**
结束


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 259


HUAWEI NetEngine40E
配置指南 1 基础配置
