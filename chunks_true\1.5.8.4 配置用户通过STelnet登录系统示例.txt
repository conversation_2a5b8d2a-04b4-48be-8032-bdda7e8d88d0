

本示例介绍用户终端通过 STelnet 方式登录 SSH 服务器，实现 STelnet 远程登录、管理设
备。

##### 组网需求


网络中有大量设备需要管理与维护，用户不可能为每台设备连接用户终端，特别是终
端与需要管理的设备之间无可达路由时，用户可以使用 Telnet 方式从当前设备登录到网
络上另一台设备，从而实现对远程设备的管理与维护。但是 Telnet 缺少安全的认证方
式，而且传输过程采用 TCP 进行简单方式传输，存在很大的安全隐患。


STelnet 是一种安全的 Telnet 服务，建立在 SSH 连接的基础之上。 SSH 可以利用加密和强
大的认证功能提供安全保障，保护设备不受诸如 IP 地址欺诈等攻击。


如 图 **1-32** 所示，在作为 SSH 服务器的路由器上使能 STelnet 服务器功能后， SSH 客户端
PC 可以通过 RSA 、 DSA 、 ECC 、 SM2 、 x509v3-ssh-rsa 、 x509v3-ecdsa-sha2 、
password 、 password-rsa 、 password-ecc 、 password-dsa 、 password-sm2 、
password-x509v3-rsa 、 password-x509v3-ecdsa-sha2 和 all 认证方式登录 SSH 服务
器。这里以 RSA 认证方式为例介绍客户端通过 STelnet 登录服务器的配置过程。


为了提升系统安全性，防止非法用户登录到 SSH 服务器，用户可以在 SSH 服务器上配置
ACL 规则。


图 **1-32** 配置用户通过 STelnet 登录系统组网图


说明


本例中的 Interface1 代表接口 GigabitEthernet0/0/0 。


这里以管理网口为例介绍 STelnet 登录的配置方法，实际应用场景中，用户根据组网选择使用客户端
和服务器路由相通的端口。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 115


HUAWEI NetEngine40E
配置指南 1 基础配置

##### 配置思路


采用如下的思路配置 SSH 用户通过 STelnet 登录服务器：


1. 通过 Console 口登录并配置 SSH 服务器的管理网口 IP 地址。


2. 配置 SSH 服务器的 VTY 用户界面。


3. 创建本地用户，并配置认证方式和服务类型。


4. 创建 SSH 用户，并配置认证方式。


5. SSH 客户端根据配置的 SSH 用户认证类型创建相应的密钥对，并将公钥拷贝至 SSH
服务器。


6. SSH 服务端编辑公钥，并将编辑好的公钥分配给用户。


7. 使能 SSH 服务器的 STelnet 功能，配置 SSH 用户的服务类型为 STelnet 。


8. 在 SSH 服务器上配置允许 STelnet 客户端登录的 ACL 规则。


9. 配置客户端登录软件的参数， STelnet 至服务器。

##### 数据准备


为完成此配置示例，需准备如下数据：


       - SSH 客户端已安装 PuTTYGen.exe 和 PuTTY.exe 软件。


       - SSH 服务器管理网口的 IP 地址 **************/24 。


       - 本地用户的认证方式为 password ，用户名为“ huawei123 ”，密码为
“ YsHsjx_202206 ”。


       - SSH 用户的认证方式为 RSA 。


       - 配置基本的 ACL 2000 ，允许 ************/24 网段的客户端合法接入 SSH 服务器。

##### 操作步骤


步骤 **1** 配置 SSH 服务器的管理网口 IP 地址。


<HUAWEI> **system-view**

[ ~ HUAWEI] **sysname SSH Server**

[*HUAWEI] **commit**

[ ~ SSH Server] **interface GigabitEthernet0/0/0**

[ ~ SSH Server-GigabitEthernet0/0/0] **undo shutdown**

[*SSH Server-GigabitEthernet0/0/0] **ip address ************** ***************

[*SSH Server-GigabitEthernet0/0/0] **commit**

[ ~ SSH Server-GigabitEthernet0/0/0] **quit**


步骤 **2** 配置 SSH 服务器的 VTY 用户界面。


[ ~ SSH Server] **user-interface vty 0 4**

[*SSH Server-ui-vty0-4] **authentication-mode aaa**

[*SSH Server-ui-vty0-4] **protocol inbound ssh**


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 116


HUAWEI NetEngine40E
配置指南 1 基础配置


[*SSH Server-ui-vty0-4] **user privilege level 3**

[*SSH Server-ui-vty0-4] **commit**

[ ~ SSH Server-ui-vty0-4] **quit**


说明


若配置登录协议为 SSH ，则设备将自动禁止 Telnet 功能。


步骤 **3** 在服务端创建本地用户，将用户加入管理员组，并配置用户的服务方式。


[ ~ SSH Server] **aaa**

[ ~ SSH Server-aaa] **local-user huawei123 password**
Please configure the password (8-128)
Enter Password:
Confirm Password:


说明


          - 不选择 **cipher** 或 **irreversible-cipher** 关键字时，密码以交互式输入，系统不回显密码。


输入的密码为字符串形式，区分大小写，开启用户账户安全策略时，取值范围是 8 ～ 128 。关
闭用户账户安全策略时，长度范围是 1 ～ 128 。开启用户账户安全策略时，密码不能与用户名
及其用户名反向字符串相同，且密码必须包括大写字母、小写字母、数字及特殊字符。


特殊字符不包括“？”和空格。但是，当输入的密码两端使用双引号时，可在密码中间输入
空格。


–
如果使用双引号设置带空格密码，双引号之间不能再使用双引号。


–
如果使用双引号没有设置带空格密码，双引号之间可以再使用双引号。


例如， "Aa 123"45"" 为不合法密码， "Aa123"45"" 为合法密码。


          - 选择 **cipher** 关键字时，密码可以以简单形式输入，也可以以密文形式输入。


密码以简单形式输入，要求与不选择 **cipher** 关键字时一样。密码以简单形式输入，系统会回
显简单形式的密码，存在安全风险，因此建议使用交互式输入密码。


无论是简单输入还是密文输入，配置文件中都以密文形式体现。


          - 选择 **irreversible-cipher** 关键字时，密码可以以简单形式输入，也可以以不可逆密文形式输
入。


密码以简单形式输入，要求与不选择 **irreversible-cipher** 关键字时一样。


无论是简单输入还是不可逆密文输入，配置文件中都以密文形式体现。


[*SSH Server-aaa] **local-user huawei123 service-type ssh**

[*SSH Server-aaa] **local-user huawei123 user-group manage-ug**

[*SSH Server-aaa] **commit**

[ ~ SSH Server-aaa] **quit**


步骤 **4** 在服务端创建 SSH 用户，并配置认证方式。


[ ~ SSH Server] **ssh user huawei123**

[*SSH Server] **ssh user huawei123 authentication-type rsa**

[*SSH Server] **ssh server cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr**

[*SSH Server] **ssh server hmac sha2_512 sha2_256**

[*SSH Server] **ssh server key-exchange dh_group_exchange_sha256**

[*SSH Server] **ssh server publickey rsa_sha2_256 rsa_sha2_512**

[*SSH Server] **ssh server dh-exchange min-len 3072**

[*SSH Server] **ssh client publickey rsa_sha2_256 rsa_sha2_512**

[*SSH Server] **ssh client cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr**

[*SSH Server] **ssh client hmac sha2_512 sha2_256**

[*SSH Server] **ssh client key-exchange dh_group_exchange_sha256**

[*SSH Server] **commit**


步骤 **5** SSH 客户端使用 PuTTY 创建 RSA 密钥对，并将公钥拷贝至 SSH 服务器。


1. 进入 Windows 的命令行提示符。


2. 输入 PuTTYGen ，单击“ Generate ”，产生密钥对，如 图 **1-33** 所示。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 117


HUAWEI NetEngine40E
配置指南 1 基础配置


图 **1-33** 生成客户端密钥对（ 1 ）


在产生密钥对的过程中需不停的移动鼠标，鼠标移动仅限于窗口中除绿色标记进
程条外的地方。否则进程条的显示会不动，密钥对将停止产生，如 图 **1-34** 所示。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 118


HUAWEI NetEngine40E
配置指南 1 基础配置


图 **1-34** 生成客户端密钥对（ 2 ）


3. 密钥对产生后，在“ Key passphrase ”文本框中输入密码，并在“ Confirm
passphrase ”文本框中再次输入该密码，作为 SSH 终端用户登录 SSH 服务器的密
码。单击“ Save private key ”，输入存储私钥的文件名 private ，单击保存。将生
成的公钥全部拷贝，粘贴至记事本中，并命名为 public.txt 。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 119


HUAWEI NetEngine40E
配置指南 1 基础配置


图 **1-35** 生成客户端密钥对（ 3 ）


步骤 **6** SSH 服务器编辑公钥，并将编辑后的公钥分配给用户。


[ ~ SSH Server] **rsa peer-public-key rsa01 encoding-type openssh**

[*SSH Server-rsa-public-key] **public-key-code begin**

[*SSH Server-rsa-public-key-rsa-key-code] ssh-rsa AAAAB3NzaC1yc2EAAAABJQAAAQEAxHbcqV6
qqnb1+jQQ0qFLptxWS1xRFfDe6DuMaX2eRUCx3fp2eBA1bgUfHd7eCO05CfHfC443oNBwlj/39Obi8kS
RIQSlXOU1KIP8DNYtwU/N23p/YDHzbgOVvN6dSr+Ua2Er7m2Hehzdo2XoGuWokqhnuMpA7O7zykXs7rM
6tdf+hh/992o6GHBD9IbJe9mG6WoAmDkBmedXzBqJeeGb2wbGg9hBTIgVQqZNhthGcVlLUlPJlZQi1ZO
L3C/cVIOXqnVOqqxHk6nlWcMRo0PxOAegtyzsBETnvcEO2xVw6zF0WVFvU60C99THB+GpuHuRdWzvUNC
ZpsjmCwkg+4RFGQ== rsa-key-20190422

[*SSH Server-rsa-public-key-rsa-key-code] **public-key-code end**

[*SSH Server-key-code] **peer-public-key end**

[*SSH Server] **ssh user huawei123 assign rsa-key rsa01**

[*SSH Server] **commit**


步骤 **7** 使能 STelnet 功能，并配置用户的服务类型为 STelnet 。


[ ~ SSH Server] **stelnet server enable**

[*SSH Server] **ssh server-source** **-i** GigabitEthernet0/0/0

[*SSH Server] **ssh user huawei123 service-type stelnet**

[*SSH Server] **commit**


步骤 **8** 配置 ACL 规则。


[ ~ SSH Server] **acl 2000**

[*SSH Server-acl4-basic-2000] **rule permit source ************ 8**

[*SSH Server-acl4-basic-2000] **quit**

[*SSH Server] **ssh server acl 2000**

[*SSH Server] **commit**


步骤 **9** 客户端通过 PuTTY.exe 软件登录 SSH 服务器。


1. 打开 PuTTY.exe 程序，出现如 图 **1-36** 所示客户端配置界面。为确保安全，请使用
PuTTY 的 0.58 及以上版本。在“ Host Name (or IP address) ”文本框中输入 SSH 服
务器的 IP 地址。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 120


HUAWEI NetEngine40E
配置指南 1 基础配置


图 **1-36** SSH 客户端配置界面（ 1 ）


2. 选择 SSH 客户端配置界面左侧目录树（ Category ）中的连接协议（ Connection ）
中的“ SSH ”，出现如 图 **1-37** 所示界面。在“ Protocol options ”区域中，选择
“ SSH protocol version ”参数的值为 2 。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 121


HUAWEI NetEngine40E
配置指南 1 基础配置


图 **1-37** SSH 客户端配置界面（ 2 ）


3. 继续选择“ SSH ”下的“ Auth ”，出现如 图 **1-38** 所示界面，单击“ Browse ”，导
入存储的私钥文件“ private.ppk ”。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 122


HUAWEI NetEngine40E
配置指南 1 基础配置


图 **1-38** SSH 客户端配置界面（ 3 ）


4. 单击“ Open ”。如果连接正常则会提示用户输入用户名，如 图 **1-39** 所示。


图 **1-39** SSH 客户端登录认证界面


**----**
结束

##### 配置文件


       - SSH 服务器的配置文件


#

sysname SSH Server
#

acl number 2000
rule 5 permit source ************ *********


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 123


HUAWEI NetEngine40E
配置指南 1 基础配置


#
rsa peer-public-key rsa01 encoding-type openssh
public-key-code begin
ssh-rsa AAAAB3NzaC1yc2EAAAABJQAAAQEAxHbcqV6qqnb1+jQQ0qFLptxWS1xRFfDe6DuMaX2eRUCx
3fp2eBA1bgUfHd7eCO05CfHfC443oNBwlj/39Obi8kSRIQSlXOU1KIP8DNYtwU/N23p/YDHzbgOVvN6d
Sr+Ua2Er7m2Hehzdo2XoGuWokqhnuMpA7O7zykXs7rM6tdf+hh/
992o6GHBD9IbJe9mG6WoAmDkBmedX
zBqJeeGb2wbGg9hBTIgVQqZNhthGcVlLUlPJlZQi1ZOL3C/cVIOXqnVOqqxHk6nlWcMRo0PxOAegtyzs
BETnvcEO2xVw6zF0WVFvU60C99THB+GpuHuRdWzvUNCZpsjmCwkg+4RFGQ== rsa-key
public-key-code end
peer-public-key end
#

aaa
local-user huawei123 password irreversible-cipher $1c$+,JS+))\\2$KVNj(.
3`_5x0FCKGv}H&.kUTI`Ff&H*eBqO.ua>)$
local-user huawei123 service-type ssh
local-user huawei123 user-group manage-ug
#
interface GigabitEthernet0/0/0
undo shutdown
ip address ************** *************
#

stelnet server enable
ssh server-source -i GigabitEthernet0/0/0
ssh user huawei123
ssh user huawei123 authentication-type rsa
ssh user huawei123 assign rsa-key rsa01
ssh user huawei123 service-type stelnet
ssh server acl 2000

#
ssh server cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr
ssh server hmac sha2_512 sha2_256
ssh server key-exchange dh_group_exchange_sha256
#
ssh server publickey rsa_sha2_256 rsa_sha2_512
#
ssh server dh-exchange min-len 3072
#
ssh client publickey dsa ecc rsa rsa_sha2_256 rsa_sha2_512
#
ssh client cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr
ssh client hmac sha2_512 sha2_256
ssh client key-exchange dh_group_exchange_sha256
#
user-interface vty 0 4
authentication-mode aaa
protocol inbound ssh
user privilege level 3
#

return
