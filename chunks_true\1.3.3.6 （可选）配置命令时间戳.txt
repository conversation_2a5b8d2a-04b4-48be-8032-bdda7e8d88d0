

需要记录命令行执行时间时，可执行如下步骤。

##### 操作步骤


       - 使能系统时间戳功能


a. 执行命令 **system-view** ，进入系统视图。


b. 执行命令 **timestamp enable** ，使能系统的时间戳功能。


使能系统的时间戳功能后，用户执行 display 查询命令时，系统会在显示信息
前打上执行该命令的时间。


c. 执行命令 **commit** ，提交配置。


说明


该功能仅对 display 查询命令生效。


       - 使能当前会话的命令时间戳功能


a. 执行命令 **terminal command timestamp** ，使能当前会话的命令时间戳功
能。


说明


              - 使能该功能后，用户输入任意命令回车后都会显示执行时间。


              - 该功能仅对当前会话生效，用户退出系统重新登录后，该功能失效，需重新配
置。


              - 如果用户执行命令 **undo terminal command timestamp** 关闭当前会话时间戳功
能，但是已执行 **timestamp enable** 命令使能系统的时间戳功能，则用户执行
display 查询命令时依然会显示时间戳。


**----**
结束
