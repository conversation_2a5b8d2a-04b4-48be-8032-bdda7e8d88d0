

为提升系统安全性，当 SSH 会话满足下面三个条件中的一个或多个时，系统会重新进
行密钥协商，使用协商出的新密钥来建立 SSH 会话连接，从而提升系统安全性。


       - 交互报文个数达到配置的密钥重协商条件。


       - 累计报文数据量达到配置的密钥重协商条件。


       - 会话持续时长达到配置的密钥重协商条件。


       - 此命令对 IPv4 和 IPv6 客户端均生效。


说明


SSH 客户端和服务器端有一方先满足密钥重协商条件，就会发起密钥重协商申请，另外一方响
应。

##### 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 根据 SSH 服务类型选择相应的命令配置触发密钥重协商条件参数。


1. 执行命令 **ssh client rekey** { **data-limit** data-limit | **max-patchet** max-packet |
**time** minutes } [*] ，配置 SSH 客户端密钥重协商的触发条件参数。


2. 执行命令 **ssh server rekey** { **data-limit** data-limit | **max-patchet** max-packet |
**time** minutes } [*] ，配置 SSH 服务端密钥重协商的触发条件参数。


步骤 **3** 执行命令 **commit** ，提交配置。


**----**
结束


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 99


HUAWEI NetEngine40E
配置指南 1 基础配置

##### 检查配置结果


       - 执行 **display ssh client** **session** 命令，可以查看到 SSH 客户端密钥重协商后在线会
话的收发包数量，收发包数据量以及在线时长。
