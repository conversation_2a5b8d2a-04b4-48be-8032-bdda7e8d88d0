

Telnet 可使用户远程登录设备进行管理与维护。

##### 应用环境


如果网络中有一台或多台设备需要配置和管理，用户无需为每一台设备连接用户终端
进行本地维护。如果已知待登录设备的 IP 地址，且非首次登录设备，用户可以通过
Telnet 方式从用户终端登录设备，对设备进行远程配置。用户可以通过此方式在一台用
户终端上维护网络中的多台设备，极大地方便了用户的操作。


说明


设备的 IP 地址需要通过 Console 口预先配置。


此协议不安全，建议使用 STelnet 。

##### 前置任务


在配置用户通过 Telnet 登录设备之前，必须通过 Console 口登录设备，更改设备的缺省
配置，以便用户能够通过 Telnet 方式远程登录设备并实现管理和维护。更改的缺省配置
如下：


       - 配置设备管理网口的 IP 地址，确保终端和登录的设备之间路由可达。


       - 配置 **VTY** 用户界面的用户级别和验证方式 ，实现远程管理和维护设备。


       - 使能 **Telnet** 服务器功能 ，以便用户能够通过 Telnet 方式远程登录设备。
