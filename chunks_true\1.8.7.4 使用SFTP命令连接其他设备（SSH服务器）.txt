

用户可以从 SSH 客户端以 SFTP 方式登录到 SSH 服务器上。

##### 背景信息


SFTP 客户端访问 SSH 服务器的命令跟 STelnet 客户端很相似，支持携带源地址，选择密
钥交换算法、加密算法和 HMAC 算法。


在作为 SSH 客户端的设备上进行如下的配置。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 255


HUAWEI NetEngine40E
配置指南 1 基础配置

##### 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** （可选）执行命令 **ssh client cipher** { **des_cbc** | **3des_cbc** | **aes128_cbc** |
**aes192_cbc** | **aes256_cbc** | **aes128_ctr** | **aes192_ctr** | **aes256_ctr** | **arcfour128** |
**arcfour256** | **aes128_gcm** | **aes256_gcm** | **sm4_cbc** | **sm4_gcm** } [*] ，配置 SSH 客户端
上的加密算法。


说明


为保证更好的安全性，建议使用以下安全性更高的加密算法： aes128_ctr 、 aes256_ctr 、
aes192_ctr 、 aes128_gcm 、 aes256_gcm 。


该命令中的参数 **des_cbc** 、 **3des_cbc** 、 **aes128_cbc** 、 **aes256_cbc** 、 **arcfour128** 、 **arcfour256** 、
**aes192_cbc** 和 **sm4_cbc** 为弱安全算法不建议使用，如需配置，需执行 **undo crypto weak-**
**algorithm disable** 命令使能弱安全算法功能。为避免安全风险，建议改用更安全的算法。


步骤 **3** （可选）执行命令 **ssh client hmac** { **md5** | **md5_96** | **sha1** | **sha1_96** | **sha2_256** |
**sha2_256_96** | **sha2_512** | **sm3** } [*] ，配置 SSH 客户端 HMAC 认证算法。


说明


为保证更好的安全性，建议使用以下安全性更高的 HMAC 算法： sha2_256 、 sha2_512 。


该命令中的参数 **md5** 、 **md5_96** 、 **sha1** 、 **sha1_96** 和 **sha2_256_96** 为弱安全算法不建议使用，如
需配置，需执行 **undo crypto weak-algorithm disable** 命令使能弱安全算法功能。为避免安全
风险，建议改用更安全的算法。


步骤 **4** （可选）执行命令 **ssh client key-exchange** { **dh_group14_sha1** | **dh_group1_sha1**
| **dh_group_exchange_sha1** | **dh_group_exchange_sha256** | **dh_group16_sha512**
| **ecdh_sha2_nistp256** | **ecdh_sha2_nistp384** | **ecdh_sha2_nistp521** | **sm2_kep** |
**curve25519_sha256** } [*] ，配置 SSH 客户端上的密钥交换算法列表。


说明


为保证更好的安全性，建议使用以下安全性更高的密钥交换算法： dh_group16_sha512 。


命令中的参数 **dh_group_exchange_sha1** 、 **dh_group1_sha1** 和 **dh_group14_sha1** 为弱安全算
法不建议使用，如需配置，需执行 **undo crypto weak-algorithm disable** 命令使能弱安全算法
功能。为避免安全风险，建议改用更安全的算法。


步骤 **5** 执行基于网络协议的如下操作步骤：


       - 基于 IPv4 协议的配置情况：


执行命令 **sftp** [ **-a** source-ip-address ] [ **-force-receive-pubkey** ] host-ipaddress [ port-number ] [ [ **prefer_kex** prefer_kex ] | [ **prefer_ctos_cipher**
prefer_ctos_cipher ] | [ **prefer_stoc_cipher** prefer_stoc_cipher ] |

[ **prefer_ctos_hmac** prefer_ctos_hmac ] | [ **prefer_stoc_hmac**
prefer_stoc_hmac ] | [ **prefer_ctos_compress zlib** ] | [ **prefer_stoc_compress**
**zlib** ] | [ **public-net** | **-vpn-instance** vpn-instance-name ] | [ **-ki** interval ] | [ **-**
**kc** count ] | [ **identity-key** identity-key-type ] | [ **user-identity-key** userkey ] ] [*] ，以 SFTP 方式通过使用 IPv4 的地址登录到 SSH 服务器上，并进入 SFTP 客户
端视图。


       - 基于 IPv6 协议的配置情况：


执行命令 **sftp ipv6** [ **-force-receive-pubkey** ] [ **-a** source-ipv6-address ] hostipv6-address [ [ [ **-vpn-instance** vpn-instance-name ] | **public-net** ] | [ **-oi**
{ interface-name | interface-type interface-number } ] [ port-number ] |

[ **prefer_kex** { prefer_kex } ] | [ **prefer_ctos_cipher** prefer_ctos_cipher ] |

[ **prefer_stoc_cipher** prefer_stoc_cipher ] | [ **prefer_ctos_hmac**


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 256


HUAWEI NetEngine40E
配置指南 1 基础配置


prefer_ctos_hmac ] | [ **prefer_stoc_hmac** prefer_stoc_hmac ] |

[ **prefer_ctos_compress** **zlib** ] | [ **prefer_stoc_compress** **zlib** ] | [ **-ki** interval ]
| [ **-kc** count ] | [ **identity-key** identity-key-type ] | [ **user-identity-key** userkey ] ] [*] ，以 SFTP 方式通过使用 IPv6 的地址登录到 SSH 服务器上，并进入 SFTP 客户
端视图。


步骤 **6** 执行命令 **commit** ，提交配置。


**----**
结束
