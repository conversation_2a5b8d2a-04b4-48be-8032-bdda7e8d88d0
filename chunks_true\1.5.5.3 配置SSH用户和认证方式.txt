

通过 STelnet 方式登录设备时，必需配置 SSH 用户、创建本地密钥对、设置用户验证方
式以及指定 SSH 用户的服务方式。

##### 背景信息


SSH 用户支持 RSA 、 DSA 、 ECC 、 SM2 、 x509v3-ssh-rsa 、 x509v3-ecdsa-sha2 、
password 、 password-rsa 、 password-ecc 、 password-dsa 、 password-sm2 、
password-x509v3-rsa 、 password-x509v3-ecdsa-sha2 和 all 认证方式。


如果 SSH 用户使用 RSA 、 DSA 、 SM2 、 ECC 认证，则在服务端和客户端都需要生成本地
RSA 、 DSA 、 SM2 、 ECC 密钥对。并且服务端要将客户端的公钥配置到本地并进行编
辑，编辑后公钥和本地用户绑定。


RSA 、 DSA 、 SM2 、 x509v3-ssh-rsa 与 ECC 密钥算法的比较如 表 **1-14** 所示。


表 **1-14** RSA/DSA ， SM2 、 x509v3-ssh-rsa 和 ECC 密钥算法比较






|密钥算法|应用场景|
|---|---|
|RSA/DSA|是一种公开密钥加密体系，是一种非对称加密算法，通过这种方法可以<br>有效地提高加密的效率并能简化对密钥的管理。服务器必须检查用户是<br>否是合法的SSH用户，检查公钥对于该用户是否合法，用户数字签名是<br>否合法。若三者同时满足，则身份认证通过；若其中任何一项不能验证<br>通过均告验证失败，拒绝该用户的登录请求。|
|ECC|ECC和RSA、DSA算法相同，同属于非对称密码算法。但是相比RSA、<br>DSA算法，有如下优势：<br>●相同的安全性，ECC算法的密钥长度比RSA算法更短。<br>●计算量小，处理速度快。<br>●存储空间小。<br>●带宽要求低。|
|SM2|SM2是非对称密码算法，是基于ECC算法的非对称算法。|
|x509v3-<br>ssh-rsa、<br>x509v3-<br>ecdsa-<br>sha2|x509v3-ssh-rsa、x509v3-ecdsa-sha2算法是基于PKI证书的，需要用户<br>和服务端都绑定相应的PKI证书，扩展性更好，安全性更高。|



各种认证方式的应用场景如 表 **1-15** 所示。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 76


HUAWEI NetEngine40E
配置指南 1 基础配置


表 **1-15** 各种认证方式应用场景







|认证方式|应用场景|
|---|---|
|RSA、DSA认证|RSA和DSA是一种公开密钥加密体系，是<br>一种非对称加密算法。通过这种方法可<br>以有效地提高加密的效率并能简化对密<br>钥的管理。服务器必须检查用户是否是<br>合法的SSH用户，检查公钥对于该用户是<br>否合法，用户数字签名是否合法。若三<br>者同时满足，则身份认证通过；若其中<br>任何一项不能验证通过均告验证失败，<br>拒绝该用户的登录请求。|
|ECC认证|与RSA认证一样，服务器必须检查用户是<br>否是合法的SSH用户，检查公钥对于该用<br>户是否合法，用户数字签名是否合法。<br>若三者同时满足，则身份认证通过；若<br>其中任何一项不能验证通过均告验证失<br>败，拒绝该用户的登录请求。但相比RSA<br>认证，ECC认证有如下优势：<br>●相同的安全性，ECC的密钥长度要比<br>RSA短。<br>●计算量小，处理速度快。<br>●存储空间小。<br>●带宽要求低。|
|password认证|在服务器端由AAA为每一个合法用户分<br>配一个用于登录时进行身份验证的口<br>令，即在服务器端存在“用户名＋口<br>令”的一一对应的关系。当某个用户请<br>求登录时，服务器需要对用户名以及其<br>口令分别进行鉴别，其中任何一项不能<br>验证通过均告验证失败，拒绝该用户的<br>登录请求。<br>说明<br>建议用户在配置客户端身份认证方式时选择<br>公钥认证方式，替代密码认证方式。|
|password-rsa认证、password-dsa、<br>password-ecc认证、password-sm2认<br>证、password-x509v3-rsa认证、<br>password-x509v3-ecdsa-sha2认证|SSH服务器可以要求客户端进行身份认证<br>的过程中同时进行Publickey身份认证和<br>password身份认证，只有当两者同时满<br>足的情况下，才认为客户端身份认证通<br>过。|
|SM2认证|SM2是国家标准加密算法，服务器检查<br>用户是否是合法的SSH用户，检查公钥对<br>于该用户是否合法，用户数字签名是否<br>合法。若三者同时满足，则身份认证通<br>过；若其中任何一项不能验证通过均告<br>验证失败，拒绝该用户的登录请求。|


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 77


HUAWEI NetEngine40E
配置指南 1 基础配置






|认证方式|应用场景|
|---|---|
|x509v3-ssh-rsa、x509v3-ecdsa-sha2认<br>证|x509v3-ssh-rsa和x509v3-ecdsa-sha2认<br>证是PKI证书认证，扩展性更好，安全性<br>更高。|
|all认证|服务器可以要求客户端进行身份认证的<br>过程中进行公钥认证或密码认证，只要<br>满足其中一个认证，就认为客户端身份<br>认证通过。|


##### 数据准备 操作步骤



说明


为了保证更好的安全性，建议不要使用小于 3072 位的 RSA 算法作为 SSH 用户的认证方式，建议您
使用更安全的 ECC 认证算法。


用户的级别需根据接入用户选择的认证方式和配置来决定：


  - 如果 SSH 用户的认证方式为 password ，则用户级别为 AAA 视图下配置的用户级别。


  - 如果 SSH 用户的认证方式为公钥认证或者 PKI 证书认证，且已执行命令 **ssh authorization-**
**type default** { **aaa** | **root** } 配置用户的授权方式为 AAA ，则 SSH 用户级别由指定授权方式
取 AAA 视图下配置的级别决定。


    - 如果指定授权类型为 AAA ，则用户级别取 AAA 视图下配置的用户级别。


    - 如果指定授权类型为 Root ，则用户级别取 VTY 界面下配置的用户级别。


  - 如果 SSH 用户的认证方式为公钥认证或者 PKI 证书认证，没有执行命令 **ssh authorization-**
**type default** { **aaa** | **root** } 配置用户的授权方式，则由 SSH 用户级别 AAA 视图下配置的用
户级别决定。


编辑公共密钥前需先生成密钥对，生成密钥对的过程请参见 用户通过终端 **STelnet** 到服
务器 中的步骤 1 。



步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **ssh user** user-name ，创建 SSH 用户。


如果创建认证方式为 password 、 password-rsa 、 password-dsa 、 password-sm2 、
password-x509v3-rsa 、 password-x509v3-ecdsa-sha2 或 password-ecc 的 SSH 用户，
则需要在 AAA 视图下创建同名的本地用户，此时还需要配置本地用户的接入类型为
SSH 。具体配置请见 表 **1-16** 所示。


如果创建认证方式为 RSA 、 DSA 、 SM2 、 x509v3-ssh-rsa 、 x509v3-ecdsa-sha2 或 ECC
的 SSH 用户，且 SSH 连接的授权类型为默认授权类型 AAA 时，则需要在 AAA 视图下创建
同名的本地用户，此时还需要配置本地用户的接入类型为 SSH ，否则需要在系统视图
下执行命令 **ssh authorization-type default** **root** ，将 SSH 连接的授权类型设置为
Root 。具体配置请见 表 **1-16** 所示。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 78


HUAWEI NetEngine40E
配置指南 1 基础配置


表 **1-16** 本地用户配置












|操作|命令|
|---|---|
|（可选）设置<br>本地用户密码<br>的加密方式|在系统视图下执行命令**crypto password irreversible-algorithm**<br>**hmac-sha256**。|
|进入AAA视图|在系统视图下执行命令**aaa**。|
|配置本地用户<br>名和密码|执行命令**local-user** user-name**password** [**cipher** password |<br>**irreversible-cipher** irreversible-cipher-password ]。<br>●不选择**cipher**或**irreversible-cipher**关键字时，密码以交互式输<br>入，系统不回显密码。<br>输入的密码为字符串形式，区分大小写，开启用户账户安全策略<br>时，取值范围是8～128。关闭用户账户安全策略时，长度范围<br>是1～128。开启用户账户安全策略时，密码不能与用户名及其<br>用户名反向字符串相同，且密码必须包括大写字母、小写字母、<br>数字及特殊字符。<br>说明<br>特殊字符不包括“？”和空格。但是，当输入的密码两端使用双引号<br>时，可在密码中间输入空格。<br>–<br>如果使用双引号设置带空格密码，双引号之间不能再使用双引号。<br>–<br>如果使用双引号没有设置带空格密码，双引号之间可以再使用双引<br>号。<br>例如，"Aa 123"45""为不合法密码，"Aa123"45""为合法密码。<br>●选择**cipher**关键字时，密码可以以简单形式输入，也可以以密文<br>形式输入。<br>密码以简单形式输入，要求与不选择**cipher**关键字时一样。密码<br>以简单形式输入，系统会回显简单形式的密码，存在安全风险，<br>因此建议使用交互式输入密码。<br>无论是简单输入还是密文输入，配置文件中都以密文形式体现。<br>●选择**irreversible-cipher**关键字时，密码可以以简单形式输入，<br>也可以以不可逆密文形式输入。<br>密码以简单形式输入，要求与不选择**irreversible-cipher**关键字<br>时一样。<br>无论是简单输入还是不可逆密文输入，配置文件中都以密文形式<br>体现。|
|配置本地用户<br>的接入类型为<br>SSH|执行命令**local-user** user-name**service-type** **ssh**。|
|退出AAA视<br>图，返回系统<br>视图|执行命令**quit**。|



步骤 **3** 根据实际配置的 SSH 用户认证方式，选择如 表 配置 **SSH** 用户认证方式 所示操作之一。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 79


HUAWEI NetEngine40E
配置指南 1 基础配置


表 **1-17** 配置 SSH 用户认证方式








|操作|命令|说明|
|---|---|---|
|对SSH用户进行<br>Password验证|执行命令**ssh user** user-name<br>**authentication-type**<br>**password**|采用本地认证或HWTACACS服<br>务器认证时，如果用户数量少<br>可以采用密码验证方式。|
|对SSH用户进行<br>RSA验证|1、执行命令**ssh user** user-<br>name**authentication-type**<br>**rsa**，对SSH用户配置RSA验证。|-|
|对SSH用户进行<br>RSA验证|2、执行命令**rsa peer-public-**<br>**key** key-name**encoding-type**<br>enc-type，进入RSA公钥视图。|-|
|对SSH用户进行<br>RSA验证|3、执行命令**public-key-code**<br>**begin**，进入公钥编辑视图。|-|
|对SSH用户进行<br>RSA验证|4、输入hex-data，编辑公钥。<br>说明<br>编辑公共密钥前需要在SSH客户端<br>使用SSH客户端软件生成RSA密钥<br>对，文中以PuTTYGen.exe软件为例<br>进行说明，配置过程请参见用户通<br>过终端**STelnet**到服务器步骤1。将<br>示例中步骤1.c产生的公钥全部拷贝<br>至此处。<br>编辑PuTTY生成的公钥时，步骤2中<br>的编码方式需选择**openssh**方式，<br>即**rsa peer-public-key** key-name<br>**encoding-type** **openssh**。|●进入公钥编辑视图后，键入<br>的公钥必须是按公钥格式编<br>码的十六进制字符串，由支<br>持SSH的客户端软件随机生<br>成。具体操作参见相应的<br>SSH客户端软件的帮助文<br>档。<br>●进入公钥编辑视图后，即可<br>将客户端上产生的RSA公钥<br>传送到服务器端。请采用拷<br>贝粘贴方式将RSA公钥配置<br>到作为SSH服务器的设备<br>上。|
|对SSH用户进行<br>RSA验证|5、执行命令**public-key-code**<br>**end**，退出公钥编辑视图。|-|
|对SSH用户进行<br>RSA验证|6、执行命令**peer-public-key**<br>**end**，退出公钥视图，回到系统<br>视图。|●如果未输入合法的密钥编码<br>hex-data，执行**peer-**<br>**public-key end**后，将无<br>法生成密钥。<br>●如果步骤2中指定的密钥<br>key-name已经在别的窗口<br>下被删除，再执行**peer-**<br>**public-key end**时，系统<br>会提示：密钥已经不存在，<br>此时直接退到系统视图。|
|对SSH用户进行<br>RSA验证|7、执行命令**ssh user** user-<br>name**assign** **rsa-key** key-<br>name，为SSH用户分配RSA公<br>钥。|-|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 80


HUAWEI NetEngine40E
配置指南 1 基础配置








|操作|命令|说明|
|---|---|---|
|对SSH用户进行<br>DSA验证|1、执行命令**ssh user** user-<br>name**authentication-type**<br>**dsa**，对SSH用户配置DSA验<br>证。|-|
|对SSH用户进行<br>DSA验证|2、执行命令**dsa peer-public-**<br>**key** key-name**encoding-type**<br>enc-type，进入DSA公钥视图。|-|
|对SSH用户进行<br>DSA验证|3、执行命令**public-key-code**<br>**begin**，进入公钥编辑视图。|-|
|对SSH用户进行<br>DSA验证|4、输入hex-data，编辑公钥。<br>说明<br>编辑公共密钥前需要在SSH客户端<br>使用SSH客户端软件生成DSA密钥<br>对，文中以PuTTYGen.exe软件生成<br>RSA秘钥对为例进行说明。配置过<br>程请参见用户通过终端**STelnet**到<br>服务器步骤1。将示例中步骤1.c产<br>生的公钥全部拷贝至此处。<br>编辑PuTTY生成的公钥时，步骤2中<br>的编码方式需选择**openssh**方式，<br>即**dsa peer-public-key** key-name<br>**encoding-type** **openssh**。|●进入公钥编辑视图后，键入<br>的公钥必须是按公钥格式编<br>码的十六进制字符串，由支<br>持SSH的客户端软件随机生<br>成。具体操作参见相应的<br>SSH客户端软件的帮助文<br>档。<br>●进入公钥编辑视图后，即可<br>将客户端上产生的DSA公钥<br>传送到服务器端。请采用拷<br>贝粘贴方式将DSA公钥配置<br>到作为SSH服务器的设备<br>上。|
|对SSH用户进行<br>DSA验证|5、执行命令**public-key-code**<br>**end**，退出公钥编辑视图。|-|
|对SSH用户进行<br>DSA验证|6、执行命令**peer-public-key**<br>**end**，退出公钥视图，回到系统<br>视图。|●如果未输入合法的密钥编码<br>hex-data，执行**peer-**<br>**public-key end**后，将无<br>法生成密钥。<br>●如果步骤2中指定的密钥<br>key-name已经在别的窗口<br>下被删除，再执行**peer-**<br>**public-key end**时，系统<br>会提示：密钥已经不存在，<br>此时直接退到系统视图。|
|对SSH用户进行<br>DSA验证|7、执行命令**ssh user** user-<br>name**assign** **dsa-key** key-<br>name，为SSH用户分配DSA公<br>钥。|-|
|对SSH用户进行<br>ECC验证|1、执行命令**ssh user** user-<br>name**authentication-type**<br>**ecc**，对SSH用户配置ECC验证。|-|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 81


HUAWEI NetEngine40E
配置指南 1 基础配置








|操作|命令|说明|
|---|---|---|
||2、执行命令**ecc peer-public-**<br>**key** key-name [**encoding-**<br>**type** enc-type ]，进入ECC公钥<br>视图。|-|
||3、执行命令**public-key-code**<br>**begin**，进入公钥编辑视图。|-|
||4、输入hex-data，编辑公钥。<br>说明<br>编辑公共密钥前需要在SSH客户端<br>使用SSH客户端软件生成ECC密钥<br>对，文中以PuTTYGen.exe软件生成<br>RSA秘钥对为例进行说明。配置过<br>程请参见用户通过终端**STelnet**到<br>服务器步骤1。将示例中步骤1.c产<br>生的公钥全部拷贝至此处。<br>编辑PuTTY生成的公钥时，步骤2中<br>的编码方式需选择**openssh**方式，<br>即**ecc peer-public-key** key-name<br>**encoding-type** **openssh**。|●进入公钥编辑视图后，键入<br>的公钥必须是按公钥格式编<br>码的十六进制字符串，由支<br>持SSH的客户端软件随机生<br>成。具体操作参见相应的<br>SSH客户端软件的帮助文<br>档。<br>●进入公钥编辑视图后，即可<br>将客户端上产生的ECC公钥<br>传送到服务器端。请采用拷<br>贝粘贴方式将ECC公钥配置<br>到作为SSH服务器的设备<br>上。|
||5、执行命令**public-key-code**<br>**end**，退出公钥编辑视图。|-|
||6、执行命令**peer-public-key**<br>**end**，退出公钥视图，回到系统<br>视图。|●如果未输入合法的密钥编码<br>hex-data，执行**peer-**<br>**public-key end**后，将无<br>法生成密钥。<br>●如果步骤2中指定的密钥<br>key-name已经在别的窗口<br>下被删除，再执行**peer-**<br>**public-key end**时，系统<br>会提示：密钥已经不存在，<br>此时直接退到系统视图。|
||7、执行命令**ssh user** user-<br>name**assign** **ecc-key** key-<br>name，为SSH用户分配ECC公<br>钥。|-|
|对SSH用户进行<br>SM2验证|1、执行命令**ssh server**<br>**publickey** **sm2**，使能SSH服务<br>器公钥算法。|-|
|对SSH用户进行<br>SM2验证|2、执行命令**ssh user** user-<br>name**authentication-type**<br>**sm2**，对SSH用户配置SM2验<br>证。|-|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 82


HUAWEI NetEngine40E
配置指南 1 基础配置














|操作|命令|说明|
|---|---|---|
||3、执行命令**sm2 peer-public-**<br>**key** key-name，进入SM2公钥<br>视图。|-|
||4、执行命令**public-key-code**<br>**begin**，进入公钥编辑视图。|-|
||5、输入hex-data，编辑公钥。|●进入公钥编辑视图后，键入<br>的公钥必须是按公钥格式编<br>码的十六进制字符串，由支<br>持SSH的客户端软件随机生<br>成。具体操作参见相应的<br>SSH客户端软件的帮助文<br>档。<br>●进入公钥编辑视图后，即可<br>将客户端上产生的SM2公<br>钥传送到服务器端。请采用<br>拷贝粘贴方式将SM2公钥<br>配置到作为SSH服务器的设<br>备上。|
||6、执行命令**public-key-code**<br>**end**，退出公钥编辑视图。|-|
||7、执行命令**peer-public-key**<br>**end**，退出公钥视图，回到系统<br>视图。|●如果未输入合法的密钥编码<br>hex-data，执行**peer-**<br>**public-key end**后，将无<br>法生成密钥。<br>●如果步骤2中指定的密钥<br>key-name已经在别的窗口<br>下被删除，再执行**peer-**<br>**public-key end**时，系统<br>会提示：密钥已经不存在，<br>此时直接退到系统视图。|
||8、执行命令**ssh user** user-<br>name**assign** **sm2-key** key-<br>name，为SSH用户分配SM2公<br>钥。|-|
|对SSH用户进行<br>x509v3-ssh-rsa<br>验证|1、执行命令**ssh user** user-<br>name**authentication-type**<br>**x509v3-rsa**，为SSH用户配置<br>x509v3-ssh-rsa验证。|-|
|对SSH用户进行<br>x509v3-ssh-rsa<br>验证|2、执行命令**ssh user** user-<br>name**assign** **pki** pki-name，<br>为SSH用户分配PKI证书。|-|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 83


HUAWEI NetEngine40E
配置指南 1 基础配置








|操作|命令|说明|
|---|---|---|
|对SSH用户进行<br>x509v3-ecdsa-<br>sha2验证|1、执行命令**ssh user** user-<br>name**authentication-type** <br>**x509v3-ecdsa-sha2**，为SSH用<br>户配置x509v3-ecdsa-sha2验<br>证。|-|
|对SSH用户进行<br>x509v3-ecdsa-<br>sha2验证|2、执行命令**ssh user** user-<br>name**assign** **pki** pki-name，<br>为SSH用户分配PKI证书。|-|



步骤 **4** 执行命令 **ssh user** user-name **service-type** { **stelnet** | **all** } ，配置 SSH 用户的服务方
式。


步骤 **5** （可选）执行命令 **ssh server rsa-key min-length** min-length-val ，指定 RSA 公钥的
最小长度。


步骤 **6** （可选）执行命令 **ssh user** user-name **cert-verify-san enable** ，配置 SSH 用户的
SAN/CN 校验。


步骤 **7** 执行命令 **commit** ，提交配置。


**----**
结束
