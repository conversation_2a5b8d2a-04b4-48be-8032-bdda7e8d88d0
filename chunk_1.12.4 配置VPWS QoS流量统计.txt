头标题: 1.12.4 配置VPWS QoS流量统计
尾标题: 1.12.5 配置VPLS QoS基本功能
内容: HUAWEI NetEngine40E配置指南 1 QoSI-Weight(Inverse schedule weight used by TM)A-Weight(Actual schedule weight obtained by users)Shp(Shaping value)Pct(The percentage of subscriber queue's PIR)Drop-Arith(The name of the WRED object used by the flow queue)Flow Queue Template : test-----------------------------------------------------------------Cos Arith U-Weight I-Weight A-Weight Shp   Pct Drop-Arith-----------------------------------------------------------------be  wfq  10    3     10.00   -    -  Tail Dropaf1 lpq  -     -     -     10000  -  testaf2 wfq  10    3     10.00   -    -  Tail Dropaf3 wfq  15    2     15.00   -    -  Tail Dropaf4 wfq  15    2     15.00   -    -  Tail Dropef  pq   -     -     -     30000  -  testcs6 pq   -     -     -     -    -  Tail Dropcs7 pq   -     -     -     -    -  Tail DropReference relationships : NULL步骤 **3** 配置用户组队列 shaping 值。# 配置 user-group-queue 。<HUAWEI> **system-view**[ ~ HUAWEI] **user-group-queue test**[*HUAWEI-user-group-queue-test-slot-all] **shaping 500000 inbound**[*HUAWEI-user-group-queue-test-slot-all] **commit**[ ~ HUAWEI-user-group-queue-test-slot-all] **return**完成上述配置后，执行命令 **display user-group-queue configuration verbose** ，可以查看用户组队列的配置信息以及引用关系。<HUAWEI> **display user-group-queue configuration verbose test**user-group-queue-name : testslot : all[current configuration]inboundshaping-value <kbps> : 500000pbs-value <byte> : 524288outboundshaping-value <kbps> : NApbs-value <byte> : NAweight-value : NA[reference relationship]步骤 **4** 配置业务模板的精度调整长度。# 配置 service-template 及 network-header-length 。<HUAWEI> **system-view**[ ~ HUAWEI] **service-template test**[*HUAWEI-service-template-test-slot-all] **network-header-length 12 inbound**[*HUAWEI-service-template-test-slot-all] **commit**[ ~ HUAWEI-service-template-test-slot-all] **return**完成上述配置后，执行命令 **display service-template configuration verbose** ，可以查看业务模板的配置信息， network-header-length 值以及引用关系。<HUAWEI> **display service-template configuration verbose**[service-template detail information]total number : 1slot all   : 1service-template-name : testslot : all[current configuration]inbound network-header-length: 12outbound network-header-length: NA文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 244HUAWEI NetEngine40E配置指南 1 QoS[reference relationship]NULL步骤 **5** 配置 QoS 模板调度参数并应用到接口上。# 配置 QoS 模板中 user-queue 的调度参数<HUAWEI> **system-view**[ ~ HUAWEI] **qos-profile test**[*HUAWEI-qos-profile-test] **user-queue cir 100000 flow-queue test user-group-queue test service-****template test**[*HUAWEI-qos-profile-test] **commit**[ ~ HUAWEI-qos-profile-test] **return**# 创建 QinQ 子接口并配置 QinQ Termination, 在接口 GE1/0/0.1 、 GE1/0/0.2 和GE1/0/0.3 上应用 QoS 模板。<HUAWEI> **system-view**[ ~ HUAWEI] **interface gigabitethernet 1/0/0.1**[*HUAWEI-GigabitEthernet1/0/0.1] **control-vid 1 qinq-termination**[*HUAWEI-GigabitEthernet1/0/0.1] **qinq termination pe-vid 1 ce-vid 1 to 100**[*HUAWEI-GigabitEthernet1/0/0.1] **ip address ********* 24**[*HUAWEI-GigabitEthernet1/0/0.1] **qos-profile test inbound pe-vid 1 ce-vid 1 to 100 identifier ce-vid****group group1**[*HUAWEI-GigabitEthernet1/0/0.1] **commit**[ ~ HUAWEI-GigabitEthernet1/0/0.1] **quit**[ ~ HUAWEI] **interface gigabitethernet 1/0/0.2**[*HUAWEI-GigabitEthernet1/0/0.2] **control-vid 2 qinq-termination**[*HUAWEI-GigabitEthernet1/0/0.2] **qinq termination pe-vid 2 ce-vid 1 to 100**[*HUAWEI-GigabitEthernet1/0/0.2] **ip address ********* 24**[*HUAWEI-GigabitEthernet1/0/0.2] **qos-profile test inbound pe-vid 2 ce-vid 1 to 100 identifier ce-vid****group group1**[*HUAWEI-GigabitEthernet1/0/0.2] **commit**[ ~ HUAWEI-GigabitEthernet1/0/0.2] **quit**[ ~ HUAWEI] **interface gigabitethernet 1/0/0.3**[*HUAWEI-GigabitEthernet1/0/0.3] **control-vid 3 qinq-termination**[*HUAWEI-GigabitEthernet1/0/0.3] **qinq termination pe-vid 3 ce-vid 1 to 100**[*HUAWEI-GigabitEthernet1/0/0.3] **ip address ********* 24**[*HUAWEI-GigabitEthernet1/0/0.3] **qos-profile test inbound pe-vid 3 ce-vid 1 to 100 identifier ce-vid****group group1**[*HUAWEI-GigabitEthernet1/0/0.3] **commit**[ ~ HUAWEI-GigabitEthernet1/0/0.3] **return**完成上述配置后，执行命令 **display qos-profile configuration** qos-profile-name 和**display qos-profile application** profile-name ，可以看到 QoS 模板的配置和应用信息。<HUAWEI> **display qos-profile configuration test**qos-profile: testinbound:outbound:both:user-queue cir 100000 pir 100000 flow-queue test user-group-queue test service-template test<HUAWEI> **display qos-profile application test**qos-profile test:GigabitEthernet1/0/0.1GigabitEthernet1/0/0.2GigabitEthernet1/0/0.3Reference number by access user ： [inbound] 0, [outbound] 0Reference number by VNI ： [inbound] 0, [outbound] 0执行命令 **display qos-profile statistics interface gigabitethernet1/0/0.1 pe-vid 1****ce-vid 1 inbound** 可以看到接口 GE1/0/0.1 上 QoS 模板的统计信息。<HUAWEI> **display qos-profile statistics interface gigabitethernet 1/0/0.1 pe-vid 1 ce-vid 1 inbound**GigabitEthernet1/0/0.1 inbound traffic statistics:[be]文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 245HUAWEI NetEngine40E配置指南 1 QoSPass:              248,785 packets,          249,780,140 bytesDiscard:             23,986,035 packets,         24,081,979,140 bytesRandom-discard:         0 packets,             0 bytesLast 5 minutes pass rate:12,460 pps,             100,071,104 bpsLast 5 minutes discard rate:1,209,158 pps,           9,711,951,656 bpsLast 5 minutes random discard rate:0 pps,               0 bpsbuffer size:           1600 kbytesused buffer size:        1600 kbyteslast packet was received at:   0000-00-00 00:00:00[af1]Pass:              0 packets,             0 bytesDiscard:             0 packets,             0 bytesRandom-discard:         0 packets,             0 bytesLast 5 minutes pass rate:0 pps,               0 bpsLast 5 minutes discard rate:0 pps,               0 bpsLast 5 minutes random discard rate:0 pps,               0 bpsbuffer size:           1600 kbytesused buffer size:        1600 kbyteslast packet was received at:   0000-00-00 00:00:00[af2]Pass:              0 packets,             0 bytesDiscard:             0 packets,             0 bytesRandom-discard:         0 packets,             0 bytesLast 5 minutes pass rate:0 pps,               0 bpsLast 5 minutes discard rate:0 pps,               0 bpsLast 5 minutes random discard rate:0 pps,               0 bpsbuffer size:           1600 kbytesused buffer size:        1600 kbyteslast packet was received at:   0000-00-00 00:00:00[af3]Pass:              0 packets,             0 bytesDiscard:             0 packets,             0 bytesRandom-discard:         0 packets,             0 bytesLast 5 minutes pass rate:0 pps,               0 bpsLast 5 minutes discard rate:0 pps,               0 bpsLast 5 minutes random discard rate:0 pps,               0 bpsbuffer size:           1600 kbytesused buffer size:        1600 kbyteslast packet was received at:   0000-00-00 00:00:00[af4]Pass:              0 packets,             0 bytesDiscard:             0 packets,             0 bytesRandom-discard:         0 packets,             0 bytesLast 5 minutes pass rate:0 pps,               0 bpsLast 5 minutes discard rate:0 pps,               0 bpsLast 5 minutes random discard rate:0 pps,               0 bpsbuffer size:           1600 kbytesused buffer size:        1600 kbyteslast packet was received at:   0000-00-00 00:00:00[ef]Pass:              0 packets,             0 bytesDiscard:             0 packets,             0 bytesRandom-discard:         0 packets,             0 bytesLast 5 minutes pass rate:文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 246HUAWEI NetEngine40E配置指南 1 QoS0 pps,               0 bpsLast 5 minutes discard rate:0 pps,               0 bpsLast 5 minutes random discard rate:0 pps,               0 bpsbuffer size:           1600 kbytesused buffer size:        1600 kbyteslast packet was received at:   0000-00-00 00:00:00[cs6]Pass:              0 packets,             0 bytesDiscard:             0 packets,             0 bytesRandom-discard:         0 packets,             0 bytesLast 5 minutes pass rate:0 pps,               0 bpsLast 5 minutes discard rate:0 pps,               0 bpsLast 5 minutes random discard rate:0 pps,               0 bpsbuffer size:           1600 kbytesused buffer size:        1600 kbyteslast packet was received at:   0000-00-00 00:00:00[cs7]Pass:              0 packets,             0 bytesDiscard:             0 packets,             0 bytesRandom-discard:         0 packets,             0 bytesLast 5 minutes pass rate:0 pps,               0 bpsLast 5 minutes discard rate:0 pps,               0 bpsLast 5 minutes random discard rate:0 pps,               0bpsbuffer size:           1600 kbytesused buffer size:        1600 kbyteslast packet was received at:   0000-00-00 00:00:00[total]Pass:              248,785 packets,          249,780,140 bytesDiscard:             23,986,035 packets,         24,081,979,140 bytesRandom-discard:         0 packets,             0 bytesLast 5 minutes pass rate:12,460 pps,             100,071,104 bpsLast 5 minutes discard rate:1,209,158 pps,           9,711,951,656 bpsLast 5 minutes random discard rate:0 pps,               0 bpsTraffic towards this interface:                    0 bpsConfigured CIR and PIR:100,000 kbps,            100,000 kbps步骤 **6** 配置端口队列引用的 WRED 对象# 配置端口队列引用的 port-wred 报文丢弃的参数。<HUAWEI> **system-view**[ ~ HUAWEI] **port-wred test**[*HUAWEI-port-wred-test] **color green low-limit 70 high-limit 100 discard-percentage 100**[*HUAWEI-port-wred-test] **color yellow low-limit 60 high-limit 90 discard-percentage 100**[*HUAWEI-port-wred-test] **color red low-limit 50 high-limit 80 discard-percentage 100**[*HUAWEI-port-wred-test] **commit**[ ~ HUAWEI-port-wred-test] **return**完成上述配置后，执行命令 **display port-wred configuration verbose** ，可以查看端口队列 WRED 对象的配置参数。<HUAWEI> **display port-wred configuration verbose test**Port wred name : test--------------------------------------------------Color  Low-limit  High-limit  Discard-percent--------------------------------------------------green  70      100      100文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 247HUAWEI NetEngine40E配置指南 1 QoSyellow  60      90      100red   50      80      100Queue Depth(kbytes) : 8000Reference relationships : NULL步骤 **7** 配置端口队列# 配置 port-queue 的调度算法、 WRED 参数及 shaping 值。<HUAWEI> **system-view**[ ~ HUAWEI] **interface gigabitethernet 2/0/0**[ ~ HUAWEI-GigabitEthernet2/0/0] **undo shutdown**[*HUAWEI-GigabitEthernet2/0/0] **port-queue ef pq shaping 120 port-wred test outbound**[*HUAWEI-GigabitEthernet2/0/0] **commit**[ ~ HUAWEI-GigabitEthernet2/0/0] **return**完成上述配置后，执行命令 **display port-queue configuration interface** ，可以查看端口队列的详细配置信息。<HUAWEI> **display port-queue configuration interface gigabitethernet 2/0/0 outbound**GigabitEthernet2/0/0 outbound current port-queue configuration:be : arithmetic: wfq        weight: 10     tm weight: 3fact weight: 10.00       shaping(kbps): NAport-wred: NAgreen (low-high limit) (kbytes)         12800 - 12800yellow(low-high limit) (kbytes)         12800 - 12800red  (low-high limit) (kbytes)         12800 - 12800current queue-length   (kbytes)         12800cir:123            cir-percentage:NAcir-arithmetic:pq       cir-weight:NApir:123            pir-percentage:NApir-arithmetic:lpq       pir-weight:NAaf1: arithmetic: wfq        weight: 10     tm weight: 3fact weight: 10.00       shaping(kbps): NAport-wred: NAgreen (low-high limit) (kbytes)         12800 - 12800yellow(low-high limit) (kbytes)         12800 - 12800red  (low-high limit) (kbytes)         12800 - 12800current queue-length   (kbytes)         12800cir:NA             cir-percentage:10cir-arithmetic:pq       cir-weight:NApir:NA             pir-percentage:20pir-arithmetic:wfq       pir-weight:15af2: arithmetic: wfq        weight: 10     tm weight: 3fact weight: 10.00       shaping(kbps): NAport-wred: NAgreen (low-high limit) (kbytes)         12800 - 12800yellow(low-high limit) (kbytes)         12800 - 12800red  (low-high limit) (kbytes)         12800 - 12800current queue-length   (kbytes)         12800cir:NA             cir-percentage:NAcir-arithmetic:NA       cir-weight:NApir:NA             pir-percentage:NApir-arithmetic:NA       pir-weight:NAaf3: arithmetic: wfq        weight: 15     tm weight: 2fact weight: 15.00       shaping(kbps): NAport-wred: NAgreen (low-high limit) (kbytes)         12800 - 12800yellow(low-high limit) (kbytes)         12800 - 12800red  (low-high limit) (kbytes)         12800 - 12800current queue-length   (kbytes)         12800cir:NA             cir-percentage:NAcir-arithmetic:NA       cir-weight:NApir:NA             pir-percentage:NApir-arithmetic:NA       pir-weight:NAaf4: arithmetic: wfq        weight: 15     tm weight: 2fact weight: 15.00       shaping(kbps): NAport-wred: NAgreen (low-high limit) (kbytes)         12800 - 12800yellow(low-high limit) (kbytes)         12800 - 12800文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 248HUAWEI NetEngine40E配置指南 1 QoSred  (low-high limit) (kbytes)         12800 - 12800current queue-length   (kbytes)         12800cir:NA             cir-percentage:NAcir-arithmetic:NA       cir-weight:NApir:NA             pir-percentage:NApir-arithmetic:NA       pir-weight:NAef : arithmetic: pq         weight: NA     tm weight: 0fact weight: 0.00       shaping(kbps): NAport-wred: NAgreen (low-high limit) (kbytes)         1280 - 1280yellow(low-high limit) (kbytes)         1280 - 1280red  (low-high limit) (kbytes)         1280 - 1280current queue-length   (kbytes)         1280cir:NA             cir-percentage:NAcir-arithmetic:NA       cir-weight:NApir:NA             pir-percentage:NApir-arithmetic:NA       pir-weight:NAcs6: arithmetic: pq         weight: NA     tm weight: 0fact weight: 0.00       shaping(kbps): NAport-wred: NAgreen (low-high limit) (kbytes)         1280 - 1280yellow(low-high limit) (kbytes)         1280 - 1280red  (low-high limit) (kbytes)         1280 - 1280current queue-length   (kbytes)         1280cir:NA             cir-percentage:NAcir-arithmetic:NA       cir-weight:NApir:NA             pir-percentage:NApir-arithmetic:NA       pir-weight:NAcs7: arithmetic: pq         weight: NA     tm weight: 0fact weight: 0.00       shaping(kbps): NAport-wred: NAgreen (low-high limit) (kbytes)         1280 - 1280yellow(low-high limit) (kbytes)         1280 - 1280red  (low-high limit) (kbytes)         1280 - 1280current queue-length   (kbytes)         1280cir:NA             cir-percentage:NAcir-arithmetic:NA       cir-weight:NApir:NA             pir-percentage:NApir-arithmetic:NA       pir-weight:NA步骤 **8** 检查配置结果当网络中有流量通过时，发现用户 1 的 AF1 、 EF 流量和用户 2 的 EF 流量均按照配置的保证带宽进行转发。在设备的下行接口 GE2/0/0 执行命令 **display port-queue statistics** ，可以看到 EF 流量迅速增长。<HUAWEI> **display port-queue statistics interface gigabitethernet 2/0/0 ef outbound**GigabitEthernet2/0/0 outbound traffic statistics:[ef]Current usage percentage of queue: 10Total pass:5,097,976 packets,        458,817,750 bytesTotal discard:0 packets,             0 bytesDrop tail discard:0 packets,             0 bytesWred discard:0 packets,             0 bytesLast 30 seconds pass rate:12,030 pps,           8,661,600 bpsLast 30 seconds discard rate:0 pps,               0 bpsDrop tail discard rate:0 pps,               0 bpsWred discard rate:0 pps,               0 bpsbuffer size:          10 kbytes文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 249HUAWEI NetEngine40E配置指南 1 QoSused buffer size:        0 kbytesPeak rate:2013-11-17 13:15:18         8,661,600 bps**----**结束##### 配置文件       - HUAWEI 的配置文件#flow-wred testcolor green low-limit 70 high-limit 100 discard-percentage 100color yellow low-limit 60 high-limit 90 discard-percentage 100color red low-limit 50 high-limit 80 discard-percentage 100#flow-queue testqueue af1 lpq shaping 10000 flow-wred testqueue ef pq shaping 30000 flow-wred test#user-group-queue testshaping 500000 inbound#service-template testnetwork-header-length 12 inbound#qos-profile testuser-queue cir 100000 pir 100000 flow-queue test user-group-queue test service-template test#port-wred testcolor green low-limit 70 high-limit 100 discard-percentage 100color yellow low-limit 60 high-limit 90 discard-percentage 100color red low-limit 50 high-limit 80 discard-percentage 100#interface GigabitEthernet1/0/0.1encapsulation qinq-terminationqinq termination pe-vid 1 ce-vid 1 to 100ip address ********* *************qos-profile test inbound pe-vid 1 ce-vid 1 to 100 identifier ce-vid group group1#interface GigabitEthernet1/0/0.2encapsulation qinq-terminationqinq termination pe-vid 2 ce-vid 1 to 100ip address ********* *************qos-profile test inbound pe-vid 2 ce-vid 1 to 100 identifier ce-vid group group1#interface GigabitEthernet1/0/0.3encapsulation qinq-terminationqinq termination pe-vid 3 ce-vid 1 to 100ip address ********* *************qos-profile test inbound pe-vid 3 ce-vid 1 to 100 identifier ce-vid group group1#interface GigabitEthernet2/0/0undo shutdownip address ********* *************port-queue ef pq shaping 120 port-wred test outbound#ospf 10area 0.0.0.0network ********* *********network ********* *********network ********* *********network ********* *********#return##### ******** 配置基于 8 队列增强模式流队列的 HQoS 示例以在指定接口应用 QoS 模板为例，介绍如何配置并应用 8 队列增强模式的 QoS 模板。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 250HUAWEI NetEngine40E配置指南 1 QoS##### 组网需求如 图 **1** 所示，以用户 1 和用户 2 为例，两个用户分别从 PE1 设备的接口 interface1 和interface2 接入。在 PE1 设备上配置 8 队列增强模式流队列模板的相关参数、指定队列并使能 remark-color 功能，可对指定队列的 CIR 与 PIR 流量进行区分和标记（本例中以使能 cos 5 队列的 remark-color 功能为例），并映射成对应的 DEI （ Drop EligibleIndicator ）值。在 PE1 节点， CIR 流量对应的 DEI=0 ， PIR 流量对应的 DEI=1 。 PE1 节点生成的 DEI 将会随报文被发送到下游 P 节点， P 节点通过 DEI 可识别报文的丢弃优先级别。P 节点上同样需要先配置 8 队列增强模式流队列模板的相关参数，并根据上游节点 PE1发来的报文中包含的 DEI 值来映射颜色，当 DEI=1 时，报文的内部丢弃优先级（ color ）被标记为 yellow ，如果 DEI=0 ，则被标记为 Green ，被标记为 Green 的报文有优先通过的权利。然后根据各队列的优先级、调度权重和报文的优先级，在 P 节点完成报文重新入队列的操作。说明本例中 interface1 ， interface2 ， interface3 ， interface4 ， interface5 分别代表 GE1/0/0 ，GE2/0/0 ， GE3/0/0 ， GE1/0/3 ， GE1/0/4 。图 **1-28** 配置基于 8 队列增强模式流队列的 HQoS 组网图##### 配置思路采用如下的思路配置基于 8 队列增强模式流队列的 HQoS 功能。1. 配置 8 队列增强模式 SQ 资源个数。2. 配置 8 队列增强模式流队列模板相关参数，并使能 remark-color 功能。3. 配置 8 队列增强模式用户队列模板及用户带宽。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 251HUAWEI NetEngine40E配置指南 1 QoS4. 配置 8 队列增强模式 QoS 模板。5. 配置流队列 WRED 模板及丢弃参数。6. 在接口下应用 QoS 模板。##### 数据准备完成此例配置，需准备以下数据：       - 8 队列增强模式 SQ 资源个数。       - QoS 模板中 flow-queue CIR 、 PIR 的值。       - QoS 模板中 user-queue CIR 、 PIR 的值。       - 流队列 WRED 模板的丢弃参数。##### 操作步骤       - 以下为在 PE1 节点的配置。a. 配置 8 队列增强模式的上下行 SQ 资源个数。<HUAWEI> **system-view**[ ~ HUAWEI] **slot 3**[ ~ HUAWEI-slot-3] **qos user-queue resource 8cos-enhance 1000 inbound**[*HUAWEI-slot-3] **qos user-queue resource 8cos-enhance 1000 outbound**[*HUAWEI-slot-3] **commit**[ ~ HUAWEI-slot-3] **quit**b. 创建名为 qos1 的 8 队列增强模式流队列模板，并配置 cos 5 队列的峰值信息速率（ PIR ）、承诺信息速率（ CIR ）和 remark-color 使能。[ ~ HUAWEI] **flow-queue qos1 8cos-enhance-mode**[*HUAWEI-flow-queue-template-qos1] **cos 5**[*HUAWEI-flow-queue-template-qos1-cos5] **remark-color enable**[*HUAWEI-flow-queue-template-qos1-cos5] **pir 20000 cir 20000**[*HUAWEI-flow-queue-template-qos1-cos5] **commit**[ ~ HUAWEI-flow-queue-template-qos1-cos5] **quit**[ ~ HUAWEI-flow-queue-template-qos1] **quit**创建名为 qos2 的 8 队列增强模式流队列模板，并配置 cos 5 队列的峰值信息速率（ PIR ）、承诺信息速率（ CIR ）和 remark-color 使能。[ ~ HUAWEI] **flow-queue qos2 8cos-enhance-mode**[*HUAWEI-flow-queue-template-qos2] **cos 5**[*HUAWEI-flow-queue-template-qos2-cos5] **remark-color enable**[*HUAWEI-flow-queue-template-qos2-cos5] **pir 200000 cir 100000**[*HUAWEI-flow-queue-template-qos2-cos5] **commit**[ ~ HUAWEI-flow-queue-template-qos2-cos5] **quit**[ ~ HUAWEI-flow-queue-template-qos2] **quit**完成上述配置后，执行命令 **display flow-queue configuration** ，可以查看 8队列增强模式的流队列模板信息（此处查看名为 qos1 的流队列模板信息）。[ ~ HUAWEI] **display flow-queue configuration verbose qos1**Codes: Cos(Priority of queue's)CIR-PCT(The percentage of committed information rate)CIR-P(The priority schedule of cir)CIR-W(Schedule weight of cir)PIR-PCT(The percentage of peak information rate)PIR-P(The priority schedule of pir)PIR-W(Schedule weight of pir)Drop-Arith(The name of the WRED object used by the flow queue)Flow Queue Template : qos1                     mode: 8cos-enhancemode文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 252HUAWEI NetEngine40E配置指南 1 QoS------------------------------------------------------------------------------------------Cos CIR/CIR-PCT/CBS/CIR-P/CIR-W  PIR/PIR-PCT/PBS/PIR-P/PIR-W CAR-MODE Remark DropArith------------------------------------------------------------------------------------------0  -/-/-/1/10          -/-/-/3/10          0    0    TailDrop1  -/-/-/1/10          -/-/-/3/10          0    0    TailDrop2  -/-/-/1/10          -/-/-/3/10          0    0    TailDrop3  -/-/-/1/15          -/-/-/3/15          0    0    TailDrop4  -/-/-/1/15          -/-/-/3/15          0    0    TailDrop5  20000/-/-/0/20        20000/-/-/2/20        0    1    TailDrop6  -/-/-/0/20          -/-/-/2/20          0    0    TailDrop7  -/-/-/0/20          -/-/-/2/20          0    0    TailDropReference relationships : NULLc. 创建名为 qos 的 8 队列增强模式用户队列模板并配置用户队列的峰值信息速率（ PIR ）和承诺信息速率（ CIR ）等调度参数。[ ~ HUAWEI] **user-queue qos**[*HUAWEI-user-queue-template-qos] **pir 800000 cir 400000**[*HUAWEI-user-queue-template-qos] **priority 0 cir-parent-priority 0 pir-parent-priority 3**[*HUAWEI-user-queue-template-qos] **priority 0 pir 800000**[*HUAWEI-user-queue-template-qos] **commit**[ ~ HUAWEI-user-queue-template-qos] **quit**d. 创建名为 qos1 的 8 队列增强模式 QoS 模板。[ ~ HUAWEI] **qos-profile qos1 8cos-enhance-mode**[*HUAWEI-qos-profile-qos1-8cos-enhance] **user-queue qos flow-queue qos1**[*HUAWEI-qos-profile-qos1-8cos-enhance] **commit**[ ~ HUAWEI-qos-profile-qos1-8cos-enhance] **quit**创建名为 qos2 的 8 队列增强模式 QoS 模板。[ ~ HUAWEI] **qos-profile qos2 8cos-enhance-mode**[*HUAWEI-qos-profile-qos2-8cos-enhance] **user-queue qos flow-queue qos2**[*HUAWEI-qos-profile-qos2-8cos-enhance] **commit**[ ~ HUAWEI-qos-profile-qos2-8cos-enhance] **quit**完成上述配置后，执行命令 **display qos-profile configuration** ，可以查看 8队列增强模式 QoS 模板的配置信息（此处查看名为 qos1 的 QoS 模板信息）。[ ~ HUAWEI] **display qos-profile configuration qos1**qos-profile: qos1  mode: 8cos-enhance-modeinbound:outbound:both:flow-queue: qos1user-queue: qosuser-group-queue: -------------------------------------------------------------service-template: CIR(kbps)/CIR-PCT/CBS(bytes): 400000/-/PIR(kbps)/PIR-PCT/PBS(bytes): 800000/-/-------------------------------------------------------------PRI SCH-MODE PIR     PBS     CIR-P/CIR-W PIR-P/PIR-W-------------------------------------------------------------0  pq    800000   -      0/10     3/101  wfq    -      -      1/10     3/102  pq    -      -      2/10     3/103  wfq    -      -      3/10     3/10e. 在 PE1 节点入接口 GE1/0/0.1 、 GE2/0/0.1 上应用 QoS 模板。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 253HUAWEI NetEngine40E配置指南 1 QoS[ ~ HUAWEI] **interface gigabitethernet 1/0/0.1**[*HUAWEI-GigabitEthernet1/0/0.1] **vlan-type dot1q 1**[*HUAWEI-GigabitEthernet1/0/0.1] **ip address ************ 24**[*HUAWEI-GigabitEthernet1/0/0.1] **trust upstream default**[*HUAWEI-GigabitEthernet1/0/0.1] **trust 8021p**[*HUAWEI-GigabitEthernet1/0/0.1] **qos-profile qos1 inbound identifier none**[*HUAWEI-GigabitEthernet1/0/0.1] **commit**[ ~ HUAWEI-GigabitEthernet1/0/0.1] **quit**[ ~ HUAWEI] **interface gigabitethernet 2/0/0.1**[*HUAWEI-GigabitEthernet2/0/0.1] **vlan-type dot1q 2**[*HUAWEI-GigabitEthernet2/0/0.1] **ip address ******** 24**[*HUAWEI-GigabitEthernet2/0/0.1] **trust upstream default**[*HUAWEI-GigabitEthernet2/0/0.1] **trust 8021p**[*HUAWEI-GigabitEthernet2/0/0.1] **qos-profile qos2 inbound identifier none**[*HUAWEI-GigabitEthernet2/0/0.1] **commit**[ ~ HUAWEI-GigabitEthernet2/0/0.1] **quit**在 PE1 节点出接口 GE3/0/0.1 上使能接口报文的 DEI 能力。[ ~ HUAWEI] **mpls**[*HUAWEI-mpls] **mpls** **ldp**[*HUAWEI-mpls-ldp] **commit**[ ~ HUAWEI-mpls-ldp] **quit**[ ~ HUAWEI] **interface gigabitethernet 3/0/0.1**[*HUAWEI-GigabitEthernet3/0/0.1] **vlan-type dot1q 11**[*HUAWEI-GigabitEthernet3/0/0.1] **ip address ************ 24**[*HUAWEI-GigabitEthernet3/0/0.1] **trust upstream default**[*HUAWEI-GigabitEthernet3/0/0.1] **mpls**[*HUAWEI-GigabitEthernet3/0/0.1] **mpls ldp**[*HUAWEI-GigabitEthernet3/0/0.1] **trust 8021p**[*HUAWEI-GigabitEthernet3/0/0.1] **field dei enable**[*HUAWEI-GigabitEthernet3/0/0.1] **commit**[ ~ HUAWEI-GigabitEthernet3/0/0.1] **quit**完成上述配置后，执行命令 **display qos-profile application** profilename ，可以看到 QoS 模板的应用信息（此处查看名为 qos2 的 qos-profile 模板应用信息）。[ ~ HUAWEI] **display qos-profile application qos2**qos-profile qos2:GigabitEthernet2/0/0.1Reference number by access user:[inbound] 0, [outbound] 0Reference number by VNI:[inbound] 0, [outbound] 0       - 以下为在 P 节点的配置。a. 配置 qos user-queue resource 8 队列增强模式上下行 SQ 资源个数。<HUAWEI> **system-view**[ ~ HUAWEI] **slot 3**[ ~ HUAWEI-slot-3] **qos user-queue resource 8cos-enhance 1000 inbound**[*HUAWEI-slot-3] **qos user-queue resource 8cos-enhance 1000 outbound**[*HUAWEI-slot-3] **commit**[ ~ HUAWEI-slot-3] **quit**b. 创建名为 network 的流队列 WRED 模板并配置丢弃参数。[ ~ HUAWEI] **flow-wred network**[*HUAWEI-flow-wred-network] **color yellow low-limit 50 high-limit 50 discard-percentage****100**[*HUAWEI-flow-wred-network] **commit**[ ~ HUAWEI-flow-wred-network] **quit**完成上述配置后，执行命令 **display flow-wred configuration** ，可以查看流队列 WRED 模板的配置参数。包括每种颜色报文的高低门限百分比，丢弃概率以及该对象的引用关系。[ ~ HUAWEI] **display flow-wred configuration verbose network**Flow wred name : network--------------------------------------------------Color  Low-limit  High-limit  Discard-percent--------------------------------------------------文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 254HUAWEI NetEngine40E配置指南 1 QoSgreen  100     100      100yellow  50      50      100red   100     100      100Queue Depth(kbytes) : 1000Reference relationships : NULLc. 创建名为 network 的 8 队列增强模式流队列模板，并配置 cos 5 队列的峰值信息速率（ PIR ）、承诺信息速率（ CIR ）和 WRED 模板。[ ~ HUAWEI] **flow-queue network 8cos-enhance-mode**[*HUAWEI-flow-queue-template-network] **cos 5**[*HUAWEI-flow-queue-template-network-cos5] **flow-wred network**[*HUAWEI-flow-queue-template-network-cos5] **pir 200000 cir 120000**[*HUAWEI-flow-queue-template-network-cos5] **commit**[ ~ HUAWEI-flow-queue-template-network-cos5] **quit**[ ~ HUAWEI-flow-queue-template-network] **quit**完成上述配置后，执行命令 **display flow-queue configuration verbose****network** ，可以查看名为 network 的 8 队列增强模式的流队列模板信息。[ ~ HUAWEI] **display flow-queue configuration verbose network**Codes: Cos(Priority of queue's)CIR-PCT(The percentage of committed information rate)CIR-P(The priority schedule of cir)CIR-W(Schedule weight of cir)PIR-PCT(The percentage of peak information rate)PIR-P(The priority schedule of pir)PIR-W(Schedule weight of pir)Drop-Arith(The name of the WRED object used by the flow queue)Flow Queue Template : network                   mode: 8cos-enhancemode------------------------------------------------------------------------------------------Cos CIR/CIR-PCT/CBS/CIR-P/CIR-W  PIR/PIR-PCT/PBS/PIR-P/PIR-W  CAR-MODE Remark DropArith------------------------------------------------------------------------------------------0  -/-/-/1/10          -/-/-/3/10          0    0    TailDrop1  -/-/-/1/10          -/-/-/3/10          0    0    TailDrop2  -/-/-/1/10          -/-/-/3/10          0    0    TailDrop3  -/-/-/1/15          -/-/-/3/15          0    0    TailDrop4  -/-/-/1/15          -/-/-/3/15          0    0    TailDrop5  120000/-/-/0/20        200000/-/-/2/20        0    0network6  -/-/-/0/20          -/-/-/2/20          0    0    TailDrop7  -/-/-/0/20          -/-/-/2/20          0    0    TailDropReference relationships : NULLd. 创建名为 network 的 8 队列增强模式用户队列模板，并配置用户队列的峰值信息速率（ PIR ）和承诺信息速率（ CIR ）等调度参数。[ ~ HUAWEI] **user-queue network**[*HUAWEI-user-queue-template-network] **pir 8000000 cir 4000000**[*HUAWEI-user-queue-template-network] **priority 0 cir-parent-priority 0 pir-parent-priority 3**[*HUAWEI-user-queue-template-network] **priority 0 pir 800000**[*HUAWEI-user-queue-template-network] **commit**[ ~ HUAWEI-user-queue-template-network] **quit**e. 创建名为 network 的 8 队列增强模式 QoS 模板。[ ~ HUAWEI] **qos-profile network 8cos-enhance-mode**[*HUAWEI-qos-profile-network-8cos-enhance] **user-queue network flow-queue network**[*HUAWEI-qos-profile-network-8cos-enhance] **commit**文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 255HUAWEI NetEngine40E配置指南 1 QoS[ ~ HUAWEI-qos-profile-network-8cos-enhance] **quit**f. 在 P 节点入接口 GE1/0/3.1 上使能接口报文的 DEI 能力。[ ~ HUAWEI] **mpls**[*HUAWEI-mpls] **mpls** **ldp**[*HUAWEI-mpls-ldp] **commit**[ ~ HUAWEI-mpls-ldp] **quit**[ ~ HUAWEI] **interface gigabitethernet** **1/0/3.1**[*HUAWEI-GigabitEthernet1/0/3.1] **vlan-type dot1q 11**[*HUAWEI-GigabitEthernet1/0/3.1] **ip address ************ 24**[*HUAWEI-GigabitEthernet1/0/3.1] **trust upstream default**[*HUAWEI-GigabitEthernet1/0/3.1] **mpls**[*HUAWEI-GigabitEthernet1/0/3.1] **mpls ldp**[*HUAWEI-GigabitEthernet1/0/3.1] **trust 8021p**[*HUAWEI-GigabitEthernet1/0/3.1] **field dei enable**[*HUAWEI-GigabitEthernet1/0/3.1] **commit**[ ~ HUAWEI-GigabitEthernet1/0/3.1] **quit**在 P 节点出接口 GE1/0/4.1 上应用 QoS 模板。[ ~ HUAWEI] **interface gigabitethernet 1/0/4.1**[*HUAWEI-GigabitEthernet1/0/4.1] **vlan-type dot1q 22**[*HUAWEI-GigabitEthernet1/0/4.1] **ip address ************ 24**[*HUAWEI-GigabitEthernet1/0/4.1] **trust upstream default**[*HUAWEI-GigabitEthernet1/0/4.1] **mpls**[*HUAWEI-GigabitEthernet1/0/4.1] **mpls ldp**[*HUAWEI-GigabitEthernet1/0/4.1] **trust 8021p**[*HUAWEI-GigabitEthernet1/0/4.1] **qos-profile network outbound identifier none**[*HUAWEI-GigabitEthernet1/0/4.1] **field dei enable**[*HUAWEI-GigabitEthernet1/0/4.1] **commit**[ ~ HUAWEI-GigabitEthernet1/0/4.1] **quit**完成上述配置后，执行命令 **display qos-profile application** profilename ，可以看到 QoS 模板的配置和应用信息。[ ~ HUAWEI] **display qos-profile application network**qos-profile network:GigabitEthernet1/0/4.1Reference number by access user:[inbound] 0, [outbound] 0Reference number by VNI:[inbound] 0, [outbound] 0g. 检查配置结果。在 GE1/0/4.1 接口查询 QoS 模板统计信息，可以看到 cos 5 队列限速结果。[ ~ HUAWEI] **display qos-profile statistics interface gigabitethernet 1/0/4.1 outbound**GigabitEthernet1/0/4.1 outbound traffic statistics:[COS0]Pass              0 packets,             0 bytesDiscard             0 packets,             0 bytesLast 5 minutes pass rate0 pps,               0 bpsLast 5 minutes discard rate0 pps,               0 bpsbuffer size           10496 kbytesused buffer size        0 kbytes[COS1]Pass              0 packets,             0 bytesDiscard             0 packets,             0 bytesLast 5 minutes pass rate0 pps,               0 bpsLast 5 minutes discard rate0 pps,               0 bpsbuffer size           10496 kbytesused buffer size        0 kbytes[COS2]Pass              0 packets,             0 bytesDiscard             0 packets,             0 bytesLast 5 minutes pass rate0 pps,               0 bps文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 256HUAWEI NetEngine40E配置指南 1 QoSLast 5 minutes discard rate0 pps,               0 bpsbuffer size           10496 kbytesused buffer size        0 kbytes[COS3]Pass              0 packets,             0 bytesDiscard             0 packets,             0 bytesLast 5 minutes pass rate0 pps,               0 bpsLast 5 minutes discard rate0 pps,               0 bpsbuffer size           10496 kbytesused buffer size        0 kbytes[COS4]Pass              0 packets,             0 bytesDiscard             0 packets,             0 bytesLast 5 minutes pass rate0 pps,               0 bpsLast 5 minutes discard rate0 pps,               0 bpsbuffer size           10496 kbytesused buffer size        0 kbytes[COS5]Pass              0 packets,             0 bytesDiscard             0 packets,             0 bytesLast 5 minutes pass rate0 pps,               0 bpsLast 5 minutes discard rate0 pps,               0 bpsbuffer size           2560 kbytesused buffer size        0 kbytes[COS6]Pass              0 packets,             0 bytesDiscard             0 packets,             0 bytesLast 5 minutes pass rate0 pps,               0 bpsLast 5 minutes discard rate0 pps,               0 bpsbuffer size           2560 kbytesused buffer size        0 kbytes[COS7]Pass              0 packets,             0 bytesDiscard             0 packets,             0 bytesLast 5 minutes pass rate0 pps,               0 bpsLast 5 minutes discard rate0 pps,               0 bpsbuffer size           2560 kbytesused buffer size        0 kbytes[total]Pass              0 packets,             0 bytesDiscard             0 packets,             0 bytesLast 5 minutes pass rate0 pps,               0 bpsLast 5 minutes discard rate0 pps,               0 bpsTraffic towards this interface                    0 bpsConfigured CIR and PIR400,000 kbps,            800,000 kbps**----**结束##### 配置文件       - PE1 节点配置：slot 3qos user-queue resource 8cos-enhance 1000 inboundqos user-queue resource 8cos-enhance 1000 outbound#文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 257HUAWEI NetEngine40E配置指南 1 QoSflow-queue qos1 8cos-enhance-modecos 5remark-color enablepir 20000 cir 20000#flow-queue qos2 8cos-enhance-modecos 5remark-color enablepir 200000 cir 100000#user-queue qospir 800000 cir 400000priority 0 cir-parent-priority 0 pir-parent-priority 3priority 0 pir 800000#qos-profile qos1 8cos-enhance-modeuser-queue qos flow-queue qos1#qos-profile qos2 8cos-enhance-modeuser-queue qos flow-queue qos2#interface gigabitethernet 1/0/0.1vlan-type dot1q 1ip address ************ 24trust upstream defaulttrust 8021pqos-profile qos1 inbound identifier none#interface gigabitethernet 2/0/0.1vlan-type dot1q 2ip address ******** 24trust upstream defaulttrust 8021pqos-profile qos2 inbound identifier none#mplsmpls ldp#interface gigabitethernet 3/0/0.1vlan-type dot1q 11ip address ************ 24trust upstream defaultmplsmpls ldptrust 8021pfield dei enable#       - P 节点配置：slot 3qos user-queue resource 8cos-enhance 1000 inboundqos user-queue resource 8cos-enhance 1000 outbound#flow-wred networkcolor yellow low-limit 50 high-limit 50 discard-percentage 100#flow-queue network 8cos-enhance-modecos 5flow-wred networkpir 200000 cir 120000#user-queue networkpir 8000000 cir 4000000priority 0 cir-parent-priority 0 pir-parent-priority 3priority 0 pir 800000#qos-profile network 8cos-enhance-modeuser-queue network flow-queue network#mpls文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 258HUAWEI NetEngine40E配置指南 1 QoSmpls ldp#interface gigabitethernet 1/0/3.1vlan-type dot1q 11ip address ************ 24trust upstream defaultmplsmpls ldptrust 8021pfield dei enable#interface gigabitethernet 1/0/4.1vlan-type dot1q 22ip address ************ 24trust upstream defaultmplsmpls ldptrust 8021pqos-profile network outbound identifier nonefield dei enable#### 1.9 MPLS DiffServ 模式配置介绍了 MPLS DiffServ 模式的基本原理、配置过程和配置举例。#### 1.9.1 MPLS DiffServ 模式概述##### MPLS DiffServ COS 处理模式DiffServ 体系结构允许 DS 域内的中间节点检查并修改 IP Precedence 、 DSCP 或 Exp 值，统称 COS （ Class of Service ）值，这会导致报文的 COS 值在 IP 网络和 MPLS 网络传输过程中都可能发生变化。图 **1-29** MPLS DiffServ COS 处理模式因此，在报文进入 MPLS 网络或从 MPLS 网络离开进入 IP 网络时，运营商需要在 MPLS 边缘路由器对 COS （ Class of Service ）处理做出选择：是否信任 IP/MPLS 报文已经携带的COS 信息。相关标准中定义了三种 COS 处理模式： Uniform 、 Pipe 和 Short Pipe 。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 259HUAWEI NetEngine40E配置指南 1 QoS#### 1.9.2 MPLS DiffServ 模式配置注意事项##### 特性限制表 **1-23** 本特性的使用限制|特性限制|系列|涉及产品||---|---|---||MPLS报文P节点和出PE节点，MPLS报文根据EXP<br>按默认域映射入队，不受入接口的简单流配置影<br>响。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK||不支持全局配置l3vpn difserv-mode short-pipe<br>enhance enable，本地vrf不支持按私网报文优先<br>级入队|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK||IP OVER RSVP-TE TTL的场景，egress节点TTL<br>mode为uniform时，IP的TTL继承外层MPLS TTL<br>- 1。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK||入PE节点，报文根据入口配置简单流的配置入<br>队，与vpn实例下difserv-mode配置和出口配置<br>无关|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|#### 1.9.3 MPLS DiffServ 配置解析##### BA 和 PHB 详解了解本节之情，请先了解 BA 和 PHB 的解析，详细信息请参见 BA 和 PHB 详解。##### MPLS DiffServ 场景的 BA 、 PHB 动作剖析在 MPLS diffserv 场景中，有三种模式： Uniform ， Pipe ， Short-Pipe 。       - Uniform ：按照报文原始标记设置穿越 MPLS 网络的优先级标记。从 MPLS 网出来的时候，用户报文的优先级标记按照要求重标记。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 260HUAWEI NetEngine40E配置指南 1 QoS       - Pipe 和 short-pipe ：不关心报文原始标记，在穿越骨干网络时，统一打一个固定的优先级标记。从骨干网出来的时候，用户报文的优先级标记要保持不变。–Pipe ：骨干网尾节点不按照报文原始优先级标记进行调度。–short-pipe ：尾节点按照报文原始优先级标记进行调度。图 **1-30** PE 节点的 BA 、 PHB 动作剖析在 PE 的网络侧接口要同时做 BA 和 PHB ，因此要配置 **trust upstream** 命令。在 PE 的用户侧接口，上行要做 BA ，下行方向：       - Uniform 模式：需执行 PHB 。即， Uniform 模式， PE 的用户侧接口要同时做 BA 和PHB ，因此要配置 **trust upstream** 命令。       - Pipe 和 short-pipe 模式： PE 的用户侧接口只做 BA 不做 PHB 。 **diffserv-mode** 命令与**trust upstream** 命令互斥。图 **1-31** P 节点的 BA 、 PHB 动作剖析P 节点两侧接口要同时做 BA 和 PHB ，因此要配置 **trust upstream** 命令。#### 1.9.4 配置 MPLS TE 的 Uniform/Pipe 模式介绍 MPLS TE 的 Uniform/Pipe 模式的配置过程。##### 应用环境在 MPLS 公网中，为了保证不同 MPLS TE 业务的优先级，可以配置 Uniform/Pipe 模式实现根据不同的服务等级进行队列调度。##### 前置任务在配置 MPLS TE 的 Uniform/Pipe 之前，需要完成以下任务：文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 261HUAWEI NetEngine40E配置指南 1 QoS       - 配置各接口的物理参数、链路属性，保证接口的正常工作。       - 配置 PE 间的 MPLS TE 隧道。请参见《 HUAWEI NetEngine40E 路由器 配置指南       MPLS 》中的“ MPLS TE 配置”。说明在配置 MPLS TE 的 Uniform/Pipe 模式之前必须确定 MPLS TE 的状态为 UP 。在 Ingress PE 的用户侧 Tunnel 接口上进行以下配置。配置后只对 Ingress PE 节点产生影响，对于倒数第二跳节点默认都是采用 Uniform 模式。可参考“配置 MPLS 倒数第二跳节点的 Uniform/Pipe 模式”来改变倒数第二跳节点的 DiffServ 模式。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **interface tunnel** interface-number ，进入用户侧的 Tunnel 接口视图。步骤 **3** 执行命令 **diffserv-mode** { **pipe** service-class color | **uniform** } ，设置 MPLS TE 的DiffServ 模式。步骤 **4** 执行命令 **commit** ，提交配置。**----**结束#### 1.9.5 配置 VPN 的 Pipe/Short Pipe 模式配置 VPN 的 DiffServ 模式来实现不同 VPN 根据不同的服务等级进行队列调度。##### 应用环境1. 本特性可以单独在 Ingress PE 、 Egress PE 或同时应用：–若在 Ingress PE 用户侧接口同时配置了简单流分类和 Pipe/Short Pipe 模式，优先支持 Pipe/Short Pipe 的 DiffServ 模式。– 若在 PE 上配置了支持 DiffServ 模式配置为 Pipe/Short Pipe 时，则不需要配置简单流分类。– 若在 PE 上配置了支持 DiffServ 模式配置为 Uniform 时，则需要同时配置简单流分类。2. 需要在符合以下配置的 L3VPN 中 DiffServ 模式才能生效：–在 Egress PE 节点下 DiffServ 模式配置为 Pipe 或 Short Pipe 模式时，且下行出接口配置简单流分类时，需在 Egress PE 节点的流量出接口配置 **qos phb****disable** 命令。– 在 Egress PE 节点下 DiffServ 模式配置为 Uniform 模式时，且下行出接口配置简单流分类时，在 Egress PE 节点的流量出接口不用配置 **qos phb disable** 命令。3. 在如下情况下，建议在 P 节点上也配置简单流分类，使 P 节点根据不同的服务等级对报文进行调度：– P 节点性能有限，可能发生拥塞。– 在 P 节点实现 LDP over TE ， TE 隧道接口在 P 节点上，且隧道配置了优先级。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 262HUAWEI NetEngine40E配置指南 1 QoS##### 前置任务在配置基于 VPN Pipe/Short Pipe 之前，需要完成以下任务：       - 配置 PE 间的 MPLS TE 隧道。请参见《 HUAWEI NetEngine40E 路由器 配置指南       MPLS 》中的“ MPLS TE 配置”。       - 配置 VPN 业务，可根据具体业务需要选用不同的 L3VPN 或 L2VPN ，请参见《 HUAWEI NetEngine40E 路由器 配置指南 -VPN 》。       - 在 Ingress PE 用户侧接口配置简单流分类或者复杂流分类。请参见《 HUAWEINetEngine40E 路由器 配置指南 -QoS 》中的“基于类的 QoS 配置”。##### 操作步骤       - 配置 L3VPN 支持 DiffServ 模式a. 执行命令 **system-view** ，进入系统视图。b. （可选）执行命令 **l3vpn diffserv-mode short-pipe enhance enable** ，配置 L3VPN 场景下 Short Pipe 增强模式。在差分服务模式为 Short Pipe 模式下执行 **l3vpn diffserv-mode short-pipe****enhance enable** 命令才能生效。c. 执行命令 **ip vpn-instance** vpn-instance-name ，进入 VPN 实例视图。d. 执行命令 **diffserv-mode** { **pipe** service-class [ color ] | **short-pipe** serviceclass [ color ] [ **domain** ds-name ] | **uniform** } 或 **diffserv-mode** **ingress**{ **uniform** | **pipe** service-class color | **short-pipe** service-class color }**egress** { **uniform** | **pipe** | **short-pipe** [ **domain** ds-name ] } ，设置 VPN 实例的 DiffServ 模式。**diffserv-mode** 命令行除支持单播场景外，同时支持组播 NGMVPN 场景。e. 执行命令 **commit** ，提交配置。       - 配置 VLL 支持 DiffServ 模式a. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **interface** interface-type interface-number ，进入用户侧接口的接口视图。说明该接口为绑定了 L2VPN 的用户侧接口。c. 执行命令 **diffserv-mode** { **pipe** service-class color | **short-pipe** serviceclass color [ **domain** ds-name ] | **uniform** } 或 **diffserv-mode** **ingress**{ **uniform** | **pipe** service-class color | **short-pipe** service-class color }**egress** { **uniform** | **pipe** | **short-pipe** [ **trust** { **inner-vlan-8021p** | **ip-****dscp** } ] [ **domain** ds-name ] } ，设置 VLL 的 DiffServ 模式。d. 执行命令 **commit** ，提交配置。       - 配置 VPLS 支持 DiffServ 模式a. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **vsi** vsi-name ，进入 VSI 实例视图。c. 执行命令 **diffserv-mode** { **pipe** service-class color | **short-pipe** serviceclass color [ **domain** ds-name ] | **uniform** } 或 **diffserv-mode** **ingress**{ **uniform** | **pipe** service-class color | **short-pipe** service-class color }**egress** { **uniform** | **pipe** | **short-pipe** [ **trust** { **inner-vlan-8021p** | **ip-****dscp** } ] [ **domain** ds-name ] } ，设置 VPLS 的 DiffServ 模式。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 263HUAWEI NetEngine40E配置指南 1 QoSd. 执行命令 **commit** ，提交配置。       - 配置 EVPN 支持 DiffServ 模式– 在 EVPN 实例视图和 BD-EVPN 实例视图下配置 DiffServ 模式。i. 执行命令 **system-view** ，进入系统视图。ii. 配置 EVPN 实例，具体请参照配置 EVPN 实例。iii. 执行命令 **diffserv-mode** { **pipe** service-class color | **short-pipe**service-class color [ **domain** ds-name ] | **uniform** } 或 **diffserv-mode****ingress** { **uniform** | **pipe** service-class color | **short-pipe** service-classcolor } **egress** { **uniform** | **pipe** | **short-pipe** [ **trust** { **inner-****vlan-8021p** | **ip-dscp** } ] [ **domain** ds-name ] } ，设置 EVPN 的 DiffServ模式。iv. 执行命令 **commit** ，提交配置。– 在 EVPL 实例视图下配置 DiffServ 模式。i. 执行命令 **system-view** ，进入系统视图。ii. 配置 EVPL 实例，具体请参照配置 EVPL 实例。iii. 执行命令 **diffserv-mode** { **pipe** service-class color | **short-pipe**service-class color [ **domain** ds-name ] | **uniform** } 或 **diffserv-mode****ingress** { **uniform** | **pipe** service-class color | **short-pipe** service-classcolor } **egress** { **uniform** | **pipe** | **short-pipe** [ **trust** { **inner-****vlan-8021p** | **ip-dscp** } ] [ **domain** ds-name ] } ，设置 EVPN 的 DiffServ模式。iv. 执行命令 **commit** ，提交配置。**----**结束#### 1.9.6 配置举例介绍了配置 MPLS DiffServ 模式示例。##### ******* 配置 MPLS DiffServ 模式示例 组网需求如 图 **1-32** 所示， CE1 、 CE3 属于 VPN-A ， CE2 、 CE4 属于 VPN-B 。 VPN-A 使用的 VPNtarget 属性为 111:1 ， VPN-B 为 222:2 ，不同 VPN 用户之间不能互相访问。在 PE1 和 PE2上配置 MPLS DiffServ 模式为 Pipe ，使 VPN 中的数据业务在 MPLS 网络中以运营商配置的优先级转发，其中在 P 节点也要按优先级进行调度。说明本例中 interface1 ， interface2 ， interface3 分别代表 GE1/0/0 ， GE2/0/0 ， GE3/0/0 。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 264HUAWEI NetEngine40E配置指南 1 QoS图 **1-32** MPLS DiffServ 模式组网图##### 配置思路采用如下的思路配置 MPLS DiffServ 模式：1. 骨干网上配置 OSPF 实现 PE 之间的互通。2. 配置 MPLS 基本功能和 MPLS LDP ，建立 MPLS LSP 。3. PE 之间配置 MP-IBGP 交换 VPN 路由信息。4. PE 上配置 VPN 实例，并把与 CE 相连的接口和相应的 VPN 实例绑定。5. CE 与 PE 之间配置 EBGP 交换 VPN 路由信息。6. 在 VPN-A 和 VPN-B 分别使能 Pipe 模式，同时把不同的 Differ-serve 域应用到不同的VPN 实例中。7. 在 P 节点上配置简单流分类。##### 数据准备为完成此配置例，需准备如下的数据：       - PE 及 P 上的 MPLS LSR-ID       - VPN-A 与 VPN-B 的路由区分符 RD       - VPN-A 与 VPN-B 的收发路由属性 VPN-Target       - PE1 和 PE2 上配置不同的 Differ-serve 域##### 操作步骤步骤 **1** 在 MPLS 骨干网上配置 IGP 协议，实现骨干网 PE 和 P 的互通文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 265HUAWEI NetEngine40E配置指南 1 QoS# 配置 PE1 。<HUAWEI> **system-view**[ ~ HUAWEI] **sysname PE1**[*HUAWEI] **commit**[ ~ PE1] **interface loopback 1**[*PE1-LoopBack1] **ip address ******* 32**[*PE1-LoopBack1] **commit**[ ~ PE1-LoopBack1] **quit**[ ~ PE1] **interface gigabitethernet 3/0/0**[ ~ PE1-GigabitEthernet3/0/0] **ip address ********** 24**[*PE1-GigabitEthernet3/0/0] **commit**[ ~ PE1-GigabitEthernet3/0/0] **quit**[ ~ PE1] **ospf**[*PE1-ospf-1] **area 0**[*PE1-ospf-1-area-0.0.0.0] **network ********** ***********[*PE1-ospf-1-area-0.0.0.0] **network ******* 0.0.0.0**[*PE1-ospf-1-area-0.0.0.0] **commit**[ ~ PE1-ospf-1-area-0.0.0.0] **quit**[ ~ PE1-ospf-1] **quit**# 配置 P 。<HUAWEI> **system-view**[ ~ HUAWEI] **sysname P**[*HUAWEI] **commit**[ ~ P] **interface loopback 1**[*P-LoopBack1] **ip address ******* 32**[*P-LoopBack1] **commit**[ ~ P-LoopBack1] **quit**[ ~ P] **interface gigabitethernet 1/0/0**[ ~ P-GigabitEthernet1/0/0] **ip address ********** 24**[*P-GigabitEthernet1/0/0] **commit**[ ~ P-GigabitEthernet1/0/0] **quit**[ ~ P] **interface gigabitethernet 2/0/0**[ ~ P-GigabitEthernet2/0/0] **ip address ********** 24**[*P-GigabitEthernet2/0/0] **commit**[ ~ P-GigabitEthernet2/0/0] **quit**[ ~ P] **ospf**[*P-ospf-1] **area 0**[*P-ospf-1-area-0.0.0.0] **network ********** ***********[*P-ospf-1-area-0.0.0.0] **network ********** ***********[*P-ospf-1-area-0.0.0.0] **network ******* 0.0.0.0**[*P-ospf-1-area-0.0.0.0] **commit**[ ~ P-ospf-1-area-0.0.0.0] **quit**[ ~ P-ospf-1] **quit**# 配置 PE2 。<HUAWEI> **system-view**[ ~ HUAWEI] **sysname PE2**[*HUAWEI] **commit**[ ~ PE2] **interface loopback 1**[*PE2-LoopBack1] **ip address ******* 32**[*PE2-LoopBack1] **commit**[ ~ PE2-LoopBack1] **quit**[ ~ PE2] **interface gigabitethernet 3/0/0**[ ~ PE2-GigabitEthernet3/0/0] **ip address ********** 24**[*PE2-GigabitEthernet3/0/0] **commit**[ ~ PE2-GigabitEthernet3/0/0] **quit**[ ~ PE2] **ospf**[*PE2-ospf-1] **area 0**[*PE2-ospf-1-area-0.0.0.0] **network ********** ***********[*PE2-ospf-1-area-0.0.0.0] **network ******* 0.0.0.0**[*PE2-ospf-1-area-0.0.0.0] **commit**[ ~ PE2-ospf-1-area-0.0.0.0] **quit**[ ~ PE2-ospf-1] **quit**文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 266HUAWEI NetEngine40E配置指南 1 QoS配置完成后， PE1 、 P 、 PE2 之间应能建立 OSPF 邻居关系，执行 **display ospf peer** 命令可以看到邻居状态为 Full 。执行 **display ip routing-table** 命令可以看到 PE 之间学习到对方的 Loopback1 路由。以 PE1 的显示为例：[ ~ PE1] **display ospf peer**(M) Indicates MADJ neighborOSPF Process 1 with Router ID *******NeighborsArea 0.0.0.0 interface **********(GigabitEthernet3/0/0)'s neighborsRouter ID: *******      Address: **********State: **Full** Mode:Nbr is Master  Priority: 1DR: **********   BDR: **********    MTU: 0Dead timer due in 38 secRetrans timer interval: 5Neighbor is up for 00h02m45sNeighbor Up Time : 2020-08-15 01:41:57Authentication Sequence: [ 0 ][ ~ PE1] **display ip routing-table**Route Flags: R - relay, D - download to fib, T - to vpn-instance, B - black hole route-----------------------------------------------------------------------------Routing Table: _public_Destinations : 8    Routes : 8Destination/Mask Proto Pre Cost       Flags NextHop     Interface*******/32 Direct 0  0        D 127.0.0.1     LoopBack1*******/32 OSPF  10  1        D **********     GigabitEthernet3/0/0*******/32 OSPF  10  2        D **********     GigabitEthernet3/0/0127.0.0.0/8  Direct 0  0        D 127.0.0.1     InLoopBack0127.0.0.1/32 Direct 0  0        D 127.0.0.1     InLoopBack0**********/24 Direct 0  0        D **********     GigabitEthernet3/0/0**********/32 Direct 0  0        D 127.0.0.1     InLoopBack0**********/24 OSPF  10  2        D **********     GigabitEthernet3/0/0步骤 **2** 在 MPLS 骨干网上配置 MPLS 基本能力和 MPLS LDP ，建立 LDP LSP# 配置 PE1 。[ ~ PE1] **mpls lsr-id *********[*PE1] **mpls**[*PE1-mpls] **commit**[ ~ PE1-mpls] **quit**[ ~ PE1] **mpls ldp**[*PE1-mpls-ldp] **commit**[ ~ PE1-mpls-ldp] **quit**[ ~ PE1] **interface gigabitethernet 3/0/0**[ ~ PE1-GigabitEthernet3/0/0] **mpls**[*PE1-GigabitEthernet3/0/0] **mpls ldp**[*PE1-GigabitEthernet3/0/0] **commit**[ ~ PE1-GigabitEthernet3/0/0] **quit**# 配置 P 。[ ~ P] **mpls lsr-id *********[*P] **mpls**[*P-mpls] **commit**[ ~ P-mpls] **quit**[ ~ P] **mpls ldp**[*P-mpls-ldp] **commit**[ ~ P-mpls-ldp] **quit**[ ~ P] **interface gigabitethernet 1/0/0**[ ~ P-GigabitEthernet1/0/0] **mpls**[*P-GigabitEthernet1/0/0] **mpls ldp**[*P-GigabitEthernet1/0/0] **commit**[ ~ P-GigabitEthernet1/0/0] **quit**[ ~ P] **interface gigabitethernet 2/0/0**[ ~ P-GigabitEthernet2/0/0] **mpls**文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 267HUAWEI NetEngine40E配置指南 1 QoS[*P-GigabitEthernet2/0/0] **mpls ldp**[*P-GigabitEthernet2/0/0] **commit**[ ~ P-GigabitEthernet2/0/0] **quit**# 配置 PE2 。[ ~ PE2] **mpls lsr-id *********[*PE2] **mpls**[*PE2-mpls] **commit**[ ~ PE2-mpls] **quit**[ ~ PE2] **mpls ldp**[*PE2-mpls-ldp] **commit**[ ~ PE2-mpls-ldp] **quit**[ ~ PE2] **interface gigabitethernet 3/0/0**[ ~ PE2-GigabitEthernet3/0/0] **mpls**[*PE2-GigabitEthernet3/0/0] **mpls ldp**[*PE2-GigabitEthernet3/0/0] **commit**[ ~ PE2-GigabitEthernet3/0/0] **quit**上述配置完成后， PE1 与 P 、 P 与 PE2 之间应能建立 LDP 会话，执行 **display mpls ldp****session** 命令可以看到显示结果中 Status 项为“ Operational ”。执行 **display mpls ldp****lsp** 命令，可以看到 LDP LSP 的建立情况。以 PE1 的显示为例：[ ~ PE1] **display mpls ldp session**LDP Session(s) in Public NetworkCodes: LAM(Label Advertisement Mode), SsnAge Unit(DDDD:HH:MM)An asterisk (*) before a session means the session is being deleted.------------------------------------------------------------------------PeerID       Status   LAM SsnRole SsnAge   KASent/Rcv------------------------------------------------------------------------*******:0 **Operational** DU Passive 0000:00:01 5/5------------------------------------------------------------------------TOTAL: 1 Session(s) Found.[ ~ PE1] **display mpls ldp lsp**LDP LSP Information------------------------------------------------------------------------------Flag after Out IF: (I) - RLFA Iterated LSP, (I*) - Normal and RLFA Iterated LSP------------------------------------------------------------------------------DestAddress/Mask  In/OutLabel  UpstreamPeer  NextHop     OutInterface------------------------------------------------------------------------------*******/32     3/NULL    *******    127.0.0.1    Loopback1********/32     Liberal/48061        DS/**************/32     NULL/3    -       **********   GigabitEthernet3/0/0*******/32    48061/3    *******    **********   GigabitEthernet3/0/0*******/32     NULL/48062  -       **********   GigabitEthernet3/0/0*******/32    48062/48062   *******    **********   GigabitEthernet3/0/0------------------------------------------------------------------------------TOTAL: 5 Normal LSP(s) Found.TOTAL: 1 Liberal LSP(s) Found.TOTAL: 0 FRR LSP(s) Found.An asterisk (*) before an LSP means the LSP is not establishedAn asterisk (*) before a Label means the USCB or DSCB is staleAn asterisk (*) before an UpstreamPeer means the session is staleAn asterisk (*) before a DS means the session is staleAn asterisk (*) before a NextHop means the LSP is FRR LSP步骤 **3** 在 PE 之间建立 MP-IBGP 对等体关系# 配置 PE1 。[ ~ PE1] **bgp 100**[*PE1-bgp] **peer ******* as-number 100**[*PE1-bgp] **peer ******* connect-interface loopback 1**[*PE1-bgp] **ipv4-family vpnv4**[*PE1-bgp-af-vpnv4] **peer ******* enable**Warning: This operation will reset the peer session. Continue? [Y/N]: **y**文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 268HUAWEI NetEngine40E配置指南 1 QoS[*PE1-bgp-af-vpnv4] **commit**[ ~ PE1-bgp-af-vpnv4] **quit**[ ~ PE1-bgp] **quit**# 配置 PE2 。[ ~ PE2] **bgp 100**[*PE2-bgp] **peer ******* as-number 100**[*PE2-bgp] **peer ******* connect-interface loopback 1**[*PE2-bgp] **ipv4-family vpnv4**[*PE2-bgp-af-vpnv4] **peer ******* enable**Warning: This operation will reset the peer session. Continue? [Y/N]: **y**[*PE2-bgp-af-vpnv4] **commit**[ ~ PE2-bgp-af-vpnv4] **quit**[ ~ PE2-bgp] **quit**配置完成后，在 PE 设备上执行 **display bgp peer** 或 **display bgp vpnv4 all peer** 命令，可以看到 PE 之间的 BGP 对等体关系已建立，并达到 Established 状态。[ ~ PE1] **display bgp vpnv4 all peer**BGP local router ID : *******Local AS number : 100Total number of peers : 1         Peers in established state : 1Peer      V  AS MsgRcvd MsgSent  OutQ Up/Down  State    PrefRcv*******     4  100  12   18     0   00:09:38 **Established** 0步骤 **4** 在 PE 设备上配置 VPN 实例，将 CE 接入 PE# 配置 PE1 。[ ~ PE1] **ip vpn-instance vpna**[*PE1-vpn-instance-vpna] **route-distinguisher 100:1**[*PE1-vpn-instance-vpna-af-ipv4] **vpn-target 111:1 both**[*PE1-vpn-instance-vpna-af-ipv4] **commit**[ ~ PE1-vpn-instance-vpna-af-ipv4] **quit**[ ~ PE1-vpn-instance-vpna] **quit**[ ~ PE1] **ip vpn-instance vpnb**[*PE1-vpn-instance-vpnb] **route-distinguisher 100:2**[*PE1-vpn-instance-vpnb-af-ipv4] **vpn-target 222:2 both**[*PE1-vpn-instance-vpnb-af-ipv4] **commit**[ ~ PE1-vpn-instance-vpnb-af-ipv4] **quit**[ ~ PE1-vpn-instance-vpnb] **quit**[ ~ PE1] **interface gigabitethernet 1/0/0**[ ~ PE1-GigabitEthernet1/0/0] **ip binding vpn-instance vpna**[*PE1-GigabitEthernet1/0/0] **ip address ******** 24**[*PE1-GigabitEthernet1/0/0] **commit**[ ~ PE1-GigabitEthernet1/0/0] **quit**[ ~ PE1] **interface gigabitethernet 2/0/0**[ ~ PE1-GigabitEthernet2/0/0] **ip binding vpn-instance vpnb**[*PE1-GigabitEthernet2/0/0] **ip address ******** 24**[*PE1-GigabitEthernet2/0/0] **commit**[ ~ PE1-GigabitEthernet2/0/0] **quit**# 配置 PE2 。[ ~ PE2] **ip vpn-instance vpna**[*PE2-vpn-instance-vpna] **route-distinguisher 200:1**[*PE2-vpn-instance-vpna-af-ipv4] **vpn-target 111:1 both**[*PE2-vpn-instance-vpna-af-ipv4] **commit**[ ~ PE2-vpn-instance-vpna-af-ipv4] **quit**[ ~ PE2-vpn-instance-vpna] **quit**[ ~ PE2] **ip vpn-instance vpnb**[*PE2-vpn-instance-vpnb] **route-distinguisher 200:2**[*PE2-vpn-instance-vpnb-af-ipv4] **vpn-target 222:2 both**[*PE2-vpn-instance-vpnb-af-ipv4] **commit**[ ~ PE2-vpn-instance-vpnb-af-ipv4] **quit**[ ~ PE2-vpn-instance-vpnb] **quit**[ ~ PE2] **interface gigabitethernet 1/0/0**[ ~ PE2-GigabitEthernet1/0/0] **ip binding vpn-instance vpna**[*PE2-GigabitEthernet1/0/0] **ip address ******** 24**文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 269HUAWEI NetEngine40E配置指南 1 QoS[*PE2-GigabitEthernet1/0/0] **commit**[ ~ PE2-GigabitEthernet1/0/0] **quit**[ ~ PE2] **interface gigabitethernet 2/0/0**[ ~ PE2-GigabitEthernet2/0/0] **ip binding vpn-instance vpnb**[*PE2-GigabitEthernet2/0/0] **ip address ******** 24**[*PE2-GigabitEthernet2/0/0] **commit**[ ~ PE2-GigabitEthernet2/0/0] **quit**# 按 图 **1-32** 配置各 CE 的接口 IP 地址，配置过程略。配置完成后，在 PE 设备上执行 **display ip vpn-instance verbose** 命令可以看到 VPN 实例的配置情况。各 PE 能 ping 通自己接入的 CE 。说明当 PE 上有多个绑定了同一个 VPN 的接口，则使用 **ping -vpn-instance** 命令 ping 对端 PE 接入的 CE时，要指定源 IP 地址，即要指定 **ping -vpn-instance** vpn-instance-name **-a** source-ip-addressdest-ip-address 命令中的参数 **-a** source-ip-address ，否则可能 ping 不通。以 PE1 和 CE1 为例：[ ~ PE1] **display ip vpn-instance verbose**Total VPN-Instances configured : 2Total IPv4 VPN-Instances configured : 2Total IPv6 VPN-Instances configured : 0VPN-Instance Name and ID : vpna, 4Interfaces : GigabitEthernet1/0/0Address family ipv4Create date : 2020/09/21 11:30:35Up time : 0 days, 00 hours, 05 minutes and 19 secondsVrf Status : UPRoute Distinguisher : 100:1Export VPN Targets : 111:1Import VPN Targets : 111:1Label Policy : label per routeThe diffserv-mode Information is : uniformThe ttl-mode Information is : pipeLog Interval : 5Interfaces : GigabitEthernet1/0/0VPN-Instance Name and ID : vpnb, 5Interfaces : GigabitEthernet2/0/0Address family ipv4Create date : 2020/09/21 11:31:18Up time : 0 days, 00 hours, 04 minutes and 36 secondsVrf Status : UPRoute Distinguisher : 100:2Export VPN Targets : 222:2Import VPN Targets : 222:2Label Policy : label per routeThe diffserv-mode Information is : uniformThe ttl-mode Information is : pipeLog Interval : 5Interfaces : GigabitEthernet2/0/0[ ~ PE1] **ping -vpn-instance vpna **********PING ********: 56 data bytes, press CTRL_C to breakReply from ********: bytes=56 Sequence=1 ttl=255 time=56 msReply from ********: bytes=56 Sequence=2 ttl=255 time=4 msReply from ********: bytes=56 Sequence=3 ttl=255 time=4 msReply from ********: bytes=56 Sequence=4 ttl=255 time=52 msReply from ********: bytes=56 Sequence=5 ttl=255 time=3 ms--- ******** ping statistics --5 packet(s) transmitted5 packet(s) received0.00% packet lossround-trip min/avg/max = 3/23/56 ms文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 270HUAWEI NetEngine40E配置指南 1 QoS步骤 **5** 在 PE 与 CE 之间建立 EBGP 对等体关系，引入 VPN 路由# 配置 CE1 。[ ~ CE1] **bgp 65410**[*CE1-bgp] **peer ******** as-number 100**[*CE1-bgp] **import-route direct**[*CE1-bgp] **commit**说明另外 3 个 CE 设备（ CE2 ～ CE4 ）配置与 CE1 设备配置类似，配置过程省略。# 配置 PE1 。[ ~ PE1] **bgp 100**[*PE1-bgp] **ipv4-family vpn-instance vpna**[*PE1-bgp-vpna] **peer ******** as-number 65410**[*PE1-bgp-vpna] **import-route direct**[*PE1-bgp-vpna] **commit**[ ~ PE1-bgp-vpna] **quit**[ ~ PE1-bgp] **ipv4-family vpn-instance vpnb**[*PE1-bgp-vpnb] **peer ******** as-number 65420**[*PE1-bgp-vpnb] **import-route direct**[*PE1-bgp-vpnb] **commit**[ ~ PE1-bgp-vpnb] **quit**[ ~ PE1-bgp] **quit**说明PE2 的配置与 PE1 类似，配置过程省略。配置完成后，在 PE 设备上执行 **display bgp vpnv4 vpn-instance peer** 命令，可以看到PE 与 CE 之间的 BGP 对等体关系已建立，并达到 Established 状态。以 PE1 与 CE1 的对等体关系为例：[ ~ PE1] **display bgp vpnv4 vpn-instance vpna peer**BGP local router ID : *******Local AS number : 100VPN-Instance vpna, Router ID *******:Total number of peers : 1      Peers in established state : 1Peer      V  AS MsgRcvd MsgSent  OutQ Up/Down  State    PrefRcv********    4  65410 11   9     0   00:06:37 **Established** 1步骤 **6** 检查配置结果在 PE 设备上执行 **display ip routing-table vpn-instance** 命令，可以看到去往对端 CE的路由。以 PE1 的显示为例：[ ~ PE1] **display ip routing-table vpn-instance vpna**Route Flags: R - relay, D - download to fib-----------------------------------------------------------------------------Routing Table: vpnaDestinations : 3    Routes : 3Destination/Mask  Proto Pre Cost   Flags NextHop     Interface10.1.1.0/24  Direct 0  0    D   ********    GigabitEthernet1/0/0********/32  Direct 0  0    D   127.0.0.1    GigabitEthernet1/0/0**********/24** IBGP  255 0    RD  *******     GigabitEthernet3/0/0[ ~ PE1] **display ip routing-table vpn-instance vpnb**Route Flags: R - relay, D - download to fib-----------------------------------------------------------------------------Routing Table: vpnbDestinations : 3    Routes : 3文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 271HUAWEI NetEngine40E配置指南 1 QoSDestination/Mask  Proto Pre Cost   Flags NextHop     Interface10.2.1.0/24  Direct 0  0    D   ********    GigabitEthernet2/0/0********/32  Direct 0  0    D   127.0.0.1    GigabitEthernet2/0/0**********/24** IBGP  255 0    RD  *******     GigabitEthernet3/0/0同一 VPN 的 CE 能够相互 Ping 通，不同 VPN 的 CE 不能相互 Ping 通。例如： CE1 能够 Ping 通 CE3 （ ********/24 ），但不能 Ping 通 CE4 （ ********/24 ）。[ ~ CE1] **ping **********PING ********: 56 data bytes, press CTRL_C to breakReply from ********: bytes=56 Sequence=1 ttl=253 time=72 msReply from ********: bytes=56 Sequence=2 ttl=253 time=34 msReply from ********: bytes=56 Sequence=3 ttl=253 time=50 msReply from ********: bytes=56 Sequence=4 ttl=253 time=50 msReply from ********: bytes=56 Sequence=5 ttl=253 time=34 ms--- ******** ping statistics --5 packet(s) transmitted5 packet(s) received0.00% packet lossround-trip min/avg/max = 34/48/72 ms[ ~ CE1] **ping **********PING ********: 56 data bytes, press CTRL_C to breakRequest time outRequest time outRequest time outRequest time outRequest time out--- ******** ping statistics --5 packet(s) transmitted0 packet(s) received100.00% packet loss步骤 **7** 在 PE1 和 PE2 上配置 differ-serve 模式，同时将 Differ-serve 域应用到不同的 VPN 实例中# 配置 PE1 。[ ~ PE1] **ip vpn-instance vpna**[*PE1-vpn-instance-vpna] **ipv4-family**[*PE1-vpn-instance-vpna-af-ipv4] **diffserv-mode pipe af1 green**[*PE1-vpn-instance-vpna-af-ipv4] **commit**[ ~ PE1-vpn-instance-vpna-af-ipv4] **quit**[ ~ PE1-vpn-instance-vpna] **quit**[ ~ PE1] **ip vpn-instance vpnb**[*PE1-vpn-instance-vpnb] **ipv4-family**[*PE1-vpn-instance-vpnb-af-ipv4] **diffserv-mode pipe be yellow**[*PE1-vpn-instance-vpnb-af-ipv4] **commit**[ ~ PE1-vpn-instance-vpnb-af-ipv4] **quit**[ ~ PE1-vpn-instance-vpnb] **quit**# 配置 PE2 。[ ~ PE2] **ip vpn-instance vpna**[*PE2-vpn-instance-vpna] **ipv4-family**[*PE2-vpn-instance-vpna-af-ipv4] **diffserv-mode pipe af1 green**[*PE2-vpn-instance-vpna-af-ipv4] **commit**[ ~ PE2-vpn-instance-vpna-af-ipv4] **quit**[ ~ PE2-vpn-instance-vpna] **quit**[ ~ PE2] **ip vpn-instance vpnb**[*PE2-vpn-instance-vpnb] **ipv4-family**[*PE2-vpn-instance-vpnb-af-ipv4] **diffserv-mode pipe be yellow**[*PE2-vpn-instance-vpnb-af-ipv4] **commit**[ ~ PE2-vpn-instance-vpnb-af-ipv4] **quit**[ ~ PE2-vpn-instance-vpnb] **quit**步骤 **8** 在 P 节点上配置简单流分类# 配置 P 。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 272HUAWEI NetEngine40E配置指南 1 QoS[ ~ P] **interface gigabitethernet 1/0/0**[ ~ P-GigabitEthernet1/0/0] **trust upstream** **default**[*P-GigabitEthernet1/0/0] **quit**[*P] **interface gigabitethernet 2/0/0**[*P-GigabitEthernet2/0/0] **trust upstream** **default**[*P-GigabitEthernet2/0/0] **quit**[*P] **commit**步骤 **9** 检查配置结果在 PE 设备上执行 **display ip vpn-instance verbose vpna** 命令，可以看到该 VPN 实例下配置的 differ-serve 模式。以 PE1 的显示为例：[ ~ PE1] **display ip vpn-instance verbose vpna**VPN-Instance Name and ID : vpna, 23Address family ipv4Create date : 2020/09/21 11:08:12Up time : 0 days, 00 hours, 06 minutes and 32 secondsVrf Status : UPLabel Policy : label per route**The diffserv-mode Information is : pipe af1 green**The ttl-mode Information is : pipeLog Interval : 5**----**结束##### 配置文件       - PE1 的配置文件#sysname PE1#ip vpn-instance vpnaipv4-familyroute-distinguisher 100:1apply-label per-instancevpn-target 111:1 export-extcommunityvpn-target 111:1 import-extcommunitydiffserv-mode pipe af1 green#ip vpn-instance vpnbipv4-familyroute-distinguisher 100:2apply-label per-instancevpn-target 222:2 export-extcommunityvpn-target 222:2 import-extcommunitydiffserv-mode pipe be yellow#mpls lsr-id *******#mpls#mpls ldp#interface GigabitEthernet1/0/0undo shutdownip binding vpn-instance vpnaip address ******** *************#interface GigabitEthernet2/0/0undo shutdownip binding vpn-instance vpnbip address ******** *************#interface GigabitEthernet3/0/0undo shutdown文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 273HUAWEI NetEngine40E配置指南 1 QoSip address ********** *************mplsmpls ldp#interface LoopBack1ip address ******* ***************#bgp 100peer ******* as-number 100peer ******* connect-interface LoopBack1#ipv4-family unicastundo synchronizationpeer ******* enable#ipv4-family vpnv4policy vpn-targetpeer ******* enable#ipv4-family vpn-instance vpnaimport-route directpeer ******** as-number 65410#ipv4-family vpn-instance vpnbimport-route directpeer ******** as-number 65420#ospf 1area 0.0.0.0network ******* 0.0.0.0network ********** *********#return       - P 的配置文件#sysname P#mpls lsr-id *******#mpls#mpls ldp#interface GigabitEthernet1/0/0undo shutdownip address ********** *************mplsmpls ldptrust upstream default#interface GigabitEthernet2/0/0undo shutdownip address ********** *************mplsmpls ldptrust upstream default#interface LoopBack1ip address ******* ***************#ospf 1area 0.0.0.0network ******* 0.0.0.0network ********** *********network ********** *********#return       - PE2 的配置文件文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 274HUAWEI NetEngine40E配置指南 1 QoS#sysname PE2#ip vpn-instance vpnaipv4-familyroute-distinguisher 200:1apply-label per-instancevpn-target 111:1 export-extcommunityvpn-target 111:1 import-extcommunitydiffserv-mode pipe af1 green#ip vpn-instance vpnbipv4-familyroute-distinguisher 200:2apply-label per-instancevpn-target 222:2 export-extcommunityvpn-target 222:2 import-extcommunitydiffserv-mode pipe be yellow#mpls lsr-id *******#mpls#mpls ldp#interface GigabitEthernet1/0/0undo shutdownip binding vpn-instance vpnaip address ******** *************#interface GigabitEthernet2/0/0undo shutdownip binding vpn-instance vpnbip address ******** *************#interface GigabitEthernet3/0/0undo shutdownip address ********** *************mplsmpls ldp#interface LoopBack1ip address ******* ***************#bgp 100peer ******* as-number 100peer ******* connect-interface LoopBack1#ipv4-family unicastundo synchronizationpeer ******* enable#ipv4-family vpnv4policy vpn-targetpeer ******* enable#ipv4-family vpn-instance vpnapeer ******** as-number 65430import-route direct#ipv4-family vpn-instance vpnbpeer ******** as-number 65440import-route direct#ospf 1area 0.0.0.0network ******* 0.0.0.0network ********** *********文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 275HUAWEI NetEngine40E配置指南 1 QoS#return       - CE1 的配置文件#sysname CE1#interface GigabitEthernet1/0/0undo shutdownip address ******** *************#bgp 65410peer ******** as-number 100#ipv4-family unicastundo synchronizationimport-route directpeer ******** enable#return       - CE2 的配置文件#sysname CE2#interface GigabitEthernet1/0/0undo shutdownip address ******** *************#bgp 65420peer ******** as-number 100#ipv4-family unicastundo synchronizationimport-route directpeer ******** enable#return       - CE3 的配置文件#sysname CE3#interface GigabitEthernet1/0/0undo shutdownip address ******** *************#bgp 65430peer ******** as-number 100#ipv4-family unicastundo synchronizationimport-route directpeer ******** enable#return       - CE4 的配置文件#sysname CE4#interface GigabitEthernet1/0/0undo shutdownip address ******** *************#bgp 65440peer ******** as-number 100#ipv4-family unicast文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 276HUAWEI NetEngine40E配置指南 1 QoSundo synchronizationimport-route directpeer ******** enable#return### 1.10 用户接入 QoS 配置本章介绍了用户接入相关的 QoS 配置。#### 1.10.1 用户接入 QoS 配置注意事项##### 特性限制表 **1-24** 本特性的使用限制|特性限制|系列|涉及产品||---|---|---||当域下配置了user-max-session时，不支持基于<br>Option82对用户进行GQ限速。若用户上线接口<br>上配置基于option82识别的GQ模板且绑定了有<br>user-max-session配置的域时，用户上线会失<br>败。|NE40E|NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A||同一家庭的用户从不同子接口上线时，如果各子<br>接口配置不同补偿值按本家庭最后一个上线用户<br>所在子接口配置生效。建议各子接口配置相同的<br>最后一公里补偿值|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK||同一个家庭session-limit配置应保持一致，如果<br>配置不一致，不能确定按照哪条配置生效。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK||COA下发qos-profle模板时，如果前后模式不一<br>致（8queue模式和4queue模式），切换过程会<br>存在短暂用户HQOS不生效。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|#### 1.10.2 配置基于 UCL 的复杂流分类配置 UCL 可以实现不同优先级用户的区分，保证用户的承诺带宽和峰值带宽。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 277HUAWEI NetEngine40E配置指南 1 QoS##### 应用环境UCL 是针对用户级别的 ACL 控制。接入用户在认证的过程中，如果需要对一些用户的流量进行限制，例如允许或限制用户访问某些网站的权利，可以通过配置基于 UCL 的流量策略来实现。在接入设备上，配置 UCL 可以实现不同优先级用户的区分，并对用户的流量进行 QoS 保证，保证用户的承诺带宽和峰值带宽。 UCL 根据规格的配置方式分为用户到网络、用户到用户、网络到网络和网络到用户四类。当在 ACL 定义的规则中指定匹配规则的源用户组时为用户到网络类型，指定匹配规则的目的用户组时为网络到用户类型，既指定源用户组也指定目的用户组时为用户到用户类型，当源和目的都没有指定用户组时为网络到网络类型。说明UCL 规则中支持基于 IPv4 和 IPv6 进行流分类，其中 IPv6 只支持上行。##### 前置任务在配置 UCL 用户的流量策略之前，需要完成以下任务：       - 配置接口的链路层协议参数（和 IP 地址），使接口的链路协议状态为 Up       - 配置路由协议，实现骨干网的 IP 连通性       - 配置接入业务，使用户可以正常接入网络##### ******** 配置用户组创建需要进行流量策略的用户组，作为 UCL 匹配规格的对象。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **user-group** group-name ，创建一个新的用户组。步骤 **3** 执行命令 **commit** ，提交配置。**----**结束##### ******** 配置业务组创建需要进行流量策略的业务组，作为 UCL 匹配规格的对象。##### 背景信息VS 模式下，该配置仅在 Admin VS 支持。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **service-group** server-group-name ，创建一个新的业务组。步骤 **3** 执行命令 **commit** ，提交配置。**----**结束文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 278HUAWEI NetEngine40E配置指南 1 QoS##### ******** 配置 UCL 用户规则根据 IP 承载的不同协议类型，配置不同的用户访问控制列表。##### 背景信息UCL 即用户 ACL ，具体配置步骤请参见配置用户 ACL 。##### ******** 定义流分类要对网络中的流量进行基于类的 QoS 配置，就需要先定义流分类。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **traffic classifier** classifier-name [ **operator** { **and** | **or** } ] ，定义一个流分类并进入流分类视图。说明在 UCL 中配置流分类时，各规则之间“与”（ and ）关系不生效。步骤 **3** 执行命令 **if-match** [ **ipv6** ] **acl** acl-number [ **precedence** precedence-value ] ，配置基于 UCL 列表进行复杂流分类的匹配规则。～在本配置过程中， acl-number 的取值范围是 6000 9999 。如果要配置多条基于 UCL 列表的匹配规则，可以重复执行该步骤。步骤 **4** 执行命令 **commit** ，提交配置。**----**结束##### ******** 定义流行为并配置动作介绍设备支持的流行为及如何配置。##### 背景信息设备支持设置的流行为动作类型比较丰富，可以根据实际需要选择下面的一种或多种。##### 操作步骤       - 配置报文过滤动作在待配置路由器上进行以下配置。a. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **traffic behavior** behavior-name ，定义流行为并进入流行为视图。c. 执行命令 **permit | deny** ，允许 / 禁止报文的通过。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 279HUAWEI NetEngine40E配置指南 1 QoS说明当用户同时配置 **if-match any** 和 **deny** 时，复杂流分类将会禁止流经某个接口的所有流量通过，包括协议报文，因此请用户慎重进行上述流分类和流行为的组合配置。当 **rule** 命令和流行为视图下同时配置了 **permit** 或 **deny** 动作时，只有 **rule** 命令允许通过的报文才会进行流行为的处理。只要 rule 命令或流行为视图中的任意一个配了 deny动作，匹配规则的报文都会被丢弃。d. 执行命令 **commit** ，提交配置。       - 配置流量监管动作在待配置路由器上进行以下配置。a. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **traffic behavior** behavior-name ，定义流行为并进入流行为视图。c. 执行命令 **car** { **cir** cir-value [ **pir** pir-value ] } [ **cbs** cbs-value [ **pbs** pbsvalue ] ] [ **adjust** adjust-value ] [ **green** { **discard** | **pass** [ **remark dscp**dscp-value | **service-class** class **color** color ] } | **yellow** { **discard** | **pass**[ **remark dscp** dscp-value | **service-class** class **color** color ] } | **red**{ **discard** | **pass** [ **remark dscp** dscp-value | **service-class** class **color**color ] } ] [*] [ **summary** ] [ **color-aware** ] [ **limit-type pps** ] ，配置流量监管动作。配置了流量监管动作的流量策略，可以应用到接口的入方向或出方向。接口上应用配置了流量监管的流量策略将影响原有的 **qos car** 命令。本命令为覆盖式命令，即在同一个策略的类上多次进行该配置后，按最后一次配置生效。说明报文被 remark 为 ef 、 be 、 cs6 和 cs7 服务等级后，报文颜色只能被 remark 为 green 。对于 LPUF-53A/LPUF-243A/LPUI-242A/LPUF-483A/LPUI-52C/LPUI-402C/LPUF-400-E/LPUI-2TA/LPUF-1T2A/LPUI-1T2A-CM/LPUI-483A-CM/LPUI-243A-CM/LPUF-243-K/LPUF-53-K/VSUI-400-K/LPUI-2T-K/LPUF-53D/LPUF-243D/LPUF-483D/VSUI-400A 单板，若接入用户量太大需要增加 Car 资源规格时，可执行**car resource-pool enhanced mode** 命令调整 Car 资源分配模式为增强模式。d. 执行命令 **user-queue** **cir** cir-value [ [ **pir** pir-value ] | [ **flow-queue** flowqueue-name ] | [ **flow-mapping** mapping-name ] | [ **user-group-queue**group-name ] | [ **service-template** service-template-name ] ]* ，指定流行为的动作为基于类的 HQoS 调度。说明相同流行为下的 **user-queue** 命令与 **car** 命令不能同时配置。e. 执行命令 **commit** ，提交配置。       - 设置报文的优先级在待配置路由器上进行以下配置。a. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **traffic behavior** behavior-name ，定义流行为并进入流行为视图。c. 请根据实际情况进行如下配置。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 280HUAWEI NetEngine40E配置指南 1 QoS#### ▪ 如果重新设置 IP 报文的优先级，执行命令 remark ip-precedence ipprecedence 。#### ▪ 如果重新设置 IP 报文的 DSCP 值，执行命令 remark [ ipv6 ] dscp dscpvalue 。#### ▪ 如果重新设置 VLAN 报文的优先级，执行命令 remark 8021p 8021pvalue 。#### ▪ 如果重新设置 IP 报文的 ToS 值，执行命令 remark tos tos 。 ▪ 如果重新设置 IP 报文的 DF （ Don ’ t Fragment ）值，执行命令 remark**ip-df** 。#### ▪ 如果重新设置 IP 报文的 ttl 值，执行命令 remark ttl ttl-value 。说明如果重新设置 IPv6 报文的 DSCP 值，执行命令 **remark ipv6 dscp** dscp-value 。**remark ip-df** 命令仅在路由器上行支持。d. 执行命令 **commit** ，提交配置。       - 配置强制流分类a. 执行命令 **service-class** service-class **color** color ，配置指定服务等级的报文标记颜色。b. （可选）执行命令 **service-class** service-class **color** color **track** { **master** |**slave** } **bfd-session** **session-name** bfd-session-name ，配置依据指定的BFD 的会话状态来标记匹配流策略后的报文服务等级和颜色。c. 执行命令 **commit** ，提交配置。       - 设置报文的转发动作须知         - 重定向到公网目标 LSP 只能在 MPLS 网络的 ingress 节点上配置，在其他节点（如transit ， egress ）不允许配置。         - 重定向到公网目标 LSP 仅支持在单层 MPLS 标签应用下进行配置。在待配置路由器上进行以下配置。a. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **traffic behavior** behavior-name ，定义流行为并进入流行为视图。c. 请根据实际情况进行如下配置。#### ▪ 如果不进行重定向操作，直接将报文发送，请在流行为视图下执行命令**permit** 。#### ▪ 如果不进行重定向操作，直接将报文丢弃，请在流行为视图下执行命令**deny**动作 **deny** 与其它流动作是互斥的，对于已经执行 **deny** 动作的流行为，只有先 **permit** 才能执行其它流动作。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 281HUAWEI NetEngine40E配置指南 1 QoS#### ▪ 如果配置 IPv4 报文单个重定向的下一跳的 IP 地址和出接口、 VPN 实例、NQA 测试例，执行如下命令：           - **redirect ip-nexthop** ip-address **interface** interface-typeinterface-number [ **route-forward** ]           - **redirect ip-nexthop** ip-address **nqa** nqa-test-administer-namename-of-nqa-test-instance [ **routing-filter** { **default-routing** |**blackhole-routing** } * ]           - **redirect ip-nexthop** ip-address **vpn** vpn-instance-name [ **nqa**nqa-test-administer-name name-of-nqa-test-instance ][ **routing-filter** { **default-routing** | **blackhole-routing** } * ]#### ▪ 如果配置 IPv4 报文多个重定向的出接口及其对应的下一跳、 VPN 实例、NQA 测试例，执行如下命令：           - **redirect ipv4-multinhp** { **nhp** ip-address **interface** interfacetype interface-number } &<2-42> [ **loadbalance** ] [ **route-****forward** ] [ **unequal-cost** ]           - **redirect ipv4-multinhp** { **nhp** ip-address **nqa** nqa-testadminister-name name-of-nqa-test-instance } &<2-42>[ **routing-filter** { **default-routing** | **blackhole-routing** } * ]           - **redirect ipv4-multinhp** { **nhp** ip-address **vpn** vpn-instancename [ **nqa** nqa-test-administer-name name-of-nqa-testinstance ] } &<2-42> [ **routing-filter** { **default-routing** |**blackhole-routing** } * ] [ **pri-type common** ] [ **non-revertive** ]#### ▪ 如果配置 IPv6 报文单个重定向的下一跳的 IP 地址和出接口、 VPN 实例、NQA 测试例，执行如下命令：           - **redirect ipv6-nexthop** ip-address **interface** interface-typeinterface-number [ **route-forward** ]           - **redirect ipv6-nexthop** ip-address **nqa** nqa-test-administer-namename-of-nqa-test-instance [ **routing-filter** { **default-routing** |**blackhole-routing** } * ]           - **redirect ipv6-nexthop** ip-address **vpn** vpn-instance-name [ **nqa**nqa-test-administer-name name-of-nqa-test-instance ][ **routing-filter** { **default-routing** | **blackhole-routing** } * ]#### ▪ 如果配置 IPv6 报文多个重定向的出接口及其对应的下一跳、 VPN 实例、NQA 测试例，执行如下命令：           - **redirect ipv6-multinhp** { **nhp** ip-address **interface** interfacetype interface-number } &<2-16> [ **loadbalance** [ **sip-hash** ] ][ **route-forward** ] [ **pri-type common** ] [ **unequal-cost** ]           - **redirect ipv6-multinhp** { **nhp** ip-address **nqa** nqa-testadminister-name name-of-nqa-test-instance } &<2-16>[ **routing-filter** { **default-routing** | **blackhole-routing** } * ]           - **redirect ipv6-multinhp** { **nhp** ip-address **vpn** vpn-instancename [ **nqa** nqa-test-administer-name name-of-nqa-testinstance ] } &<2-16> [ **routing-filter** { **default-routing** |**blackhole-routing** } * ]文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 282HUAWEI NetEngine40E配置指南 1 QoS#### ▪ 如果在重定向单下一跳场景下，报文重定向下一跳的 IP 地址匹配到缺省路由或者黑洞路由时，希望报文使用携带的目的 IP 地址进行转发时，执行如下命令：           - **redirect ip-nexthop** ip-address [ **routing-filter** { **default-****routing** | **blackhole-routing** } * ] [ **deny** ]           - **redirect ip-nexthop** ip-address [ **routing-filter** { **default-****routing** | **blackhole-routing** } * ]#### ▪ 如果在重定向多下一跳场景下，报文重定向下一跳的 IP 地址匹配到缺省路由或者黑洞路由时，希望报文使用携带的目的 IP 地址进行转发时，执行如下命令：           - **redirect ipv4-multinhp** { **nhp** ip-address } &<2-42> [ **routing-****filter** { **default-routing** | **blackhole-routing** } * ] [ **deny** ]           - **redirect ipv6-multinhp** { **nhp** ip-address } &<2-16> [ **routing-****filter** { **default-routing** | **blackhole-routing** } * ]#### ▪ 如果配置私网路由转发的 IPv4 报文或 IPv6 报文重定向到公网路由出接口上，执行命令 **redirect** { **ip** | **ipv6** } **public-network** 。#### ▪ 如果将 IP 数据流重定向到公网目标 LSP 上，执行命令 redirect lsp publicdest-ipv4-address [ nexthop-address | **interface** interface-typeinterface-number | **secondary** ] 。#### ▪ 如果将报文重定向到指定的 VPN 组，执行命令 redirect vpn-group vpngroup-name 。#### ▪ 如果将报文重定向到指定的 VSI ，执行命令 redirect vsi vsi-name 。 ▪ 如果希望 IPv4 报文直接重定向到指定的出接口，执行命令 redirect to**interface** { interface-name | interface-type interface-number }[ **route-forward** ] 。#### ▪ 如果希望 IPv6 报文直接重定向到指定的出接口，执行命令 redirect ipv6**to interface** { interface-name | interface-type interface-number }[ **route-forward** ] 。说明在 IPv6 中应用，使用强策略时指定的下一跳 IP 地址可以为 IPv6 链路地址或 IPv6 单播地址，使用弱策略时指定的下一跳 IP 地址只能是 IPv6 单播地址。d. 执行命令 **commit** ，提交配置。       - 设置报文的负载分担方式在待配置路由器上进行以下配置。a. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **traffic behavior** behavior-name ，定义流行为并进入流行为视图。c. 执行命令 **load-balance** { **flow** [ **l2** | **l3** ] | **packet** } ，设置报文的负载分担方式。d. 执行命令 **commit** ，提交配置。**----**结束文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 283HUAWEI NetEngine40E配置指南 1 QoS##### ******** 定义流量策略定义了流分类和动作后，需要配置流量策略，为定义的流关联动作。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **traffic policy** policy-name ，定义流量策略并进入策略视图。步骤 **3** 执行命令 **classifier** classifier-name **behavior** behavior-name [ **precedence**precedence-value ] ，在流量策略中为流分类指定采用的行为并设定策略匹配的优先级。说明若存在多个用户组或业务组需要使用相同的 rule 时，则需要针对每个用户组或业务组重复 **3.** 执行命令 **classifier classif...** 的配置过程。此时建议执行命令 **classifier** classifier-name { **source** |**destination** } { **user-group** | **service-group** } group-name **behavior** behavior-name[ **precedence** precedence-value ] 进行简化的方式的配置， rule 命令中不再配置 User-group 或Service-group 。步骤 **4** ( 可选 ) 执行命令 **statistics enable** ，使能流量策略的统计功能。步骤 **5** （可选）执行命令 **share-mode** ，指定策略为共享属性。说明为节省内存，系统缺省不使能流量策略的统计功能。当用户需要显示流量策略的统计数据时，可配置 **statistics enable** 命令使能流量策略的统计功能。缺省为共享属性。          - 在接口上应用流量策略后，不能修改流量策略的共享 / 非共享模式。如果用户需要修改流量策略的共享 / 非共享模式，需要先取消该流量策略在接口上的应用。          - 具有共享属性的策略：尽管策略应用在不同的接口，但显示的数据是汇总的统计数据，无法区分各个接口的单独数据。          - 具有非共享属性的策略：可以根据策略应用的不同接口来区分接口上的策略统计数据。          - 无论是共享属性还是非共享属性，都会区分 inbound 和 outbound 进行流量统计。步骤 **6** （可选）执行命令 **step** step-value ，配置策略间的步长。步骤 **7** 执行命令 **commit** ，提交配置。**----**结束##### 1.10.2.7 应用 UCL 用户的流分类策略应用基于 UCL 的流量策略后，对所有上线用户的流量都会按照 UCL 规则进行分类。##### 背景信息VS 模式下，该配置任务仅在 Admin VS 支持。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **traffic-policy** policy-name { **inbound** | **outbound** } ，对上线用户应用流量策略。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 284HUAWEI NetEngine40E配置指南 1 QoS在系统视图下应用了基于 UCL 的流量策略后，对所有上线用户的流量都会按照 UCL 规则进行分类。当系统视图和接口上同时应用了流量策略时，网络侧：接口上的流量策略生效，用户侧：基于 UCL 的流量策略生效。说明          - 执行命令 **traffic-policy match-type destination-user inbound** ，可以配置为 UCL 生效。          - 执行命令 **traffic-policy match-type interface-acl** ，可以配置为 ACL 生效。VS 模式下，该配置任务仅在 Admin VS 支持。步骤 **3** 执行命令 **commit** ，提交配置。**----**结束##### ******** 将用户组加入域对用户应用 UCL 流分类策略时，需要先配置用户组加入域。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **aaa** ，进入 AAA 视图。步骤 **3** 执行命令 **domain** domain-name ，进入 AAA 域视图。步骤 **4** 执行命令 **user-group** user-group-name ，将用户组加入域。步骤 **5** （可选）执行命令 **traffic-policy ucl** **upstream match-source-ip** [ **ipv4** | **ipv6** ][ **user-group-list** user-group-list-name ] ，配置匹配用户源 IP 进行复杂流分类。当需要按照用户源 IP 区分用户进行复杂流分类时，可以配置本步骤。步骤 **6** 执行命令 **commit** ，提交配置。**----**结束##### ******** 配置用户域下的优先级映射配置用户优先级和 QoS 服务等级、颜色之间的映射关系，实现用户报文的 QoS 调度。##### 背景信息用户上线时会根据需要配置不同的优先级。当需要根据用户指定的优先级进行调度时可配置用户域下的优先级映射，该域下的用户上线时统一按照配置的映射关系进行用户优先级和设备内部优先级的映射。在待配置路由器上进行以下配置。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **aaa** ，进入 AAA 视图。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 285HUAWEI NetEngine40E配置指南 1 QoS步骤 **3** 执行命令 **domain** domain-name ，进入 AAA 域视图。步骤 **4** 执行命令 **user-priority** { **upstream** | **downstream** } { priority | **trust-8021p-inner** |**trust-8021p-outer** | **trust-dscp-outer** | **trust-dscp-inner** | **trust-exp-inner** | **trust-****exp-outer** | **unchangeable** } ，配置用户优先级。配置用户优先级有以下几种方式：       - 直接指定用户优先级的值，取值范围 0 ～ 7 。       - 使用用户二层报文的内层或外层 802.1p 值作为用户优先级，这种方式对于从网络侧到用户侧的报文无效。       - 使用用户报文的 DSCP 值作为用户优先级。       - 使用 MPLS 报文的 EXP 作为用户优先级。步骤 **5** 执行命令 **quit** ，退回 AAA 视图。步骤 **6** 执行命令 **quit** ，退回系统视图。步骤 **7** 执行命令 **diffserv domain** { ds-domain-name | **default** | **5p3d** } [ **domain-id**domain-id ] ，定义 DS 域并进入 Diff-Serv 域视图。步骤 **8** （可选）执行命令 **user-priority** priority **phb** { **af1** | **af2** | **af3** | **af4** | **be** | **cs6** | **cs7** |**ef** } [ **green** | **yellow** | **red** ] ，配置用户域下的优先级映射。配置 **user-priority phb** 命令前必须在 AAA 域下配置 **user-priority** 命令指定用户优先级，映射关系才能生效。服务等级为 CS7 、 CS6 、 EF 和 BE 时，只能将报文着色为 green 。表 **1-25** 用户优先级与服务等级之间缺省的映射表|用户优先级|Service|Color||---|---|---||0|BE|Green||1|AF1|Green||2|AF2|Green||3|AF3|Green||4|AF4|Green||5|EF|Green||6|CS6|Green||7|CS7|Green|步骤 **9** 执行命令 **quit** ，退回系统视图。步骤 **10** 执行命令 **aaa** ，进入 AAA 视图。步骤 **11** 执行命令 **domain** domain-name ，进入 AAA 域视图。步骤 **12** 执行命令 **trust upstream** ds-domain-name 使能域下的简单流分类，保证 IP 报文、MPLS 报文和组播报文的优先级映射。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 286HUAWEI NetEngine40E配置指南 1 QoS步骤 **13** （可选）执行命令 **qos phb** { **dscp** | **inner-8021p** | **outer-8021p** | **mpls-exp** }**disable** ，配置发往 AAA 域用户的报文的优先级不做下行 PHB 映射。步骤 **14** （可选）配置企业用户的简单流分类策略重定向功能。1. 执行命令 **quit** ，退回 AAA 视图。2. 执行命令 **quit** ，退回系统视图。3. 执行命令 **dhcp option** option-code **include** option-include **redirect ds-domain****enable** ，配置使能企业用户的简单流分类策略重定向功能。4. 执行命令 **aaa** ，进入 AAA 视图。5. 执行命令 **domain** domain-name ，进入 AAA 域视图。6. 执行命令 **redirect ds-domain** ds-domain-name ，配置域下企业用户的简单流分类策略重定向至指定域。步骤 **15** 执行命令 **commit** ，提交配置。**----**结束##### 检查配置结果完成配置后，可以按以下指导来检查配置结果。       - 使用 **display diffserv domain** [ ds-domain-name ] [ **8021p** | **dscp** | **exp** ][ **inbound** | **outbound** ] 命令查看 DS 域的配置信息。       - 使用 **display diffserv domain application** [ ds-domain-name ] 命令查询指定DS 域下应用的接口列表。##### ********0 检查配置结果UCL 的流量策略配置成功后，可以查看指定接口或所有接口配置的流量策略信息，以及 UCL 用户的流量策略统计信息 .##### 操作步骤       - 使用 **display interface** [ interface-type [ interface-number ] ] [ | { **begin** |**exclude** | **include** } regular-expression ] 命令查看接口的流量信息。       - 使用 **display traffic behavior** { **system-defined** | **user-defined** } [ behaviorname ] 命令查看流行为的配置信息。       - 使用 **display traffic classifier** { **system-defined** | **user-defined** } [ classifiername ] 命令查看流分类的配置信息。       - 使用 **display traffic policy** { **system-defined** | **user-defined** } [ policy-name[ **classifier** classifier-name ] ] 命令查看流策略中所有流分类与流行为的关联信息或特定流分类与流行为的关联信息。**----**结束#### 1.10.3 配置普通用户的 HQoS 调度HQoS 调度对普通用户内多个业务通过优先级进行区分，进行 QoS 调度。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 287HUAWEI NetEngine40E配置指南 1 QoS##### 应用环境当一个 IPoE 用户上线，不需要进行家庭识别方式，而是作为单个用户接入时即为普通用户上线。对于 IPoE 用户使用 IP 地址进行识别。普通用户内也会用多个业务，业务通过优先级进行区分。普通用户的 QoS 信息会从认证域下的 QoS 模板中获取。说明普通用户认证过程中，可以通过 RADIUS 服务器动态下发 QoS 参数，不在 NE40E 进行配置。在用户接入场景中，当后置 TM 不在位时默认下行不使能 HQoS 调度，用户配置的 HQoS 将被强制转换成 Car 进行限速。此时如果需要实现 HQoS 调度，需要先执行命令 **access-user board-****schedule enable outbound** 来使能该功能。##### 前置任务在配置普通用户的 HQoS 调度之前，需要完成以下任务 :配置 NE40E 的 BRAS 功能，用户能够正常上线。相关配置请参照 HUAWEI                            NetEngine40E 路由器 配置指南 用户接入。##### ******** （可选）指定队列资源池根据此配置，指定队列资源池，使 QoS 资源得到充分利用。##### 背景信息每个物理口下，用户上线单方向使用的 SQ 资源有限，当需要单个物理口的单方向 SQ 资源大于限定值时，单方向的 QoS 资源就无法满足需求，这时可以通过命令 **qos****scheduling-mode** 实现不同的子接口下使用不同的资源调度模式，实现 QoS 资源的充分利用。VS 模式下，该配置仅在 Admin VS 支持。##### 操作步骤       - 指定 mode2 模式下 QoS 队列的资源池。a. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **qos scheduling-mode** **mode2** **slot** slot-id ，选择带宽调度模式为mode2 。说明只有带宽调度模式为 mode2 时，以下配置才生效。c. 执行命令 **interface** interface-type interface-number ，进入接口视图。d. 执行命令 **qos queue-resource pool** id { **inbound** | **outbound** } ，指定当前接口的 QoS 资源池。e. 执行命令 **commit** ，提交配置。       - 指定 mode3 模式下 QoS 队列的资源池。a. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **qos scheduling-mode** **mode3** **slot** slot-id ，选择带宽调度模式为mode3 。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 288HUAWEI NetEngine40E配置指南 1 QoSc. 执行命令 **commit** ，提交配置。**----**结束##### 1.10.3.2 （可选）配置流队列模板用户可以根据网络需求配置基于 8 队列普通模式流队列的调度参数、流量整形和队列缓存资源。##### 背景信息用户可以采用非缺省的流队列（ flow-wred ）模板，并根据网络需求来配置流队列的调度参数，也可在系统中同时创建多个 flow-wred 对象，供流队列引用，并为创建的flow-wred 模板设定高低门限百分比和丢弃概率，当队列的实际长度占流队列的长度百分比小于低门限百分比时，不丢弃报文；当队列的实际长度占流队列的长度百分比在低门限百分比和高门限百分比之间时，开始随机丢弃报文（队列的长度越长，报文被丢弃的概率越高）；当队列的实际长度占流队列的长度百分比大于高门限百分比时，丢弃所有的报文。各队列间共享队列缓存，如果队列缓存耗尽，会存在队列不拥塞但是丢包的问题，因此需要配置队列缓存资源。说明          - 红色报文丢弃优先级的高低门限可以配置得最小，黄色报文丢弃优先级的高低门限可以配置得稍大些，绿色报文丢弃优先级的高低门限可以配置得最大。          - 用户在实际配置时，低门限百分比建议从 50% 开始取值，根据不同颜色的丢弃优先级逐级调整，丢弃概率建议取值为 100% 。          - 如果用户不配置流队列的 WRED 对象，系统采用缺省的尾丢弃策略。##### 操作步骤       - 配置流队列 WRED 对象。a. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **flow-wred** wred-name ，创建流队列 WRED 对象，进入流队列WRED 视图。c. 执行命令 **color** { **green** | **yellow** | **red** } **low-limit** low-limit-percentage**high-limit** high-limit-percentage **discard-percentage** discardpercentage ，设置不同颜色报文的高低门限百分比和丢弃概率。d. 执行命令 **queue-depth** queue-depth-value ，配置队列的深度。e. 执行命令 **commit** ，提交配置。f. 执行命令 **quit** ，返回系统视图。       - 配置流队列的调度参数。a. 执行命令 **flow-queue** flow-queue-name ，进入流队列视图。b. 执行命令 **queue** cos-value { { **pq** | **wfq** **weight** weight-value | **lpq** } |{ **shaping** { shaping-value | **shaping-percentage** shaping-percentagevalue } [ **pbs** pbs-value ] | **car** { car-value | **car-percentage** carpercentage-value } [ **pbs** pbs-value ] } | **flow-wred** wred-name | **low-****latency** | **low-jitter** } * 或 **queue** cos-value **cir** { { cir-value [ **cbs** cbs-value ]**cir-schedule pq** **pir** pir-value } | { **cir-percentage** cir-percentage-value[ **cbs** cbs-value ] **cir-schedule pq** **pir** **pir-percentage** pir-percentagevalue } } [ **pbs** pbs-value ] **pir-schedule** { **pq** | **wfq weight** weight-value |**lpq** } [ **flow-wred** wred-name ] ，修改流队列的调度参数和调度策略。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 289HUAWEI NetEngine40E配置指南 1 QoSc. 执行命令 **queue** cos-value **random-discard** random-discard-value ，配置流队列的随机丢弃值。d. 执行命令 **commit** ，提交配置。       - 配置联合流量整形。a. 执行命令 **share-shaping** [ shap-id ] { **af1** | **af2** | **af3** | **af4** | **be** | **cs6** | **cs7** |**ef** } [*] [ **pq** | **wfq** **weight** weight-value | **lpq** ] shaping-value [ **pbs** pbsvalue ] ，配置多个流队列的联合流量整形。配置联合流量整形后，联合流量整形中的队列将先做整形，再与其它的用户队列一起做调度。b. 执行命令 **share-shaping** { **be** | **af1** | **af2** | **af3** | **af4** | **ef** | **cs6** | **cs7** } [ *]**random-discard** random-discard-value ，配置流队列的联合流量整形随机丢弃值。c. 执行命令 **commit** ，提交配置。d. 执行命令 **quit** ，返回系统视图。       - 使能流队列下所有 PQ 调度队列的低时延功能。a. 执行命令 **qos flow-queue low-latency enable** ，使能流队列下所有 PQ 调度队列的低时延功能，以保证 PQ 调度队列的时延。       - 配置 PQ 调度优先级映射、桶深预备值和全局缓存。a. 执行命令 **qos cos** { **be** | **af1** | **af2** | **af3** | **af4** | **ef** | **cs6** | **cs7** } **burst-size**buffer-size-value ，配置队列的桶深预借值。b. 执行命令 **qos cos all pack-size** pack-size-value ，配置后置 eTM 子卡的报文数据块容量阈值。c. 执行命令 **slot** slot-id ，进入槽位视图。d. 执行命令 **qos pq-scheduler priority** { **high** | **low** } { **inbound** |**outbound** } ，更改单板的 PQ 调度优先级映射，改变原有优先级默认映射关系。e. 执行命令 **qos pq-scheduler priority** { **high** | **low** } **outbound** [ **card**card_id ] ，更改子卡的 PQ 调度优先级映射，改变原有优先级默认映射关系。f. 执行命令 **qos global-buffer** { **share-threshold** share-value | { **be** | **af1** |**af2** | **af3** | **af4** | **ef** | **cs6** | **cs7** } **share** } { **inbound** | **outbound** } ，控制整个单板的队列缓存资源。为了避免队列缓存被部分队列大量占用，需要通过配置限制队列最大可以使用的队列缓存。当通过 flow-wred 来限制时，如果配置的 flow-wred 过小，会导致突发流量丢包的问题，此时可以通过命令 **qos global-buffer** 设置全局队列缓存，在共享的队列缓存资源耗尽前， flow-wred 配置不生效，从而避免流量突发导致的丢包问题。g. 执行命令 **commit** ，提交配置。h. 执行命令 **quit** ，返回系统视图。       - 配置流队列的映射关系。a. 执行命令 **flow-mapping** mapping-name ，进入流队列映射视图。用户可以在一个流队列映射模板中，分别配置 8 个 flow-queue 到 port-queue的映射关系，也可以根据需要，创建多个 flow-mapping 映射模板，供用户队列引用，系统最多可以配置 15 个 flow-mapping 映射模板。b. 执行命令 **map flow-queue** cos-value **to** **port-queue** cos-value ，设置用户队列中某个业务进入端口队列的优先级。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 290HUAWEI NetEngine40E配置指南 1 QoS如果用户不配置流队列到端口队列的映射关系，系统将采取缺省的一一对应映射关系。c. 执行命令 **commit** ，提交配置。d. 执行命令 **quit** ，返回系统视图。**----**结束##### 1.10.3.3 （可选）配置 4 队列模式流队列在 4 队列模式流队列模板下可以配置用户 4 个流队列的调度参数，相对于配置 8 队列，可以减少用户配置的工作量。##### 背景信息类队列优先级定义方式有 8 队列优先级（ BE/AF1/AF2/AF3/AF4/EF/CS6/CS7 ）和 4 队列优先级（ cos0/cos1/cos2/cos3 ）两种，其中 8 队列优先级是系统默认采用的方式，而 4队列优先级的定义方式可以通过配置四个队列的流量限速实现 8 个队列流量限速的配置，可以减少配置工作量。说明VS 模式下，该配置任务仅在 Admin VS 支持。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **flow-queue** flow-queue-name **4cos-mode** ，进入 4 队列模式流队列视图。步骤 **3** 执行命令 **queue** cos-value { { **pq** | **wfq** **weight** weight-value | **lpq** } | **shaping**{ shaping-value | **shaping-percentage** shaping-percentage-value } [ **pbs** pbsvalue ] | **flow-wred** wred-name } [*] ，设置不同等级的队列调度策略。步骤 **4** 执行命令 **commit** ，提交配置。**----**结束##### 1.10.3.4 （可选）配置 8 队列与 4 队列的映射关系自定义 8 队列优先级到 4 队列优先级的映射关系，可以更灵活地实现 4 队列优先级的相关配置。##### 背景信息系统中存在缺省的 8 队列优先级与 4 队列优先级的映射关系，缺省映射关系如下表所示：|流队列|与8队列优<br>先级的映射<br>关系|调度方式|权重|整形速率百<br>分比|丢弃方式||---|---|---|---|---|---||cos0|BE、AF1|WFQ|10|-|尾丢弃|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 291HUAWEI NetEngine40E配置指南 1 QoS|流队列|与8队列优<br>先级的映射<br>关系|调度方式|权重|整形速率百<br>分比|丢弃方式||---|---|---|---|---|---||cos1|AF2、<br>AF3、AF4|WFQ|15|-|尾丢弃||cos2|EF|PQ|-|-|尾丢弃||cos3|CS6、CS7|PQ|-|-|尾丢弃|但也支持对二者的映射关系进行自定义，自定义时的配置如下。VS 模式下，该配置仅在 Admin VS 支持。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **queue-4cos-mapping** mapping-name ，创建 4 队列映射模板，并进入 4 队列映射模板视图。步骤 **3** 执行命令 **queue** serviceclass **mapping** { **cos0** | **cos1** | **cos2** | **cos3** } ，配置 8 队列优先级与 4 队列优先级的映射关系。步骤 **4** 执行命令 **commit** ，提交配置。**----**结束##### 1.10.3.5 （可选）配置业务模板在业务模板中配置精度调整长度，对报文长度进行补偿，补偿报文在设备上处理后的差值，从而精确地进行流量控制。##### 背景信息用户报文的长度会因为调度模块不同的处理方式而变化，影响流量整形的准确性。此时用户可以通过配置业务模板中的精度调整长度来补偿报文在设备上处理后的差值，从而提高流量整形的准确性。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **service-template** service-template-name ，进入业务模板视图。步骤 **3** 执行命令 **network-header-length** network-header-length { **inbound** |**outbound** } ，设置业务模板的精度补偿长度。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 292HUAWEI NetEngine40E配置指南 1 QoS说明如需同时指定前置 TM 和后置 TM （ ETM ）的精度补偿值，需要在全局的业务模板中针对前后置TM 分别进行配置，具体操作如下：          - 在业务模板视图下执行命令 **network-header-length** network-header-length **outbound** ，设置业务模板的精度补偿长度，在 QoS 模板中应用该业务模板后，精度补偿值会在出方向的前置 TM 生效。          - 在业务模板视图下执行命令 **network-header-length** network-header-length **outbound** ，设置业务模板的精度补偿长度，在 QoS 模板中应用该业务模板并使用参数 **adjust-on-card** 指定其在后置 TM 生效时，精度补偿值将会在出方向的后置 TM 生效。          - 在业务模板视图下执行命令 **network-header-length** network-header-length **outbound****adjust-on-etm** ，设置业务模板的精度补偿长度，并指定其在后置 TM 生效。在 QoS 模板中应用该业务模板后，精度补偿值会在出方向的后置 TM 生效。步骤 **4** 执行命令 **commit** ，提交配置。**----**结束##### 1.10.3.6 配置基于 QoS 模板的用户队列QoS 模板是 QoS 调度参数的集合，可配置用户队列的调度参数包括用户队列的保证带宽、峰值带宽、流队列模板和业务模板精度的调整长度。##### 背景信息在用户接入场景下，认证接收报文中携带的 RADIUS 属性 HW-QOS-Profile-Name (31)可为用户动态指定 Qos 限速模板。前提条件是设备上需要配置 QoS 模板并配置调度参数。关于属性 HW-QOS-Profile-Name (31) 的详细说明可参考产品文档中的 Radius 属性列表。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **qos-profile** qos-profile-name ，定义 QoS 模板并进入 QoS 模板视图。步骤 **3** 执行命令 **user-queue** { { **cir** cir-value [ **cbs** cbs-value ] [ [ **pir** pir-value [ **pbs** pbsvalue ] [ **pir-priority high** ] ] | [ **flow-queue** flow-queue-name ] | [ **queue-4cos-****mapping** queue-mapping-name ] | [ **flow-mapping** mapping-name ] | [ **user-****group-queue** group-name ] ] * [ **inbound** | **outbound** ] [ **service-template**template-name [ **adjust-on-card** ] ] } | { **cir-percentage** cir-percentage-value[ **cbs** cbs-value ] [ [ **pir-percentage** pir-percentage-value [ **pbs** pbs-value ] [ **pir-****priority high** ] ] | [ **flow-queue** flow-queue-name ] | [ **queue-4cos-mapping**queue-mapping-name ] | [ **flow-mapping** mapping-name ] | [ **user-group-queue**group-name ] ] * [ **inbound** | **outbound** ] [ **service-template** template-name[ **adjust-on-card** ] ] } } ，配置用户队列的调度参数。说明          - 如果绑定了优先级模式的流队列模板，那么承诺信息速率必须为零。          - 配置 **user-queue** 命令时应用的业务模板 service-template 必须为全局的。步骤 **4** 执行命令 **car** { **cir** cir-value [ **pir** pir-value ] | **cir** **cir-percentage** cir-percentagevalue [ **pir** **pir-percentage** pir-percentage-value ] } [ **cbs** cbs-value [ **pbs** pbsvalue ] ] [ **green** { **discard** | **pass** [ **service-class** class **color** color ] } | **yellow**{ **discard** | **pass** [ **service-class** class **color** color ] } | **red** { **discard** | **pass** [ **service-**文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 293HUAWEI NetEngine40E配置指南 1 QoS**class** class **color** color ] } ]* [ **inbound** | **outbound** ] [ **color-aware** ] ，配置 CAR 来保证用户流量。说明对于 LPUF-53A/LPUF-243A/LPUI-242A/LPUF-483A/LPUI-52C/LPUI-402C/LPUF-400-E/LPUI-2TA/LPUF-1T2A/LPUI-1T2A-CM/LPUI-483A-CM/LPUI-243A-CM/LPUF-243-K/LPUF-53K/VSUI-400-K/LPUI-2T-K/LPUF-53D/LPUF-243D/LPUF-483D/VSUI-400A 单板，当 CAR 资源不足时，可以通过在槽位试图下执行 **car resource-pool enhanced mode** 命令调整 CAR 资源分配模式为增强模式，适量提升 CAR 资源。步骤 **5** 执行命令 **commit** ，提交配置。**----**结束##### 1.10.3.7 （可选）配置动态修改 QoS 模板用户在线时更改使用的 QoS 模板，用户域下原来配置的 QoS 模板不再生效。##### 背景信息说明VS 模式下，该配置任务仅在 Admin VS 支持。为了保持用户在线情况时能更改使用的 QoS 模板，需要配置动态修改 QoS 模板。配置该功能后，用户使用的 QoS 模板更改为 **update qos-profile** 命令中的模板，用户域下原来配置的 QoS 模板不再生效。动态修改 QoS 模板只对用户的业务流生效，不能修改家庭调度的 QoS 模板，即只能修改域下的 QoS 模板，不能修改接口下的 QoS 模板。请在路由器上进行以下配置。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **aaa** ，进入 AAA 视图。步骤 **3** 执行命令 **update qos-profile** **user-id** user-id **profile** qos-profile-name { **inbound** |**outbound** } ，配置动态修改 QoS 模板。步骤 **4** 执行命令 **commit** ，提交配置。**----**结束##### 1.10.3.8 在域下应用 QoS 模板定义 QoS 模板并应用到 AAA 域下来实现对接入用户的 QoS 调度管理。##### 背景信息接口下应用 QoS 模板和域下应用 QoS 模板的作用不同。家庭用户的带宽参数从接口下应用的 QoS 模板中获取，基于业务类型分配的带宽参数从域下应用的 QoS 模板中获取。如果是普通用户上线，则直接获取域下 QoS 模板的参数；如果是家庭用户上线，则只取域下 QoS 模板中 **car** 命令中配置的参数进行业务流量限速。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 294HUAWEI NetEngine40E配置指南 1 QoS##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** （可选）若 QoS 模板中通过 **user-queue** 命令绑定了一个 4 队列模式的 flow-queue ，由于缺省情况下不存在可支持 4 队列模式的 user-queue 资源，那么在应用该 QoS 模板前，还需要进行以下配置。1. 执行命令 **slot** slot-id ，进入槽位视图。2. 执行命令 **qos user-queue resource 4cos** 4cos-size ，设置可支持 4 队列模式的user-queue 资源规格大小。3. 执行命令 **quit** ，返回系统视图。步骤 **3** 执行命令 **aaa** ，进入 AAA 视图。步骤 **4** 执行命令 **domain** domain-name ，创建域并进入 AAA 域视图。此处的 domain-name 为业务映射的域名。步骤 **5** 执行命令 **qos-profile** profile-name { **inbound** | **outbound** } **lns-gts** ，在域下绑定QoS 模板。说明**lns-gts** 仅支持在 LNS 设备上对 L2TP 用户绑定 QoS 模板时使用。步骤 **6** 执行命令 **quit** ，返回 AAA 视图。步骤 **7** 执行命令 **quit** ，返回系统视图。步骤 **8** （可选）当用户通过 Eth-Trunk 接口接入，且 Trunk 下的多个成员口跨了转发模块或流量管理模块时，为了防止 Trunk 口的限速带宽翻倍，可通过以下配置将限速总带宽在Trunk 成员口间按权重分配1. 执行命令 **interface** interface-type interface-number ，进入 Eth-Trunk 接口视图。2. 根据用户的接入类型，选择以下命令进行配置。–对于普通接入用户，执行命令 **qos subscriber-access member-link-****scheduler distribute** **inbound** ，将限速带宽在 Trunk 成员口间按权重分配。–对于专线接入用户，执行命令 **qos leased-line-access member-link-****scheduler distribute** { **inbound** | **outbound** } ，将限速带宽在 Trunk 成员口间按权重分配。步骤 **9** 执行命令 **commit** ，提交配置。**----**结束##### 1.10.3.9 定义 GQ 模板并配置调度参数GQ 模板是用户组队列调度参数的集合，可配置用户组队列的调度参数包括用户组队列的保证带宽、峰值带宽。##### 背景信息在路由器上进行以下配置。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 295HUAWEI NetEngine40E配置指南 1 QoS##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **user-group-queue** group-name ，进入用户组队列视图。步骤 **3** 请根据实际情况选择配置用户队列的调度。       - 执行命令 **shaping** shaping-value [ **pbs** pbs-value ] { **inbound** | **outbound** } ，设置用户组的 shaping 值。       - 执行命令 **weight** weight-value **outbound** ，设置用户组队列权重值。       - 执行命令 **cir** cir-value [ **cbs** cbs-value ] [ **pir** pir-value [ **pbs** pbs-value ] ]**outbound** ，设置用户组 Cir 。说明**shaping** shaping-value [ **pbs** pbs-value ] { **inbound** | **outbound** } 和 **cir** cir-value [ **cbs** cbsvalue ] [ **pir** pir-value [ **pbs** pbs-value ] ] **outbound** 互斥。步骤 **4** 执行命令 **commit** ，提交配置。**----**结束##### ********0 应用 GQ 模板将 GQ 模板应用到用户 VLAN 视图或用户上线的 BAS 口下来实现对用户组队列的调度管理。##### 背景信息在路由器上进行以下配置。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 在用户 VLAN 视图下应用 GQ 模板，对用户流量进行调度。1. 执行命令 **interface** interface-type interface-number ，进入子接口视图。2. 执行命令 **user-vlan** { { start-vlan-id [ end-vlan-id ] [ **qinq** start-pe-vlan [ endpe-vlan ] ] } | **any-other** } ，进入用户 VLAN 视图。3. 执行命令 **user-group-queue** user-group-queue-name [ **identifier** identifier ]{ **inbound** | **outbound** } [ **group** groupname ] ，在用户 VLAN 视图下为用户分配一个用户组队列。4. 执行命令 **commit** ，提交配置。5. 执行命令 **quit** ，退回到子接口视图。6. 执行命令 **quit** ，退回到系统视图。7. 执行命令 **interface** **Eth-trunk** trunk-number ，进入 Eth-Trunk 接口视图。8. （可选）执行命令 **bas-load-balance exclude user-group-queue outbound** ，配置用户下行方向的流量不依据 GQ 配置进行选路。若用户上线接口为 Eth-trunk 接口，同一个 GQ 下用户的下行方向流量默认依据 GQ配置进行选路，即通过同一个 Trunk 成员口转发该 GQ 下所有用户的下行方向流量，可能会出现 Trunk 各成员口间流量负载不均的问题。如果想要用户下行方向流量负载分担到 Trunk 接口的不同成员口，可配置本命令实现。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 296HUAWEI NetEngine40E配置指南 1 QoS9. 执行命令 **commit** ，提交配置。步骤 **3** 在 BAS 视图下应用 GQ 模板，配置父 GQ 对用户下行方向的流量进行调度。1. 执行命令 **quit** ，退回到系统视图。2. 执行命令 **interface** interface-type interface-number ，进入接口视图。3. 执行命令 **bas** ，进入 BAS 视图。4. 执行命令 **user-group-queue** user-group-queue-name **outbound** [ **group**group ] ，在 BAS 视图下应用 GQ 模板。5. 执行命令 **commit** ，提交配置。6. 执行命令 **quit** ，退回到接口视图。7. 执行命令 **quit** ，退回到系统视图。8. 执行命令 **interface** **Eth-trunk** trunk-number ，进入 Eth-trunk 接口视图。9. （可选）执行命令 **bas-load-balance exclude sub-port-queue outbound** ，配置用户下行方向的流量不依据父 GQ 配置进行选路。若用户上线接口为 Eth-trunk 接口，同一个父 GQ 下用户的下行方向流量默认依据父 GQ 配置进行选路，即通过同一个 Trunk 成员口转发该父 GQ 下所有用户的下行流量，可能会出现 Trunk 各成员口间流量负载不均的问题。如果想要用户下行方向流量负载分担到 Trunk 接口的不同成员口，可配置本命令实现。10. 执行命令 **commit** ，提交配置。**----**结束##### ********1 在域下配置 QoS用户可以配置普通用户的限速模式，并根据需要选择性的配置域下用户业务流不参与家庭 QoS 调度。##### 背景信息用户在配置了限速模式后，系统将提取对应 QoS 模板中的 CAR 或 SQ 参数进行调度。如果指定域上线的用户的业务流不参与家庭策略调度，系统则按单业务进行带宽管理。VS 模式下，该配置仅在 Admin VS 支持。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **aaa** ，进入 AAA 视图。步骤 **3** 执行命令 **domain** domain-name ，创建域并进入 AAA 域视图。此处的 domain-name 为业务需映射进的域的名称。步骤 **4** 执行命令 **qos rate-limit-mode** { **car** | **user-queue** } { **inbound** | **outbound** } ，配置普通用户的限速模式。在 AAA 域下应用 QoS 模板时，如果 QoS 模板中既配置了 **car** 又配置了 **user-queue** ，则判断域下是否配置 **qos rate-limit-mode** 命令。如果配置了 **qos rate-limit-mode car** 则提取 QoS 模板中的 CAR 参数进行调度；如果域下配置了 **qos rate-limit-mode user-****queue** ，则提取 QoS 模板中的 SQ 参数进行调度；如果没配置 **qos rate-limit-mode** 命令，则默认对上行流量应用 qos-profile 中的 CAR 参数，对下行流量应用 qos-profile 中的SQ 参数。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 297HUAWEI NetEngine40E配置指南 1 QoS步骤 **5** （可选）执行命令 **session-group-exclude** [ **car** | **user-queue** ] { **inbound** |**outbound** } ，配置域下用户业务流不参与家庭 QoS 调度。       - 配置命令 **session-group-exclude** **car** ，则从该域上线的用户的业务流不统一做家庭流量限速；       - 配置命令 **session-group-exclude** **user-queue** ，则从该域上线的用户的业务流不统一做家庭调度；步骤 **6** （可选）执行命令 **qos user-queue granularity** granularity-value ，配置从指定域上线的用户的 user-queue 带宽的粒度。步骤 **7** 执行命令 **commit** ，提交配置。步骤 **8** 执行命令 **quit** ，返回 AAA 视图。步骤 **9** 执行命令 **quit** ，退回到系统视图。步骤 **10** 执行命令 **slot** slot-id ，进入槽位视图。步骤 **11** （可选）执行命令 **access-user user-queue recovery** **disable** ，关闭用户 user-queue资源申请失败自动回切功能。步骤 **12** 执行命令 **commit** ，提交配置。**----**结束##### ********2 （可选）阶梯调整用户带宽根据此配置，阶梯调整用户带宽，使 SQ 资源得到充分利用。##### 背景信息设备允许配置的 SQ 的 CIR/PIR 之和不能无限大，所以当用户通过配置申请 SQ 资源时，会出现因为 SQ 的 CIR/PIR 之和超过设备处理能力而出现资源申请失败的情况，为避免该问题，可以通过命令 **qos bandwidth-adjustment** ，实时调整用户带宽，及时补充带宽不足，收回多余带宽，实现 SQ 资源的充分利用，用户带宽得到有效保证。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **slot** slot-id ，进入槽位视图。步骤 **3** 执行命令 **qos bandwidth-adjustment** { **degrade-cycle** cycle-num | **upgrade-cycle**cycle-num | **monitor-number** monitor-number | **adjust-number** adjust-number |**disable-percent** disable-value **enable-percent** enable-value | **upgrade-threshold**upgrade-value **degrade-threshold** degrade-value [ **inbound** | **outbound** ] } ，设置用户带宽调整的一系列参数。说明由于下行带宽调整功能默认是关闭的，需要进行下行带宽调整时，要先执行 **qos bandwidth-****adjustment enable outbound** 使能下行带宽调整功能。步骤 **4** 执行命令 **commit** ，提交配置。**----**结束文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 298HUAWEI NetEngine40E配置指南 1 QoS##### ********3 （可选）配置 VE 接入的用户其 HQoS 功能在 eTM 上实现 背景信息请在 VE 接口上进行如下配置。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **interface virtual-ethernet** interface-number ，创建并进入 VE 接口视图。步骤 **3** 执行命令 **ve-group** ve-group-id **l3-access** ，创建 VE-Group ，并将虚拟以太网接口（ VE 接口）绑定到指定的 VE-Group 。步骤 **4** 执行命令 **access-user loop-back on-card** ，配置 VE 接入的用户其 HQoS 功能在 eTM 上实现。该命令仅在 LUPI-120-E/LPUI-240-E 单板支持，或在 LPUF-120 系列母板 /LPUF-240 系列母板配合 P52-E/P51-H/P120-E/P120-H/P240-E/P240-H 子卡使用时支持。步骤 **5** 执行命令 **commit** ，提交配置。**----**结束##### ********4 检查配置结果普通用户的 HQoS 调度配置成功后，可以查看指定 QoS 模板的配置信息和应用信息等内容。##### 操作步骤步骤 **1** 使用 **display qos user-id** user-id { **inbound** | **outbound** } 命令查看指定用户八个优先级队列的统计数据。步骤 **2** 使用 **display qos-profile configuration** [ profile-name ] 命令查看 QoS 模板的配置。步骤 **3** 使用 **display qos-profile application** profile-name 命令查看 QoS 模板的应用信息。步骤 **4** 使用 **display qos scheduling-mode** **slot** slot-id 命令查询指定单板当前使用的带宽调度模式。步骤 **5** 使用 **display qos resource sub-port-queue** **slot** slot-id **outbound** 命令查看子接口队列资源的使用情况。步骤 **6** 使用 **display sub-port-queue configuration** [ **verbose** [ sub-port-queue-name ] ]命令查看子接口队列信息。步骤 **7** 使用 **display sub-port-queue statistics** **interface** interface-type interface-number**outbound** 命令查看子接口队列的统计信息。步骤 **8** 使用 **display qos bandwidth-adjustment information** **slot** slot-id 命令查询指定单板的资源使用情况及带宽调整情况。步骤 **9** 使用 **display queue-4cos-mapping configuration** [ **verbose** [ queue-4cosmapping-name ] ] 命令查询 4 队列映射模板的配置信息，包括模板数目、名称、映射关系以及引用关系等。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 299HUAWEI NetEngine40E配置指南 1 QoS步骤 **10** 使用 **display user-group-queue statistics user-id** user-id { **inbound** | **outbound** }命令查看指定用户 ID 的用户组队列统计信息。**----**结束#### 1.10.4 配置家庭用户的 HQoS 调度配置家庭用户的 HQoS 调度将一个家庭作为一个整体进行统一调度，不再以终端为单位进行调度。##### 应用环境当一个家庭同时存在多种终端，并且同时开展多种业务如 VOIP 、 IPTV 和 HSI 时，不同业务具有不同的时延、时延抖动、带宽等要求，且要求在网络资源不足的情况下保证其高优先级业务的需求。这就要求将一个家庭作为一个整体进行统一调度，不再以终端为单位进行调度。－用户上线后会根据用户报文携带的内外层 VLAN ID 、 8021p 值、 DSCP 值或 DHCPOPTION60 信息进行业务识别，符合识别条件的报文被映射到相应的域中并进行认证。用户认证成功后，如果接口下有配置 QoS 模板且报文的接入信息符合 QoS 模板的调度，则认为是家庭用户接入，否则认为是普通用户接入。说明BAS 接口或不同子接口下具有相同家庭属性的业务可参与统一的家庭调度。##### 前置任务在配置家庭用户的 HQoS 调度之前，需要完成以下任务 :配置 NE40E 的 BRAS 功能，用户能够正常上线。相关配置请参照 HUAWEI                            NetEngine40E 路由器 配置指南 用户接入。##### 1.10.4.1 （可选）指定队列资源池根据此配置，指定队列资源池，使 QoS 资源得到充分利用。##### 背景信息每个物理口下，用户上线单方向使用的 SQ 资源有限，当需要单个物理口的单方向 SQ 资源大于限定值时，单方向的 QoS 资源就无法满足需求，这时可以通过命令 **qos****scheduling-mode** 实现不同的子接口下使用不同的资源调度模式，实现 QoS 资源的充分利用。VS 模式下，该配置仅在 Admin VS 支持。##### 操作步骤       - 指定 mode2 模式下 QoS 队列的资源池。a. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **qos scheduling-mode** **mode2** **slot** slot-id ，选择带宽调度模式为mode2 。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 300HUAWEI NetEngine40E配置指南 1 QoS说明只有带宽调度模式为 mode2 时，以下配置才生效。c. 执行命令 **interface** interface-type interface-number ，进入接口视图。d. 执行命令 **qos queue-resource pool** id { **inbound** | **outbound** } ，指定当前接口的 QoS 资源池。e. 执行命令 **commit** ，提交配置。       - 指定 mode3 模式下 QoS 队列的资源池。a. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **qos scheduling-mode** **mode3** **slot** slot-id ，选择带宽调度模式为mode3 。c. 执行命令 **commit** ，提交配置。**----**结束##### 1.10.4.2 （可选）配置流队列用户可以根据网络需求配置流队列的调度参数、流量整形和队列缓存资源等。##### 背景信息流队列（ Flow-queue ： FQ ）用于暂存一个用户各个优先级中的一个优先级的数据流。不同用户之间不能共享 FQ 。每个 FQ 可以配置 shaping 值来限制该队列的最大带宽。FQ 队列具有的属性：       - 队列优先级、队列权重       - 队列整形速率 PIR       - 报文丢弃策略，尾丢弃（ Tail-drop ）或 WRED用户可以根据网络需求配置流队列的调度参数、流量整形和队列缓存资源等。##### 操作步骤       - 配置流队列 WRED 对象。a. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **flow-wred** wred-name ，创建流队列 WRED 对象，进入流队列WRED 视图。c. 执行命令 **color** { **green** | **yellow** | **red** } **low-limit** low-limit-percentage**high-limit** high-limit-percentage **discard-percentage** discardpercentage ，设置不同颜色报文的高低门限百分比和丢弃概率。d. 执行命令 **queue-depth** queue-depth-value ，配置队列的深度。e. 执行命令 **commit** ，提交配置。f. 执行命令 **quit** ，返回系统视图。       - 创建流队列并配置其调度参数。a. 执行命令 **flow-queue** flow-queue-name [ **4cos-mode** ] ，创建流队列进入流队列视图。家庭用户支持普通的 8 队列模式的流队列和 4 队列模式的流队列。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 301HUAWEI NetEngine40E配置指南 1 QoS如果创建的是 4 队列模式的流队列，系统中存在缺省的 8 队列模式的流队列优先级与 4 队列模式的流队列优先级的映射关系，缺省映射关系如下表所示，同时也支持自定义两种队列优先级的映射关系。|流队列|与8队列优<br>先级的映<br>射关系|调度方式|权重|整形速率<br>百分比|丢弃方式||---|---|---|---|---|---||cos0|BE、AF1|WFQ|10|-|尾丢弃||cos1|AF2、<br>AF3、AF4|WFQ|15|-|尾丢弃||cos2|EF|PQ|-|-|尾丢弃||cos3|CS6、CS7|PQ|-|-|尾丢弃|如需自定义两种队列优先级的映射关系，可执行如下操作：i. 执行命令 **quit** ，返回系统视图。ii. 执行命令 **queue-4cos-mapping** mapping-name ，创建 4 队列映射模板，并进入 4 队列映射模板视图。iii. 执行命令 **queue** serviceclass **mapping** { **cos0** | **cos1** | **cos2** | **cos3** } ，配置 8 队列优先级与 4 队列优先级的映射关系。iv. 执行命令 **commit** ，提交配置。b. 执行命令 **queue** cos-value { { **pq** | **wfq** **weight** weight-value | **lpq** } |{ **shaping** { shaping-value | **shaping-percentage** shaping-percentagevalue } [ **pbs** pbs-value ] | **car** { car-value | **car-percentage** carpercentage-value } [ **pbs** pbs-value ] } | **flow-wred** wred-name | **low-****latency** | **low-jitter** } * 或 **queue** cos-value **cir** { { cir-value [ **cbs** cbs-value ]**cir-schedule pq** **pir** pir-value } | { **cir-percentage** cir-percentage-value[ **cbs** cbs-value ] **cir-schedule pq** **pir** **pir-percentage** pir-percentagevalue } } [ **pbs** pbs-value ] **pir-schedule** { **pq** | **wfq weight** weight-value |**lpq** } [ **flow-wred** wred-name ] ，配置流队列调度参数。c. 执行命令 **queue** cos-value **random-discard** random-discard-value ，配置流队列的随机丢弃值。       - 配置联合流量整形。说明4 队列流队列不支持联合流量整形。a. 执行命令 **share-shaping** [ shap-id ] { **af1** | **af2** | **af3** | **af4** | **be** | **cs6** | **cs7** |**ef** } [*] [ **pq** | **wfq** **weight** weight-value | **lpq** ] shaping-value [ **pbs** pbsvalue ] ，配置多个流队列的联合流量整形。配置联合流量整形后，联合流量整形中的队列将先做整形，再与其它的用户队列一起做调度。b. 执行命令 **share-shaping** { **be** | **af1** | **af2** | **af3** | **af4** | **ef** | **cs6** | **cs7** } [ *]**random-discard** random-discard-value ，配置流队列的联合流量整形随机丢弃值。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 302HUAWEI NetEngine40E配置指南 1 QoSc. 执行命令 **commit** ，提交配置。d. 执行命令 **quit** ，返回系统视图。       - 使能流队列下所有 PQ 调度队列的低时延功能。a. 执行命令 **qos flow-queue low-latency enable** ，使能 flow-queue 下所有 PQ调度队列的低时延功能，以保证 PQ 调度队列的时延。       - 配置 PQ 调度优先级映射、全局缓存和桶深预备值。a. 执行命令 **slot** slot-id ，进入槽位视图。b. 执行命令 **qos pq-scheduler priority** { **high** | **low** } **outbound** [ **card**card_id ] ，更改子卡的 PQ 调度优先级映射，改变原有优先级默认映射关系。c. 执行命令 **qos cos** { **be** | **af1** | **af2** | **af3** | **af4** | **ef** | **cs6** | **cs7** } **burst-size**buffer-size-value ，配置队列的桶深预借值。d. 执行命令 **qos cos all pack-size** pack-size-value ，配置后置 eTM 子卡的报文数据块容量阈值。e. 执行命令 **qos global-buffer** { **share-threshold** share-value | { **be** | **af1** |**af2** | **af3** | **af4** | **ef** | **cs6** | **cs7** } **share** } { **inbound** | **outbound** } ，控制整个单板的队列缓存资源。为了避免队列缓存被部分队列大量占用，需要通过配置限制队列最大可以使用的队列缓存。当通过 flow-wred 来限制时，如果配置的 flow-wred 过小，会导致突发流量丢包的问题，此时可以通过命令 **qos global-buffer** 设置全局队列缓存，在共享的队列缓存资源耗尽前， flow-wred 配置不生效，从而避免流量突发导致的丢包问题。f. 执行命令 **commit** ，提交配置。g. 执行命令 **quit** ，返回系统视图。       - 配置流队列的映射关系。a. 执行命令 **flow-mapping** mapping-name ，进入流队列映射视图。用户可以在一个流队列映射模板中，分别配置 8 个 flow-queue 到 port-queue的映射关系，也可以根据需要，创建多个 flow-mapping 映射模板，供用户队列引用，系统最多可以配置 15 个 flow-mapping 映射模板。b. 执行命令 **map flow-queue** cos-value **to** **port-queue** cos-value ，设置用户队列中某个业务进入端口队列的优先级。如果用户不配置流队列到端口队列的映射关系，系统将采取缺省的一一对应映射关系。c. 执行命令 **commit** ，提交配置。d. 执行命令 **quit** ，返回系统视图。**----**结束##### 1.10.4.3 （可选）配置业务模板在业务模板中配置精度调整长度，对报文长度进行补偿，补偿报文在设备上处理后的差值，从而精确地进行流量控制。##### 背景信息用户报文的长度会因为调度模块不同的处理方式而变化，影响流量整形的准确性。此时用户可以通过配置业务模板中的精度调整长度来补偿报文在设备上处理后的差值，从而提高流量整形的准确性。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 303HUAWEI NetEngine40E配置指南 1 QoS##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **service-template** service-template-name ，进入业务模板视图。步骤 **3** 执行命令 **network-header-length** network-header-length { **inbound** |**outbound** } ，设置业务模板的精度补偿长度。说明如需同时指定前置 TM 和后置 TM （ ETM ）的精度补偿值，需要在全局的业务模板中针对前后置TM 分别进行配置，具体操作如下：          - 在业务模板视图下执行命令 **network-header-length** network-header-length **outbound** ，设置业务模板的精度补偿长度，在 QoS 模板中应用该业务模板后，精度补偿值会在出方向的前置 TM 生效。          - 在业务模板视图下执行命令 **network-header-length** network-header-length **outbound** ，设置业务模板的精度补偿长度，在 QoS 模板中应用该业务模板并使用参数 **adjust-on-card** 指定其在后置 TM 生效时，精度补偿值将会在出方向的后置 TM 生效。          - 在业务模板视图下执行命令 **network-header-length** network-header-length **outbound****adjust-on-etm** ，设置业务模板的精度补偿长度，并指定其在后置 TM 生效。在 QoS 模板中应用该业务模板后，精度补偿值会在出方向的后置 TM 生效。步骤 **4** 执行命令 **commit** ，提交配置。**----**结束##### 1.10.4.4 配置基于 QoS 模板的用户队列QoS 模板是 QoS 调度参数的集合，可配置用户队列的调度参数包括用户队列的保证带宽、峰值带宽、流队列模板和业务模板精度的调整长度。##### 背景信息在用户接入场景下，认证接收报文中携带的 RADIUS 属性 HW-QOS-Profile-Name (31)可为用户动态指定 Qos 限速模板。前提条件是设备上需要配置 QoS 模板并配置调度参数。关于属性 HW-QOS-Profile-Name (31) 的详细说明可参考产品文档中的 Radius 属性列表。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **qos-profile** qos-profile-name ，定义 QoS 模板并进入 QoS 模板视图。步骤 **3** 执行命令 **user-queue** { { **cir** cir-value [ **cbs** cbs-value ] [ [ **pir** pir-value [ **pbs** pbsvalue ] [ **pir-priority high** ] ] | [ **flow-queue** flow-queue-name ] | [ **queue-4cos-****mapping** queue-mapping-name ] | [ **flow-mapping** mapping-name ] | [ **user-****group-queue** group-name ] ] * [ **inbound** | **outbound** ] [ **service-template**template-name [ **adjust-on-card** ] ] } | { **cir-percentage** cir-percentage-value[ **cbs** cbs-value ] [ [ **pir-percentage** pir-percentage-value [ **pbs** pbs-value ] [ **pir-****priority high** ] ] | [ **flow-queue** flow-queue-name ] | [ **queue-4cos-mapping**queue-mapping-name ] | [ **flow-mapping** mapping-name ] | [ **user-group-queue**group-name ] ] * [ **inbound** | **outbound** ] [ **service-template** template-name[ **adjust-on-card** ] ] } } ，配置用户队列的调度参数。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 304HUAWEI NetEngine40E配置指南 1 QoS说明          - 如果绑定了优先级模式的流队列模板，那么承诺信息速率必须为零。          - 配置 **user-queue** 命令时应用的业务模板 service-template 必须为全局的。步骤 **4** 执行命令 **car** { **cir** cir-value [ **pir** pir-value ] | **cir** **cir-percentage** cir-percentagevalue [ **pir** **pir-percentage** pir-percentage-value ] } [ **cbs** cbs-value [ **pbs** pbsvalue ] ] [ **green** { **discard** | **pass** [ **service-class** class **color** color ] } | **yellow**{ **discard** | **pass** [ **service-class** class **color** color ] } | **red** { **discard** | **pass** [ **service-****class** class **color** color ] } ]* [ **inbound** | **outbound** ] [ **color-aware** ] ，配置 CAR 来保证用户流量。说明对于 LPUF-53A/LPUF-243A/LPUI-242A/LPUF-483A/LPUI-52C/LPUI-402C/LPUF-400-E/LPUI-2TA/LPUF-1T2A/LPUI-1T2A-CM/LPUI-483A-CM/LPUI-243A-CM/LPUF-243-K/LPUF-53K/VSUI-400-K/LPUI-2T-K/LPUF-53D/LPUF-243D/LPUF-483D/VSUI-400A 单板，当 CAR 资源不足时，可以通过在槽位试图下执行 **car resource-pool enhanced mode** 命令调整 CAR 资源分配模式为增强模式，适量提升 CAR 资源。步骤 **5** 执行命令 **commit** ，提交配置。**----**结束##### 1.10.4.5 （可选）配置动态修改 QoS 模板用户在线时更改使用的 QoS 模板，用户域下原来配置的 QoS 模板不再生效。##### 背景信息说明VS 模式下，该配置任务仅在 Admin VS 支持。为了保持用户在线情况时能更改使用的 QoS 模板，需要配置动态修改 QoS 模板。配置该功能后，用户使用的 QoS 模板更改为 **update qos-profile** 命令中的模板，用户域下原来配置的 QoS 模板不再生效。动态修改 QoS 模板只对用户的业务流生效，不能修改家庭调度的 QoS 模板，即只能修改域下的 QoS 模板，不能修改接口下的 QoS 模板。请在路由器上进行以下配置。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **aaa** ，进入 AAA 视图。步骤 **3** 执行命令 **update qos-profile** **user-id** user-id **profile** qos-profile-name { **inbound** |**outbound** } ，配置动态修改 QoS 模板。步骤 **4** 执行命令 **commit** ，提交配置。**----**结束文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 305HUAWEI NetEngine40E配置指南 1 QoS##### 1.10.4.6 配置业务识别策略根据用户报文携带的某些字段判断上线用户的认证域信息。##### 背景信息NE40E 支持以下四种业务识别方式，请根据实际进行配置。       - 基于内 / 外层 VLAN ID 的业务识别方式       - 基于内 / 外层 VLAN TAG 中的 8021p 值的业务识别方式       - 基于 DSCP 值的业务识别方式       - 基于 DHCP-OPTION60 的业务识别方式##### 操作步骤       - 配置基于内 / 外层 VLAN ID 的业务识别方式a. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **service-identify-policy** policy-name ，创建业务识别策略，并进入业务识别策略视图。c. 执行命令 **service-identify** { **inner-vlan** | **outer-vlan** } ，配置业务识别方式。d. 执行命令 **vlan** vlan-id1 [ vlan-id2 ] **domain** domain-name ，配置指定 VLANID 的报文与域的映射。e. 执行命令 **commit** ，提交配置。       - 配置基于内 / 外层 VLAN TAG 中的 8021p 值的业务识别方式VS 模式下，该配置任务仅在 Admin VS 支持。a. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **service-identify-policy** policy-name ，创建业务识别策略，并进入业务识别策略视图。c. 执行命令 **service-identify** **8021p** { **inner-vlan** | **outer-vlan** } ，配置业务识别方式。d. 执行命令 **8021p** start-num [ end-num ] **domain** domain-name ，配置指定内 / 外层 VLAN TAG 中的 8021p 值的报文与域的映射。e. 执行命令 **commit** ，提交配置。       - 配置基于 DSCP 值的业务识别方式a. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **service-identify-policy** policy-name ，创建业务识别策略，并进入业务识别策略视图。c. 执行命令 **service-identify** **dscp** ，配置业务识别方式。d. 执行命令 **dscp** start-dscp-id [ end-dscp-id ] **domain** domain-name ，配置指定 dscp 值的报文与域的映射。e. 执行命令 **commit** ，提交配置。       - 配置基于 DHCP-OPTION60 的业务识别方式a. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **service-identify-policy** policy-name ，创建业务识别策略，并进入业务识别策略视图。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 306HUAWEI NetEngine40E配置指南 1 QoSc. 执行命令 **service-identify** **dhcp-option60** ，配置业务识别方式。d. （可选）执行命令 **option60 partial-match** { **domain-included** |**included-in-domain** } 或 **vendor-class partial-match** { **domain-included**| **included-in-domain** } ，配置 VENDOR-CLASS （ DHCPv4 OPTION60/DHCPv6 OPTION16 ）字符串内容部分匹配。e. （可选）执行命令 **option60 partial-info** { **cn** | [ **offset** offset ] { **length**length | **sub-option** option [ **sub-offset** sub-offset ] [ **sub-length** sublength ] } } 或 **vendor-class partial-info** { **cn** | [ **offset** offset ] { **length**length | **sub-option** option [ **sub-offset** sub-offset ] [ **sub-length** sublength ] } } ，配置使用部分 vendor-class （ DHCPv4 option60/DHCPv6option16 ）属性内容识别业务。f. （可选）执行命令 **option60 encrypt** 或 **vendor-class encrypt** ，配置VENDOR-CLASS （ DHCPv4 OPTION60/DHCPv6 OPTION16 ）字符串内容加密。g. 执行命令 **commit** ，提交配置。**----**结束##### 1.10.4.7 在 BAS 接口下绑定 QoS 模板和业务识别策略在接口对各类用户报文应用所配置的 QoS 模板，通过队列的调度参数对用户业务进行HQoS 调度；绑定业务识别策略，对符合条件的报文映射到相应的域中并进行认证。##### 背景信息说明接口下应用 QoS 模板和域下应用 QoS 模板的作用不同。家庭用户的带宽参数从接口下应用的 QoS模板中获取，基于业务类型分配的带宽参数从域下应用的 QoS 模板中获取。对于家庭用户，可以先在 AAA 域下针对单个业务应用配置了 CAR 的 qos-profile 模板，然后在接口下针对整个家庭应用配置了 CAR 的 qos-profile 模板。对于家庭用户，在 AAA 域的单业务只能应用配置了 CAR 的 qos-profile 模板，而在接口下配置整个家庭的限速时，上行方向 CAR 或 user-queue 不能同时生效，如果同时配置了 CAR 和 user-queue ，则 CAR 生效；下行方向 CAR 和 user-queue 可以同时生效。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **interface** interface-type interface-number ，进入接口视图。步骤 **3** 执行命令 **bas** ，进入 BAS 接口视图。步骤 **4** 配置用户接入类型和相关属性。       - 执行命令 **access-type** **layer2-subscriber** [ **bas-interface-name** bname |**default-domain** { **pre-authentication** predname | **authentication** [ **force** |**replace** ] dname } [*] | **accounting-copy radius-server** rd-name ] [*] ，配置二层普通用户接入类型及相关属性。       - 或执行命令 **access-type** **layer3-subscriber** [ **default-domain** { [ **pre-****authentication** predname ] **authentication** [ **force** | **replace** ] dname } [*] ] ，配置三层普通用户接入类型及相关属性。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 307HUAWEI NetEngine40E配置指南 1 QoS说明在设置 BAS 接口的用户接入类型时，还可以一起设置和该种用户类型相关的业务属性，这些属性也可以在后续的配置中逐项配置。–在配置三层普通用户时，可以在系统视图下执行命令 **layer3-subscriber**{ start-ip-address [ end-ip-address ] | start-ipv6-address [ end-ipv6address ] | **delegation-prefix** start-ipv6-address [ end-ipv6-address ][ end-ip-address ] mask-length} * [ **vpn-instance** instance-name ]**domain-name** domain-name 和 **layer3-subscriber ip-address any****domain-name** domain-name 指定所在 IP 地址段以及相关联的认证域的域名。–在配置三层静态网段用户时，可以在系统视图下执行命令 **layer3-subscriber****subnet-session** start-ip-address { mask-address | mask-length } [ **vpn-****instance** instance-name ] [ **domain-name** domain-name ] 通过配置掩码方式为用户指定所在 IPv4 地址段，以及相关联的认证域域名。说明– 对于已经被 Eth-Trunk 接口包含的以太网接口，不能配置其用户接入类型，而只能在相应的 Eth-Trunk 接口下配置用户接入类型。– 在配置三层用户静态路由时，应指定下一跳为用户的 IP 地址，不能指定出接口，否则会造成网络侧到用户侧回程流量转发不通。       - 或执行命令 **access-type** **layer2-leased-line** **user-name** uname **password**{ **cipher** password | **simple** password } [ **bas-interface-name** bname |**default-domain** **authentication** dname | **accounting-copy** **radius-server** rdname | **nas-port-type** { **async** | **sync** | **isdn-sync** | **isdn-async-v120** | **isdn-****async-v110** | **virtual** | **piafs** | **hdlc** | **x.25** | **x.75** | **g.3-fax** | **sdsl** | **adsl-cap** |**adsl-dmt** | **idsl** | **ethernet** | **xdsl** | **cable** | **wireless-other** | **802.11** } ] [*] 或**access-type** **layer3-leased-line** { **user-name** uname | **user-name-template** }**password** { **cipher** password | **simple** password } [ **default-domain****authentication** dname | **bas-interface-name** bname | **accounting-copy****radius-server** rd-name | **nas-port-type** { **async** | **sync** | **isdn-sync** | **isdn-****async-v120** | **isdn-async-v110** | **virtual** | **piafs** | **hdlc** | **x.25** | **x.75** | **g.3-fax** |**sdsl** | **adsl-cap** | **adsl-dmt** | **idsl** | **ethernet** | **xdsl** | **cable** | **wireless-other** |**802.11** } | **mac-address** mac-address | **client-id** client-id ] [*] ，配置二、三层专线用户接入类型及相关属性。说明          - 有用户在线时，只有当用户类型是专线用户时，可以在线修改 BAS 接口的用户接入类型，其他情况不能修改。          - 当用户类型配置为专线用户后， NE40E 立即对该专线用户进行认证。步骤 **5** （可选）执行命令 **user-bandwidth auto-adapt** { **enable** | **type1** } ，配置 DHCPv4 ，DHCPv6 和 PPPoE 用户 PIR 带宽的自动调整功能。步骤 **6** （可选）执行命令 **option82-identify include static-user** ，为静态用户打上 option82信息，在配置 QoS 模板后，可以对静态用户进行识别。步骤 **7** 请根据用户的报文类型选择不同的命令行应用 QoS 模板。       - 执行命令 **qos-profile** profile-name [ **inbound** | **outbound** ] [ **identifier** { **none**| **option82** | **access-line-id** | **pe-vid** | **ce-vid** | **pe-ce-vid** | **vlan-id** } ] [ **group**group-name ] [ **session-limit** max-session-number ] ，对三层用户报文应用QoS 模板并指定家庭用户识别方式。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 308HUAWEI NetEngine40E配置指南 1 QoS       - 执行命令 **qos-profile** profile-name [ **inbound** | **outbound** ] **vlan** vlan-id-begin[ **to** vlan-id-end ] [ **identifier** { **vlan-id** | **none** | **option82** | **access-line-id** } ][ **group** group-name ] [ **session-limit** max-session-number ] ，对 VLAN 用户报文应用 QoS 模板并指定家庭用户识别方式。       - 执行命令 **qos-profile** profile-name [ **inbound** | **outbound** ] **pe-vid** pe-vlan-id**ce-vid** ce-vlan-id-begin [ **to** ce-vlan-id-end ] [ **identifier** { **pe-vid** | **ce-vid** |**pe-ce-vid** | **none** | **option82** | **access-line-id** } ] [ **group** group-name ][ **session-limit** max-session-number ] ，对 QinQ 方式接入的用户报文应用 QoS模板并指定家庭用户识别方式。说明一个 SQ 下只能对应一个 GQ 。步骤 **8** 执行命令 **service-identify-policy** policy-name ，在 BAS 接口下绑定业务识别策略。步骤 **9** （可选）当家庭用户通过 Eth-Trunk 接口接入时，且 Trunk 下的多个成员口跨了转发模块或流量管理模块时，为了防止 Trunk 口的限速带宽翻倍，可根据用户的接入类型，选择以下命令配置将限速总带宽在 Trunk 成员口间按权重分配。       - 对于普通接入的家庭用户，执行命令 **qos subscriber-access member-link-****scheduler distribute** **inbound** ，将限速带宽在 Trunk 成员口间按权重分配。       - 对于专线接入的家庭用户，执行命令 **qos leased-line-access member-link-****scheduler distribute** { **inbound** | **outbound** } ，将限速带宽在 Trunk 成员口间按权重分配。步骤 **10** 执行命令 **commit** ，提交配置。**----**结束##### 1.10.4.8 定义 GQ 模板并配置调度参数GQ 模板是用户组队列调度参数的集合，可配置用户组队列的调度参数包括用户组队列的保证带宽、峰值带宽。##### 背景信息在路由器上进行以下配置。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **user-group-queue** group-name ，进入用户组队列视图。步骤 **3** 请根据实际情况选择配置用户队列的调度。       - 执行命令 **shaping** shaping-value [ **pbs** pbs-value ] { **inbound** | **outbound** } ，设置用户组的 shaping 值。       - 执行命令 **weight** weight-value **outbound** ，设置用户组队列权重值。       - 执行命令 **cir** cir-value [ **cbs** cbs-value ] [ **pir** pir-value [ **pbs** pbs-value ] ]**outbound** ，设置用户组 Cir 。说明**shaping** shaping-value [ **pbs** pbs-value ] { **inbound** | **outbound** } 和 **cir** cir-value [ **cbs** cbsvalue ] [ **pir** pir-value [ **pbs** pbs-value ] ] **outbound** 互斥。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 309HUAWEI NetEngine40E配置指南 1 QoS步骤 **4** 执行命令 **commit** ，提交配置。**----**结束##### 1.10.4.9 应用 GQ 模板将 GQ 模板应用到用户 VLAN 视图或用户上线的 BAS 口下来实现对用户组队列的调度管理。##### 背景信息在路由器上进行以下配置。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 在用户 VLAN 视图下应用 GQ 模板，对用户流量进行调度。1. 执行命令 **interface** interface-type interface-number ，进入子接口视图。2. 执行命令 **user-vlan** { { start-vlan-id [ end-vlan-id ] [ **qinq** start-pe-vlan [ endpe-vlan ] ] } | **any-other** } ，进入用户 VLAN 视图。3. 执行命令 **user-group-queue** user-group-queue-name [ **identifier** identifier ]{ **inbound** | **outbound** } [ **group** groupname ] ，在用户 VLAN 视图下为用户分配一个用户组队列。4. 执行命令 **commit** ，提交配置。5. 执行命令 **quit** ，退回到子接口视图。6. 执行命令 **quit** ，退回到系统视图。7. 执行命令 **interface** **Eth-trunk** trunk-number ，进入 Eth-Trunk 接口视图。8. （可选）执行命令 **bas-load-balance exclude user-group-queue outbound** ，配置用户下行方向的流量不依据 GQ 配置进行选路。若用户上线接口为 Eth-trunk 接口，同一个 GQ 下用户的下行方向流量默认依据 GQ配置进行选路，即通过同一个 Trunk 成员口转发该 GQ 下所有用户的下行方向流量，可能会出现 Trunk 各成员口间流量负载不均的问题。如果想要用户下行方向流量负载分担到 Trunk 接口的不同成员口，可配置本命令实现。9. 执行命令 **commit** ，提交配置。步骤 **3** 在 BAS 视图下应用 GQ 模板，配置父 GQ 对用户下行方向的流量进行调度。1. 执行命令 **quit** ，退回到系统视图。2. 执行命令 **interface** interface-type interface-number ，进入接口视图。3. 执行命令 **bas** ，进入 BAS 视图。4. 执行命令 **user-group-queue** user-group-queue-name **outbound** [ **group**group ] ，在 BAS 视图下应用 GQ 模板。5. 执行命令 **commit** ，提交配置。6. 执行命令 **quit** ，退回到接口视图。7. 执行命令 **quit** ，退回到系统视图。8. 执行命令 **interface** **Eth-trunk** trunk-number ，进入 Eth-trunk 接口视图。9. （可选）执行命令 **bas-load-balance exclude sub-port-queue outbound** ，配置用户下行方向的流量不依据父 GQ 配置进行选路。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 310HUAWEI NetEngine40E配置指南 1 QoS若用户上线接口为 Eth-trunk 接口，同一个父 GQ 下用户的下行方向流量默认依据父 GQ 配置进行选路，即通过同一个 Trunk 成员口转发该父 GQ 下所有用户的下行流量，可能会出现 Trunk 各成员口间流量负载不均的问题。如果想要用户下行方向流量负载分担到 Trunk 接口的不同成员口，可配置本命令实现。10. 执行命令 **commit** ，提交配置。**----**结束##### 1.10.4.10 （可选）阶梯调整用户带宽根据此配置，阶梯调整用户带宽，使 SQ 资源得到充分利用。##### 背景信息设备允许配置的 SQ 的 CIR/PIR 之和不能无限大，所以当用户通过配置申请 SQ 资源时，会出现因为 SQ 的 CIR/PIR 之和超过设备处理能力而出现资源申请失败的情况，为避免该问题，可以通过命令 **qos bandwidth-adjustment** ，实时调整用户带宽，及时补充带宽不足，收回多余带宽，实现 SQ 资源的充分利用，用户带宽得到有效保证。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **slot** slot-id ，进入槽位视图。步骤 **3** 执行命令 **qos bandwidth-adjustment** { **degrade-cycle** cycle-num | **upgrade-cycle**cycle-num | **monitor-number** monitor-number | **adjust-number** adjust-number |**disable-percent** disable-value **enable-percent** enable-value | **upgrade-threshold**upgrade-value **degrade-threshold** degrade-value [ **inbound** | **outbound** ] } ，设置用户带宽调整的一系列参数。说明由于下行带宽调整功能默认是关闭的，需要进行下行带宽调整时，要先执行 **qos bandwidth-****adjustment enable outbound** 使能下行带宽调整功能。步骤 **4** 执行命令 **commit** ，提交配置。**----**结束##### 1.10.4.11 （可选）配置 VE 接入的用户其 HQoS 功能在 eTM 上实现 背景信息请在 VE 接口上进行如下配置。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **interface virtual-ethernet** interface-number ，创建并进入 VE 接口视图。步骤 **3** 执行命令 **ve-group** ve-group-id **l3-access** ，创建 VE-Group ，并将虚拟以太网接口（ VE 接口）绑定到指定的 VE-Group 。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 311HUAWEI NetEngine40E配置指南 1 QoS步骤 **4** 执行命令 **access-user loop-back on-card** ，配置 VE 接入的用户其 HQoS 功能在 eTM 上实现。该命令仅在 LUPI-120-E/LPUI-240-E 单板支持，或在 LPUF-120 系列母板 /LPUF-240 系列母板配合 P52-E/P51-H/P120-E/P120-H/P240-E/P240-H 子卡使用时支持。步骤 **5** 执行命令 **commit** ，提交配置。**----**结束##### 1.10.4.12 检查配置结果家庭用户的 HQoS 调度配置成功后，可以查看指定 ID 的上线用户信息和占用的带宽、QoS 模板的应用信息和等内容。##### 背景信息完成上述配置后，请执行下面的命令检查配置结果。##### 操作步骤步骤 **1** 使用 **display qos user-id** user-id { **inbound** | **outbound** } 命令查看指定用户八个优先级队列的统计数据。VS 模式下，该命令仅在 Admin VS 支持。步骤 **2** 使用 **display qos-profile configuration** [ profile-name ] 命令查看 QoS 模板的配置。步骤 **3** 使用 **display qos-profile application** profile-name 命令查看 QoS 模板的应用信息。步骤 **4** 使用 **display qos scheduling-mode** **slot** slot-id 命令查询指定单板当前使用的带宽调度模式。步骤 **5** 使用 **display qos resource sub-port-queue** **slot** slot-id **outbound** 命令查看子接口队列资源的使用情况。步骤 **6** 使用 **display sub-port-queue configuration** [ **verbose** [ sub-port-queue-name ] ]命令查看子接口队列信息。步骤 **7** 使用 **display sub-port-queue statistics** **interface** interface-type interface-number**outbound** 命令查看子接口队列的统计信息。步骤 **8** 使用 **display qos bandwidth-adjustment information** **slot** slot-id 命令查询指定单板的资源使用情况及带宽调整情况。VS 模式下，该命令仅在 Admin VS 支持。**----**结束#### 1.10.5 配置最后一公里 QoS最后一公里基于 DSLAM 和用户之间运行的链路层协议来调整下行的流量，防止 DSLAM流量超过线路实际能力造成拥塞。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 312HUAWEI NetEngine40E配置指南 1 QoS##### 背景信息说明VS 模式下，该特性仅在 Admin VS 支持。##### ******** 最后一公里 QoS 简介介绍最后一公里 QoS 的概念和原理。**********.1** 最后一公里 **QoS** 概述最后一公里 QoS 特性，根据 DSLAM 和用户之间运行的链路层协议来调整下行的流量。##### **-** 整形精度调整 最后一公里 QoS最后一公里是指宽带接入交换机（如以太 DSLAM ）到最终用户的这一段传输线路。在最后一公里，如 图 **1-33** ，企业 / 家庭用户通常采用 IPoE 、 PPPoE 方式接入到以太DSLAM ， DSLAM 再通过城域网接入到 IP 骨干网边缘设备 BRAS/SR 。图 **1-33** 最后一公里组网示例对于宽带接入的场景， BRAS/SR 和 DSLAM 之间是以太链路， BRAS/SR 采用以太封装报文，其流量整形是基于以太封装的。即使用户和 DSLAM 之间也是以太链路，其封装开销也可能比 BRAS/SR 上的用户侧封装开销大。例如， BRAS/SR 封装的以太报文不带 VLAN Tag ，用户和 DSLAM 之间采用VLAN 或 QinQ 封装，其报文则携带了一层或两层 VLAN TAG 。为了解决上述问题，可在 BRAS/SR 配置最后一公里 QoS 特性。最后一公里 QoS 是指设备在进行流量整形时，根据用户订购带宽和 DSLAM 下行接口带宽进行精度调整，通过调整报文字节对整形带宽进行补偿。以上原因导致 BRAS/SR 无法自动推断出报文经过 DSLAM 封装后的总长度，需要手工配置补偿字节数 overhead 。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 313HUAWEI NetEngine40E配置指南 1 QoS配置补偿字节数之后，对于 DSLAM 采用以太链路接入 CPE 的场景， BRAS/SR 会自动根据转发的报文长度及配置的 overhead ，直接推断出报文经过 DSLAM 封装后的总长度，从而自动对整形带宽进行修正。作为参考，下文给出了常见报文封装开销及几种典型场景下需要配置的补偿值。表 **1-26** 报文封装开销|封装类型|Col2|封装开销（字节）||---|---|---||PPP首部|PPP首部|2||Eth首部|Eth首部|14||VLAN首部|VLAN首部|4||QinQ首部|QinQ首部|8||AAL5封<br>装|VC方式|AAL5 Header + AAL5 tail = 0 + 8<br>= 8||AAL5封<br>装|LLC Type1（无连接模式，如IPoE、<br>PPPoE）|AAL5 Header + AAL5 tail = 8 + 8<br>= 16|表 **1-27** 最后一公里 QoS 常用调整字节表|场景|配置补偿值(字节)||---|---|||= VLAN header - QinQ<br>header<br>= - 4|||= 0 - QinQ header<br>= - 8|**********.2** 支持的最后一公里 **QoS**最后一公里 QoS 调度包括以下模式：       - 以太网帧链路级 QoS 调度模式用户和 DSLAM 之间通过以太传输， DSLAM 和 NE40E 之间通过 VLAN 或 QinQ 传输。配置链路级 QoS 后， NE40E 可以去掉 BRAS 和 DSLAM 间的 VLAN 头开销。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 314HUAWEI NetEngine40E配置指南 1 QoS##### ******** 配置最后一公里 QoS通过配置最后一公里 QoS ，使设备在 QoS 整形时能够扣除 ATM 用户信元头或以太帧头等多余的额外开销，避免下行的流量超过 DSLAM 线路能力，造成 DSLAM 线路的拥塞。##### 应用环境NE40E 作为 BRAS 设备时，通过配置最后一公里 QoS ， NE40E 在 QoS 整形时能够扣除ATM 用户信元头或以太帧头等多余的额外开销，避免 NE40E 下行的流量超过 DSLAM 线路能力，造成 DSLAM 线路的拥塞。NE40E 支持在接口视图和 AAA 域视图下配置最后一公里 QoS 功能，其中 AAA 域视图下的配置只对 L2TP 业务有效。当同时在域视图和接口视图下配置该功能时，域视图下配置生效。##### 前置任务在配置最后一公里 QoS 之前，需要完成以下任务 :       - 配置 NE40E 的 BRAS 功能，用户能够正常上线。**********.1** 使能最后一公里 **QoS** 功能使能最后一公里 QoS 功能是配置的首要步骤。##### 背景信息NE40E 支持在 AAA 域视图下使能最后一公里 QoS 功能， AAA 域视图下的相关配置只对L2TP 业务有效。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **aaa** ，进入 AAA 视图。步骤 **3** 执行命令 **domain** domain-name ，进入 AAA 域视图。步骤 **4** 执行命令 **qos link-adjustment remote enable** ，在 AAA 域下使能最后一公里 QoS 。步骤 **5** 执行命令 **commit** ，提交配置。**----**结束**********.2** 配置远端链路模式及调整值设备根据用户接入 DSLAM 的不同链路类型对报文进行链路带宽调整。##### 背景信息NE40E 可以对两种链路类型进行最后一公里 QoS 调度。NE40E 根据 DSLAM 两端的链路类型不同对报文进行调整。如果用户以 PPPoE 方式接入，则配置远端链路类型为 **frame** 模式。NE40E 支持在接口视图和 AAA 域视图下配置远端链路报文调整值，其中 AAA 域视图下只对 L2TP 业务生效。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 315HUAWEI NetEngine40E配置指南 1 QoS请根据需要在路由器上进行如下配置。##### 操作步骤       - 在接口视图下配置远端链路模式及调整值a. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **interface** interface-type interface-number ，进入接口视图。c. 执行命令 **qos link-adjustment shaping-mode** { **frame** | **cell** } ，配置最后一公里 QoS 模式。d. 执行命令 **qos link-adjustment** adjust-value **remote** ，配置远端链路调整值。e. 执行命令 **commit** ，提交配置。       - 在 AAA 域视图下配置远端链路模式及调整值a. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **aaa** ，进入 AAA 视图。c. 执行命令 **domain** domain-name ，进入 AAA 域视图。d. 执行命令 **qos link-adjustment shaping-mode** { **frame** | **cell** } ，配置最后一公里 QoS 模式。e. 执行命令 **qos link-adjustment** adjust-value **remote** ，配置远端链路调整值。AAA 域视图下配置的远端链路调整值只对 L2TP 业务有效。当接口视图和 AAA域视图下同时配置了调整值时， AAA 域视图下的生效。f. 执行命令 **commit** ，提交配置。**----**结束**********.3** （可选）使能三层报文的上行 **CAR** 功能和报文统计功能对三层报文执行 CAR 和报文统计功能，用以调整链路带宽。##### 背景信息在路由器上进行如下配置。VS 模式下，该配置仅在 Admin VS 支持。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **qos link-adjustment** { **link-layer-exclude** | **l2tp-layer-exclude** } [ **slot**slot-id ] ，使能三层报文上行 CAR 功能和统计功能。说明**link-layer-exclude** 不支持对指定槽位进行配置。步骤 **3** 执行命令 **commit** ，提交配置。**----**结束文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 316HUAWEI NetEngine40E配置指南 1 QoS**********.4** 检查配置结果最后一公里 QoS 的配置成功后，可以查看接口的最后一公里 QoS 的配置信息。##### 背景信息完成上述配置后，请执行下面的命令检查配置结果。##### 操作步骤步骤 **1** 使用 **display qos link-adjustment configuration** [ **interface** interface-typeinterface-number ] 命令查看接口的最后一公里 QoS 的配置信息。**----**结束#### 1.10.6 配置举例##### 1.10.6.1 配置 UCL 用户的流量策略示例以 UCL 用户的流量策略为例，介绍简单流分类的配置。##### 组网需求如 图 **1-34** 所示，用户采用 PPPoE 方式通过 DSLAM 接入网络，用户到网络的流量在Device 上能够通过标记用户的优先级区分金、银牌用户，并根据用户的优先级对流量进行带宽保证和限速。图 **1-34** UCL 用户的流量策略组网图##### 配置思路采用如下的思路配置 UCL 用户的流量策略。1. 配置用户正常上线。2. 配置用户组。3. 配置 UCL 用户规则。4. 配置流分类、流行为和流量策略。5. 应用 UCL 用户的流策略。6. 指定用户所属的域。##### 数据准备为完成此配置例，需准备如下的数据。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 317HUAWEI NetEngine40E配置指南 1 QoS       - 用户组名       - UCL 编号       - 流分类、流行为、流策略的名称       - 用户接入速率 15Mbit/s ，承诺突发流量尺寸 300000 字节，峰值接入速率20Mbit/s ，峰值突发尺寸为 500000 字节       - 用户所属的域##### 操作步骤步骤 **1** 配置 Device 的 BRAS 业务功能，使用户能够上线。                                              详细配置过程请参见《 HUAWEI NetEngine40E 路由器 配置指南 用户接入》。步骤 **2** 配置用户组。< ~ HUAWEI> **system-view**[ ~ HUAWEI] **sysname Device**[*Device] **commit**[ ~ Device] **user-group group1**[*Device] **commit**步骤 **3** 配置 UCL 用户规则。[ ~ Device] **acl 6001**[*Device-acl-ucl-6001] **rule 1 permit ip source user-group group1 destination any**[*Device-acl-ucl-6001] **commit**[*Device-acl-ucl-6001] **quit**步骤 **4** 配置流分类，定义基于 ACL 的匹配规则。[ ~ Device] **traffic classifier c1**[*Device-classifier-c1] **if-match acl 6001**[*Device-classifier-c1] **commit**[*Device-classifier-c1] **quit**步骤 **5** 配置流行为。[ ~ Device] **traffic behavior b1**[*Device-behavior-b1] **car cir 15000 pir 20000 cbs 300000 pbs 500000**[*Device-behavior-b1] **commit**[*Device-behavior-b1] **quit**步骤 **6** 定义流量策略，将流分类与流行为关联。[ ~ Device] **traffic policy p1**[*Device-trafficpolicy-p1] **classifier c1 behavior b1**[*Device-trafficpolicy-p1] **statistics enable**[*Device-trafficpolicy-p1] **commit**[*Device-trafficpolicy-p1] **quit**步骤 **7** 应用 UCL 用户的流分类策略。 VS 模式下，命令 **traffic-policy** 仅在 Admin-VS 支持。[ ~ Device] **traffic-policy p1 inbound**说明在系统视图下应用了基于 UCL 的流量策略后，对所有上线用户的流量都会按照 UCL 规则进行分类。步骤 **8** 指定用户所属的域。[ ~ Device] **aaa**[*Device-aaa] **domain isp1**[*Device-aaa-domain-isp1] **user-group group1**[*Device-aaa-domain-isp1] **commit**[*Device-aaa-domain-isp1] **quit**文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 318HUAWEI NetEngine40E配置指南 1 QoS步骤 **9** 检查配置结果。完成上述配置后，使用 **display traffic policy** 命令可以查看流量策略、策略中定义的流分类以及与流分类相关的流行为的配置情况。<Device> **display traffic policy user-defined p1**User Defined Traffic Policy Information:Policy: p1Total: 5120 Used: 2 Free: 5118Description:Step: 1Statistic EnableShare-modeClassifier: c1 Precedence: 1Behavior: b1Committed Access Rate:CIR 15000 (Kbps), PIR 20000 (Kbps), CBS 300000 (byte), PBS 500000 (byte), ADJUST 0Conform Action: pass Yellow Action: pass Exceed Action: discard Color-aware: noClassifier: default-class Precedence: 65535Behavior: be-none完成配置后，可以通过命令 **display traffic policy statistics ucl** [ **slot** slot-id ]{ **inbound** | **outbound** } 查看 UCL 用户的流量策略统计信息。 VS 模式下，该命令仅在Admin VS 支持。<Device> **display traffic policy statistics ucl slot 1** **inbound**Traffic policy inbound: p1Slot: 1Traffic policy applied at 2022-10-19 10:15:57Statistics enabled at 2022-10-18 19:17:37Statistics last cleared: NeverRule number: 2 IPv4, 0 IPv6Current status: OK!Item               Packets           Bytes------------------------------------------------------------------Matched            20,935,529       2,009,808,208+--Passed           543,363         52,178,560+--Dropped         20,392,166       1,957,629,648Missed                 0             0Last 30 seconds rateItem                 pps            bps------------------------------------------------------------------Matched            1,007,607        773,842,816+--Passed            26,326         20,225,840+--Dropped           981,281        753,616,976Missed                 0             0**----**结束##### 配置文件Device 的配置文件#sysname Device#radius-server group rd1radius-server authentication ************* 1645 weight 0radius-server accounting ************* 1646 weight 0radius-server shared-key-cipher %^%#clY:%[]x='-RMNJus[s/VJ:3YBq3&lt;..|.{'xgbp+%^%radius-server type plus11radius-server traffic-unit kbyte#interface Virtual-Template1#interface GigabitEthernet2/0/0文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 319HUAWEI NetEngine40E配置指南 1 QoSundo shutdown#interface GigabitEthernet2/0/0.1pppoe-server bind Virtual-Template 1user-vlan 1basaccess-type layer2-subscriber default-domain authentication isp1authentication-method ppp#ip pool pool1 bas localgateway *********** *************section 0 *********** ***********00dns-server *************#acl number 6001rule 1 permit ip source user-group group1#traffic classifier c1 operator orif-match acl 6001#traffic behavior b1car cir 15000 pir 20000 cbs 300000 pbs 500000 green pass yellow pass red discard#traffic policy p1share-modestatistics enableclassifier c1 behavior b1#traffic-policy p1 inbound#user-group group1#aaaauthentication-scheme auth1accounting-scheme acct1#domain isp1authentication-scheme auth1accounting-scheme acct1radius-server group rd1ip-pool pool1user-group group1#return##### ******** 配置用户域下的优先级映射示例以用户域下的优先级映射场景为例，介绍简单流分类的配置。##### 组网需求如 图 **1-35** 所示，用户 subscriber1 通过 PPPoEoVLAN 接入网络，接入组网需求如下：       - 用户归属于 isp1 域，从 Device 的 GE2/0/0.1 接口下以 PPPoEoVLAN 方式接入，且与Device 连接的设备具有拨号功能。       - 采用 RADIUS 认证和 RADIUS 计费。       - RADIUS 服务器地址为 ************* ，认证和计费端口分别是 1645 和 1646 ，采用RADIUS**** 协议，密钥为 itellin 。       - DNS 服务器地址为 ************* 。       - isp1 域中上线用户的优先级为 3 。当 subscriber1 的 VLAN 报文从 Device 上线时，通过在 Device 上配置基于优先级的映射关系，从而实现修改 VLAN 报文的 8021p 值。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 320HUAWEI NetEngine40E配置指南 1 QoS说明本例中 Interface1 、 Interface2.1 分别代表 GE1/0/0 、 GE2/0/0.1 。图 **1-35** 配置用户域下的优先级映射组网图##### 配置思路采用如下思路配置用户域下的优先级映射：1. 在 Device 上配置 BRAS 业务，使用户能够上线。2. 在 Device 上配置用户域下的优先级映射。##### 数据准备为完成此配置举例，需要准备以下数据：       - 用户认证的域名       - 需要映射用户优先级       - 报文的服务等级和颜色##### 操作步骤步骤 **1** 配置 Device 的 BRAS 业务功能，使用户能够上线                                              详细配置过程请参见《 HUAWEI NetEngine40E 路由器 配置指南 用户接入》。步骤 **2** 配置用户域下的优先级映射# 配置上线用户的优先级。<HUAWEI> **system-view**[ ~ HUAWEI] **sysname Device**[*HUAWEI] **commit**[ ~ Device] **aaa**[ ~ Device-aaa] **domain isp1**[*Device-aaa] **commit**[ ~ Device-aaa-domain-isp1] **user-priority upstream 3**[*Device-aaa-domain-isp1] **commit**[ ~ Device-aaa-domain-isp1] **quit**[ ~ Device-aaa] **quit**# 配置基于用户优先级的映射关系。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 321HUAWEI NetEngine40E配置指南 1 QoS[ ~ Device] **diffserv domain isp1**[*Device-dsdomain-isp1] **user-priority 3 phb af4 yellow**[*Device-dsdomain-isp1] **commit**[ ~ Device-dsdomain-isp1] **quit**# 使能域下的简单流分类。[ ~ Device] **aaa**[ ~ Device-aaa] **domain isp1**[*Device-aaa-domain-isp1] **trust upstream isp1**[*Device-aaa-domain-isp1] **commit**[ ~ Device-aaa-domain-isp1] **return**完成上述配置后，通过 isp1 域上线的用户的报文优先级均为 3 ，且在 Device 内部映射的业务类型和颜色分别为 AF4 和 yellow 。步骤 **3** 检查配置结果在 Device 上执行 **display port-queue statistics interface gigabitethernet 1/0/0****outbound** 命令，由于对 isp1 域下所有上线优先级为 3 的用户使能了简单流分类，所以流量入 AF4 队列，且出接口报文携带的 8021p 值为 4 。<Device> **display port-queue statistics gigabitethernet 1/0/0 outbound**GigabitEthernet1/0/0 outbound traffic statistics:[be]Current usage percentage of queue: 0Total pass:1,003,905,621 packets,       90,351,505,260 bytesTotal discard:0 packets,             0 bytesDrop tail discard:0 packets,             0 bytesWred discard:0 packets,             0 bytesLast 30 seconds pass rate:0 pps,               0 bpsLast 30 seconds discard rate:0 pps,               0 bpsDrop tail discard rate:0 pps,               0 bpsWred discard rate:0 pps,               0 bpsbuffer size:          10 kbytesused buffer size:        0 kbytesPeak rate:0000-00-00 00:00:00             0 bps[af1]Current usage percentage of queue: 0Total pass:0 packets,             0 bytesTotal discard:0 packets,             0 bytesDrop tail discard:0 packets,             0 bytesWred discard:0 packets,             0 bytesLast 30 seconds pass rate:0 pps,               0 bpsLast 30 seconds discard rate:0 pps,               0 bpsDrop tail discard rate:0 pps,               0 bpsWred discard rate:0 pps,               0 bpsbuffer size:          10 kbytesused buffer size:        0 kbytesPeak rate:0000-00-00 00:00:00             0 bps[af2]文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 322HUAWEI NetEngine40E配置指南 1 QoSCurrent usage percentage of queue: 0Total pass:0 packets,             0 bytesTotal discard:0 packets,             0 bytesDrop tail discard:0 packets,             0 bytesWred discard:0 packets,             0 bytesLast 30 seconds pass rate:0 pps,               0 bpsLast 30 seconds discard rate:0 pps,               0 bpsDrop tail discard rate:0 pps,               0 bpsWred discard rate:0 pps,               0 bpsbuffer size:          10 kbytesused buffer size:        0 kbytesPeak rate:0000-00-00 00:00:00             0 bps[af3]Current usage percentage of queue: 0Total pass:0 packets,             0 bytesTotal discard:0 packets,             0 bytesDrop tail discard:0 packets,             0 bytesWred discard:0 packets,             0 bytesLast 30 seconds pass rate:0 pps,               0 bpsLast 30 seconds discard rate:0 pps,               0 bpsDrop tail discard rate:0 pps,               0 bpsWred discard rate:0 pps,               0 bpsbuffer size:          10 kbytesused buffer size:        0 kbytesPeak rate:0000-00-00 00:00:00             0 bps[af4]Current usage percentage of queue: 0Total pass:**1,748,941 packets,        157,404,690 bytes**Total discard:0 packets,             0 bytesDrop tail discard:0 packets,             0 bytesWred discard:0 packets,             0 bytesLast 30 seconds pass rate:**26,117 pps,           18,804,240 bps**Last 30 seconds discard rate:0 pps,               0 bpsDrop tail discard rate:0 pps,               0 bpsWred discard rate:0 pps,               0 bpsbuffer size:          10 kbytesused buffer size:        0 kbytesPeak rate:0000-00-00 00:00:00             0 bps[ef]Current usage percentage of queue: 0Total pass:0 packets,             0 bytes文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 323HUAWEI NetEngine40E配置指南 1 QoSTotal discard:0 packets,             0 bytesDrop tail discard:0 packets,             0 bytesWred discard:0 packets,             0 bytesLast 30 seconds pass rate:0 pps,               0 bpsLast 30 seconds discard rate:0 pps,               0 bpsDrop tail discard rate:0 pps,               0 bpsWred discard rate:0 pps,               0 bpsbuffer size:          10 kbytesused buffer size:        0 kbytesPeak rate:0000-00-00 00:00:00             0 bps[cs6]Current usage percentage of queue: 0Total pass:335 packets,           25,502 bytesTotal discard:0 packets,             0 bytesDrop tail discard:0 packets,             0 bytesWred discard:0 packets,             0 bytesLast 30 seconds pass rate:0 pps,               0 bpsLast 30 seconds discard rate:0 pps,               0 bpsDrop tail discard rate:0 pps,               0 bpsWred discard rate:0 pps,               0 bpsbuffer size:          10 kbytesused buffer size:        0 kbytesPeak rate:0000-00-00 00:00:00             0 bps[cs7]Current usage percentage of queue: 0Total pass:0 packets,             0 bytesTotal discard:0 packets,             0 bytesDrop tail discard:0 packets,             0 bytesWred discard:0 packets,             0 bytesLast 30 seconds pass rate:0 pps,               0 bpsLast 30 seconds discard rate:0 pps,               0 bpsDrop tail discard rate:0 pps,               0 bpsWred discard rate:0 pps,               0 bpsbuffer size:          10 kbytesused buffer size:        0 kbytesPeak rate:0000-00-00 00:00:00             0 bps**----**结束##### 配置文件Device 的配置文件文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 324HUAWEI NetEngine40E配置指南 1 QoS#sysname Device#diffserv domain defaultdiffserv domain 5p3ddiffserv domain isp1user-priority 3 phb af4 yellowdiffserv domain qinq#radius-server group rd1radius-server shared-key-cipher %^%#clY:%[]x='-RMNJus[s/VJ:3YBq3<..|.{'xgbp+%^%radius-server authentication ************* 1645 weight 0radius-server accounting ************* 1646 weight 0radius-server type plus11radius-server traffic-unit kbyte#ip pool pool1 bas localgateway ********* *************section 0 ********* *********00dns-server *************#aaaauthentication-scheme auth1accounting-scheme acct1#domain default0domain default1domain default_admindomain isp1authentication-scheme auth1accounting-scheme acct1radius-server group rd1ip-pool pool1trust upstream isp1user-priority upstream 3#interface Virtual-Template1#interface GigabitEthernet2/0/0undo shutdown#interface GigabitEthernet2/0/0.1user-vlan 1pppoe-server bind Virtual-Template 1basaccess-type layer2-subscriber default-domain authentication isp1#interface GigabitEthernet1/0/0undo shutdownip address *********** *************#return##### ******** 通过 RADIUS 下发 EDSG 业务限制带宽示例 (Traffic-policy 简化配置方式 ) 组网需求运营商 A 和运营商 B 均可为客户提供访问 Internet 服务，但所提供的带宽不同。如 图**1-36** 所示，用户 A 和用户 B 通过交换机接入 Device ，分别向运营商 A 和运营商 B 订购了访问 Internet 的业务。其中：       - 运营商 A 为用户 A 提供访问 Internet 的带宽限制为 1Mbit/s ，通过 Radius 下发流量策略 p_carrier_A 和 EDSG 业务策略 edsgA 实现。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 325HUAWEI NetEngine40E配置指南 1 QoS       - 运营商 B 为用户 B 提供访问 Internet 的带宽限制为 2Mbit/s ，通过 Radius 下发流量策略 p_carrier_B 和 EDSG 业务策略 edsgB 实现。       - 限制用户的 TCP 协议报文接入速率为 100kbits/s, 承诺突发流量尺寸为 18700 字节。图 **1-36** 通过 RADIUS 下发 EDSG 业务限制带宽示例组网图说明本例中 interface1 ， interface2 分别代表 GE1/0/1 ， GE1/0/2 。##### 配置思路采用如下的思路进行配置：1. 配置用户接入。2. 配置用户组。3. 配置 EDSG 业务组。4. 配置 ACL 规则5. 配置流分类。6. 配置流行为。7. 配置流量策略。8. 全局应用流量策略。9. 配置 EDSG 业务策略。10. 通过 Radius 属性下发流量策略、 EDSG 业务策略和用户组。11. 检查配置结果。##### 数据准备配置前，请准备好如下数据：       - 认证计费方案及名称       - RADIUS 服务器组名称、服务器地址       - 用户归属域文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 326HUAWEI NetEngine40E配置指南 1 QoS       - BAS 接口参数       - 用户组名       - EDSG 业务组名       - ACL 编号       - 流分类、流行为、流策略的名称       - EDSG 业务策略名##### 操作步骤步骤 **1** 配置用户接入。用户接入的配置方法详见配置文件，此处不再赘述。步骤 **2** 配置用户组。# 配置用户组[ ~ Device] **user-group groupA**[*Device] **user-group groupB**[ ~ Device] **commit**# 在用户上线域中指定用户组[ ~ Device] **aaa**[ ~ Device-aaa] **domain isp1**[*Device-aaa-domain-isp1] **user-group groupA**[*Device-aaa-domain-isp1] **quit**[*Device-aaa] **domain isp2**[*Device-aaa-domain-isp2] **user-group groupB**[*Device-aaa-domain-isp2] **quit**[*Device-aaa] **commit**步骤 **3** 配置 EDSG 业务组。# 使能增值业务[ ~ Device] **value-added-service enable**# 创建业务服务组[ ~ Device] **service-group s_1m**[*Device] **commit**[ ~ Device] **service-group s_2m**[*Device] **commit**步骤 **4** 定义 ACl 规则[ ~ Device] **acl number 6020**[*Device-acl-ucl-6020] **rule 5 permit tcp source-port eq 1**[*Device-acl-ucl-6020] **commit**[ ~ Device-acl-ucl-6020] **quit**[ ~ Device] **acl number 6021**[*Device-acl-ucl-6021] **rule 5 permit ip destination ip-address ************* ***********[*Device-acl-ucl-6021] **commit**[ ~ Device-acl-ucl-6021] **quit**[ ~ Device] **acl number 6022**[*Device-acl-ucl-6022] **rule 20 permit ip source ip-address ************* ***********[*Device-acl-ucl-6022] **commit**[ ~ Device-acl-ucl-6022] **quit**说明ACL 6021 和 ACL 6022 定义了匹配用户访问 Internet 的报文。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 327HUAWEI NetEngine40E配置指南 1 QoS步骤 **5** 配置流分类。[ ~ Device] **traffic classifier cin1**[*Device-classifier-cin1] **if-match acl 6020**[*Device-classifier-cin1] **commit**[ ~ Device-classifier-cin1] **quit**[ ~ Device] **traffic classifier cout1**[*Device-classifier-cout1] **if-match acl 6020**[*Device-classifier-cout1] **commit**[ ~ Device-classifier-cout1] **quit**[ ~ Device] **traffic classifier cin2**[*Device-classifier-cin2] **if-match acl 6021**[*Device-classifier-cin2] **commit**[ ~ Device-classifier-cin2] **quit**[ ~ Device] **traffic classifier cout2**[*Device-classifier-cout2] **if-match acl 6022**[*Device-classifier-cout2] **commit**[ ~ Device-classifier-cout2] **quit**步骤 **6** 配置流行为。[ ~ Device] **traffic behavior b_user-group**[*Device-behavior-b_user-group] **car cir 100 cbs 18700 green pass red discard**[*Device-behavior-b_user-group] **commit**[ ~ Device-behavior-b_user-group] **quit**[ ~ Device] **traffic behavior b_s_1m**[*Device-behavior-b_s_1m] **commit**[ ~ Device-behavior-b_s_1m] **quit**[ ~ Device] **traffic behavior b_s_2m**[*Device-behavior-b_s_2m] **commit**[ ~ Device-behavior-b_s_2m] **quit**步骤 **7** 定义流量策略，将流分类与流行为关联。[ ~ Device] **traffic policy p_carrier_A**[*Device-trafficpolicy-p_carrier_A] **undo share-mode**[*Device-trafficpolicy-p_carrier_A] **statistics enable**[*Device-trafficpolicy-p_carrier_A] **classifier cin1 source user-group groupA behavior b_user-group****precedence 1**[*Device-trafficpolicy-p_carrier_A] **classifier cout1 destination user-group groupA behavior b_user-group****precedence 2**[*Device-trafficpolicy-p_carrier_A] **classifier cin2 source service-group s_1m behavior b_s_1m precedence****3**[*Device-trafficpolicy-p_carrier_A] **classifier cout2 destination service-group s_1m behavior b_s_1m****precedence 4**[*Device-trafficpolicy-p_carrier_A] **commit**[ ~ Device-trafficpolicy-p_carrier_A] **quit**[ ~ Device] **traffic policy p_carrier_B**[*Device-trafficpolicy-p_carrier_B] **undo share-mode**[*Device-trafficpolicy-p_carrier_B] **statistics enable**[*Device-trafficpolicy-p_carrier_B] **classifier cin1 source user-group groupb behavior b_user-group****precedence 1**[*Device-trafficpolicy-p_carrier_B] **classifier cout1 destination user-group groupb behavior b_user-group****precedence 2**[*Device-trafficpolicy-p_carrier_B] **classifier cin2 source service-group s_2m behavior b_s_2m precedence****3**[*Device-trafficpolicy-p_carrier_B] **classifier cout2 destination service-group s_2m behavior b_s_2m****precedence 4**[*Device-trafficpolicy-p_carrier_B] **commit**[ ~ Device-trafficpolicy-p_carrier_B] **quit**步骤 **8** 全局应用流量策略。[ ~ Device] **traffic-policy p_carrier_A inbound**[*Device] **traffic-policy p_carrier_A outbound**[*Device] **traffic-policy p_carrier_B inbound**[*Device] **traffic-policy p_carrier_B outbound**[*Device] **commit**步骤 **9** 配置 EDSG 业务策略[ ~ Device] **radius-attribute hw-policy-name support-type edsg**[*Device] **commit**文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 328HUAWEI NetEngine40E配置指南 1 QoS[ ~ Device] **service-policy name service_edsgA edsg**[*Device-service-policy-service_edsgA] **accounting-scheme acct1**[*Device-service-policy-service_edsgA] **radius-server group rd1**[*Device-service-policy-service_edsgA] **service-group s_1m**[*Device-service-policy-service_edsgA] **rate-limit cir 1000 inbound**[*Device-service-policy-service_edsgA] **rate-limit cir 1000 outbound**[*Device-service-policy-service_edsgA] **commit**[ ~ Device-service-policy-service_edsgA] **quit**[ ~ Device] **service-policy name service_edsgB edsg**[*Device-service-policy-service_edsgB] **accounting-scheme acct1**[*Device-service-policy-service_edsgB] **radius-server group rd1**[*Device-service-policy-service_edsgB] **service-group s_2m**[*Device-service-policy-service_edsgB] **rate-limit cir 2000 inbound**[*Device-service-policy-service_edsgB] **rate-limit cir 2000 outbound**[*Device-service-policy-service_edsgB] **commit**[ ~ Device-service-policy-service_edsgB] **quit**步骤 **10** 通过 Radius 属性下发流量策略、 EDSG 业务策略和用户组。# 在 AAA 服务器上为用户 A 下发 RADIUS 属性 Filter-Id 、 HW-Policy-Name 和 HWAVpair ：       - <Filter-Id> ：通过该属性下发用户组 groupA 。       - HW-Policy-Name ：通过该属性下发 EDSG 业务策略 service_edsgA 。       - HW-AVpair 属性中的 <subscriber:traffic-policy> ：通过该属性下发流量策略p_carrier_A 。# 在 AAA 服务器上为用户 B 下发 RADIUS 属性 Filter-Id 、 HW-Policy-Name 和 HWAVpair ：       - <Filter-Id> ：通过该属性下发用户组 groupB 。       - HW-Policy-Name ：通过该属性下发 EDSG 业务策略 service_edsgB 。       - HW-AVpair 属性中的 <subscriber:traffic-policy> ：通过该属性下发流量策略p_carrier_B 。步骤 **11** 用户上线后，检查配置结果# 执行如下命令查看上线用户 A 的信息，可看到 Radius 已为其下发流量策略 p_carrier_A（见加粗内容）。[ ~ Device] display access-user user-id 2------------------------------------------------------------------User access index       : 2State             : UsedUser name           : user#Domain name          : isp1User backup state       : NoRUI user state        : User access interface     : GigabitEthernet1/0/1.1User access PeVlan/CeVlan   : 1/User access slot       : 1User MAC           : 00e0-fc12-3456User IP address        : *********95User IP netmask        : ***************User gateway address     : *********User Authen IP Type      : ipv4/-/User Basic IP Type      : -/-/User MSIDSN name       : EAP user           : NoMD5 end            : NoMTU              : 1480MRU              : 1480Vpn-Instance         : User access type       : PPPoE文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 329HUAWEI NetEngine40E配置指南 1 QoSUser authentication type   : PPP authenticationRADIUS-server-template    : rd1Server-template of second acct: Agent-Circuit-Id       : Agent-Remote-Id        : Access-line-id Information(pppoe+): Current authen method     : RADIUS authenticationAuthen result         : SuccessCurrent author method     : IdleAuthor result         : SuccessAction flag          : IdleAuthen state         : AuthedAuthor state         : IdleConfigured accounting method : RADIUS accountingQuota-out           : OfflineCurrent accounting method   : RADIUS accountingRealtime-accounting-switch      : CloseRealtime-accounting-interval(sec)   : Realtime-accounting-send-update    : NoRealtime-accounting-traffic-update  : NoAccess start time       : 2016-09-24 16:35:44Accounting start time     : Online time (h:min:sec)    : 00:00:29Accounting state       : IdleAccounting session ID     : HUAWEI030000000000004dce2aAAAAAAjbvb Idle-cut direction      : BothIdle-cut-data (time,rate,idle): 0 sec, 60 kbyte/min, 0 min 0 secIpv4 Realtime speed      : 0 kbyte/minIpv4 Realtime speed inbound  : 0 kbyte/minIpv4 Realtime speed outbound : 0 kbyte/minLink bandwidth auto adapt   : DisableUpPriority          : UnchangeableDownPriority         : Unchangeable**TrafficPolicy         : p_carrier_A(Radius)**Multicast-profile       : Multicast-profile-ipv6    : Max Multicast List Number   : 4IGMP enable          : Yes**User-Group          : groupA**Next-hop           : Policy-route-IPV6-address   : If flow info contain l2-head : YesFlow-Statistic-Up       : YesFlow-Statistic-Down      : YesUp packets number(high,low)  : (0,0)Up bytes number(high,low)   : (0,0)Down packets number(high,low) : (0,0)Down bytes number(high,low)  : (0,0)IPV6 Up packets number(high,low)   : (0,0)IPV6 Up bytes number(high,low)    : (0,0)IPV6 Down packets number(high,low)  : (0,0)IPV6 Down bytes number(high,low)   : (0,0)Service-type         : ------------------------------------------------------------------# 执行如下命令指定流策略名 p_carrier_A 查询其统计信息。[ ~ Device] display traffic policy name p_carrier_A statistics ucl inboundTraffic policy inbound: p_carrier_ASlot: 1Traffic policy applied at 2016-09-24 16:25:10Statistics enabled at 2016-09-24 16:21:35Statistics last cleared: NeverRule number: 4 IPv4, 0 IPv6Current status: OK!Item               Packets           Bytes------------------------------------------------------------------Matched              1,000          100,000+--Passed             500           50,000+--Dropped             500           50,000文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 330HUAWEI NetEngine40E配置指南 1 QoSMissed                500           50,000Last 30 seconds rateItem                 pps            bps------------------------------------------------------------------Matched              1,000          100,000+--Passed             500           50,000+--Dropped             500           50,000Missed                500           50,000**----**结束##### 配置文件Device 的配置文件#sysname Device#value-added-service enable#service-group s_1mservice-group s_2mser#user-group groupAuser-group groupB#radius-attribute hw-policy-name support-type edsg#radius-server group rd1radius-server authentication ************* 1813 weight 0radius-server accounting ************* 1812 weight 0#ip pool pool1 bas localgateway ********* *************section 0 ********* *********00#acl number 6020rule 5 permit tcp source-port eq 1#acl number 6021rule 5 permit ip destination ip-address ************* *********#acl number 6022rule 20 permit ip source ip-address ************* *********#traffic classifier cin1 operator orif-match acl 6020#traffic classifier cin2 operator orif-match acl 6021#traffic classifier cout1 operator orif-match acl 6020#traffic classifier cout2 operator orif-match acl 6022#traffic behavior b_s_1m#traffic behavior b_s_2m#traffic behavior b_user-groupcar cir 100 cbs 18700 green pass red discard#traffic policy p_carrier_Aundo share-modestatistics enable文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 331HUAWEI NetEngine40E配置指南 1 QoSclassifier cin1 source user-group groupA behavior b_user-group precedence 1classifier cout1 destination user-group groupA behavior b_user-group precedence 2classifier cin2 source service-group s_1m behavior b_s_1m precedence 3classifier cout2 destination service-group s_1m behavior b_s_1m precedence 4#traffic policy p_carrier_Bundo share-modestatistics enableclassifier cin1 source user-group groupB behavior b_user-group precedence 1classifier cout1 destination user-group groupB behavior b_user-group precedence 2classifier cin2 source service-group s_2m behavior b_s_2m precedence 3classifier cout2 destination service-group s_2m behavior b_s_2m precedence 4#aaa#authentication-scheme auth1#accounting-scheme acct1#domain isp1authentication-scheme auth1accounting-scheme acct1radius-server group rd1ip-pool pool1user-group groupA#domain isp2authentication-scheme auth1accounting-scheme acct1radius-server group rd1ip-pool pool1user-group groupB#service-policy name service_edsgA edsgaccounting-scheme acct1radius-server group rd1service-group s_1mrate-limit cir 1000 inboundrate-limit cir 1000 outbound#service-policy name service_edsgB edsgaccounting-scheme acct1radius-server group rd1service-group s_2mrate-limit cir 2000 inboundrate-limit cir 2000 outbound#interface GigabitEthernet1/0/1.1user-vlan 1bas#access-type layer2-subscriber default-domain authentication isp1##interface GigabitEthernet1/0/1.2user-vlan 2bas#access-type layer2-subscriber default-domain authentication isp2##interface GigabitEthernet1/0/2undo shutdownip address ************* *************dcn#traffic-policy p_carrier_A inboundtraffic-policy p_carrier_B inboundtraffic-policy p_carrier_A outbound文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 332HUAWEI NetEngine40E配置指南 1 QoStraffic-policy p_carrier_B outbound#return### 1.11 组播 QoS 配置组播 QoS 可以保证用户流畅的观看电视节目，做到精细化区分和管理用户带宽。##### 背景信息说明VS 模式下，该特性仅在 Admin VS 支持。#### 1.11.1 简介介绍组播 QoS 的概念和原理。##### ******** 组播 QoS 概述应用组播 QoS ，可以保证组播节目的正常播放，防止组播流量突发。##### 组播虚拟调度随着 BTV （ Broadband TV ）业务的开展，电视视频业务的质量保证成为关键，直接关系到用户的体验和感受。因此，如何保证用户能够观看流畅的电视节目，如何做到精细化区分和管理用户带宽，成为运营商急需解决的事情。图 **1-37** 组播虚拟调度产生背景示意图如 图 **1-37** 所示，一个家庭通过机顶盒 STB （ Set Top Box ）观看组播节目（组播数据），通过 PC 浏览 Internet （单播数据）。假设这个家庭可用的最大带宽为 3M ，用户上网占用了全部的 3M 带宽，之后用户又通过 STB 点播了 2M 带宽的组播节目。此时，由于组播数据和单播数据共需要 5M 的带宽，接入网络将造成数据拥塞，部分报文被丢弃，组播节目质量无法得到保证。组播虚拟调度技术正是为了解决上述问题而提出的。组播虚拟调度是用户级的调度，在保证用户总带宽有限的情况下，实现用户单播带宽与组播数据的联动，保证 BTV 业务的质量。解决 图 **1-37** 问题，路由器配置组播虚拟调度功能后，当用户收到的组播流量与单播流量之和大于分配的带宽时，将用户的单播流量带宽减少至 1M ，从而满足 2M文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 333HUAWEI NetEngine40E配置指南 1 QoS带宽的组播流量，保证了组播节目的正常播放。另外开展 IPTV 组播业务后，如果组播流量比较大、组播源比较繁忙时，组播源容易发生严重的抖动。 NE40E 可以对组播流量进行整形，保证将组播源抖动限定在可接受范围内。用户使用组播业务时，如果在NE40E 配置了组播整形， NE40E 可以对用户的组播流量进行限制，防止组播流量突发，使组播报文以比较均匀的速度向外发送，将组播源的抖动限定在可接受的范围内。#### 1.11.2 组播 QoS 配置注意事项##### 特性限制无#### 1.11.3 配置组播虚拟调度组播虚拟调度是用户级的调度，保证用户总带宽有限的情况下，实现用户单播带宽与组播带宽的联动， 保证 BTV 业务的质量。##### 应用环境在用户总带宽有限的情况下，若要通过调整单播节目的带宽来保证组播节目的正常使用，可以配置组播虚拟调度功能。如果需要在 MPLS 接入组播业务组网中实现组播虚拟调度，则要求下层设备支持 IGMPFORK 。同时，对于单边缘组网则需要配置设备进行组播数据转发。##### 前置任务在配置组播虚拟调度之前，需完成以下任务：       - 用户正常接入网络       - 通过设备或 Radius 服务器为用户分配单播和组播总带宽       - 用户正常点播组播节目##### ******** 配置组播节目列表组播节目列表是一个或若干个组播地址，用来定义 IPTV 的一个或若干个频道（ Channel ）或者节目（ Program ）。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **aaa** ，进入 AAA 视图。步骤 **3** 执行命令 **multicast-list** list-name [ **index** list-index ] [ **source-address** sourceaddress [ source-mask-len | source-mask ] ] **group-address** group-address[ group-mask | group-mask-length ] [ **vpn-instance** vpn-instance-name ] ，配置组播节目列表。可以通过参数 group-mask 或 group-mask-length 同时配置多个组播节目。如果未配置group-mask 或 group-mask-length 参数，系统默认 group-mask 为 *************** ，即 group-mask-length 值为 32 ，表示只配置了一个组播节目。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 334HUAWEI NetEngine40E配置指南 1 QoS步骤 **4** 执行命令 **commit** ，提交配置。**----**结束##### ******** 配置组播节目带宽配置组播节目静态带宽，为指定的组播节目分配大小为固定带宽；配置组播节目动态带宽，对超过保证带宽的单播带宽进行调整，保证组播带宽。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **multicastbandwidth** ，进入组播带宽视图。步骤 **3** 执行命令 **multicast-list** { **name** list-name | **index** list-index | **start-list-index** startlist-index **end-list-index** end-list-index } { **bandwidth** bandwidth-value | **detect-****interval** interval **threshold** threshold } ，配置组播节目带宽。       - **bandwidth** bandwidth-value ：配置组播节目静态带宽。路由器始终为相应的组播节目分配大小为 bandwidth-value 的带宽。       - **detect-interval** interval **threshold** threshold ：配置组播节目动态带宽。路由器每隔 interval 秒，统计一次节目带宽。带宽变化阈值不超过 threshold 就认为节目带宽正常，如果超过则会在保证用户总带宽不变的情况下对单播带宽进行调整。步骤 **4** 执行命令 **commit** ，提交配置。**----**结束##### 1.11.3.3 配置不复制转发组播数据在双边缘组网中，其中一边的设备仅担任组播虚拟调度的任务，对于接收到的组播数据仅用于带宽统计，不进行数据复制转发，因此需要配置 NE40E 不进行组播数据复制转发。组播流量转发的任务由另外一边的设备来进行。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **aaa** ，进入 AAA 视图。步骤 **3** 执行命令 **domain** domain-name ，创建域并进入域视图。步骤 **4** 执行命令 **multicast without-forwarding** ，配置 NE40E 不进行组播数据复制转发。步骤 **5** 执行命令 **commit** ，提交配置。**----**结束##### 1.11.3.4 配置单播保留带宽对超过保证带宽的单播带宽进行调整时，配置单播最小保留带宽。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 335HUAWEI NetEngine40E配置指南 1 QoS步骤 **2** 执行命令 **aaa** ，进入 AAA 视图。步骤 **3** 执行命令 **domain** domain-name ，创建域并进入域视图。步骤 **4** 执行命令 **multicast unicast-reservable-bandwidth** **cir** cir-value [ **pir** pir-value ][ **coexist-with** **copy-by-session** ] ，配置组播虚拟调度时单播最小保留带宽，使能组播虚拟调度功能。步骤 **5** 执行命令 **commit** ，提交配置。**----**结束##### 1.11.3.5 （可选）配置用户同时观看最大节目数组播节目列表对应一个或若干个组播地址，即多个组播节目，可通过在设备上配置本任务，定义用户可以同时点播的最大组播节目数。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **aaa** ，进入 AAA 视图。步骤 **3** 执行命令 **domain** domain-name ，创建域并进入域视图。步骤 **4** 执行命令 **multicast max-list** max-num ，配置用户可以同时观看的最大节目数。步骤 **5** 执行命令 **commit** ，提交配置。**----**结束##### 1.11.3.6 （可选）配置跨 VPN 的组播虚拟调度介绍如何配置跨 VPN 的组播虚拟调度。##### 背景信息缺省情况下，组播虚拟调度技术可以对同一个 VPN 内的单播和组播流量进行调度。如果需要调度的单播和组播流量不在一个 VPN 上，可以通过配置该命令，绑定组播用户VPN ，实现跨 VPN 的组播虚拟调度。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **aaa** ，进入 AAA 视图。步骤 **3** 执行命令 **domain** domain-name ，进入 AAA 域视图。步骤 **4** 执行命令 **multicast virtual-scheduling-vpn** vpn-name ，绑定组播用户 VPN ，实现跨VPN 的组播虚拟调度。步骤 **5** 执行命令 **commit** ，提交配置。**----**结束文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 336HUAWEI NetEngine40E配置指南 1 QoS##### 1.11.3.7 （可选）调整家庭用户的单播 CAR 带宽介绍如何使能组播虚拟调度调整家庭用户的单播 CAR 带宽。##### 背景信息在申请 car 资源的家庭用户总带宽有限的情况下，若要通过调整单播节目的带宽来保证组播节目的正常使用，可以使能组播虚拟调度调整家庭用户的单播 CAR 带宽。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **multicast-virtual-scheduling family-user unicast car-bandwidth-****adjust enable** ，使能组播虚拟调度调整家庭用户的单播 CAR 带宽。步骤 **3** 执行命令 **commit** ，提交配置。**----**结束##### ******** 检查配置结果组播虚拟调度配置成功后，可以查看组播节目带宽和组播节目列表等内容。##### 前提条件组播虚拟调度已配置。##### 操作步骤步骤 **1** 执行 **display multicast bandwidth** [ **name** list-name | [ **vpn-instance** vpninstance-name ] [ **source** source-address ] **group** group-address ] 命令查看 IPv4 组播节目带宽。**----**结束#### 1.11.4 配置组播整形组播流量整形可以实现在组播流量比较大、组播源比较繁忙时，保证将组播源抖动限定在可接受范围内。##### 应用环境开展 IPTV 组播业务后，如果组播流量比较大、组播源比较繁忙时，组播源容易发生严重的抖动。通过在路由器上配置组播流量整形，保证将组播源抖动限定在可接受范围内。##### 前置任务在配置组播整形之前，需要完成以下任务：       - 配置完成组播业务。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 337HUAWEI NetEngine40E配置指南 1 QoS##### ******** 配置组播节目列表组播节目列表是一个或若干个组播地址，用来定义 IPTV 的一个或若干个频道（ Channel ）或者节目（ Program ）。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **aaa** ，进入 AAA 视图。步骤 **3** 执行命令 **multicast-list** list-name [ **index** list-index ] [ **source-address** sourceaddress [ source-mask-len | source-mask ] ] **group-address** group-address[ group-mask-length | group-mask ] [ **vpn-instance** vpn-instance-name ] ，配置组播节目列表。其中 **group-address** group-address 必须是组播地址。步骤 **4** 执行命令 **commit** ，提交配置。**----**结束##### ******** 使能组播整形组播源在比较繁忙时，容易发生严重的抖动，进行组播流量整形，可以保证将组播源抖动限定在可接受范围内。##### 背景信息组播整形功能可以在全局下使能，也可以在接口下使能。全局下使能组播整形时，则路由器上所有接口都使能了组播整形功能。##### 操作步骤       - 在全局使能组播整形功能a. 在执行命令 **system-view** ，进入系统视图。b. 执行命令 **multicast shaping enable** ，使能全局的组播整形功能。c. 执行命令 **commit** ，提交配置。       - 在接口下使能组播整形功能a. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **interface** interface-type interface-number ，进入接口视图。c. 执行命令 **multicast shaping enable** ，使能接口的组播整形功能。d. 执行命令 **commit** ，提交配置。**----**结束##### 1.11.4.3 配置组播列表的带宽对指定组播节目组播进行整形，设置保证带宽和峰值带宽。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 338HUAWEI NetEngine40E配置指南 1 QoS##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **multicast shaping** ，进入组播整形模板视图。步骤 **3** 执行命令 **multicast-list** { **name** list-name | **list-index** start-index [ end-index ] } **cir**cir-value [ **pir** pir-value ] [ **queue-length** queue-length ] ，配置组播列表的带宽。步骤 **4** 执行命令 **commit** ，提交配置。**----**结束##### 1.11.4.4 配置单板的组播流量限制为避免单板组播流量突发导致业务丢包，可以对单板的组播流量进行限制。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **slot** slot-id ，进入槽位视图。步骤 **3** 执行命令 **multicast shaping** shaping-value ，限制单板的组播流量。步骤 **4** 执行命令 **commit** ，提交配置。**----**结束##### 1.11.4.5 检查配置结果组播整形配置成功后，可以查看指定 ID 的上线用户信息和占用的带宽、 QoS 模板的应用信息和等内容。##### 前提条件组播整形已配置。##### 操作步骤步骤 **1** 使用 **display multicast-list** [ group-list-name ] 命令查看组播整形列表配置信息。**----**结束#### 1.11.5 配置举例从具体应用场景、配置命令等方面对组播 QoS 的应用进行了详细的描述。##### 背景信息说明组播虚拟调度只需在现有网络的基础上，在路由器上增加相关配置即可，无需对组网和路由器其他配置进行变动。因此，以下举例仅列出了组播虚拟调度的相关配置，对其他配置不再介绍。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 339HUAWEI NetEngine40E配置指南 1 QoS##### 1.11.5.1 单边缘组播虚拟调度配置示例以单台设备同时承担用户接入和组播流量转发的任务的场景为例，介绍如何配置单边缘组播虚拟调度示例。##### 组网需求说明          - 本示例是普通的单边缘组网，如果是在 MPLS 接入组播业务中的组网，则必须通过命令**multicast without-forwarding** 配置为不进行组播数据复制。          - 另外， MPLS 接入组播业务的组网中， IGMP 、 PIM 则只能在 VE 接口配置。具体请参见《 HUAWEI NetEngine40E 路由器 配置指南 -IP 组播》的“ IGMP 配置”和“ PIM-SM（ IPv4 ）配置”。如 图 **1-38** 所示，用户以家庭为单位，通过路由器的 GE1/0/0.100 接口以 IPoEoVLAN 方式接入网络。路由器通过 Radius 服务器对用户进行认证，并为用户分配总带宽。用户总带宽默认为 2M 。要求 DSLAM 支持 IGMP Snooping 。路由器同时承担用户接入和组播流量转发的任务。要求在现有网络的基础上，配置路由器对用户进行组播虚拟调度，保证组播节目的正常收看。在用户总带宽不变的情况下，如果用户此时点播了组播组为 *********/24 的 CCTV 节目或者 *********/24 的 JSTV 节目，则路由器根据组播带宽调整用户单播带宽。用户单播带宽最小值为 1M 。说明本例中 subinterface1 和 interface2 分别代表 GE 1/0/0.100 和 GE 2/0/0 。图 **1-38** 单边缘组播虚拟调度组网图##### 配置思路采用如下的思路配置单边缘组播虚拟调度：1. 配置接口 IP 地址和单播路由协议，实现 IP 连通性2. 配置用户通过 IPoEoVLAN 方式接入网络，正常上网3. 配置 IGMP 和 PIM-SM 协议4. 在用户上线的主接口下配置组播 VLAN ，路由器通过组播 VLAN 下发组播数据5. 配置组播节目以及节目动态带宽6. 配置单播保留带宽，启动组播虚拟调度功能文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 340HUAWEI NetEngine40E配置指南 1 QoS##### 数据准备为完成此配置举例，需准备如下的数据：       - 用户接入网络的接口号、用户侧 VLAN 号以及用户归属域       - 组播 VLAN 号       - 组播节目源地址以及带宽       - 单播保留带宽值##### 操作步骤步骤 **1** 配置接口 IP 地址和单播路由协议请参见《 HUAWEI NetEngine40E 路由器 配置指南 -IP 业务》和《 HUAWEINetEngine40E 路由器 配置指南 -IP 路由》。步骤 **2** 配置用户接入网络配置用户通过路由器的 GE1/0/0.100 接口以 IPoEoVLAN 方式接入网络，用户 VLAN 为1~100 ，用户归属域为名称为“ huawei ”。配置路由器通过 Radius 服务器对用户进行                                               认证。具体配置请参见《 HUAWEI NetEngine40E 路由器 配置指南 用户接入》中“配置 BRAS 接入”的相关信息。步骤 **3** 配置用户总带宽默认值<HUAWEI> **system-view**[ ~ HUAWEI] **qos-profile huawei**[*HUAWEI-qos-profile-huawei] **user-queue cir 2000 pir 2000**[*HUAWEI-qos-profile-huawei] **quit**[*HUAWEI] **interface GigabitEthernet 1/0/0.100**[*HUAWEI-GigabitEthernet1/0/0.100] **commit**[ ~ HUAWEI-GigabitEthernet1/0/0.100] **user-vlan 1 100**[*HUAWEI-GigabitEthernet1/0/0.100-vlan-1-100] **bas**[ ~ HUAWEI-GigabitEthernet1/0/0.100-bas] **access-type layer2-subscriber**[*HUAWEI-GigabitEthernet1/0/0.100-bas] **authentication-method bind**[*HUAWEI-GigabitEthernet1/0/0.100-bas] **qos-profile huawei outbound**[*HUAWEI-GigabitEthernet1/0/0.100-bas] **commit**[ ~ HUAWEI-GigabitEthernet1/0/0.100-bas] **quit**[ ~ HUAWEI-GigabitEthernet1/0/0.100] **quit**步骤 **4** 使能组播路由功能，配置 IGMP 和 PIM 协议说明这里仅列出了 Device 上 PIM 的相关配置，有关 Internet 的组播路由和其他网络设备的 PIM 配置，请参见《 HUAWEI NetEngine40E 路由器 配置指南 -IP 组播》中“ MBGP 配置”和“ PIM-SM(IPv4) 配置”。# 在路由器上使能组播路由功能。[ ~ HUAWEI] **multicast routing-enable**# 在用户侧接口 GE1/0/0.100 上使能 IGMP 协议。[ ~ HUAWEI] **interface gigabitethernet 1/0/0.100**[ ~ HUAWEI-GigabitEthernet1/0/0.100] **igmp enable**[*HUAWEI-GigabitEthernet1/0/0.100] **commit**[ ~ HUAWEI-GigabitEthernet1/0/0.100] **quit**# 在网络侧接口 GE2/0/0 上使能 PIM 协议。[ ~ HUAWEI] **interface gigabitethernet 2/0/0**[ ~ HUAWEI-GigabitEthernet2/0/0] **pim sm**文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 341HUAWEI NetEngine40E配置指南 1 QoS[*HUAWEI-GigabitEthernet2/0/0] **undo shutdown**[*HUAWEI-GigabitEthernet2/0/0] **commit**[ ~ HUAWEI-GigabitEthernet2/0/0] **quit**步骤 **5** 配置组播用户聚合[ ~ HUAWEI] **interface gigabitethernet 1/0/0**[ ~ HUAWEI-GigabitEthernet1/0/0] **igmp enable**[*HUAWEI-GigabitEthernet1/0/0] **multicast user-aggregation vlan 1**[*HUAWEI-GigabitEthernet1/0/0] **undo shutdown**[*HUAWEI-GigabitEthernet1/0/0] **commit**[ ~ HUAWEI-GigabitEthernet1/0/0] **quit**步骤 **6** 配置组播节目列表及带宽[ ~ HUAWEI] **aaa**[*HUAWEI-aaa] **multicast-list cctv group-address ********* 24**[*HUAWEI-aaa] **multicast-list jstv group-address ********* 24**[*HUAWEI-aaa] **quit**[*HUAWEI] **multicastbandwidth**[*HUAWEI-mbandwidth] **multicast-list name cctv detect-interval 15 threshold 15**[*HUAWEI-mbandwidth] **multicast-list name jstv detect-interval 15 threshold 20**[*HUAWEI-mbandwidth] **commit**[ ~ HUAWEI-mbandwidth] **quit**步骤 **7** 配置单播保留带宽，启动组播虚拟调度功能。[ ~ HUAWEI] **aaa**[*HUAWEI-aaa] **domain huawei**[*HUAWEI-aaa-domain-huawei] **multicast unicast-reservable-bandwidth cir 1024**[*HUAWEI-aaa-domain-huawei] **quit**[*HUAWEI-aaa] **commit**[ ~ HUAWEI-aaa] **quit****----**结束##### 配置文件       - 路由器配置文件：#multicast routing-enable#qos-profile huaweiuser-queue cir 2000 pir 2000#interface GigabitEthernet1/0/0undo shutdownigmp enablemulticast user-aggregation vlan 1#interface GigabitEthernet1/0/0.100igmp enableuser-vlan 1 100basaccess-type layer2-subscriberauthentication-method bindqos-profile huawei outbound identifier none#interface GigabitEthernet2/0/0undo shutdownip address ******** *************pim sm#aaamulticast-list cctv group-address ********* 24multicast-list jstv group-address ********* 24domain huaweimulticast unicast-reservable-bandwidth cir 1024#multicastbandwidth文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 342HUAWEI NetEngine40E配置指南 1 QoSmulticast-list name cctv detect-interval 15 threshold 15multicast-list name jstv detect-interval 15 threshold 20#return##### ******** 双边缘组播虚拟调度配置示例以两台设备分别承担用户接入和组播流量转发的任务的场景为例，介绍如何配置双边缘组播虚拟调度示例。##### 组网需求如 图 **1-39** 所示，用户以家庭为单位，通过 DeviceA 的 GE1/0/0.100 接口以 IPoEoVLAN 方式接入网络。 DeviceA 通过 Radius 服务器对用户进行认证，为用户分配总带宽。用户总带宽默认为 2M 。要求 DSLAM 支持 IGMP Snooping 。DeviceA 承担用户接入的任务， DeviceB 设备进行组播流量转发。要求在现有网络的基础上，配置 DeviceA 对用户进行组播虚拟调度，保证组播节目的正常收看。在用户总带宽不变的情况下，如果用户此时点播了组播组为 *********/24 的 CCTV 节目或者*********/24 的 JSTV 节目，则 DeviceA 根据组播带宽调整用户单播带宽。用户单播带宽最小值为 1M 。说明本例中 subinterface 1 和 interface 2 分别代表 GE 1/0/0.100 和 GE 2/0/0 。图 **1-39** 双边缘组播虚拟调度组网图##### 配置思路采用如下的思路配置双边缘组播虚拟调度：1. 配置接口 IP 地址和单播路由协议，实现 IP 连通性2. 配置用户通过 IPoEoVLAN 方式接入网络，正常上网3. 配置 IGMP 和 PIM-SM 协议4. 在用户上线的主接口下配置组播 VLAN ， DeviceA 通过组播 VLAN 下发组播数据5. 配置组播节目以及节目动态带宽文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 343HUAWEI NetEngine40E配置指南 1 QoS6. 配置单播保留带宽，启动组播虚拟调度功能##### 数据准备为完成此配置举例，需准备如下的数据：       - 用户接入网络的接口号、用户侧 VLAN 号以及用户归属域       - 组播 VLAN 号       - 组播节目源地址以及带宽       - 单播保留带宽值##### 操作步骤步骤 **1** 配置接口 IP 地址和单播路由协议请参见《 HUAWEI NetEngine40E 路由器 配置指南 -IP 业务》和《 HUAWEINetEngine40E 路由器 配置指南 -IP 路由》。步骤 **2** 配置用户接入网络配置用户通过 DeviceA 的 GE1/0/0.100 接口以 IPoEoVLAN 方式接入网络，用户 VLAN 为1~100 ，用户归属域为名称为“ huawei ”。配置 DeviceA 通过 Radius 服务器对用户进行                                               认证。具体配置请参见《 HUAWEI NetEngine40E 路由器 配置指南 用户接入》中“配置 BRAS 接入”的相关信息。步骤 **3** 配置用户总带宽默认值<HUAWEI> **system-view**[ ~ HUAWEI] **qos-profile huawei**[*HUAWEI-qos-profile-huawei] **user-queue cir 2000 pir 2000**[*HUAWEI-qos-profile-huawei] **quit**[*HUAWEI] **interface GigabitEthernet 1/0/0.100**[*HUAWEI-GigabitEthernet1/0/0.100] **commit**[ ~ HUAWEI-GigabitEthernet1/0/0.100] **user-vlan 1 100**[*HUAWEI-GigabitEthernet1/0/0.100-vlan-1-100] **bas**[ ~ HUAWEI-GigabitEthernet1/0/0.100-bas] **access-type layer2-subscriber**[*HUAWEI-GigabitEthernet1/0/0.100-bas] **authentication-method bind**[*HUAWEI-GigabitEthernet1/0/0.100-bas] **qos-profile huawei outbound**[*HUAWEI-GigabitEthernet1/0/0.100-bas] **commit**[ ~ HUAWEI-GigabitEthernet1/0/0.100-bas] **quit**[ ~ HUAWEI-GigabitEthernet1/0/0.100] **quit**步骤 **4** 使能组播路由功能，配置 IGMP 和 PIM 协议说明这里仅列出了 DeviceA 上 PIM 的相关配置，有关 Internet 的组播路由和其他网络设备的 PIM 配置，请参见《 HUAWEI NetEngine40E 路由器 配置指南 -IP 组播》。# 在 DeviceA 上使能组播路由功能。[ ~ DeviceA] **multicast routing-enable**# 在用户侧接口 GE1/0/0.100 上使能 IGMP 协议。[ ~ DeviceA] **interface gigabitethernet 1/0/0.100**[ ~ DeviceA-GigabitEthernet1/0/0.100] **igmp enable**[*DeviceA-GigabitEthernet1/0/0.100] **commit**[ ~ DeviceA-GigabitEthernet1/0/0.100] **quit**# 在网络侧接口 GE2/0/0 上使能 PIM 协议。[ ~ DeviceA] **interface gigabitethernet 2/0/0**文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 344HUAWEI NetEngine40E配置指南 1 QoS[ ~ DeviceA-GigabitEthernet2/0/0] **pim sm**[*DeviceA-GigabitEthernet2/0/0] **undo shutdown**[*DeviceA-GigabitEthernet2/0/0] **commit**[ ~ DeviceA-GigabitEthernet2/0/0] **quit**步骤 **5** 配置组播用户聚合[ ~ DeviceA] **interface gigabitethernet 1/0/0**[ ~ DeviceA-GigabitEthernet1/0/0] **igmp enable**[*DeviceA-GigabitEthernet1/0/0] **multicast user-aggregation vlan 1**[*DeviceA-GigabitEthernet1/0/0] **undo shutdown**[*DeviceA-GigabitEthernet1/0/0] **commit**[ ~ DeviceA-GigabitEthernet1/0/0] **quit**步骤 **6** 配置组播节目列表及带宽[ ~ DeviceA] **aaa**[*DeviceA-aaa] **multicast-list cctv group-address ********* 24**[*DeviceA-aaa] **multicast-list jstv group-address ********* 24**[*DeviceA-aaa] **quit**[*DeviceA] **multicastbandwidth**[*DeviceA-mbandwidth] **multicast-list name cctv detect-interval 15 threshold 15**[*DeviceA-mbandwidth] **multicast-list name jstv detect-interval 15 threshold 20**[*DeviceA-mbandwidth] **commit**[ ~ DeviceA-mbandwidth] **quit**步骤 **7** 配置单播保留带宽，启动组播虚拟调度功能[ ~ DeviceA] **aaa**[*DeviceA-aaa] **domain huawei**[*DeviceA-aaa-domain-huawei] **multicast unicast-reservable-bandwidth cir 1024**[*DeviceA-aaa-domain-huawei] **commit**[ ~ DeviceA-aaa-domain-huawei] **quit**[ ~ DeviceA-aaa] **quit**步骤 **8** 配置不进行组播数据的复制转发。[ ~ DeviceA] **aaa**[*DeviceA-aaa] **domain huawei**[*DeviceA-aaa-domain-huawei] **multicast without-forwarding**[*DeviceA-aaa-domain-huawei] **commit**[ ~ DeviceA-aaa-domain-huawei] **quit**[ ~ DeviceA-aaa] **quit****----**结束##### 配置文件       - DeviceA 的配置文件 :#sysname DeviceA#multicast routing-enable#qos-profile huaweiuser-queue cir 2000 pir 2000#interface GigabitEthernet1/0/0undo shutdownigmp enablemulticast user-aggregation vlan 1#interface GigabitEthernet1/0/0.100igmp enableuser-vlan 1 100basaccess-type layer2-subscriberauthentication-method bindqos-profile huawei outbound identifier none#interface GigabitEthernet2/0/0文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 345HUAWEI NetEngine40E配置指南 1 QoSundo shutdownip address ******** *************pim sm#aaamulticast-list cctv group-address ********* 24multicast-list jstv group-address ********* 24domain huaweimulticast unicast-reservable-bandwidth cir 1024multicast without-forwarding#multicastbandwidthmulticast-list name cctv detect-interval 15 threshold 15multicast-list name jstv detect-interval 15 threshold 20#return##### ******** 配置组播整形示例以特性的典型场景为例，介绍如何配置组播整形示例。##### 组网需求如 图 **1-40** ，路由器为 BRAS 设备，为 isp1 域的用户提供上网业务和 IPTV 组播业务。在主机侧使用 IGMP 协议， PIM 网络采用 SM 方式。要求配置组播流量整形功能：       - 组播整形保证带宽 5M ，峰值带宽 10M 。       - 组播源 ******** ，组播组地址 ********* 。说明本例中 interface 1 和 interface 2 分别代表 GE 1/0/0 和 GE 2/0/0 。图 **1-40** 组播整形典型组网##### 配置思路采用如下的思路配置组播整形：1. 配置接口 IP 地址和单播路由协议，实现 IP 连通性。2. 配置 BRAS 业务，使用户可以正常上网。3. 配置 IGMP 和 PIM-SM 协议。4. 配置组播整形功能。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 346HUAWEI NetEngine40E配置指南 1 QoS##### 数据准备为完成此配置举例，需准备如下的数据：       - 组播整形列表名称##### 操作步骤步骤 **1** 配置接口 IP 地址和单播路由协议详细配置过程请参见《 HUAWEI NetEngine40E 路由器 配置指南 -IP 路由》。步骤 **2** 配置接入业务                                              详细配置过程请参见《 HUAWEI NetEngine40E 路由器 配置指南 用户接入》。步骤 **3** 配置 IGMP 和 PIM 协议使能组播功能，并在主机侧接口上使能 IGMP 功能# 在路由器上使能组播功能，并在主机侧接口上使能 IGMP 功能。在 GE2/0/0 接口上使能 IGMP 功能， IGMP 版本为 v2 。<HUAWEI> **system-view**[ ~ HUAWEI] **multicast routing-enable**[*HUAWEI] **interface gigabitethernet 2/0/0**[*HUAWEI-GigabitEthernet2/0/0] **igmp enable**[*HUAWEI-GigabitEthernet2/0/0] **igmp version 2**[*HUAWEI-GigabitEthernet2/0/0] **commit**[ ~ HUAWEI-GigabitEthernet2/0/0] **quit**# 在路由器的 GE1/0/0 接口上使能 PIM-SM 功能。[ ~ HUAWEI] **interface gigabitethernet 1/0/0**[ ~ HUAWEI-GigabitEthernet1/0/0] **pim sm**[*HUAWEI-GigabitEthernet1/0/0] **commit**[ ~ HUAWEI-GigabitEthernet1/0/0] **quit**步骤 **4** 配置组播整形# 配置组播列表。[ ~ HUAWEI] **aaa**[*HUAWEI-aaa] **multicast-list list1 source-address ******** group-address ***********[*HUAWEI-aaa] **commit**[ ~ HUAWEI-aaa] **quit**# 使能组播整形。[ ~ HUAWEI] **multicast shaping enable**# 配置组播整形参数，保证带宽为 5M ，峰值带宽为 10M 。[*HUAWEI] **multicast shaping**[*HUAWEI-qos-mshaping] **multicast-list name list1 cir 5000 pir 10000**[*HUAWEI-qos-mshaping] **commit**[ ~ HUAWEI-qos-mshaping] **quit**步骤 **5** 检查配置结果使用 **display multicast-list** 命令可以查看到组播列表详细信息。<HUAWEI> **display multicast-list list1**----------------------------------------------------------------------Multicast-list name : list1Index        : 0文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 347HUAWEI NetEngine40E配置指南 1 QoSSource IP/mask    : ********/32Group IP/mask    : *********/32Group vpn-instance  : -----------------------------------------------------------------------在接口 GE1/0/0 下执行命令 **display interface** [ interface-type [ interface-number ] ][ | { **begin** | **exclude** | **include** } regular-expression ] 可以查看到接口下的组播流量带宽保证为 5M ，超过 5M 的组播流量会被丢弃。**----**结束##### 配置文件       - 路由器的配置文件#sysname HUAWEI#multicast routing-enable#multicast shaping enable#interface GigabitEthernet1/0/0undo shutdownpim sm#interface GigabitEthernet2/0/0undo shutdownpppoe-server bind Virtual-Template 1igmp enablebasaccess-type layer2-subscriber#ip pool pool1 bas localgateway *********** *************section 0 *********** ***********00dns-server *************#aaamulticast-list list1 index 0 source-address ******** group-address *********authentication-scheme auth1accounting-scheme acct1domain isp1authentication-scheme auth1accounting-scheme acct1radius-server group rd1ip-pool pool1#multicast shapingmulticast-list name list1 cir 5000 pir 10000#return### 1.12 VPN QoS 配置介绍了 VPN QoS （ Virtual Private Network Quality of Service ）技术的原理、应用和配置。#### 1.12.1 VPN QoS 概述VPN QoS 就是通过带宽限制、队列调度等，保证各流量之间获得指定的带宽。保证流量在拥塞时，各种类型的流量能够按照服务等级获得不同的调度顺序，实现重要数据的优先调度。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 348HUAWEI NetEngine40E配置指南 1 QoS##### **VPN QoS**如 图 **1-41** 所示， PE1 和 PE2 之间建立 TE 隧道。两个节点之间进行通信的数据流包括：       - 进入各种隧道的流量。– VPN 流量，如图中 VPN1 ～ VPNn 的流量。– 非 VPN 流量。       - 不进入隧道的公网 IP 流量。这些不同类型的流量之间容易发生带宽抢占。图 **1-41** VPN QoS 组网图为了实现服务质量的要求，需要限制不同流量的带宽，以保证各种业务能够分配到预定的带宽。#### 1.12.2 VPN QoS 配置注意事项##### 特性限制表 **1-28** 本特性的使用限制|特性限制|系列|涉及产品||---|---|---||VPLS PW HQoS/VLL PW HQoS不支持bgp lsp隧<br>道负载分担、不支持bgp lsp隧道负载分担和frr共<br>存。VPLS PW HQoS/VLL PW HQoS在配置迭代<br>两路及以上的bgp lsp隧道的时候，只会选择其中<br>一条bgp lsp隧道做转发。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 349HUAWEI NetEngine40E配置指南 1 QoS|特性限制|系列|涉及产品||---|---|---||SR-MPLS TE node类型隧道只有一路出接口支持<br>VPN QoS。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK||VPN QoS不支持SR-MPLS TE的ECMP或TI-LFA。<br>在VPN Qos迭代SR-MPLS TE场景下，隧道配置<br>ECMP或TI-LFA保护，保护不生效。建议合理部<br>署。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK||PBB方式的VSI不支持VPLS QoS，PBB VPLS和<br>VPLS QoS命令互斥，给用户错误信息，建议使能<br>接口HQoS。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK||二层组播不支持VPLS QoS。建议使能接口<br>HQoS。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK||对于LPUF-50/LPUF-50-L单板：<br>1、TM多播流量出口为Trunk/Global-VE情况下不<br>支持下行HQoS。<br>2、TM单板多播流量出口为APS保护端口(trunk-<br>serial/global-mp-group/pos-trunk等)时，如果<br>端口的两个成员口在同一TM，则备端口的流量不<br>支持下行HQoS。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A||如果TM是LPUF-480/LPUF-480-B/LPUI-480/<br>LPUI-480-B/LPUI-480-L/LPUF-480-E/LPUI-480-<br>CM/LPUI-200/LPUI-200-L/LPUF-200/LPUF-200-<br>B/LPUI-1T/LPUI-1T-B/LPUI-1T-L/LPUI-241/<br>LPUI-241-B/LPUI-241-CM/LPUF-241/LPUF-241-<br>E/LPUF-52单板，网络侧出口跨端口的情况下，<br>基于每个端口进行带宽限制和带宽保证。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 350HUAWEI NetEngine40E配置指南 1 QoS|特性限制|系列|涉及产品||---|---|---||只有TE-HSB场景才能支持备链路VPN QoS，其他<br>保护场景均不支持。影响非TE-HSB保护场景主备<br>链路切换后VPN QoS的可用性。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK||VPN配置QoS公网侧隧道为TE时，TE隧道变化<br>（如TE HSB LSP Down或UP）可能会导致VPN流<br>量少量丢包。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK||LPU1TG/LPU1T6C/LPUF2TF/LPUF4TA不支持<br>VPN QoS。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK||LDP FRR、LDP RLFA、TE FRR、TE逃生路径、<br>TE保护组、BGP FRR，隧道的备路径不支持VPLS<br>QoS。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK||VPN QoS场景网络侧出口跨端口的情况下，基于<br>每个端口进行带宽限制和带宽保证。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 351HUAWEI NetEngine40E配置指南 1 QoS|特性限制|系列|涉及产品||---|---|---||VLL VPN QoS场景Over如下场景：<br>1、TE over GRE<br>2、LDP over TE over GRE<br>3、BGP over TE over GRE<br>4、BGP over LDP over TE over GRE<br>5、BGP over SR-MPLS Policy<br>6、SR-MPLS BE over GRE<br>7、SR-MPLS TE over GRE<br>8、SR-MPLS TE over RSVP-TE<br>9、SR-MPLS Policy over RSVP-TE<br>如果配置以上场景，可能导致VPN QoS场景转发<br>不通。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK||LPUF-50/LPUF-50-L单板，多播流量出口为Trunk<br>时，不支持下行VPLS QoS。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK||多播目前仅支持每出接口的VPLS QoS，不支持每<br>VSI的VPLS QoS，根据端口限速。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK||VLL VPN QoS迭代BGP LSP场景，不支持BGP熵<br>标签功能。<br>VPLS基本转发、VPLS VPN QoS、PBB VPLS，迭<br>代BGP LSP场景，不支持BGP熵标签功能。<br>未同时使能VPLS公私网解耦功能和MPLS业务的<br>公私网分离功能的场景，VPLS迭代BGP LSP场<br>景，已知单播流量不支持BGP熵标签功能。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK||端口扩展不支持VLL的VPN QoS功能。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 352HUAWEI NetEngine40E配置指南 1 QoS|特性限制|系列|涉及产品||---|---|---||端口扩展不支持VPLS的VPN QoS功能。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|#### 1.12.3 配置 VPWS QoS 基本功能隧道中不同的 VPN 有着不同的资源需求。为了实现 VPWS 在隧道中的资源需求，同时不影响其他 VPN 的服务质量，需要配置 VPWS QoS 。##### 应用场景在 L2VPN 环境中，存在多个 VPN 共用隧道的情况。这种情况会导致 VPN 之间抢占带宽， VPN 流量在转发和丢弃时不能保证 VPN 内部各种业务类型的优先级，隧道中非VPN 流量也会抢占 VPN 带宽。隧道中不同的 VPN 有着不同的资源需求。为了实现 VPWS 在隧道中的资源需求，同时不影响其它 VPN 的服务质量，需要配置 VPWS QoS 。##### 前置任务在配置 VPWS QoS 基本功能之前，需要完成以下任务：       - 配置 VPWS PW 。##### 操作步骤       - 在接口视图下配置 VPN QoS 基本功能a. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **interface** interface-type interface-number ，进入 AC 接口的接口视图。c. 执行命令 **mpls l2vpn qos** **cir** cir-value [ **pir** pir-value ] [ **qos-profile** qosprofile-name ] [ **secondary** | **bypass** ] ，配置 VPWS PW 的带宽资源。#### ▪ secondary ：配置备份 VPWS PW 的带宽资源。 ▪ bypass ：配置 Bypass PW 的带宽资源。d. 执行命令 **commit** ，提交配置。       - 在 PW 模板下配置 VPN QoS 基本功能a. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **pw-template** pwname ，创建 PW 模板并进入 PW 模板视图。c. 执行命令 **qos cir** cir-value [ **pir** pir-value ] [ **qos-profile** qos-profilename ] ，配置 VPWS PW 的带宽资源。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 353HUAWEI NetEngine40E配置指南 1 QoSd. 执行命令 **commit** ，提交配置。       - 在系统视图下配置 VPN QoS 基本功能a. 执行命令 **system-view** ，进入系统视图。b. 交换 PW 的类型不同，使用的命令行不同：#### ▪ 配置纯静态交换 PW 的 QoS 基本功能： mpls switch-l2vc [ instance**name** instance-name ] ip-address vc-id **trans** trans-label **recv**received-label **cir** cir-value [ **pir** pir-value ] [ **qos-profile** qos-profilename ] [ **tunnel-policy** policy-name [ **endpoint** endpoint-address**color** color-value ] ] [ **oam-packet** **pop** **flow-label** ] **between** ipaddress vc-id **trans** trans-label **recv** received-label **cir** cir-value [ **pir**pir-value ] [ **qos-profile** qos-profile-name ] [ **tunnel-policy** policyname [ **endpoint** endpoint-address **color** color-value ] ] [ **oam-****packet** **pop** **flow-label** ] **encapsulation** encapsulation-type[ **control-word** | **no-control-word** ]#### ▪ 配置纯动态交换 PW 的 QoS 基本功能： mpls switch-l2vc [ instance**name** instance-name ] ip-address vc-id **cir** cir-value [ **pir** pir-value ][ **qos-profile** qos-profile-name ] [ **tunnel-policy** policy-name[ **endpoint** endpoint-address **color** color-value ] ] [ **oam-packet** **pop****flow-label** ] **between** ip-address vc-id [ **cir** cir-value ] [ **pir** pirvalue ] [ **qos-profile** qos-profile-name ] [ **tunnel-policy** policy-name[ **endpoint** endpoint-address **color** color-value ] ] [ **oam-packet** **pop****flow-label** ] **encapsulation** encapsulation-type [ **control-word-****transparent** ]#### ▪ 配置动静混合交换 PW 的 QoS 基本功能： mpls switch-l2vc [ instance**name** instance-name ] ip-address vc-id **cir** cir-value [ **pir** pir-value ][ **qos-profile** qos-profile-name ] [ **tunnel-policy** policy-name[ **endpoint** endpoint-address **color** color-value ] ] [ **oam-packet** **pop****flow-label** ] **between** ip-address vc-id **trans** trans-label **recv** recvlabel **cir** cir-value [ **pir** pir-value ] [ **qos-profile** qos-profile-name ][ **tunnel-policy** policy-name [ **endpoint** endpoint-address **color**color-value ] ] [ **oam-packet** **pop** **flow-label** ] **encapsulation**encapsulation-type [ **mtu** mtu-value ] [ **control-word** | **no-control-****word** ] [ **rtp-header** ] **timeslotnum** timeslotnum-value [ **tdm-****encapsulation** number ] [ **flow-label** { **both** | **send** | **receive** } ][ **control-word-transparent** ]c. 执行命令 **commit** ，提交配置。**----**结束##### 检查配置结果配置 VPWS QoS 基本功能后，可以按照以下结果来检查配置结果。       - 使用 **display mpls l2vpn qos** 命令查看已配置的 VPWS QoS 的资源带宽。##### ******** （可选）配置 VPN 的 QoS 模板VPN 的 QoS 模板通过定义不同的 QoS 模板并应用到 VPN 中来实现对 VPN 用户的 QoS 调度。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 354HUAWEI NetEngine40E配置指南 1 QoS**********.1** （可选）配置流队列 **WRED** 对象用户可以为配置的 flow-wred 对象设定高低门限百分比和丢弃概率，当队列长度超出门限值时，进行 WRED 随机丢弃报文。##### 背景信息在待配置的路由器上进行以下配置。说明          - 如果用户不配置流队列的 WRED 对象，系统采用缺省的尾丢弃策略。          - 红色丢弃优先级队列的高低门限百分比可以配置得最小，黄色丢弃优先级队列的高低门限百分比可以配置得稍大些，绿色丢弃优先级队列的高低门限百分比可以配置得最大。          - 用户在实际配置时， WRED 低门限百分比建议从 50% 开始取值，根据不同颜色的丢弃优先级逐级调整。丢弃概率建议取值为 100% 。通过配置 flow-wred 对象，用户可以为队列设定高低门限百分比和丢弃概率。当队列的实际长度占流队列的长度百分比小于低门限百分比时，不丢弃报文；当队列的实际长度占流队列的长度百分比在低门限百分比和高门限百分比之间时， WRED 开始随机丢弃报文（队列的长度越长，丢弃的概率越高）；当队列的实际长度占流队列的长度百分比大于高门限百分比时，丢弃所有的报文。用户可以根据需要创建多个 flow-wred 对象，供流队列引用。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **flow-wred** wred-name ，创建流队列 WRED 对象，进入流队列 WRED 视图。步骤 **3** 执行命令 **color** { **green** | **yellow** | **red** } **low-limit** low-limit-percentage **high-limit**high-limit-percentage **discard-percentage** discard-percentage ，设置不同颜色的报文的高低门限百分比和丢弃概率。步骤 **4** （可选）执行命令 **queue-depth** queue-depth-value ，配置队列的深度。步骤 **5** 执行命令 **commit** ，提交配置。**----**结束**********.2** 配置流队列的调度参数采用非缺省的流队列模板，根据网络需求配置流队列的 WFQ 调度权重、流量整形、整形速率及丢弃方式。##### 背景信息在待配置的路由器上进行以下配置。可以在一个流队列模板中，分别配置某个用户 8 个流队列的调度参数。如果用户不配置流队列，系统将采用缺省的流队列模板。       - 优先级为 ef 、 cs6 和 cs7 的流队列对应系统默认为 PQ 调度。       - 优先级为 be 、 af1 、 af2 、 af3 、 af4 的流队列系统默认为 WFQ 调度，调度权重为10 ： 10 ： 10 ： 15 ： 15 。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 355HUAWEI NetEngine40E配置指南 1 QoS       - 系统缺省不进行流量整形。       - 丢弃策略系统缺省为尾丢弃。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **flow-queue** flow-queue-name [ **priority-mode** ] ，进入流队列视图。若配置了 **priority-mode** ，则进入到优先级模式的流队列视图下。步骤 **3** （可选）执行命令 **priority** priority-value { **pq** | **wfq** } ，在优先级流队列视图下配置相同调度器上用户的优先级流队列采用 pq 调度方式或者 wfq 调度方式。说明该命令需要在执行命令 **flow-queue** flow-queue-name **priority-mode** 的优先级流队列视图下配置。步骤 **4** （可选）执行命令 **share-shaping** [ shap-id ] { **af1** | **af2** | **af3** | **af4** | **be** | **cs6** | **cs7** |**ef** } [*] [ **pq** | **wfq** **weight** weight-value | **lpq** ] shaping-value [ **pbs** pbs-value ] ，配置多个流队列的联合流量整形。配置联合流量整形后，联合流量整形中的队列将先做整形，再与其它的用户队列一起做调度。在优先级模式流队列视图下，若联合 shaping 不指定调度方式，则联合shaping 与子调度器的调度方式保持一致。步骤 **5** （可选）执行命令 **share-shaping** { **be** | **af1** | **af2** | **af3** | **af4** | **ef** | **cs6** | **cs7** } [*]**random-discard** random-discard-value ，配置流队列的联合 shaping 随机丢弃值。步骤 **6** （可选）执行命令 **queue** cos-value **random-discard** random-discard-value ，配置FQ 的随机丢弃值。步骤 **7** 执行命令 **queue** cos-value { { **pq** | **wfq weight** weight-value | **lpq** } | { **shaping**{ shaping-value | **shaping-percentage** shaping-percentage-value } [ **pbs** pbsvalue ] | **car** { car-value | **car-percentage** car-percentage-value } [ **pbs** pbsvalue ] } | **flow-wred** wred-name | **low-latency** | **low-jitter** } * 或 **queue** cos-value**cir** { { cir-value [ **cbs** cbs-value ] **cir-schedule pq** **pir** pir-value } | { **cir-percentage**cir-percentage-value [ **cbs** cbs-value ] **cir-schedule pq** **pir** **pir-percentage** pirpercentage-value } } [ **pbs** pbs-value ] **pir-schedule** { **pq** | **wfq weight** weightvalue | **lpq** } [ **flow-wred** wred-name ] ，修改流队列的调度参数和调度策略。步骤 **8** 执行命令 **quit** ，返回系统视图。步骤 **9** （可选）执行命令 **qos flow-queue low-latency enable** ，使能 flow-queue 下所有 PQ调度队列的低时延功能，以保证 PQ 调度队列的时延。步骤 **10** 执行命令 **slot** slot-id ，进入槽位视图。步骤 **11** （可选）执行命令 **qos user-queue burst-size bytes** min-bytes **time** burst-time ，配置用户队列默认突发尺寸的最小值及突发时间。步骤 **12** （可选）执行命令 **qos cos** { **be** | **af1** | **af2** | **af3** | **af4** | **ef** | **cs6** | **cs7** } **burst-size**burst-size-value ，配置后置 eTM 模块的桶深预借值。步骤 **13** 执行命令 **commit** ，提交配置。**----**结束文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 356HUAWEI NetEngine40E配置指南 1 QoS**********.3** （可选）配置流队列到类队列的映射采用非缺省的流队列到端口队列的映射关系，根据网络需求配置用户队列中某个业务进入端口队列的优先级。##### 背景信息在待配置的路由器上进行以下配置。可以在一个流队列映射模板中，分别配置 8 个 flow-queue 到 port-queue 的映射关系。如果用户不配置流队列到端口队列的映射关系，系统将采取缺省的一一对应映射关系。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **flow-mapping** mapping-name ，进入流队列映射视图。步骤 **3** 执行命令 **map flow-queue** cos-value **to** **port-queue** cos-value ，设置用户队列中某个业务进入端口队列的优先级。步骤 **4** 执行命令 **commit** ，提交配置。**----**结束**********.4** （可选）配置业务模板在接口应用业务模板，配置精度调整长度对报文长度补偿，用于补偿报文在设备上处理后的差值，精确地进行流量控制。##### 背景信息在待配置的路由器上进行以下配置。##### 操作步骤步骤 **1** 执行命令 **system-view**, 进入系统视图。步骤 **2** 执行命令 **service-template** service-template-name ，进入业务模板视图。该命令中应用的业务模板 service-template 必须是全局的，不能是基于单板配置的。步骤 **3** 执行命令 **network-header-length** network-header-length { **inbound** |**outbound** } ，设置业务模板的精度调整长度。报文进入设备后，进行处理后的报文长度和其实际的长度会存在一定的差值。为了更精确地进行流量控制，需要配置精度调整长度对报文的长度进行一些补偿。步骤 **4** 执行命令 **commit** ，提交配置。**----**结束**********.5** （可选）配置用户组队列的流量整形设置用户组队列的整形值，限制用户组队列流量与突发，以均匀的速度向外发送流量。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 357HUAWEI NetEngine40E配置指南 1 QoS##### 背景信息在待配置 HQoS 功能的路由器上进行以下配置。如果用户不配置用户组队列的流量整形，则系统缺省不进行流量整形。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **user-group-queue** group-name ，进入用户组队列视图。步骤 **3** 执行命令 **shaping** shaping-value [ **pbs** pbs-value ] { **inbound** | **outbound** } ，设置用户组的 shaping 值。步骤 **4** （可选）执行命令 **mode template** 命令，使能 GQ （ Group Queue ）按照 QoS 模板的实例共享组共享 QoS 资源。说明GQ 创建完成后只有在 **qos-profile** （接口视图）命令中应用时才申请 QoS 资源。配置该命令后，GQ 在申请 QoS 资源时按照 **qos-profile** （接口视图）命令中的 **group** 关键字共享资源；如果不配置该命令，则 GQ 共享 QoS 资源，即当多个 **qos-profile** （接口视图）命令中的 **group** 不同， GQ 也申请相同资源。步骤 **5** 执行命令 **commit** ，提交配置。**----**结束**********.6** 定义 **VPN** 的 **QoS** 模板并配置调度参数将流队列模板、流队列映射对象、业务模板、用户组队列应用于 QoS 模板。##### 背景信息在待配置的路由器上进行以下配置。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **qos-profile** qos-profile-name ，定义 VPN 的 QoS 模板并进入 QoS 模板视图。步骤 **3** 执行命令 **mpls-hqos flow-queue** flow-queue-name [ **flow-mapping** mappingname | **service-template** template-name | **user-group-queue** group-queuename ] * ，配置 QoS 模板下 VPN 的流队列调度参数。说明该命令中应用的业务模板 service-template 必须是全局的，不能是基于单板配置的。该命令与 QoS 模板下的 **user-queue** 和 **car** 命令互斥，不能同时配置。步骤 **4** 执行命令 **commit** ，提交配置。**----**结束文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 358HUAWEI NetEngine40E配置指南 1 QoS**********.7** 检查配置结果基于 VPN 的 QoS 模板配置成功后，可以查看流队列模板配置信息、 QoS 模板的配置信息、 QoS 模板的应用信息等内容。##### 前提条件VPN 的 QoS 模板已配置。##### 操作步骤       - 使用 **display flow-mapping configuration** [ **verbose** [ mapping-name ] ] 命令查看流队列映射对象的配置参数和该对象的引用关系。       - 使用 **display flow-queue configuration** [ **verbose** [ flow-queue-name ] ] 命令查看流队列模板配置信息。       - 使用 **display qos-profile configuration** [ profile-name ] 命令查看 QoS 模板的配置信息。       - 使用 **display qos-profile application** profile-name 命令查看 QoS 模板的应用信息。**----**结束#### 1.12.4 配置 VPWS QoS 流量统计在 VPWS 环境中，可以实时的统计 VPWS QoS 的流量，以检验是否满足服务质量。##### 应用场景在 VPWS 网络中，为了更好的监控网络的运行情况和定位问题，可以部署流量统计功能。配置了 QoS 的情况下，使能流量统计功能后，可以通过查看流量的适时状态，检验是否满足服务质量的要求。##### 前置任务在配置 VPWS QoS 流量统计功能之前，需要完成以下任务：       - 配置 VPWS PW 。       - 配置 VPWS QoS 功能。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **interface** interface-type interface-number ，进入 AC 接口的接口视图。步骤 **3** 执行命令 **mpls l2vpn pw traffic-statistics enable** [ **secondary** | **bypass** ] ，配置VPWS QoS 的流量统计功能。       - 如果不指定参数 **secondary** 和 **bypass** ，则配置主用 VPWS QoS 流量统计信息。       - 如果指定参数 **secondary** ，则配置备用 VPWS QoS 的流量统计功能。       - 如果指定参数 **bypass** ，则配置 Bypass PW 的流量统计功能。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 359HUAWEI NetEngine40E配置指南 1 QoS步骤 **4** 执行命令 **commit** ，提交配置。**----**结束##### 检查配置结果配置 VPWS QoS 流量统计功能后，可以按照以下结果来检查配置结果。       - 使用 **display traffic-statistics l2vpn qos pw** **interface** interface-typeinterface-number [ **secondary** | **bypass** ] 命令查看已配置的 VPWS QoS 的流量统计信息。#### 1.12.5 配置 VPLS QoS 基本功能隧道中不同的 VPN 有着不同的资源需求。为了实现 VPLS 在隧道中的资源需求，同时不影响其他 VPN 的服务质量，需要配置 VPLS QoS 。##### 应用场景在 L2VPN 环境中，存在多个 VPN 共用隧道的情况，这会导致 VPN 之间抢占带宽，隧道中非 VPN 流量也会抢占 VPN 带宽。因此， VPN 流量在转发时不能保证 VPN 内部各种业务类型的优先级。为了实现 VPLS 在隧道中的资源需求，同时不影响其它 VPN 的服务质量，需要配置 VPLSQoS 。##### 前置任务配置基于 VSI 的 QoS 功能，对 VSI 视图下所有 PW 生效，而配置基于 VSI PW 的 QoS 功能，对 LDP 方式 VSI 下指定的 PW 生效。说明LDP 方式的 VSI ，不能同时配置基于 VSI 的 QoS 和基于 VSI PW 的 QoS 。在配置 VPLS QoS 基本功能之前，需要完成以下任务：       - 配置 VPLS VSI 。##### 操作步骤       - 在 VSI 视图下配置 VPN QoS 基本功能a. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **vsi** vsi-name ，创建 VSI 实例并进入 VSI 视图。c. 执行命令 **qos cir** cir-value [ **pir** pir-value ] [ **qos-profile** qos-profilename ] ，配置 VSI 的 QoS 参数。d. 执行命令 **commit** ，提交配置。       - 在 VSI-LDP-PW 视图下配置 VPN QoS 基本功能a. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **vsi** vsi-name ， 创建 VSI 实例并进入 VSI 视图。c. 执行命令 **pwsignal** **ldp** ，创建 VSI 采用 LDP 信令并进入 VSI-LDP 视图。d. 执行命令 **pw** pw-name ，创建 PW 并进入 VSI-LDP-PW 视图。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 360HUAWEI NetEngine40E配置指南 1 QoSe. 执行命令 **qos cir** cir-value [ **pir** pir-value ] [ **qos-profile** qos-profilename ] ，配置 VSI PW 的 QoS 参数。f. 执行命令 **commit** ，提交配置。**----**结束##### 检查配置结果配置 VPLS QoS 基本功能后，可以按照以下结果来检查配置结果。       - 使用 **display mpls l2vpn qos** 命令查看已配置的 VPLS QoS 的资源带宽。##### 1.12.5.1 （可选）配置 VPN 的 QoS 模板VPN 的 QoS 模板通过定义不同的 QoS 模板并应用到 VPN 中来实现对 VPN 用户的 QoS 调度。**1.12.5.1.1** （可选）配置流队列 **WRED** 对象用户可以为配置的 flow-wred 对象设定高低门限百分比和丢弃概率，当队列长度超出门限值时，进行 WRED 随机丢弃报文。##### 背景信息在待配置的路由器上进行以下配置。说明          - 如果用户不配置流队列的 WRED 对象，系统采用缺省的尾丢弃策略。          - 红色丢弃优先级队列的高低门限百分比可以配置得最小，黄色丢弃优先级队列的高低门限百分比可以配置得稍大些，绿色丢弃优先级队列的高低门限百分比可以配置得最大。          - 用户在实际配置时， WRED 低门限百分比建议从 50% 开始取值，根据不同颜色的丢弃优先级逐级调整。丢弃概率建议取值为 100% 。通过配置 flow-wred 对象，用户可以为队列设定高低门限百分比和丢弃概率。当队列的实际长度占流队列的长度百分比小于低门限百分比时，不丢弃报文；当队列的实际长度占流队列的长度百分比在低门限百分比和高门限百分比之间时， WRED 开始随机丢弃报文（队列的长度越长，丢弃的概率越高）；当队列的实际长度占流队列的长度百分比大于高门限百分比时，丢弃所有的报文。用户可以根据需要创建多个 flow-wred 对象，供流队列引用。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **flow-wred** wred-name ，创建流队列 WRED 对象，进入流队列 WRED 视图。步骤 **3** 执行命令 **color** { **green** | **yellow** | **red** } **low-limit** low-limit-percentage **high-limit**high-limit-percentage **discard-percentage** discard-percentage ，设置不同颜色的报文的高低门限百分比和丢弃概率。步骤 **4** （可选）执行命令 **queue-depth** queue-depth-value ，配置队列的深度。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 361
最终截取: #### 1.12.4 配置 VPWS QoS 流量统计


在 VPWS 环境中，可以实时的统计 VPWS QoS 的流量，以检验是否满足服务质量。

##### 应用场景


在 VPWS 网络中，为了更好的监控网络的运行情况和定位问题，可以部署流量统计功
能。


配置了 QoS 的情况下，使能流量统计功能后，可以通过查看流量的适时状态，检验是
否满足服务质量的要求。

##### 前置任务


在配置 VPWS QoS 流量统计功能之前，需要完成以下任务：


       - 配置 VPWS PW 。


       - 配置 VPWS QoS 功能。

##### 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **interface** interface-type interface-number ，进入 AC 接口的接口视图。


步骤 **3** 执行命令 **mpls l2vpn pw traffic-statistics enable** [ **secondary** | **bypass** ] ，配置
VPWS QoS 的流量统计功能。


       - 如果不指定参数 **secondary** 和 **bypass** ，则配置主用 VPWS QoS 流量统计信息。


       - 如果指定参数 **secondary** ，则配置备用 VPWS QoS 的流量统计功能。


       - 如果指定参数 **bypass** ，则配置 Bypass PW 的流量统计功能。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 359



HUAWEI NetEngine40E
配置指南 1 QoS


步骤 **4** 执行命令 **commit** ，提交配置。


**----**
结束

##### 检查配置结果


配置 VPWS QoS 流量统计功能后，可以按照以下结果来检查配置结果。

       - 使用 **display traffic-statistics l2vpn qos pw** **interface** interface-type
interface-number [ **secondary** | **bypass** ] 命令查看已配置的 VPWS QoS 的流量统
计信息。

