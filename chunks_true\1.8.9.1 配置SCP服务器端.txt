

配置 SCP 服务器，使得客户端和服务器之间经过协商建立安全连接，实现安全的远程访
问。

##### 背景信息


SCP 协议基于 SSH2 协议。用户界面必须支持 SSH 协议，才能通过 SCP 访问其他设备文
件。

##### 操作步骤


步骤 **1** 配置 VTY 用户界面支持 SSH 协议，请参考 配置 **VTY** 用户界面支持 **SSH** 协议


步骤 **2** 配置 SSH 用户，请参见 为 **SSH** 用户配置验证方式并指定服务方式


步骤 **3** 根据 SCP 服务的类型，选择执行如下步骤之一。


       - 基于 IPv4 ：执行命令 **scp server enable** 或 **scp ipv4 server enable** ，使能 SCP 服
务。


       - 基于 IPv6 ：执行命令 **scp server enable** 或 **scp ipv6 server enable** ，使能 IPv6
SCP 服务。


步骤 **4** （可选）执行命令 **ssh server dh-exchange min-len** min-len ，配置与 SSH 客户端进
行 Diffie-hellman-group-exchange 密钥交换时，支持的最小密钥长度。


说明


如果 SSH 客户端支持大于 1024bits 的 Diffie-hellman-group-exchange 密钥交换算法时，建议执行
**ssh server dh-exchange min-len** 命令配置最小密钥长度为 3072bits ，以提高安全性。


步骤 **5** 执行命令 **commit** 提交配置。


**----**
结束
