#!/usr/bin/env python3
"""
排查markdown格式丢失问题
"""

import fitz
import re
from main3 import PDFToMarkdownConverter

def debug_markdown_formats():
    """调试markdown格式丢失问题"""
    
    with PDFToMarkdownConverter("2.pdf", {"debug_mode": True}) as converter:
        # 生成raw markdown
        raw_markdown = converter.generate_raw_markdown()
        
        print("=== 检查raw_markdown中的格式 ===")
        
        lines = raw_markdown.split('\n')
        print(f"Raw markdown总行数: {len(lines)}")
        
        # 检查各种markdown格式
        formats_found = {
            'tables': [],
            'code_blocks': [],
            'quotes': [],
            'lists': [],
            'bold': [],
            'italic': [],
            'headers': []
        }
        
        for i, line in enumerate(lines):
            line_strip = line.strip()
            if not line_strip:
                continue
            
            # 检查表格
            if '|' in line_strip and line_strip.count('|') >= 2:
                formats_found['tables'].append((i, line_strip))
            
            # 检查代码块
            if line_strip.startswith('```') or line_strip.startswith('~~~'):
                formats_found['code_blocks'].append((i, line_strip))
            
            # 检查引用
            if line_strip.startswith('>'):
                formats_found['quotes'].append((i, line_strip))
            
            # 检查列表
            if re.match(r'^[\*\-\+]\s+', line_strip) or re.match(r'^\d+\.\s+', line_strip):
                formats_found['lists'].append((i, line_strip))
            
            # 检查粗体
            if '**' in line_strip or '__' in line_strip:
                formats_found['bold'].append((i, line_strip))
            
            # 检查斜体
            if '*' in line_strip and '**' not in line_strip:
                formats_found['italic'].append((i, line_strip))
            
            # 检查标题（原始的#标题）
            if line_strip.startswith('#'):
                formats_found['headers'].append((i, line_strip))
        
        # 显示发现的格式
        for format_type, items in formats_found.items():
            print(f"\n--- {format_type.upper()} ({len(items)}个) ---")
            for i, (line_num, content) in enumerate(items[:5]):  # 只显示前5个
                print(f"  行{line_num}: {content[:80]}...")
            if len(items) > 5:
                print(f"  ... 还有 {len(items) - 5} 个")
        
        # 保存raw markdown到文件以便检查
        with open("raw_markdown_analysis.txt", "w", encoding="utf-8") as f:
            f.write(raw_markdown)
        print(f"\nRaw markdown已保存到: raw_markdown_analysis.txt")
        
        # 检查具体的表格示例
        print(f"\n=== 表格示例分析 ===")
        if formats_found['tables']:
            print("发现表格，检查前几个:")
            for i, (line_num, content) in enumerate(formats_found['tables'][:3]):
                print(f"\n表格 {i+1} (行{line_num}):")
                print(f"  内容: {content}")
                
                # 检查前后几行的上下文
                start = max(0, line_num - 2)
                end = min(len(lines), line_num + 3)
                print("  上下文:")
                for j in range(start, end):
                    marker = ">>> " if j == line_num else "    "
                    print(f"{marker}{j}: {lines[j]}")

def check_final_output():
    """检查最终输出中的格式"""
    
    print(f"\n=== 检查最终输出格式 ===")
    
    try:
        with open("test_main3_optimized.md", "r", encoding="utf-8") as f:
            final_content = f.read()
        
        lines = final_content.split('\n')
        print(f"最终markdown总行数: {len(lines)}")
        
        # 检查各种格式
        final_formats = {
            'tables': 0,
            'code_blocks': 0,
            'quotes': 0,
            'lists': 0,
            'bold': 0,
            'italic': 0
        }
        
        for line in lines:
            line_strip = line.strip()
            if not line_strip:
                continue
            
            if '|' in line_strip and line_strip.count('|') >= 2:
                final_formats['tables'] += 1
            if line_strip.startswith('```') or line_strip.startswith('~~~'):
                final_formats['code_blocks'] += 1
            if line_strip.startswith('>'):
                final_formats['quotes'] += 1
            if re.match(r'^[\*\-\+]\s+', line_strip) or re.match(r'^\d+\.\s+', line_strip):
                final_formats['lists'] += 1
            if '**' in line_strip or '__' in line_strip:
                final_formats['bold'] += 1
            if '*' in line_strip and '**' not in line_strip:
                final_formats['italic'] += 1
        
        print("最终输出中的格式统计:")
        for format_type, count in final_formats.items():
            print(f"  {format_type}: {count}")
        
    except FileNotFoundError:
        print("最终输出文件不存在")

if __name__ == "__main__":
    debug_markdown_formats()
    check_final_output()
