

配置 Telnet 登录其他设备的示例。在本示例中，通过配置用户验证方式和密码，实现
Telnet 登录。

##### 组网需求


说明


使用 Telnet 协议存在安全风险，建议使用 STelnet V2 登录设备。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 268


HUAWEI NetEngine40E
配置指南 1 基础配置


网络中有大量设备需要管理与维护，用户不可能为每台设备连接用户终端，特别是终
端与需要管理的设备之间无可达路由时，用户可以使用 Telnet 方式从当前设备登录到网
络上另一台设备，从而实现对远程设备的管理与维护。


如 图 **1-64** 所示，用户可以通过 Telnet 登录到 P1 ，但是无法直接 Telnet 到 P2 。 P1 与 P2 路
由可达，此时用户可以从 P1 上通过 Telnet 登录到 P2 ，从而远程管理和配置 P2 。


图 **1-64** 通过 Telnet 登录其他设备组网图


说明


本例中的 Interface1 代表接口 GE1/0/1 。

##### 注意事项


       - 必须保证 P1 和 P2 之间路由可达。


       - 从 P1 上 Telnet 登录到 P2 ，必须保证用户可以成功登录 P1 。


       - 当网络所处环境不足够安全时，我们建议选择安全的协议。安全协议举例参见：
**1.8.14.4** 通过 **STelnet** 登录其他设备配置示例（ **ECC** 认证方式） 。

##### 配置思路


采用如下的思路配置通过 Telnet 登录其他设备：


1. 在 P2 上配置 Telnet 验证方式和密码


2. 从 P1 上 Telnet 登录到 P2 。

##### 数据准备


为完成此配置举例，需准备如下的数据：


       - P2 的主机地址为 ******* 。


       - 用户登录的验证方式为 AAA ，用户名为“ huawei ”，密码为
“ YsHsjx_202206 ”。

##### 操作步骤


步骤 **1** 配置 P2 的 Telnet 验证方式和密码


<HUAWEI> **system-view**

[ ~ HUAWEI] **sysname P2**

[*HUAWEI] **commit**


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 269


HUAWEI NetEngine40E
配置指南 1 基础配置


[ ~ P2] **user-interface vty 0 4**

[ ~ P2-ui-vty0-4] **authentication-mode aaa**

[*P2-ui-vty0-4] **commit**

[ ~ P2-ui-vty0-4] **quit**

[ ~ P2] **aaa**

[*P2-aaa] **local-user huawei password**
Please configure the password (8-128)
Enter Password:
Confirm Password:


说明


设置的密码必须满足以下要求：


          - 密码采取交互式输入，系统不回显输入的密码。


          - 输入的密码为字符串形式，区分大小写，长度范围是 8 ～ 16 。输入的密码至少包含两种类型
字符，包括大写字母、小写字母、数字及特殊字符。


          - 特殊字符不包括“？”和空格。但是，当输入的密码两端使用双引号时，可在密码中间输入
空格。


–
如果使用双引号设置带空格密码，双引号之间不能再使用双引号。


–
如果使用双引号没有设置带空格密码，双引号之间可以再使用双引号。


例如， "Aa 123"45"" 为不合法密码， "Aa123"45"" 为合法密码。


配置文件中将以密文形式体现设置的密码。


[*P2-aaa] **local-user huawei service-type telnet**

[*P2-aaa] **local-user huawei level 3**

[*P2-aaa] **commit**

[ ~ P2-aaa] **quit**


如果采用 ACL 方式配置 Telnet 登录其他设备，需要在 P2 上进行如下配置。


[ ~ P2] **acl 2000**

[*P2-acl4-basic-2000] **rule permit source ******* 0**

[*P2-acl4-basic-2000] **quit**

[*P2] **user-interface vty 0 4**

[*P2-ui-vty0-4] **acl 2000 inbound**

[*P2-ui-vty0-4] **commit**

[ ~ P2-ui-vty0-4] **quit**


说明


采用 ACL 方式配置 Telnet 终端服务的配置为可选配置。


步骤 **2** 验证配置结果


完成以上配置后，可以从 P1 上 Telnet 到 P2 。


<HUAWEI> **system-view**

[ ~ HUAWEI] **sysname P1**

[*HUAWEI] **commit**

[ ~ P1] **quit**
<P1> **telnet *********
Trying *******
Press CTRL+K to abort

Connected to ******* ...
Warning: Telnet is not a secure protocol, and it is recommended to use Stelnet.
Username: huawei

Password:

<P2>


**----**
结束

##### 配置文件


       - P1 的配置文件


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 270


HUAWEI NetEngine40E
配置指南 1 基础配置


#

sysname P1
#
interface gigabitethernet1/0/1
undo shutdown
ip address ******* *************
#

admin

return


       - P2 的配置文件


#

sysname P2
#

acl number 2000
rule 5 permit source ******* 0
#

aaa
local-user huawei password irreversible-cipher $1c$]zV2B\j!z:$hRujV[%/IE|
0MwBQ}5sAX(RdE[oj#5otqG6=@>KK$
local-user huawei service-type telnet
local-user huawei level 3

#
interface gigabitethernet1/0/1
undo shutdown
ip address ******* *************
#
user-interface vty 0 4
authentication-mode aaa

acl 2000 inbound

#

return
