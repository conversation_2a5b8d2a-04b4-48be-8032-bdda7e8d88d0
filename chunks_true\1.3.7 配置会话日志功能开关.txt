

全局会话日志功能默认关闭，单个连接的会话日志功能默认打开，用户可根据需要选
择执行以下步骤配置会话日志功能开关。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 31


HUAWEI NetEngine40E
配置指南 1 基础配置

##### 背景信息


全局会话日志功能默认关闭，用户需要将输入、设备屏幕输出以及设备执行用户输入
命令的时间保存到根目录下 sessionlog 文件夹中时，可打开全局会话日志功能。


记录单个连接的会话日志功能默认打开，用户使能全局会话日志功能后，会将设备上
所有连接的输入、设备屏幕输出等信息记录到日志文件中，如果某些连接不需要生成
会话日志，可执行相应命令关闭指定连接的会话日志功能。

##### 操作步骤


       - 打开全局会话日志功能。


a. 执行命令 **system-view** ，进入系统视图。


b. 执行命令 **undo info-center session log disable** ，打开全局会话日志功能。


c. 执行命令 **commit** ，提交配置。


       - 关闭当前连接的会话日志功能。


执行命令 **display info-center session log status** ，查看设备上的全局会话日志功
能开关及所有在线连接的会话日志功能状态。


–
如果全局会话日志功能打开，则执行如下步骤：


i. 执行命令 **system-view** ，进入系统视图。


ii. 执行命令 **terminal session-log disable** ，关闭当前连接的会话日志功
能。


iii. 执行命令 **commit** ，提交配置。


–
如果全局会话日志功能关闭，则无需执行操作。


**----**
结束

##### 检查配置结果


执行命令 **display info-center session log status** ，查看设备上的全局会话日志功能开
关及所有在线连接的会话日志功能开关状态。
