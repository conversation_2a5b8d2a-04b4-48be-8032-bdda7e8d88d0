

用户可以设定需要的配置文件作为系统下次启动时加载的文件。

##### 背景信息


系统重新启动后使用指定的配置文件进行配置恢复，用户可以根据需要设定此配置文
件。

##### 操作步骤

       - 执行命令 **startup saved-configuration** configuration-file ，设定系统下次启动时
使用的配置文件。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 205


HUAWEI NetEngine40E
配置指南 1 基础配置


说明


不建议手工构造配置文件。如果构造配置文件格式错误，可能导致配置恢复失败或者配置
恢复发生错误。


系统下次启动时使用的配置文件必须存在，且必须满足以下条件：


            - 配置文件必须以“ .dat ”、“ .zip ”或“ .cfg ”作为扩展名，而且系统启动配置文件必须
存放在存储设备的根目录下。


            - 配置文件中只能包含配置命令、视图切换命令、 # ，其他类型（如 display 查询命令、
reset/save/ping 等维护命令、 quit 、 commit 、 return 、升级兼容命令、一阶段命令等）
命令执行时设备会报错，并继续加载后续命令。


            - 配置文件中的交互类型命令仅支持 Y/N 自动交互。


            - 配置文件中命令的缩进要正确。系统视图下的命令需顶格，系统视图下的一级视图需顶
格，一级视图下的命令需缩进一个空格，多级视图依次缩进一格。


            - # 号如果顶格，则表示退出到系统视图；非顶格，则只是用来隔离命令块，但缩进需正
确，和其下方的命令块对齐。


如果配置数据量较大，设备启动时加载配置的时间会更长，请耐心等待。

       - 执行命令 **startup default-configuration** configuration-file ，配置或者更新系统
的默认配置文件。


当系统没有设定下次启动使用的配置文件时，系统启动时会使用该默认配置文
件。


说明


            - 配置文件必须以 *.defcfg 作为扩展名，而且默认配置文件必须在设置前预先存放在存储
设备的根目录下。


            - 设置的默认配置文件大小有限制，最大不能超过 50KB 。


            - 慎重执行该命令，建议在技术支持人员指导下使用。


**----**
结束
