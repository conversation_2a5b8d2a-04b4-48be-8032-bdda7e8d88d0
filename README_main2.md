# 高精度PDF转Markdown转换器

## 概述

这是一个专门设计的高精度PDF转Markdown转换器，能够：

1. **100%保持标题结构** - 完美转换PDF的标题层级为Markdown格式
2. **智能内容过滤** - 自动去除页眉页脚、目录引导线等无用信息
3. **Markdown语法保护** - 转义特殊字符，防止影响最终的Markdown结构
4. **内容完整性** - 保持正文和标题的原始关系

## 核心特性

### 1. 双重标题提取策略
- **优先使用PDF目录（TOC）** - 如果PDF有目录，直接从目录提取标题信息
- **字体分析备选方案** - 如果没有目录，通过分析字体大小和样式识别标题

### 2. 智能内容过滤
- **页眉页脚识别** - 自动识别并过滤页眉页脚内容
- **目录内容过滤** - 识别并过滤目录页的引导线和页码
- **无用信息清理** - 去除孤立的页码数字和格式化符号

### 3. Markdown语法保护
- **特殊字符转义** - 自动转义 `*`, `_`, `[`, `]`, `\` 等Markdown特殊字符
- **#号智能处理** - 只转义非标题行首的#号，保护Markdown标题结构
- **代码块保护** - 转义反引号等代码相关字符

### 4. 内容结构优化
- **段落智能分割** - 将长文本分割成合理长度的段落
- **标题层级映射** - 将PDF标题层级映射为Markdown的1-6级标题
- **内容关系保持** - 确保正文内容归属到正确的标题下

## 使用方法

### 基本用法

```bash
# 使用默认文件名
python main2.py

# 指定输入文件
python main2.py your_file.pdf

# 指定输入和输出文件
python main2.py your_file.pdf output.md
```

### 代码调用

```python
from main2 import convert_pdf_to_markdown

# 转换PDF并保存到文件
markdown_content = convert_pdf_to_markdown("input.pdf", "output.md")

# 只获取Markdown内容，不保存文件
markdown_content = convert_pdf_to_markdown("input.pdf")
```

## 技术实现

### 核心组件

1. **PDFToMarkdownConverter类** - 主转换器
2. **TitleInfo数据类** - 标题信息存储
3. **ContentBlock数据类** - 内容块信息存储

### 关键算法

1. **标题提取算法**
   - 从PDF目录提取标题信息
   - 通过字体分析识别标题
   - 精确定位标题在页面中的位置

2. **内容分割算法**
   - 基于标题位置精确分割内容
   - 跨页面内容合并
   - 智能过滤无关内容

3. **文本清理算法**
   - 连字符换行处理
   - 目录引导线清理
   - 特殊字符转义

## 依赖库

- **PyMuPDF (fitz)** - PDF文档解析和目录提取
- **pdfplumber** - 精确的文本位置信息获取
- **re** - 正则表达式处理
- **typing** - 类型注解支持
- **dataclasses** - 数据类支持
- **logging** - 日志记录

## 安装依赖

```bash
# 使用uv（推荐）
uv sync

# 或使用pip
pip install pymupdf pdfplumber
```

## 输出示例

转换后的Markdown文件将具有以下特点：

```markdown
# 1 基础配置

## 1.1 前言

当用户需要为第一次上电的设备进行基本配置时，可以通过Console口登录设备。

## 1.2 首次登录配置

### 1.2.1 首次登录概述

设备第一次上电可以使用Console口或通过管理网口登录。

### 1.2.2 首次登录设备配置注意事项

在通过Console口登录设备之前，需要完成以下任务。
```

## 优势对比

与市面上其他PDF转Markdown工具相比：

| 特性 | 本工具 | 其他工具 |
|------|--------|----------|
| 标题结构保持 | ✅ 100%准确 | ❌ 经常错误 |
| #号处理 | ✅ 智能转义 | ❌ 全部转义或不处理 |
| 内容完整性 | ✅ 完整保持 | ❌ 经常丢失 |
| 页眉页脚过滤 | ✅ 自动识别 | ❌ 需要手动处理 |
| 目录内容过滤 | ✅ 智能过滤 | ❌ 混入正文 |

## 注意事项

1. **PDF质量要求** - PDF需要有文本层，扫描版PDF需要先进行OCR处理
2. **目录依赖** - 有目录的PDF转换效果最佳
3. **字体分析** - 无目录PDF依赖字体大小分析，需要PDF有明确的字体层级
4. **内存使用** - 大文件转换时可能需要较多内存

## 故障排除

### 常见问题

1. **转换结果为空**
   - 检查PDF是否有文本层
   - 确认PDF不是纯图片扫描版

2. **标题识别不准确**
   - 检查PDF是否有目录
   - 尝试调整字体大小阈值

3. **内容丢失**
   - 检查日志输出
   - 确认PDF文件完整性

## 更新日志

- **v1.0** - 初始版本，支持基本PDF转Markdown功能
- **v1.1** - 添加智能内容过滤和Markdown语法保护
- **v1.2** - 优化段落分割和标题层级映射

## 许可证

本项目采用MIT许可证。
