import fitz  # PyMuMuPDF

def get_titles_with_positions(file_path):
    """
    通过分析字体大小和位置来识别并获取文档中潜在标题的位置。

    Args:
        file_path (str): PDF 文件的路径。

    Returns:
        list: 包含每个找到的标题及其边界框 (bbox) 的字典列表。
              格式: [{'text': '标题内容', 'bbox': (x0, y0, x1, y1), 'page': 页码}]
    """
    titles = []
    doc = fitz.open(file_path)

    # 假设标题的最小字体大小。您需要根据您的文档进行调整。
    # 标题通常比正文大，比如大于12或14。
    min_title_size = 14

    for page_num in range(len(doc)):
        page = doc.load_page(page_num)
        # 使用 'dict' 参数获取详细的文本信息
        text_dict = page.get_text('dict')
        blocks = text_dict['blocks']

        for block in blocks:
            # 检查文本块类型是否为0 (文本)
            if block['type'] == 0:
                for line in block['lines']:
                    for span in line['spans']:
                        # 检查字体大小是否大于我们设定的阈值
                        # 并且字体名称中包含 "bold" (如果适用)
                        is_title = False
                        if span['size'] > min_title_size:
                            is_title = True
                        elif 'bold' in span['font'].lower():
                            is_title = True
                        
                        if is_title:
                            # 提取文本内容和边界框（bbox）
                            title_text = span['text'].strip()
                            bbox = span['bbox']
                            
                            # 过滤掉一些可能是页眉页脚或杂项的文本
                            if len(title_text) > 2 and title_text.isalpha():
                                titles.append({
                                    'text': title_text,
                                    'bbox': bbox,
                                    'page': page_num
                                })
    doc.close()
    return titles

# 示例用法
pdf_path = "2.pdf"  # 替换为您的文件路径
found_titles = get_titles_with_positions(pdf_path)

if found_titles:
    for title_info in found_titles:
        print(f"页码: {title_info['page']}, 标题: '{title_info['text']}', 位置: {title_info['bbox']}")
else:
    print("未找到符合条件的标题。")