

配置合适的 FTP 参数，可保证设备的安全性和资源。

##### 背景信息


FTP 服务器参数包括以下几种：


       - 指定 FTP 服务器的源地址或源接口，可限制客户端访问的目的地址，保证安全性。


       - 配置通过 FTP 方式连接服务器的最大连接数，如果设置的最大连接数小于等于当前
值，那么系统将拒绝新的连接请求，当前连接不会断开。


       - 配置 FTP 连接空闲时间，如果在连接空闲时间内， FTP 服务器和客户端没有消息交
互，则断开它们之间的连接，释放 FTP 连接资源。


请在作为 FTP 服务器的设备上进行如下的配置。

##### 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 配置 FTP 服务器的源地址或源接口，根据需要选择执行以下步骤：


       - 执行命令 **ftp server-source** { **-a** ip-address | **i** { interface-type interfacenumber | interface-name } } ，配置 FTP 服务器的源地址信息。

配置了 FTP 服务器的源地址信息后，再执行命令 **ftp** 登录服务器时，所输入的服务
器源地址必须与该步骤中配置的一致，否则无法成功登录服务器。


       - 执行命令 **ftp server-source all-interface** ，配置 FTP 服务器的源接口为设备上所
有配置了 IP 地址的接口。


说明


执行该命令后， FTP 服务器会接收来自所有接口的登录连接请求，增加系统安全风险，建
议用户取消配置该命令。


       - 执行命令 **ftp ipv6 server-source** **-a** ipv6-address [ **-vpn-instance** vpn-instancename ] ，配置 FTP 服务器的源 IPv6 地址信息。


       - 执行命令 **ftp ipv6 server-source all-interface** ，配置 FTP 服务器的 IPv6 源地址为
设备上所有配置 IPv6 地址的接口。


说明


执行该命令后， FTP 服务器会接收来自所有 IPv6 地址的登录连接请求，增加系统安全风险，
建议用户取消配置该命令。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 151


HUAWEI NetEngine40E
配置指南 1 基础配置


       - 执行命令 **ftp server-source** **physic-isolate** **-i** { interface-type interface-number
| interface-name } **-a** ip-address ，指定 FTP 服务器的源接口，并设置 FTP 服务器
的接口隔离属性。


       - 执行命令 **ftp ipv6 server-source** **physic-isolate** **-i** { interface-type interfacenumber | interface-name } **-a** ipv6-address ，指定 FTP 服务器的源 IPv6 接口，并
设置 FTP 服务器的接口隔离属性。


说明


成功设置接口隔离属性后，只能通过配置的物理口连接设备，即报文只能从配置的物理口
上送，通过其他接口上送的报文会被丢弃。


步骤 **3** （可选）执行命令 **ftp server max-sessions** max-sessions-num ，配置通过 FTP 方式连
接服务器的最大连接数。


步骤 **4** （可选）执行命令 **ftp** [ **ipv6** ] **timeout** minutes ，配置 FTP 服务器超时断连时间。


步骤 **5** 执行命令 **commit** ，提交配置。


**----**
结束
