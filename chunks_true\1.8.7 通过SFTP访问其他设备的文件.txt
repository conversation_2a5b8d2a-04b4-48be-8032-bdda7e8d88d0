

SFTP 是一种安全的 FTP 服务。配置设备作为 SFTP 客户端，服务器通过对客户端的认证
及双向的数据加密，为网络文件传输提供了安全的服务。

##### 应用环境


SFTP 是 Secure File Transfer Protocol 的简称，建立在 SSH 连接的基础之上，远程用户
可以安全地登录设备，进行文件管理和文件传送等操作，为数据传输提供了更高的安
全保障。同时，由于设备提供了 SFTP 客户端功能，可以从本设备安全登录到远程 SSH
服务器上，进行文件的安全传输。

##### 前置任务


在配置通过 SFTP 访问其他设备文件之前，需完成以下任务：


       - 成功配置 SFTP 服务器。详细配置如下：


a. 配置 **SSH** 用户并指定服务方式


b. 使能 **SFTP** 服务器功能


c. （可选）配置 **SFTP** 服务器参数
