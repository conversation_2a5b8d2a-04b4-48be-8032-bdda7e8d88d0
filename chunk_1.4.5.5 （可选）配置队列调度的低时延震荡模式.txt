头标题: ******* （可选）配置队列调度的低时延震荡模式
尾标题: 1.4.5.6 检查配置结果
内容: HUAWEI NetEngine40E配置指南 1 QoS       - 使用 **display service-template configuration** [ **verbose** [ service-templatename ] ] 命令，查看用户自定义的业务模板的信息。       - 使用 **display port-wred configuration** [ **verbose** [ port-wred-name ] ] 命令，查看端口队列 WRED 对象的配置参数。       - 使用 **display port-queue configuration** **interface** interface-type interfacenumber **outbound** 命令，查看端口队列详细配置信息。       - 使用 **display port-queue statistics** [ **slot** slot-id | **interface** interface-typeinterface-number ] [ cos-value ] **outbound** 命令，查看端口队列的统计计数信息。       - 使用 **display sub-port-queue configuration** [ **verbose** [ sub-port-queuename ] ] 命令，查看子接口队列信息。#### 1.3.7 维护流量监管、流量整形清除流量监管、流量整形的统计信息。##### 1.3.7.1 清空 CAR 的统计信息介绍如何清空 CAR 的统计信息。##### 背景信息须知清除统计信息后，以前的统计信息将无法恢复，务必在执行该操作之前仔细确认。在确认需要清除指定接口指定方向的 CAR 统计数据后，请在用户视图下执行下面的**reset** 命令。##### 操作步骤       - 执行 **reset car statistics interface** { interface-type interface-number |interface-name } [ **vlan** vlan-id | **vid** vid | **ce-vid** ce-vid | **vid** vid **ce-vid** ce-vid |**pe-vid** pe-vid **ce-vid** ce-vid ] { **inbound** | **outbound** } 命令清空指定接口指定方向的 CAR 统计数据。**----**结束#### 1.3.8 配置举例从具体应用场景、配置命令等方面对流量监管、流量整形、接口限速进行了详细的描述。##### 1.3.8.1 配置流量监管示例以特性的流量控制场景为例，介绍如何配置流量监管。通过在接口上配置流量监管，实现收发报文的总流量限制、报文流限制。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 26HUAWEI NetEngine40E配置指南 1 QoS##### 组网需求DeviceA 通过接口 GE3/0/0 与 DeviceB 的接口 GE1/0/0 互连， Server 、 PC1 、 PC2 可经由DeviceA 和 DeviceB 访问 Internet 。Server 、 PC1 与 DeviceA 的 GE1/0/0 接口在同一网段， PC2 与 DeviceA 的 GE2/0/0 接口在同一网段。在 DeviceA 上对接口 GE1/0/0 接收到的源自 Server 和 PC1 的报文流分别实施流量控制如下：       - 来自 Server 的报文流量进行带宽保证，固定为 5Mbit/s ，最高不超过 6Mbit/s ，流量超过 5Mbit/s 且不超过 6Mbit/s 时，报文正常发送，超过 6Mbit/s 时，超过部分的流量降级为 BE 流进行发送。       - 来自 PC1 的报文流量约束为 2Mbit/s ，流量超过 2Mbit/s 时则丢弃超标流量。说明本例中 interface1 ， interface2 ， interface3 分别代表 GE1/0/0 ， GE2/0/0 ， GE3/0/0 。图 **1-6** 流量监管配置组网图##### 配置注意事项在配置中需注意以下事项：       - 报文被 remark 为 ef 、 be 、 cs6 和 cs7 服务等级后，报文颜色只能被 remark 为Green 。       - 当用户需要显示流量策略的统计数据时，可配置 **statistics enable** 使能流量策略的统计功能。##### 配置思路采用如下思路配置流量监管。1. 配置各接口的 IP 地址2. 在 DeviceA 的入接口 GE1/0/0 上通过复杂流分类对来自 Server 和 PC1 的流量进行监管。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 27HUAWEI NetEngine40E配置指南 1 QoS##### 数据准备完成此例配置，需准备以下数据：       - Server 和 PC1 流量的 ACL 列表号、流分类名称、流行为名称、流量策略名称和流量策略应用的接口       - 流量监管的承诺信息速率、峰值信息速率、承诺突发尺寸、最大突发尺寸##### 操作步骤步骤 **1** 配置各接口的 IP 地址（略）步骤 **2** 配置 DeviceA# 配置 ACL 规则列表，分别匹配来源于 Server 和 PC1 的报文流。<HUAWEI> **system-view**[ ~ HUAWEI] **sysname DeviceA**[*HUAWEI] **commit**[ ~ DeviceA] **acl number 2001**[*DeviceA-acl-basic-2001] **rule permit source ******* 0.0.0.0**[*DeviceA-acl-basic-2001] **commit**[ ~ DeviceA-acl-basic-2001] **quit**[ ~ DeviceA] **acl number 2002**[*DeviceA-acl-basic-2002] **rule permit source ******* 0.0.0.0**[*DeviceA-acl-basic-2002] **commit**[ ~ DeviceA-acl-basic-2002] **quit**# 配置流分类，并定义基于 ACL 的流分类匹配规则。[ ~ DeviceA] **traffic classifier class1**[*DeviceA-classifier-class1] **if-match acl 2001**[*DeviceA-classifier-class1] **commit**[ ~ DeviceA-classifier-class1] **quit**[ ~ DeviceA] **traffic classifier class2**[*DeviceA-classifier-class2] **if-match acl 2002**[*DeviceA-classifier-class2] **commit**[ ~ DeviceA-classifier-class2] **quit**# 定义流行为，对来自 Server 的报文流量带宽设定为 5Mbit/s ，最高不超过 6Mbit/s ，流量超过 5Mbit/s 且不超过 6Mbit/s 时，报文正常通过，超过 6Mbit/s 时，超过部分的流量降级为 BE 流进行发送。[ ~ DeviceA] **traffic behavior behavior1**[*DeviceA-behavior-behavior1] **car cir 5000 pir 6000 green pass yellow pass red pass service-class be****color green**[*DeviceA-behavior-behavior1] **commit**[ ~ DeviceA-behavior-behavior1] **quit**# 定义流行为，对来自 PC1 的报文流量约束为 2Mbit/s ，流量超过 2Mbit/s 时则丢弃超标流量。[ ~ DeviceA] **traffic behavior behavior2**[*DeviceA-behavior-behavior2] **car cir 2000 green pass red discard**[*DeviceA-behavior-behavior2] **commit**[ ~ DeviceA-behavior-behavior2] **quit**# 定义策略，将类与行为关联。[ ~ DeviceA] **traffic policy policy1**[*DeviceA-trafficpolicy-policy1] **classifier class1 behavior behavior1**[*DeviceA-trafficpolicy-policy1] **classifier class2 behavior behavior2**[*DeviceA-trafficpolicy-policy1] **commit**[ ~ DeviceA-trafficpolicy-policy1] **quit**# 将策略应用到 GE1/0/0 接口上。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 28HUAWEI NetEngine40E配置指南 1 QoS[ ~ DeviceA] **interface gigabitethernet 1/0/0**[ ~ DeviceA-GigabitEthernet1/0/0] **undo shutdown**[*DeviceA-GigabitEthernet1/0/0] **traffic-policy policy1 inbound**[*DeviceA-GigabitEthernet1/0/0] **commit**步骤 **3** 检查配置结果在 DeviceB 上执行 **display interface** 命令，可以查看到接口上的流量信息。**----**结束##### 配置文件       - DeviceA 的配置文件#sysname DeviceA#acl number 2001rule 5 permit source ******* 0acl number 2002rule 5 permit source ******* 0#traffic classifier class1 operator orif-match acl 2001traffic classifier class2 operator orif-match acl 2002#traffic behavior behavior1car cir 5000 pir 6000 green pass yellow pass red pass service-class be color greentraffic behavior behavior2car cir 2000 green pass red discard#traffic policy policy1classifier class1 behavior behavior1 precedence 1classifier class2 behavior behavior2 precedence 2#interface GigabitEthernet1/0/0undo shutdownip address ******* 255.255.255.0traffic-policy policy1 inbound#return### 1.4 拥塞管理和拥塞避免配置通过调整网络的流量来解除网络拥塞并介绍当网络发生拥塞时的几种不同丢包策略。#### 1.4.1 拥塞管理和拥塞避免简介拥塞管理就是在网络间歇性出现拥塞，时延敏感业务要求得到比其他业务更高质量的QoS 服务时，通过调整报文的调度次序来满足时延敏感业务高 QoS 服务的一种拥塞控制机制。拥塞避免是指通过监控网络资源（如队列或内存缓冲区）的使用情况，在拥塞发生或有加剧趋势时主动丢弃报文，通过调整网络的流量来解除网络过载的一种拥塞控制机制。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 29HUAWEI NetEngine40E配置指南 1 QoS#### 1.4.2 拥塞管理和拥塞避免配置注意事项##### 特性限制无#### 1.4.3 配置拥塞管理介绍如何通过配置队列调度参数实现拥塞管理。##### 背景信息拥塞管理指网络在发生拥塞时，如何进行管理和控制。处理的方法是使用队列技术，将从一个接口发出的所有报文放入多个队列，按照各个队列的优先级进行处理。不同的队列调度算法用来解决不同的问题，并产生不同的效果。队列调度参数的配置，可通过端口队列模板和接口下直接配置两种方式实现。       - 基于端口队列模板配置队列调度参数，该方式适用于需要为多个接口配置队列调度参数时，可以减少配置工作量。       - 接口下直接配置队列调度参数，该方式适用于需要配置的接口数量较少，且各接口下调度参数相差较大时。##### 操作步骤       - 在接口上直接配置队列调度参数a. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **interface** interface-type interface-number ，进入接口视图。c. 执行命令 **port-queue** cos-value { { **pq** | **wfq weight** weight-value | **lpq** } |**shaping** { shaping-value | **shaping-percentage** shaping-percentagevalue } [ **pbs** pbs-value ] | **port-wred** wred-name | **low-latency** } ***outbound** 或命令 **port-queue** cos-value **cir** { { cir-value [ **cbs** cbs-value ]**cir-schedule pq pir** pir-value } | { **cir-percentage** cir-percentage-value[ **cbs** cbs-value ] **cir-schedule pq pir pir-percentage** pir-percentagevalue } } [ **pbs** pbs-value ] **pir-schedule** { **pq** | **wfq weight** weight-value |**lpq** } [ **port-wred** wred-name ] **outbound** ，在接口视图下配置队列调度参数。d. （可选）执行命令 **port-queue-alarm** cos-value **buffer percentage**percentage-value ，配置端口队列使用率的告警门限。说明如果需要配置端口队列使用率告警门限的接口多，可以在槽位视图下执行命令 **port-****queue-alarm** cos-value **buffer percentage** percentage-value 基于槽位进行配置。在槽位视图下的配置对其所有接口都生效；槽位视图和接口视图同时配置此功能时，接口视图配置的参数优先生效。e. （可选）执行命令 **port-queue-alarm** cos-value { **discard-packet** discardpacket-number | **discard-byte** discard-byte-number | **discard-packet-****ratio** discard-packet-coefficient discard-packet-exponent } [ **interval**interval-time ] ，配置端口队列丢弃告警门限值。f. 执行命令 **commit** ，提交配置。       - 基于端口队列模板配置队列调度参数。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 30HUAWEI NetEngine40E配置指南 1 QoSa. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **port-queue-template** port-queue-name ，创建端口队列模板并进入端口队列模板视图。c. 执行命令 **queue** cos-value { { **pq** | **wfq weight** weight-value | **lpq** } |**shaping** { shaping-value | **shaping-percentage** shaping-percentagevalue } [ **pbs** pbs-value ] | **port-wred** wred-name | **low-latency** } * 或命令**queue** cos-value **cir** { { cir-value [ **cbs** cbs-value ] **cir-schedule pq pir** pirvalue } | { **cir-percentage** cir-percentage-value [ **cbs** cbs-value ] **cir-****schedule pq pir pir-percentage** pir-percentage-value } } [ **pbs** pbsvalue ] **pir-schedule** { **pq** | **wfq weight** weight-value | **lpq** } [ **port-wred**wred-name ] ，配置端口队列模板中各队列的调度参数。d. 执行命令 **quit** ，返回系统视图。e. 执行命令 **interface** interface-type interface-number ，进入接口视图。f. 执行命令 **port-queue-template** port-queue-name **outbound** ，在接口视图下应用端口队列模板。g. （可选）执行命令 **port-queue-alarm** cos-value **buffer percentage**percentage-value ，配置端口队列使用率的告警门限。说明如果需要配置端口队列使用率告警门限的接口多，可以在槽位视图下执行命令 **port-****queue-alarm** cos-value **buffer percentage** percentage-value 基于槽位进行配置。在槽位视图下的配置对其所有接口都生效；槽位视图和接口视图同时配置此功能时，接口视图配置的参数优先生效。h. （可选）执行命令 **port-queue-alarm** cos-value { **discard-packet** discardpacket-number | **discard-byte** discard-byte-number | **discard-packet-****ratio** discard-packet-coefficient discard-packet-exponent } [ **interval**interval-time ] ，配置端口队列丢弃告警门限值。i. 执行命令 **commit** ，提交配置。       - a. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **port-queue-template** port-queue-name ，创建端口队列模板并进入端口队列模板视图。c. 执行命令 **queue** cos-value { { **pq** | **wfq weight** weight-value | **lpq** } |**shaping** { shaping-value | **shaping-percentage** shaping-percentagevalue } [ **pbs** pbs-value ] | **port-wred** wred-name **low-latency** }* ，配置端口队列模板中各队列的调度参数。d. 执行命令 **quit** ，返回系统视图。e. 执行命令 **interface** interface-type interface-number ，进入接口视图。f. 执行命令 **port-queue-template** port-queue-name **outbound** ，在接口视图下应用端口队列模板。g. （可选）执行命令 **port-queue-alarm** cos-value **buffer percentage**percentage-value ，配置端口队列使用率的告警门限。h. （可选）执行命令 **port-queue-alarm** cos-value { **discard-packet** discardpacket-number | **discard-byte** discard-byte-number | **discard-packet-****ratio** discard-packet-coefficient discard-packet-exponent } [ **interval**interval-time ] ，配置端口队列丢弃告警门限值。i. 执行命令 **commit** ，提交配置。**----**结束文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 31HUAWEI NetEngine40E配置指南 1 QoS#### 1.4.4 配置拥塞避免WRED （ Weighted Random Early Detection ），可以通过设定阈值决定随机丢弃报文的条件，避免使多个 TCP 连接同时降低发送速度，从而避免了 TCP 的全局同步现象。##### 应用环境由于内存资源的有限，当发生网络拥塞时，传统的处理方法是采用尾丢弃，即将超出规格的所有报文都丢弃。对于 TCP 报文，由于大量的报文被丢弃，将造成 TCP 超时，从而引发 TCP 的慢启动和拥塞避免机制，使 TCP 减少报文的发送。当队列同时丢弃多个TCP 连接的报文时，将造成多个 TCP 连接同时进入慢启动和拥塞避免，称之为 TCP 全局同步。这样多个 TCP 连接发向队列的报文将同时减少，使得发向队列的报文的量低于链路发送的速度，减少了带宽的利用率。为了避免 TCP 全局同步这种情况，队列可以采用 WRED 丢弃策略。由于 WRED 随机地丢弃报文，将避免使多个 TCP 连接同时降低发送速度，从而避免了 TCP 的全局同步现象，提高了线路带宽的利用率。说明WRED 丢弃策略通常与 WFQ 队列配合使用。##### 前置配置在配置 WRED 之前，需完成以下任务：       - 配置相关接口的物理参数       - 配置相关接口的链路层属性，保证接口的正常工作       - 配置相关接口的 IP 地址       - 使能路由协议，实现互通##### ******* 配置 WRED 模板配置 WRED 模板中不同颜色报文的高低门限百分比和丢弃概率。##### 背景信息每个 WRED 模板最多支持红、黄、绿三种颜色报文的处理。一般绿色报文设置的丢弃概率比较小，高、低门限值比较大；黄色报文次之；红色报文设置的丢弃概率最大，高、低门限值最小。通过配置 WRED 模板，用户可以为队列设定高低门限百分比和丢弃概率。       - 当报文队列的实际长度占端口队列的长度百分比小于低门限百分比时，不丢弃报文。       - 当报文队列的实际长度占端口队列的长度百分比在低门限百分比和高门限百分比之间时， WRED 开始随机丢弃报文（队列的长度越长，报文被丢弃的概率越高）。       - 当报文队列的实际长度占端口队列的长度百分比大于高门限百分比时，丢弃所有的报文。每种颜色报文的门限值百分比和丢弃概率都是可配置的。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 32HUAWEI NetEngine40E配置指南 1 QoS说明          - 如果用户不配置端口队列的 WRED 模板，系统采用缺省的尾丢弃策略。          - 红色丢弃优先级队列的高低门限百分比可以配置得最小，黄色丢弃优先级队列的高低门限百分比可以配置得稍大些，绿色丢弃优先级队列的高低门限百分比可以配置得最大。          - 用户在实际配置时， WRED 低门限百分比建议从 50% 开始取值，根据不同颜色的丢弃优先级逐级调整。丢弃概率建议取值为 100% 。在待配置路由器上进行以下配置。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **port-wred** port-wred-name ，创建 WRED 模板并进入 WRED 视图。步骤 **3** 执行命令 **color** { **green** | **yellow** | **red** } **low-limit** low-limit-percentage **high-limit**high-limit-percentage **discard-percentage** discard-percentage ，配置不同颜色的报文的高低门限百分比和丢弃概率。步骤 **4** （可选）执行命令 **queue-depth** { queue-depth-value | **buffer-time** queue-depthtime } ，配置调整端口队列的队列深度。步骤 **5** 执行命令 **commit** ，提交配置。**----**结束##### 1.4.4.2 （可选）配置端口队列的联合流量整形当多个优先级的端口队列出接口为同一个主接口时，可以针对同样调度方式的端口队列进行联合流量整形。##### 背景信息请在主接口上进行如下配置。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **interface** interface-type interface-number ，进入接口视图。步骤 **3** 执行命令 **port share-shaping** { **af1** | **af2** | **af3** | **af4** | **be** | **cs6** | **cs7** | **ef** } [*] [ **pq** | **wfq****weight** weight-value | **lpq** ] { shaping-value | **shaping-percentage** shapingpercentage-value } [ **pbs** pbs-value ] ，配置端口队列的联合流量整形。本命令为覆盖式命令，即在同一接口多次配置流量整形参数后，按最后一次配置生效。同一接口下命令 **port share-shaping** 与命令 **share-shaping** （端口队列视图） 互斥，不能同时配置。本命令不支持在 Trunk 成员口下配置，配置了本命令的接口也不能加入 Trunk 口。本命令仅在 LPUI-52-E/LPUI-120-E/LPUF-480-E/LPUF-480/LPUF-480-B/LPUI-480/LPUI-480-B/LPUI-480-L/LPUI-1T/LPUI-1T-B/LPUI-1T-L/LPUI-2T/LPUI-2T-B/LPUF-241/LPUF-241-R/LPUI-241/LPUI-241-B/LPUF-481/LPUF-402-E 单板支持，或在文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 33HUAWEI NetEngine40E配置指南 1 QoSLPUF-120 系列母板 /LPUF-240 系列母板配合 P52-E/P120-E/P120-H/P240-E/P240-H 子卡使用时支持。步骤 **4** 执行命令 **commit** ，提交配置。**----**结束##### 1.4.4.3 应用 WRED基于业务类型应用配置好的 WRED 模板。##### 背景信息在配置了 WRED 模板的路由器上进行以下配置。##### 操作步骤       - 在接口视图上应用 WREDa. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **interface** interface-type interface-number ，进入接口视图。c. 执行命令 **port-queue** cos-value { { **pq** | **wfq** **weight** weight-value | **lpq** } |**shaping** { shaping-value | **shaping-percentage** shaping-percentagevalue } [ **pbs** pbs-value ] | **port-wred** wred-name | **low-latency** } [*]**outbound** ，配置接口出方向的 QoS 服务等级、承诺信息速率和峰值速率。d. （可选）执行命令 **qos buffer-monitor default-queue enable** ，使能默认SQ 队列的缓存监控功能。接口使能默认 SQ 队列的缓存监控功能后，设备会采集接口默认 SQ 队列的缓存值，通过网管或执行命令 **display port-queue statistics interface****outbound default** 观察缓存值变化趋势，可以辅助预判是否可能会出现流量拥塞。e. （可选）执行命令 **port-queue-alarm** cos-value **buffer** **percentage**percentage-value ，配置接口出方向的 QoS 队列的使用率告警门限。f. （可选）执行命令 **port-queue-alarm** cos-value { **discard-packet** discardpacket-number | **discard-byte** discard-byte-number | **discard-packet-****ratio** discard-packet-coefficient discard-packet-exponent } **interval**interval-time ，配置端口队列丢弃告警功能。g. 执行命令 **commit** ，提交配置。h. 执行命令 **quit** ，退回到系统视图。       - 在槽位视图配置的 MTunnel 上应用 WRED 。a. 执行命令 **slot** slot-id ，进入槽位视图。b. 执行命令 **port-queue** cos-value { { **pq** | **wfq** **weight** weight-value | **lpq** } |**shaping** { shaping-value | **shaping-percentage** shaping-percentagevalue } | **port-wred** wred-name } * **outbound** **bind** **mtunnel** ，在分布式组播 VPN 绑定的 MTunnel 上为不同等级的组播报文端口队列配置调度策略，并在调度策略中应用配置好的 WRED 模板。c. 执行命令 **commit** ，提交配置。**----**结束文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 34HUAWEI NetEngine40E配置指南 1 QoS##### 1.4.4.4 检查配置结果WRED 配置成功后，可以查看 WRED 配置情况。##### 操作步骤       - 使用 **display port-wred configuration** [ **verbose** [ port-wred-name ] ] 命令查看 WRED 模板配置的参数。       - 使用 **display port-queue configuration interface** interface-type interfacenumber **outbound** 命令用来显示端口队列配置详细信息。       - 使用 **display port-queue statistics** [ **slot** slot-id | **interface** interface-typeinterface-number ] [ cos-value ] **outbound** [ **default** ] 命令查看端口队列的统计信息。       - 使用 **display port-queue statistics slot** slot-id [ cos-value ] **outbound** **bind****mtunnel** 命令查看分布式组播 VPN 的虚环回 Mtunnel 接口的端口队列的统计信息。**----**结束#### 1.4.5 配置低速链路的队列调度低速链路上，由于网络资源有限，容易产生拥塞。为了避免网络拥塞时报文被统一丢弃，需要将不同业务流量分类到不同的流队列进行 PQ 和 WFQ 队列调度。##### 应用环境低速接口建立的链路上，由于网络资源有限，容易产生拥塞。为了避免网络拥塞时报文被统一丢弃，需要将不同业务流量分类到不同的流队列进行 PQ 和 WFQ 队列调度，优先保证高优先级的报文通过。说明低速链路的队列调度只支持在 Serial 接口、 Trunk-Serial 接口、 Global-MP-Group 接口和 MPgroup 接口下配置。##### 前置任务在配置低速链路的队列调度之前，需完成以下任务：       - 配置相关接口的物理参数       - 配置相关接口的链路层属性，保证接口的正常工作       - 配置相关接口的 IP 地址       - 使能路由协议，实现互通##### ******* 配置 PQ 调度在低速接口的链路上，将不同业务流量分类到不同的队列进行绝对优先级队列调度。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 35HUAWEI NetEngine40E配置指南 1 QoS##### 背景信息说明af2 、 af3 、 af4 、 ef 、 cs6 、 cs7 队列的报文支持配置 PQ 调度。配置 PQ 队列调度的队列优先级高于 WFQ 队列调度。对一个队列如果同时配置 PQ 和 WFQ 的队列调度，后配置的生效。在待配置路由器上进行以下配置。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **interface** interface-type interface-number ，进入接口视图。说明低速链路的队列调度只支持在 Serial 接口、 Trunk-Serial 接口、 Global-MP-Group 接口和 MPgroup 接口下配置。步骤 **3** 执行命令 **port-queue** { **af2** | **af3** | **af4** | **ef** | **cs6** | **cs7** } **pq shaping pir** { pir-value |**percentage** pir-percent } ，配置 PQ 队列调度参数。步骤 **4** 执行命令 **commit** ，提交配置。**----**结束##### 1.4.5.2 配置 WFQ 调度在低速接口的链路上，将不同业务流量分类到不同的队列进行加权公平队列调度。##### 背景信息在待配置路由器上进行以下配置。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **interface** interface-type interface-number ，进入接口视图。说明低速链路的队列调度只支持在 Serial 接口、 Trunk-Serial 接口、 Global-MP-Group 接口和 MPgroup 接口下配置。步骤 **3** 根据想要选择的 WFQ 队列调度模式分别执行以下步骤：       - 选择 Weight 调度模式时，进行以下配置：a. 执行命令 **set port-queue weight-mode** 命令，将接口的 WFQ 队列调度模式配置为 Weight 模式。b. 执行命令 **port-queue** { **cs7** | **cs6** | **ef** | **af4** | **af3** | **af2** | **af1** | **be** } { **wfq****weight** weight-value | **shaping** { shaping-value | **shaping-percentage**shaping-percentage-value } } [*] **outbound** ，配置接口出方向上端口队列的WFQ 调度参数，包括权重和整形速率。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 36HUAWEI NetEngine40E配置指南 1 QoS       - 选择 CIR 调度模式时，执行命令 **port-queue** { **cs6** | **ef** | **af4** | **af3** | **af2** | **af1** | **be** }**wfq** **shaping** **cir** { cir-value | **percentage** cir-percent } [ **pir** { pir-value |**percentage** pir-percent } ] ，配置 WFQ 队列调度参数。步骤 **4** 执行命令 **commit** ，提交配置。**----**结束##### 1.4.5.3 （可选）配置端口队列的缓存时间当有持续的流量过大情况时，进入端口队列的报文会产生拥塞并有较大的时延，此时可根据实际情况设置端口队列的缓存时间。通过配置较小的缓存时间，实现加快报文转发和减少拥塞时延的目的。##### 背景信息说明队列深度的缓存时间，以 64 字节的 PPP 报文为基准。在待配置路由器上进行以下配置。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **interface** interface-type interface-number ，进入接口视图。步骤 **3** 执行命令 **port-queue** { **be** | **af1** | **af2** | **af3** | **af4** | **ef** | **cs6** | **cs7** } **queue-limit** **max-****buffer-time** time-value ，配置端口队列的缓存时间。步骤 **4** 执行命令 **commit** ，提交配置。**----**结束##### 1.4.5.4 （可选）配置端口队列的动态调整当链路成员 down/up 时，可以根据物理带宽的变化动态调整 shaping 速率。##### 背景信息对于 Global MP/MP 接口，当 port-queue pq 命令和 port-queue wfq 命令将 CIR/PIR 的shaping 参数设置为百分比的情况下，设备会按照该百分比计算并下发 shaping 速率。而当链路成员发生 down/up 动作时，将会引起接口的物理带宽发生变化，此时若仍按之前的计算值下发 shaping 速率，会导致 shaping 的调度情况不能与现实情况匹配。配置该命令后，在物理带宽发生变化时，设备会按照 CIR/PIR 的 shaping 参数的百分比重新计算和下发 shaping 速率，实现对 shaping 速率进行动态调整。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **interface** interface-type interface-number ，进入接口视图。步骤 **3** 执行命令 **port-queue dynamic** ，配置在链路成员 down/up 时引起物理带宽变化的情况下，相应的动态调整 shaping 速率。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 37HUAWEI NetEngine40E配置指南 1 QoS步骤 **4** 执行命令 **commit** ，提交配置。**----**结束##### ******* （可选）配置队列调度的低时延震荡模式设置队列调度为低时延震荡模式，降低时延抖动。##### 背景信息MP 履行公网承载 TDM 业务时，由于 TDM 业务能承受的时延抖动小，需要设置队列调度为低时延震荡模式。在待配置 HQoS 功能的路由器上进行以下配置。系统缺省没有使能低时延震荡模式。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **interface** { interface-name | interface-type interface-number } ，进入Global-Mp-Group 接口视图。步骤 **3** 执行命令 **port-queue low-latency-mode** ，配置队列调度为低时延震荡模式。步骤 **4** 执行命令 **commit** ，提交配置。**----**结束##### 1.4.5.6 检查配置结果低速链路的端口队列调度配置成功后，可以查看端口队列的详细配置信息、统计信息。##### 操作步骤步骤 **1** 使用 **display ls-port-queue configuration** **interface** interface-type interfacenumber **outbound** 命令查看端口队列的详细配置信息。步骤 **2** 使用 **display ls-port-queue statistics** **interface** interface-type interface-number[ cos-value ] **outbound** 命令查看端口队列的统计信息。**----**结束#### 1.4.6 维护拥塞避免介绍如何清除拥塞避免的统计信息。##### 1.4.6.1 清空端口队列的统计信息介绍清空端口队列的统计信息的命令。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 38HUAWEI NetEngine40E配置指南 1 QoS##### 背景信息须知清除统计信息后，以前的统计信息将无法恢复，务必仔细确认。在确认需要清空端口队列的统计计数信息后，请在用户视图下执行下面的 **reset** 命令，清除之前的统计信息。##### 操作步骤       - 执行 **reset port-queue statistics** [ **slot** slot-id | **interface** { interface-name |interface-type interface-number } ] [ cos-value ] **outbound** [ **default** ] 命令清空指定接口下的端口队列统计数据。VS 模式下， **slot** 参数仅在 Admin VS 支持。       - 执行 **reset port-queue statistics slot** slot-id [ cos-value ] **outbound** **bind****mtunnel** 命令清空指定分布式组播 VPN 的 Mtunnel 接口所有端口队列或者某一具体优先级队列的统计计数。VS 模式下，该命令仅在 Admin VS 支持。**----**结束#### 1.4.7 配置举例从具体应用场景、配置命令等方面对拥塞避免、拥塞管理的应用进行了详细的描述。##### 1.4.7.1 配置拥塞管理及拥塞避免示例以特性的典型场景为例，介绍如何配置拥塞管理及拥塞避免。##### 组网需求如 图 **1-7** 所示， Server 、 Telephone 、 PC1 和 PC2 通过 DeviceA 向网络发送数据，其中Server 发送关键业务数据， Telephone 发送语音数据， PC1 和 PC2 发送非关键业务数据。由于 DeviceA 入接口 interface1 的速率大于出接口 interface2 的速率，在 interface2接口处可能发生拥塞，并且可能出现拥塞加剧现象。要求在网络拥塞时保证 Server 和 Telephone 发送的业务数据得到优先发送，且Telephone 可以保证 5Mbit/s 的带宽， Server 可以保证 4Mbit/s 的带宽。同时由于 PC1 和PC2 是 VIP 用户，其数据在发送的过程中也需要一定的带宽保证，可以有少量延迟，但不希望延迟过大。在拥塞加剧的时候需要根据优先级来丢弃报文。需要在 DeviceA 配置 WRED 来与 WFQ 配合调度和丢弃。说明本例中 interface1 ， interface2 分别代表 GE1/0/0 ， GE2/0/0 。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 39
最终截取: ##### ******* （可选）配置队列调度的低时延震荡模式


设置队列调度为低时延震荡模式，降低时延抖动。

##### 背景信息


MP 履行公网承载 TDM 业务时，由于 TDM 业务能承受的时延抖动小，需要设置队列调
度为低时延震荡模式。


在待配置 HQoS 功能的路由器上进行以下配置。


系统缺省没有使能低时延震荡模式。

##### 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **interface** { interface-name | interface-type interface-number } ，进入
Global-Mp-Group 接口视图。


步骤 **3** 执行命令 **port-queue low-latency-mode** ，配置队列调度为低时延震荡模式。


步骤 **4** 执行命令 **commit** ，提交配置。


**----**
结束

