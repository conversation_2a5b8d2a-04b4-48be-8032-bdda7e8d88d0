头标题: ******** 清空流量策略的统计信息
尾标题: 1.5.11 配置举例
内容: HUAWEI NetEngine40E配置指南 1 QoS|特性限制|系列|涉及产品||---|---|---||接口复杂流分类支持在ACL IPv6高级模板下配置hoport<br>规则，匹配IPv6报文中Hop-by-Hop扩展头 ，命中规则<br>后做通过或丢弃动作。在同时配置了QPPB、UCL或<br>PUPP任一功能时，会导致接口复杂流匹配IPv6报文中<br>Hop-by-Hop扩展头功能失效，带Hop-by-Hop扩展头<br>的报文默认会被上送CPU，配置的流动作（通过或丢弃<br>动作）不生效。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||LPUF-50/LPUF-50-L/LPUI-21-L/LPUI-51-L/LPUF-51/<br>LPUF-51-B/LPUI-51/LPUI-51-B/LPUS-51/LPUF-101/<br>LPUF-101-B/LPUI-101/LPUI-101-B/LPUS-101单板复<br>杂流分类不支持匹配IPv6逐跳扩展头字段。<br>当流分类策略中配置的规则内容中含有IPv6逐跳扩展<br>头内容时，整条规则不生效。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A||全局复杂流不支持outbound方向同时匹配源和目的<br>qos-local-id。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||流量入SRv6隧道/VXLAN隧道/MPLS LDP场景中，在出<br>隧道节点的入接口应用复杂流分类策略，如果流策略中<br>配置了匹配隧道头字段，此时复杂流分类可以对出隧道<br>的流量生效。当在出隧道节点入接口再配置QPPB策略<br>后，流分类无法再匹配隧道头字段，复杂流分类功能失<br>效。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 60HUAWEI NetEngine40E配置指南 1 QoS|特性限制|系列|涉及产品||---|---|---||全局复杂流支持源和目的qos-local-id重定向不支持<br>LPUF-50/LPUF-50-L/LPUI-21-L/LPUI-51-L/LPUF-51/<br>LPUF-51-B/LPUI-51/LPUI-51-B/LPUS-51/LPUF-101/<br>LPUF-101-B/LPUI-101/LPUI-101-B/LPUS-101单板。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||以下业务场景，下行报文环回时复杂流分类或QPPB策<br>略无法匹配QOS-LOCAL-ID：<br>1、业务入GRE隧道；<br>2、业务入VXLAN隧道；<br>3、业务入SRv6隧道时以下9种场景：<br>3.1、List只有一层并且第一个SID是本地End.X；<br>3.2、第一个SID是Binding SID或本地End SID；<br>3.3、第一个SID是Shortcut入Policy或Flow Group；<br>3.4、L3VPNv4/L3VPNv6 over SRv6 BE，EVPN<br>L3VPNv4/L3VPNv6 over SRv6，公网IPv4/IPv6 over<br>SRv6 BE，BE首节点locator路由Shortcut到Policy。<br>3.5、第一跳SID路由不可达；<br>3.6、正切的备链路是TILFA；<br>3.7、正切防微环生效期间；<br>3.8、回切防微环生效期间；<br>3.9、触发TEFRR期间；<br>4、业务部署在VE口；<br>5、业务入4over6隧道；<br>6、业务入L2tp隧道。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||VLANIF接口下行方向应用的复杂流策略中的IPv6规则<br>对LPUF-50/LPUF-50-L/LPUI-21-L/LPUI-51-L/<br>LPUF-51/LPUF-51-B/LPUI-51/LPUI-51-B/LPUS-51/<br>LPUF-101/LPUF-101-B/LPUI-101/LPUI-101-B/<br>LPUS-101单板上接口发送的报文不生效。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 61HUAWEI NetEngine40E配置指南 1 QoS|特性限制|系列|涉及产品||---|---|---||配置trafc protocol-protect { ipv4 | ipv6 } enable后，<br>目的地址是本机IP的报文命中接口ACL或全局ACL的IP<br>层规则时， 不做deny动作，但使用display trafc<br>policy statistics查询复杂流统计时dropped统计项有统<br>计。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||配置trafc protocol-protect { ipv4 | ipv6 } enable命令<br>后，本机终结的隧道（6over4隧道或VxLAN隧道）报<br>文，命中接口ACL或全局ACL的IP层规则后报文仍正常<br>转发，即使流分类中配置了导致报文丢弃（deny、<br>car、urpf）或重定向的动作也不会执行。<br>建议在6over4隧道或VxLAN隧道终结设备上谨慎配置<br>trafc protocol-protect { ipv4 | ipv6 } enable命令。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||复杂流策略配置if-match ofset规则后，仅支持通过<br>display trafc policy statistics verbose rule-based命<br>令基于rule查询统计结果，不支持通过display trafc<br>policy statistics [ verbose classifer-based ]命令基于<br>policy和基于classifer进行查询统计结果。<br>基于policy或classifer查询统计时，匹配ofset规则的流<br>量不会被计算在内。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||复杂流分类CU分离灾备场景，CP主备切换后，UP上的<br>复杂流分类业务会出现短暂失效，统计清零，重新开始<br>计数。为避免此类情况，需保证主备CP配置流分类模<br>板顺序一致。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 62HUAWEI NetEngine40E配置指南 1 QoS|特性限制|系列|涉及产品||---|---|---||1、当流策略为共享模式时，不支持进行Telemetry条件<br>采集。<br>2、如下两类策略，最快采样周期只能达到1秒，即使配<br>置成100ms周期，上送周期也会不会快于1秒：<br>a、包含if-match any规则的策略；<br>b、多NP单板上的共享策略。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||复杂流分类策略中增量配置规则超过ACL容量后，对已<br>下发硬件的规则，继续生效，对未下发过硬件的规则不<br>生效。当单板重启后，该复杂流分类策略下所有的规则<br>均不生效。请在配置前，检查设备上ACL的剩余容量，<br>保障新增配置的ACL容量在业务允许的ACL容量范围<br>内。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||1、只有vlan-stacking接口允许配置trafc-policy不指<br>定vlan，其他二层口只允许配置trafc-policy指定<br>vlan。<br>2、vlan-stacking接口下配置了trafc-policy，删除<br>vlan-stacking，修改为其他二层口的配置，此时不校<br>验，trafc-policy的生效行为是：<br>1）带vlan的trafc-policy，对指定vlan的报文生效。<br>2）不带vlan的trafc-policy，对不带vlan的报文生效。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||下行方向流策略仅能应用在主接口。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 63HUAWEI NetEngine40E配置指南 1 QoS|特性限制|系列|涉及产品||---|---|---||在接口下应用复杂流分类后，仅支持将用户类型配置为<br>三层专线用户。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||BAS接口的接入类型为二层普通用户、三层普通用户、<br>二层VPN专线用户、二层专线用户时，复杂流分类策略<br>可以应用但功能不生效。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||IPv6 GRE的tunnel接口下的复杂流功能，仅支持ip-<br>layer模式（默认模式），配置Link-layer或者All-layer<br>不能匹配二层规则，配置mpls-layer不能匹配Mpls规<br>则。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||入GRE隧道场景下，在GRE Tunnel口inbound方向应用<br>复杂流不生效。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 64HUAWEI NetEngine40E配置指南 1 QoS|特性限制|系列|涉及产品||---|---|---||设备作为VXLAN的起点设备时，当配置上行方向的全局<br>ACL的规则内容不显式指定VXLAN知名端口号时，对经<br>本设备且走VLXAN隧道的流量，全局ACL的统计计数会<br>翻倍。<br>建议配置规则区分VXLAN知名端口号和非VXLAN知名<br>端口号放置到不同的Classifer模板中，各自Classifer模<br>板绑定对应的动作。此规避方案存在配置量会翻倍的问<br>题，硬件TCAM资源占用也同样翻倍。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||在MPLS网络的入PE的出方向和出PE的入方向，UCL规<br>则无法匹配MPLS报文内的用户IP。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||AAA domain下配置trafc-policy ucl upstream<br>match-source-ip后，对已上线用户不生效，仅对新上<br>线用户生效。<br>新用户上线后，以下特性因依赖user-group，功能不生<br>效：<br>分布式NAT、分布式Ds-lite、SA-NAT业务链、WEB重<br>定向、DAA。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||对于三层专线用户，如果在系统视图下应用的流策略中<br>配置了匹配源IP地址和目的IP地址的UCL规则，当对该<br>用户再应用EDSG业务时，原来配置的UCL规则会失<br>效。<br>建议若还想对该三层专线用户匹配网络侧到网络侧的流<br>量，可以将配置了匹配源IP地址和目的IP地址的ACL规<br>则的流量策略应用在接口视图下。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 65HUAWEI NetEngine40E配置指南 1 QoS|特性限制|系列|涉及产品||---|---|---||CU分离灾备场景下，主备CP倒换期间：<br>1、UCL特性（64K）含流分类的动作、tarif-level动<br>作，失效时间约160秒。<br>2、user-vlan CAR、接口CAR特性（64K）失效时间约2<br>分钟。<br>3、CGN业务的NAT引流动作，满规格（64个CGN业<br>务），错误引流约1秒。<br>建议主备CP配置复杂流模板的顺序保持一致。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||全局UCL场景不支持流采样，如果全局UCL应用的流策<br>略中包含流采样动作的话，该动作不生效。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||对于LPUF-51-E/LPUI-51-E/LPUI-51-S/LPUF-120/<br>LPUF-120-B/LPUF-120-E/LPUI-102-E/LPUI-120/<br>LPUI-120-B/LPUI-120-L/LPUI-52-E/LPUI-120-E/<br>LPUF-240/LPUF-240-B/LPUF-240-E/LPUI-240/<br>LPUI-240-B/LPUI-240-L/LPUF-480/LPUF-480-B/<br>LPUI-480/LPUI-480-B/LPUI-480-L/LPUF-480-E/<br>LPUI-480-CM/LPUF-400-E和LPUI-200/LPUI-200-L/<br>LPUF-200/LPUF-200-B/LPUI-1T/LPUI-1T-B/LPUI-1T-<br>L/LPUI-241/LPUI-241-B/LPUI-241-CM/LPUF-241/<br>LPUF-241-E/LPUI-480-CM/LPUF-52/LPUI-245A-CM/<br>LPUI-245-CM/LPUF-200A-E/LPUF-200-E/LPUF-240/<br>LPUF-240A-E/LPUF-240-E/LPUF-245A/LPUF-245A-E/<br>LPUI-240/LPUI-480/LPUF-480/LPUF-485A/LPUF-55A/<br>LPUF-400-E/LPUF-480-E/LPUF-485A-E/LPUF-245/<br>LPUF-245-E/LPUF-485/LPUF-485-E/LPUF-485-CM/<br>LPUI-240-CM/LPUF-241-E/LPUI-480-CM单板，IPv6<br>UCL只支持匹配前缀模式IPv6地址，当Slot视图下配置<br>trafc-policy ipv6-address-rule match mask命令行时<br>对匹配前缀模式IPv6地址的IPv6 UCL不生效。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 66HUAWEI NetEngine40E配置指南 1 QoS|特性限制|系列|涉及产品||---|---|---||bras每用户每策略（PUPP）特性，策略规则内容中含<br>有如下任意类型时，规则的处理方式如下所述：<br>1、规则内容中包含：IPv4 TTL字段、VPN-instance、<br>IPv6分片<br>处理方式：允许命令行配置，但整条规则不生效。<br>2、规则内容中包含：IPv6 TOS、IPv6 TCP-Flag<br>处理方式：允许命令行配置。该类型字段配置不生效，<br>规则的其它类型字段可以生效。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||bras每用户每策略（PUPP）特性，策略中只对<br>advanced类型IPv4、IPv6规则生效。非advanced类型<br>的IPv4、IPv6规则允许用户配置，但不会生效。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||bras每用户每策略（PUPP）特性，允许策略中配置and<br>模式的classifer，但不生效。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||bras每用户每策略（PUPP）特性，策略行为模板中配<br>置permit\deny\remark\match termination外的动作，<br>允许配置，但不生效。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 67HUAWEI NetEngine40E配置指南 1 QoS|特性限制|系列|涉及产品||---|---|---||Vlanif接口下行ACL采样会覆盖入方向的接口采样和<br>ACL采样|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A||VLANIF接口下行复杂流不支持匹配IPv6规则。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||1、VLANIF入口流量：<br>无论是否使能QPPB，VLANIF入口和其成员口入口同时<br>配置trafc policy时，成员口的流分类策略不生效。流<br>量先后匹配bgpfow策略、全局trafc policy（ACL）、<br>VLANIF口的trafc policy、上行QPPB policy，策略间<br>动作若相同，以后者生效，若不同，均生效。<br>2、VLANIF出口流量：<br>（1）若使能了上行QPPB，VLANIF出口和流量设备入<br>口同时配置trafc policy时，流量设备入口的流分类策<br>略不生效。流量先后匹配bgpfow策略、全局trafc<br>policy（ACL）、VLANIF口的trafc policy、上行QPPB<br>policy，策略间动作若相同，以后者生效，若不同，均<br>生效。<br>（2）若未使能上行QPPB，VLANIF出口和流量设备入<br>口同时配置trafc policy时，两者均生效。流量先后匹<br>配bgpfow策略、全局trafc policy（ACL）、流量设备<br>入口trafc policy、VLANIF口的trafc policy、上行<br>QPPB policy，策略间动作若相同，以后者生效，若不<br>同，均生效。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 68HUAWEI NetEngine40E配置指南 1 QoS|特性限制|系列|涉及产品||---|---|---||ARP-MAC联动功能关闭后，VLANIF接口下行复杂流功<br>能将会失效。（ARP-MAC联动功能默认开启）。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||接口应用级联复杂流分类策略时，若多个流策略中同时<br>配置了采样动作，则流行为中指定的policy对应的采样<br>动作会覆盖流策略中直接配置的采样动作。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A||1个接口应用多级复杂流分类策略，增量添加规则超过<br>ACL容量上限后，超过部分整体不生效，上报超限告<br>警。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A||流行为视图下执行命令trafc-policy命令配置级联流策<br>略并使能匹配SRv6内层报文，如果SRv6转发场景配置<br>了本地End SID、只有一层SID的本地End.X、本地<br>BSID，TE FRR、TI-LFA、防微环业务或者远端路由撤<br>销瞬间，入PE下行匹配内层报文功能不生效，不会执行<br>对应复杂流动作。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 69HUAWEI NetEngine40E配置指南 1 QoS|特性限制|系列|涉及产品||---|---|---||L3VPN接入SRv6 TE Policy场景下，出SRv6隧道上行，<br>L3VPN视图应用复杂流分类策略后，不支持ACL匹配<br>SRv6隧道头内部的用户IP地址。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||流行为视图下执行命令trafc-policy <policy-name><br>[ ip-layer srv6-inner | link-layer srv6-inner ]配置级联<br>流策略并使能匹配SRv6内层报文，该级联策略的流动<br>作中配置remark动作，remark动作不对隧道内层的报<br>文生效，对外层SRv6隧道报文头生效。针对DSCP的<br>remark动作，根据级联的子策略的规则类型来配置，<br>即IPv4的规则配置remark dscp，IPv6的规则配置<br>remark ipv6 dscp。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||VLANIF/VBDIF接口下行应用复杂流分类策略，策略在<br>流量上行入口所在单板生效。<br>1） 上行入口若配置了复杂流分类，且流动作为丢弃<br>时，流量不会再命中VLANIF/VBDIF下行流分类策略。<br>2） 上行入口若配置了复杂流分类，流动作为CAR，<br>VLANIF/VBDIF接口下行也配置了复杂流分类，流动作<br>也配置CAR时，流量先做VLANIF/VBDIF接口的策略<br>CAR，再做上行入口的策略CAR。VLANIF/VBDIF接口<br>下行流策略的流量匹配统计计数为上行入口策略CAR前<br>的流量计数。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK|#### 1.5.3 配置 IP 报文复杂流分类的流量策略介绍 IP 报文复杂流分类的应用场景和配置过程。##### 应用环境随着网络的普及，网络中的业务越来越多样化，多种业务流共享同一网络资源。如果需要对进入网络的流量或网络中的流量进行分类管理和限制（例如对语音、视频、数据等业务分别对待，分配不同的带宽、保证不同的时延；对来自不同用户的流量分别对待，保证不同的带宽和优先级），简单流分类的流量策略就难以满足需求。此时，可以配置复杂流分类的流量策略。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 70HUAWEI NetEngine40E配置指南 1 QoS基于 IP 报文复杂流分类的流量策略支持根据报文的 DSCP 值、协议类型、 IP 地址、端口号等参数对不同分类的业务提供差别服务，以满足不同业务对网络带宽、时延等的应用需求。通常是在相对边界的路由器上配置复杂流分类的流量策略，在相对核心的路由器上配置简单流分类的流量策略。##### 前置任务在配置 IP 报文复杂流分类的流量策略之前，需要完成以下任务：       - 配置相关接口的物理参数，保证物理链路正常工作       - 配置相关接口的链路层属性，保证接口的正常工作       - 配置相关接口的 IP 地址       - 配置使能路由协议，实现网络层互通##### ******* 定义流分类要对网络中的流量进行基于类的 QoS 配置，就需要先定义流分类，流量的分类可以按照 ACL 规则、报文优先级、 MAC 地址、协议地址等进行定义。##### 操作步骤       - 定义基于三层 / 四层信息的流分类a. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **traffic classifier** classifier-name [ **operator** { **and** | **or** } ] ，定义流分类并进入流分类视图。如果在一个流分类中配置了多个匹配规则，可以通过指定参数 operator 来设置这些规则之间的关系，其中：#### ▪ and ：指定类下的规则之间是逻辑“与”的关系，即数据包必须匹配全部规则才属于该类。#### ▪ or ：指定类下的规则之间是逻辑“或”的关系，即数据包只要匹配其中任何一个规则就属于该类。c. 请根据实际情况对流分类的匹配规则进行定义。说明如果为 IPv6 报文，在步骤 3 中选择匹配规则时，请指定关键字 **ipv6** 。其中定义基于源、目的 IP 地址的匹配规则只适用于 IPv6 报文， IPv4 报文不支持。ACL 匹配规则可以根据用户的不同需求定义不同的访问控制列表，包括协议类型、源地址、目的地址或则报文中的优先级字段等。 **if-match acl** 命令根据 **rule** 命令定义的ACL 规则过滤报文，然后进行对应的流行为。#### ▪ 如果定义 ACL 匹配规则，执行命令 if-match [ ipv6 ] acl { acl-number |**name** acl-name } [ **precedence** precedence-value ] 。#### ▪ 如果定义 DSCP 匹配规则，执行命令 if-match [ ipv6 ] dscp dscpvalue 。#### ▪ 如果定义 IPv4 TCP Flag 匹配规则，执行命令 if-match tcp syn-flag{ tcpflag-value [ **mask** tcpflag-mask ] | **bit-match** { **established** | **fin**| **syn** | **rst** | **psh** | **ack** | **urg** | **ece** | **cwr** | **ns** } } 。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 71HUAWEI NetEngine40E配置指南 1 QoS#### ▪ 如果定义 IPv6 TCP Flag 匹配规则，执行命令 if-match ipv6 tcp syn-flag{ tcpflag-value-ipv6 [ **mask** tcpflag-mask-ipv6 ] | **bit-match**{ **established** | **fin** | **syn** | **rst** | **psh** | **ack** | **urg** } } 。#### ▪ 如果定义 IP 报文优先级的匹配规则，执行命令 if-match [ ipv6 ] ip**precedence** ip-precedence 。#### ▪ 如果定义基于 MPLS EXP 值的匹配规则，执行命令 if-match mpls-expexp-value 。#### ▪ 如果定义匹配所有数据包的规则，执行命令 if-match [ ipv6 ] any 。 ▪ 如果定义匹配基于 IPv6 下一报文头值的规则，执行命令 if-match ipv6**next-header** header-number **first-next-header** 。#### ▪ 如果定义 IPv6 报文源 IP 地址的匹配规则，执行命令 if-match ipv6**source-address** ipv6-address prefix-length 。#### ▪ 如果定义 IPv6 报文目的 IP 地址的匹配规则，执行命令 if-match ipv6**destination-address** ipv6-address prefix-length 。#### ▪ 如果定义基于 QoS 策略 ID 的匹配规则，执行命令 if-match qos-local-idqos-local-id 。#### ▪ 如果定义基于源和目的 QoS 策略 ID 的匹配规则，执行命令 if-match qos**local-id** **source** source-qos-local-id **destination** destination-qoslocal-id 。#### ▪ 如果定义基于 IPv6 QoS 策略 ID 的匹配规则，执行命令 if-match ipv6**qos-local-id** qos-local-id 。d. 执行命令 **commit** ，提交配置。       - 定义基于二层信息的流分类a. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **traffic classifier** classifier-name [ **operator** { **and** | **or** } ] ，定义流分类并进入流分类视图。如果在一个流分类中配置了多个匹配规则，可以通过在指定参数 operator 来设置这些规则之间的关系，详细请参见上节描述。c. 请根据实际情况对路由器的匹配规则进行定义。#### ▪ 如果定义 ACL 匹配规则，执行命令 if-match acl { acl-number | nameacl-name } [ **precedence** precedence-value ] 。#### ▪ 如果定义 VLAN 报文的 8021p 匹配规则，执行命令 if-match 8021p8021p-value 。#### ▪ 如果定义服务等级的匹配规则，执行命令 if-match service-classservice-class-value 。#### ▪ 如果定义报文源 MAC 地址的匹配规则，执行命令 if-match source-macmac-address 。#### ▪ 如果定义报文目的 MAC 地址的匹配规则，执行命令 if-match**destination-mac** mac-address 。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 72HUAWEI NetEngine40E配置指南 1 QoS#### ▪ 如果定义报文 VLAN 的匹配规则，执行命令 if-match vlan vlan-id[ **cvlan** ce-vlan-id ] 。#### ▪ 如果定义报文偏移量的匹配规则，执行命令 if-match { offset offsetvalue **match-value** match-value **match-mask** match-mask }&<1-4> 。偏移量匹配规则对转发性能有影响，请谨慎使用，使用前请详细阅读 **if-****match** **offset** 命令的描述了解相关注意事项。如果在一个流量策略中配置了多个流分类规则，这些流分类对应的流行为执行的先后顺序是不同的。#### ▪ 各个流分类规则匹配不同的 IP 报文字段时，在未指定流分类优先级的情况下，流策略中先配置的流分类规则优先生效。例如：如 表 **1-6** 所示， Policy1 中先后绑定了 2 个流分类规则和对应的流行为。如果报文同时满足了两个流分类规则，则报文最终执行 behavior1 的动作， remark 8021p 标记为 1 。表 **1-6** Policy1 中定义的流分类和流行为|流分类|流分类定义的匹配<br>规则|流行为|流行为对应的动<br>作||---|---|---|---||classifer1|匹配目的MAC|behavior1|remark 8021p为<br>1||classifer3|匹配源MAC|behavior3|remark 8021p为<br>3|#### ▪ 多个流分类规则匹配相同的 IP 报文字段时，报文会执行匹配的流分类规则对应的流行为。例如：如 表 **2 Policy2** 中定义的流分类和流行为 所示， Policy2 中先后绑定了 3 个流分类规则和对应的流行为。若报文目的 MAC 为 2–2–2 ，则报文会执行 behavior2 的动作： remark 8021p 为 2 。表 **1-7** Policy2 中定义的流分类和流行为|流分类|流分类定义的匹配<br>规则|流行为|流行为对应的动<br>作||---|---|---|---||classifer1|匹配目的MAC<br>1-1-1|behavior1|remark 8021p为<br>1||classifer2|匹配目的MAC<br>2-2-2|behavior2|remark 8021p为<br>2|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 73HUAWEI NetEngine40E配置指南 1 QoS|流分类|流分类定义的匹配<br>规则|流行为|流行为对应的动<br>作||---|---|---|---||classifer3|匹配目的MAC<br>3-3-3|behavior3|remark 8021p为<br>3|d. 执行命令 **commit** ，提交配置。**----**结束##### ******* 定义流行为并配置动作介绍设备支持的流行为及如何配置。##### 背景信息##### 操作步骤设备支持设置的流行为动作类型比较丰富，可以根据实际需要选择下面的一种或多种。- 执行命令 **system-view** ，进入系统视图。- 执行命令 **traffic behavior** behavior-name ，定义流行为并进入流行为视图。- 配置报文过滤动作a. 执行命令 **permit | deny** ，允许 / 禁止报文的通过。说明当用户同时配置 **if-match any** 和 **deny** 时，复杂流分类将会禁止流经某个接口的所有流量通过，包括协议报文，因此请用户慎重进行上述流分类和流行为的组合配置。当 **rule** 命令和流行为视图下同时配置了 **permit** 或 **deny** 动作时，只有 **rule** 命令允许通过的报文才会进行流行为的处理。只要 rule 命令或流行为视图中的任意一个配了 deny动作，匹配规则的报文都会被丢弃。b. 执行命令 **commit** ，提交配置。- 配置流量监管动作详细配置请参考流量监管和流量整形配置章节的 **1.3.5.2** 定义流行为并配置监管动作 。- 配置广播、组播、未知单播报文的流量抑制a. 执行命令 **broadcast-suppression** **cir** cir-value [ **cbs** cbs-value ] [ **green**{ **discard** | **pass** [ **service-class** class **color** { **green** | **yellow** | **red** } ] } |**red** { **discard** | **pass** [ **service-class** class **color** { **green** | **yellow** |**red** } ] } ] [*] ，配置限制广播报文的速率。b. 执行命令 **multicast-suppression** **cir** cir-value [ **cbs** cbs-value ] [ **green**{ **discard** | **pass** [ **service-class** class **color** { **green** | **yellow** | **red** } ] } |**red** { **discard** | **pass** [ **service-class** class **color** { **green** | **yellow** |**red** } ] } ] [*] ，配置限制组播报文的速率。c. 执行命令 **unknown-unicast-suppression** **cir** cir-value [ **cbs** cbs-value ][ **green** { **discard** | **pass** [ **service-class** class **color** { **green** | **yellow** |**red** } ] } | **red** { **discard** | **pass** [ **service-class** class **color** { **green** | **yellow**| **red** } ] } ] [*] ，配置限制未知单播报文的速率。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 74HUAWEI NetEngine40E配置指南 1 QoSd. 执行命令 **commit** ，提交配置。配置了流量抑制动作的流行为，可以应用到接口的入方向或出方向。接口上配置了流量抑制后，匹配规则的流量会按照该命令中的参数做流量抑制，接口上不匹配规则的流量正常转发。       - 配置强制流分类a. 执行命令 **service-class** service-class **color** color ，配置指定服务等级的报文标记颜色。b. （可选）执行命令 **service-class** service-class **color** color **track** { **master** |**slave** } **bfd-session** **session-name** bfd-session-name ，配置依据指定的BFD 的会话状态来标记匹配流策略后的报文服务等级和颜色。c. 执行命令 **commit** ，提交配置。       - 配置智能流的服务等级及颜色a. 执行命令 **intelligent-flow elephant-flow-burst** elephant-flow-burst-privalue **color** color-value **elephant-flow-steady** elephant-flow-steady-privalue **color** color-value **other-flow** other-flow-pri-value **color** color-value[ **no-remark** ] ，为突发大流、平稳大流及小流设置服务等级和颜色。说明在配置本命令前，请先在槽位视图下配置 **intelligent-flow enable slot** 命令，使能单板的智能流分类功能，然后将配置了本命令的流行为绑定到具体流策略中，并将该流策略应用到具体接口下，以达到完整实现降低大流优先级的目的。       - 配置重标记报文a. IP 报文支持多种重标记动作，请根据网络中业务需要选择下表中对应的配置。表 **1-8** IP 报文重标记配置|业务需求|配置命令||---|---||需要对报文原始优先级进行修改|**remark ip-precedence** ip-<br>precedence||需要对报文携带的DSCP值进行修改|**remark** [**ipv6** ]**dscp** dscp-value||需要对报文携带的ToS值进行修改|**remark tos** tos||需要设置报文是否可分片|**remark ip-df** dfvalue||配置智能策略路由引流中，标识一<br>条流的TE Class ID|**remark te-class** classid||配置报文的qos-local-id|**remark qos-local-id**qos-local-id-<br>value||配置通过ACL匹配五元组方式进行切<br>片Slice-ID分配|**remark network-slice** sliceId||需要修改报文的ttl值|**remark ttl** ttl-value||需要在报文中标记IPv6 APN信息|**remark apn-id-ipv6** **instance**<br>instance-name|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 75HUAWEI NetEngine40E配置指南 1 QoSb. 执行命令 **commit** ，提交配置。       - 配置报文的重定向动作a. IP 报文支持多种重定向动作，请根据网络中的报文类型及业务场景选择下表中对应的配置。表 **1-9** IP 报文重定向配置|报文<br>类型|重定向场景|配置命令||---|---|---||IPv4<br>报文|配置IPv4报文单个重定<br>向的下一跳的IP地址和<br>出接口、VPN实例、<br>NQA测试例。|●**redirect ip-nexthop** ip-address<br>**interface** interface-type interface-<br>number [**route-forward** ] [**pri-type**<br>**common** ]<br>●**redirect ip-nexthop** ip-address**nqa**<br>nqa-test-administer-name name-of-<br>nqa-test-instance [**routing-flter**<br>{ **default-routing** |**blackhole-**<br>**routing** } * ] [**deny** ] [**pri-type**<br>**common** ] [**public-network** ]<br>●**redirect ip-nexthop** ip-address**vpn**<br>vpn-instance-name [**nqa** nqa-test-<br>administer-name name-of-nqa-test-<br>instance ] [**routing-flter** {**default-**<br>**routing** |**blackhole-routing** } * ]<br>[ **pri-type common** ]||IPv4<br>报文|在重定向单下一跳场景<br>下，报文重定向下一跳<br>的IP地址匹配到缺省路<br>由或者黑洞路由时，配<br>置报文使用携带的目的<br>IP地址进行转发。|**redirect ip-nexthop** ip-address<br>[ **routing-flter** {**default-routing** |<br>**blackhole-routing** } * ] [**deny** ] [**pri-**<br>**type common** ] [**public-network** ]|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 76HUAWEI NetEngine40E配置指南 1 QoS|报文<br>类型|重定向场景|配置命令||---|---|---|||配置IPv4报文多个重定<br>向的出接口及其对应的<br>下一跳、VPN实例、<br>NQA测试例。|●**redirect ipv4-multinhp** {**nhp** ip-<br>address**interface** interface-type<br>interface-number } &2-42<br>[ **loadbalance** [**sip-hash** ]<br>[ **unequal-cost | ecmp-stateful**] ]<br>[ **route-forward** ] [**pri-type**<br>**common** ]<br>●**redirect ipv4-multinhp** {**nhp** ip-<br>address**nqa** nqa-test-administer-<br>name name-of-nqa-test-instance }<br>&2-42 [**routing-flter** {**default-**<br>**routing** |**blackhole-routing** } * ]<br>[ **deny** ] [**pri-type common** ]<br>●**redirect ipv4-multinhp** {**nhp** ip-<br>address**vpn** vpn-instance-name<br>[ **nqa** nqa-test-administer-name<br>name-of-nqa-test-instance ] } &2-42<br>[ **routing-flter** {**default-routing** |<br>**blackhole-routing** } * ] [**pri-type**<br>**common** ] [**non-revertive** ]|||配置IPv4报文直接重定<br>向到指定的出接口。|**redirect to interface** { interface-name |<br>interface-type interface-number }<br>[ **route-forward** ]|||在重定向多下一跳场景<br>下，报文重定向下一跳<br>的IP地址匹配到缺省路<br>由或者黑洞路由时，配<br>置报文使用携带的目的<br>IP地址进行转发。|**redirect ipv4-multinhp** {**nhp** ip-<br>address } &2-42 [**routing-flter**<br>{ **default-routing** |**blackhole-**<br>**routing** } * ] [**deny** ] [**pri-type**<br>**common** ]||IPv4<br>报文|配置IPv4报文重定向到<br>IPv6单下一跳。|●**redirect ipv4-to-ipv6 nexthop** ipv6-<br>address [**vpn** vpn-name ] [**nqa**<br>nqa-test-administer-name name-of-<br>nqa-test-instance ] [**routing-flter**<br>{ **default-routing** |**blackhole-**<br>**routing** } * ] [**deny** ] [**pri-type**<br>**common** ]<br>●**redirect ipv4-to-ipv6 nexthop**<br><ipv6-address>**interface** { interface-<br>name | interface-type interface-<br>number } [**route-forward** ] [**pri-**<br>**type common** ]|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 77HUAWEI NetEngine40E配置指南 1 QoS|报文<br>类型|重定向场景|配置命令||---|---|---||IPv4<br>报文|配置IPv4报文重定向到<br>IPv6多下一跳。|●仅配置多下一跳IPv6地址，请执行命令<br>**redirect ipv4-to-ipv6 multinhp**<br>{ **nhp** ipv6-address } &<2-16><br>[** routing-flter** {**default-routing** |<br>**blackhole-routing** } * ] [**deny** ]<br>[ **pri-type common** ] [**non-**<br>**revertive** ]<br>●配置多下一跳IPv6地址并绑定NQA实<br>例，请执行命令**redirect ipv4-to-ipv6**<br>**multinhp** {**nhp** ipv6-address**nqa**<br>nqa-test-administer-name name-of-<br>nqa-test-instance } &<2-16><br>[** routing-flter** {**default-routing** |<br>**blackhole-routing** } * ] [**deny** ]<br>[ **pri-type common** ] [**non-**<br>**revertive** ]<br>●配置多下一跳IPv6地址并绑定VPN实<br>例，请执行命令**redirect ipv4-to-ipv6**<br>**multinhp** {**nhp** ipv6-address**vpn**<br>vpn-name } &<2-16> [** routing-flter**<br>{ **default-routing** |**blackhole-**<br>**routing** } * ] [**deny** ] [**pri-type**<br>**common** ] [**non-revertive** ]<br>●配置多下一跳IPv6地址并绑定VPN实例<br>和NQA实例，请执行命令**redirect**<br>**ipv4-to-ipv6 multinhp** {**nhp** ipv6-<br>address**vpn** vpn-name**nqa** nqa-<br>test-administer-name name-of-nqa-<br>test-instance } &<2-16> [** routing-**<br>**flter** {**default-routing** |**blackhole-**<br>**routing** } * ] [**deny** ] [**pri-type**<br>**common** ] [**non-revertive** ]<br>●配置多下一跳IPv6地址和出接口，请执<br>行命令**redirect ipv4-to-ipv6**<br>**multinhp** {**nhp** <ipv6-address><br>**interface** { interface-name |<br>interface-type interface-number } }<br>&<2-16> [**loadbalance** [**sip-hash** ]<br>[ **unequal-cost** |**ecmp-stateful** ] ]<br>[** route-forward** ] [**pri-type**<br>**common** ]|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 78HUAWEI NetEngine40E配置指南 1 QoS|报文<br>类型|重定向场景|配置命令||---|---|---||IPv6<br>报文|配置IPv6报文单个重定<br>向的下一跳的IPv6地址<br>和出接口、VPN实例、<br>NQA测试例。|●**redirect ipv6-nexthop** ipv6-address<br>**interface** interface-type interface-<br>number [**route-forward** ] [**pri-type**<br>**common** ]<br>●**redirect ipv6-nexthop** ipv6-address<br>**nqa** nqa-test-administer-name<br>name-of-nqa-test-instance<br>[ **routing-flter** {**default-routing** |<br>**blackhole-routing** } * ] [**pri-type**<br>**common** ]<br>●**redirect ipv6-nexthop** ipv6-address<br>**vpn** vpn-instance-name [**nqa** nqa-<br>test-administer-name name-of-nqa-<br>test-instance ] [**routing-flter**<br>{ **default-routing** |**blackhole-**<br>**routing** } * ] [**pri-type common** ]||IPv6<br>报文|在重定向单下一跳场景<br>下，报文重定向下一跳<br>的IPv6地址匹配到缺省<br>路由或者黑洞路由时，<br>配置报文使用携带的目<br>的IPv6地址进行转发。|●**redirect ipv6-nexthop** ip-address<br>[ **routing-flter** {**default-routing** |<br>**blackhole-routing** } * ] [**deny** ]<br>[ **pri-type common** ] [**public-**<br>**network** ]||IPv6<br>报文|配置IPv6报文多个重定<br>向的出接口及其对应的<br>下一跳、VPN实例、<br>NQA测试例。|●**redirect ipv6-multinhp** {**nhp** ipv6-<br>address**interface** interface-type<br>interface-number } &2-16<br>[ **loadbalance** [**sip-hash** ]<br>[ **unequal-cost | ecmp-stateful**] ]<br>[ **route-forward** ] [**pri-type**<br>**common** ]<br>●**redirect ipv6-multinhp** {**nhp** ipv6-<br>address**nqa** nqa-test-administer-<br>name name-of-nqa-test-instance }<br>&2-16 [**routing-flter** {**default-**<br>**routing** |**blackhole-routing** } * ]<br>[ **pri-type common** ]<br>●**redirect ipv6-multinhp** {**nhp** ipv6-<br>address**vpn** vpn-instance-name<br>[ **nqa** nqa-test-administer-name<br>name-of-nqa-test-instance ] } &2-16<br>[ **routing-flter** {**default-routing** |<br>**blackhole-routing** } * ] [**pri-type**<br>**common** ]|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 79HUAWEI NetEngine40E配置指南 1 QoS|报文<br>类型|重定向场景|配置命令||---|---|---|||在重定向多下一跳场景<br>下，报文重定向下一跳<br>的IPv6地址匹配到缺省<br>路由或者黑洞路由时，<br>配置报文使用携带的目<br>的IPv6地址进行转发。|**redirect ipv6-multinhp** **nhp** ipv6-<br>address [**routing-flter** {**default-**<br>**routing** |**blackhole-routing** } * ] [**pri-**<br>**type common** ]|||配置IPv6报文直接重定<br>向到指定的出接口。|**redirect ipv6 to interface** { interface-<br>name | interface-type interface-<br>number } [**route-forward** ]||IPv4<br>报文<br>和<br>IPv6<br>报文|配置私网路由转发的<br>IPv4报文或IPv6报文重<br>定向到公网路由出接<br>口。|**redirect** {**ip** |**ipv6** }**public-network**||IPv4<br>报文<br>和<br>IPv6<br>报文|配置IP数据流重定向到<br>公网目标LSP。|**redirect lsp public** dest-ipv4-address<br>[ nexthop-address |**interface** interface-<br>type interface-number |**secondary** ]||IPv4<br>报文<br>和<br>IPv6<br>报文|配置报文重定向到指定<br>的VPN组。|**redirect vpn-group** vpn-group-name||IPv4<br>报文<br>和<br>IPv6<br>报文|配置报文重定向到指定<br>的VSI。|**redirect vsi** vsi-name||IPv4<br>报文<br>和<br>IPv6<br>报文|配置报文重定向到指定<br>的Tunnel。|**redirect interface tunnel** tunnelname<br>[ **destination-ip** destination-ip-address<br>address-mask-length** vpn-instance**<br>vpn-instance-name ] [**route-forward** ]||IPv4<br>报文<br>和<br>IPv6<br>报文|配置公网IPv4/IPv6报<br>文重定向入单个SR-<br>MPLS TE Policy。|**redirect sr-te policy** endpoint color<br>[ **label** label ] [**route-forward** ]||IPv4<br>报文<br>和<br>IPv6<br>报文|配置公网IPv4/IPv6报<br>文重定向入单个SRv6<br>TE Policy。|**redirect srv6-te policy** endpoint color<br>[ {**sid** |**vpnsid** } sid-ip ]||IPv4<br>报文<br>和<br>IPv6<br>报文|配置IPv4/IPv6报文重<br>定向入多个SRv6 TE<br>Policy进行负载分担转<br>发。|**redirect-template srv6-te** template-<br>name<br>说明<br>在执行此配置前，需要先在系统视图下创建<br>SRv6 TE Policy重定向模板，并指定SRv6 TE<br>Policy策略。<br>1. 执行命令**redirect template** template-<br>name**srv6-te**，创建SRv6 TE Policy重定<br>向模板并进入模板视图。<br>2. 执行命令**endpoint** endpoint**color** color<br>[ {**sid** |**vpnsid** } sid-ip ]，配置IPv4/IPv6<br>报文重定向入的SRv6 TE策略。<br>3. 执行命令**commit**，提交配置。|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 80HUAWEI NetEngine40E配置指南 1 QoSb. 执行命令 **commit** ，提交配置。       - 设置报文的负载分担方式a. 执行命令 **load-balance** { **flow** [ **l2** | **l3** ] | **packet** } ，设置报文的负载分担方式。b. 执行命令 **commit** ，提交配置。       - 配置级联流策略流行为中一般使用 ACL 规则进行重定向，但是 ACL 规则是有一定的规格限制的。复杂流分类所能定义的 ACL 规则无法满足实际场景的需求时，可以将流行为重定向到一个已经配置完成的流策略中，从而实现级联复杂流分类。a. 执行命令 **traffic-policy** policy-name [ **ip-layer** [ **srv6-inner** ] | **link-layer****srv6-inner** ] ，配置级联下一个流策略。#### ▪ 配置级联流策略会造成设备转发性能下降。 ▪ 接口流量匹配级联的流策略时：           - 转发行为按照级联的流策略行为执行           - 非重复的流策略行为可以分别执行           - 重复的流策略行为按照级联的流策略行为执行#### ▪ 一个接口的一个方向上只能应用一个流量策略。如果该流策略级联了另外一个流策略，那么这个接口该方向上相当于同时应用了多个流量策略。#### ▪ 流策略接口上应用时所指定的 inbound 或 outbound 方向， link-layer 、mpls-layer 和 all-layer 等参数被级联的流策略继承。#### ▪ 两级 ACL 的流行为都是 service-class 时，以第一级 service-class 优先生效；但是如果第一级 service-class 带有 no-remark ，还是以第二级service-class 优先生效。b. （可选）执行命令 **hierarchical-car enable** ，在复杂流分类级联策略场景中使能层次化 CAR 动作。在流行为中嵌套流策略后，流量策略中可以配置 CAR 动作，实现对流量的层次化 CAR 限速。c. 执行命令 **commit** ，提交配置。       - 配置 NetStream 统计的采样功能如果需要基于匹配流分类的流实现计费或流量统计，可以先配置相应的流分类，然后用该命令来实现采样后进行统计。a. 执行命令 { **ip** | **ipv6** } **netstream sampler** { **fix-packets** packet-interval |**fix-time** time-interval | **random-packets** packet-interval } ，配置NetStream 统计的采样功能。不支持流策略为共享属性的流采样。b. （可选）执行命令 **ip netstream sampler except deny-action** ，配置复杂流分类中 ACL 规则或流动作为 deny 的流量不做 NetStream 采样。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 81HUAWEI NetEngine40E配置指南 1 QoSc. 执行命令 **commit** ，提交配置。       - 配置提升流行为动作的优先级设备上同时配置了 BGP Flow Specification 和复杂流分类，如果需要复杂流分类的动作优先生效，可以执行如下命令配置实现。a. 执行命令 **increase-priority** ，配置提升流行为动作的优先级。b. 执行命令 **commit** ，提交配置。       - 使能匹配到 ACL 流量首个报文的日志记录功能。a. 执行命令 **log first-packet** ，使能对匹配到 ACL 流量的首个报文做日志记录功能。使能本功能后，当出现匹配到 ACL 的首个报文时会记录日志。日志内容中包含入 / 出接口名称、源 IP 地址、目的 IP 地址、协议号、源端口号（ TCP/UDP ）、目的端口号（ TCP/UDP ）、流量首个报文的接收时间（毫秒级）、设置时间段内的流量包数。b. 执行命令 **quit** ，返回系统视图。c. （可选）执行命令 **traffic-behavior log** { **entries** number-of-entries |**interval** interval-time } [*] ，配置匹配到 ACL 流量的首个报文日志记录的最大流数和包数统计的时间间隔。d. 执行命令 **commit** ，提交配置。**----**结束##### ******* 定义流量策略定义了流分类和动作后，需要配置流量策略，为定义的流关联动作。##### 背景信息定义了流分类和动作后，需要配置流量策略，为定义的流关联动作。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **traffic policy** policy-name ，定义流量策略并进入流策略视图。步骤 **3** 执行命令 **classifier** classifier-name **behavior** behavior-name [ **precedence**precedence-value ] ，在流量策略中为流分类指定采用的行为并设定策略匹配的优先级。步骤 **4** （可选）执行命令 **step** step-value ，配置策略间的步长。步骤 **5** （可选）执行命令 **statistics enable** ，使能流量策略的统计功能。为节省内存资源，系统缺省不使能流量策略的统计功能。当用户需要查看流量策略的统计数据时，可使能流量策略的统计功能。步骤 **6** （可选）执行命令 **undo share-mode** ，指定策略为非共享模式。流策略缺省为共享模式。当流策略已被在接口上应用时，不能修改流策略的共享 / 非共享模式。如需要修改，需要先取消接口上的应用。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 82HUAWEI NetEngine40E配置指南 1 QoS       - 共享模式的流策略：尽管策略应用在不同的接口，但流策略统计信息显示的是各接口汇总的统计数据，无法区分各个接口的单独数据。       - 非共享模式的流策略：可以根据策略应用的不同接口来区分接口上的策略统计数据。步骤 **7** （可选）执行命令 **match-type ipv6 qos-local-id enable** ，使能匹配 IPv6 qos-localid 模式。定义流分类时如果定义了基于 IPv6 QoS 策略 ID 的匹配规则，需要使能匹配 IPv6 qoslocal-id 模式功能，匹配规则才能生效，否则不允许流分类规则应用到流策略中。步骤 **8** 执行命令 **commit** ，提交配置。**----**结束##### ******* 应用流量策略配置好的基于类的策略，需要应用到接口才能生效。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** （可选）在单板上配置应用流量策略时匹配的报文信息。1. 执行命令 **slot** slot-id ，进入槽位视图。2. 请根据实际需求选择如下一种配置。–执行命令 **traffic-policy match-ip-layer** { **mpls-pop** | **mpls-push** } [*] ，配置入 / 出公网方向的报文，只匹配 IP 层（三层）信息做复杂流分类，应用流量策略。– 配置入 / 出公网方向的报文，匹配 IP 信息、 MPLS 信息做复杂流分类。请根据实际需求选择如下一种或两种配置。#### ▪ 执行命令 traffic-policy match-mpls-layer { mpls-push | mpls-pop }                           - ，配置入 / 出公网方向的报文同时匹配 IP 信息和 MPLS 信息做复杂流分类，应用流量策略。#### ▪ 执行命令 traffic-policy match-mpls-layer l2-inbound ，配置出公网方向的报文在二层转发场景匹配 MPLS 信息做复杂流分类，应用流量策略。3. 执行命令 **quit** ，退出槽位视图。步骤 **3** （可选）执行命令 **qos match-type qos-local-id enhance** ，使能 qos-local-id 增强模式。报文上行方向应用复杂流分类对其执行 remark qos-local-id 动作或者在 Route-Policy 视图下为报文设置 qos-local-id ，叠加 CGN 或 IPsec 业务后，报文被引流至增值业务板进行处理。这种场景下，如果需要在接口下行方向匹配 qos-local-id 信息对报文应用流策略，需要使能 qos-local-id 增强模式，否则下行接口无法匹配 qos-local-id 信息。步骤 **4** 请根据流量策略应用的接口选择下面的配置步骤。       - 在三层接口应用复杂流分类策略，请执行如下步骤：a. 执行命令 **interface** interface-type interface-number ，进入接口视图。b. （可选）执行命令 **qppb-policy qos-local-id both inbound** ，配置在接口入方向上基于源和目的 QoS 策略 ID 使能复杂流策略。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 83HUAWEI NetEngine40E配置指南 1 QoS说明在 定义流分类 中，需要先执行命令 **if-match qos-local-id** **source** source-qos-localid **destination** destination-qos-local-id ，命令 **qppb-policy qos-local-id both****inbound** 才能生效。c. 执行命令 **traffic-policy** policy-name { **inbound** | **outbound** } [ **all-layer** |**link-layer** | **mpls-layer** ] ，在三层接口应用流量策略。说明#### ▪ 如果指定 link-layer 参数，设备将根据报文的二层信息进行规则匹配并执行相应的动作。#### ▪ 如果指定 mpls-layer 参数，设备将根据指定 MPLS 报文的标签头信息进行复杂流分类。#### ▪ 如果指定 all-layer 参数，设备将根据报文的二层信息进行规则匹配并执行相应的动作；如果报文的二层信息没有匹配上流分类规则，则继续根据报文的三层信息进行规则匹配并执行相应的动作。       - 在二层接口应用复杂流分类策略的时候，可以指定 VLAN 范围来应用，请执行如下步骤：a. 执行命令 **interface** interface-type interface-number ，进入接口视图。b. 执行命令 **portswitch** ，切换到二层端口视图。c. 执行命令 **port trunk allow-pass vlan** { { vlan-id1 [ **to** vlan-id2 ] }&<1-10> | **all** } ，配置二层端口以 tagged 方式加入到指定的 VLAN 中。d. 执行命令 **traffic-policy** policy-name { **inbound** | **outbound** } **vlan** { vlanid1 [ **to** vlan-id2 ] | **all** } [ **all-layer** | **link-layer** | **mpls-layer** ] ，在二层接口应用流量策略。       - 在 EVC 二层子接口应用复杂流分类策略的时候，可以指定带宽分配方式，请执行如下步骤：a. 执行命令 **interface** interface-type interface-number.subnum **mode l2** ，进入 EVC 二层子接口视图。b. 执行命令 **traffic-policy** policy-name { **inbound** | **outbound** } **identifier**{ **none** | **vid** | **ce-vid** | **vid-ce-vid** } [ **all-layer** | **link-layer** | **mpls-layer** ] ，在 EVC 二层子接口应用流量策略。说明**identifier** 指定的分配方式必须与 EVC 二层子接口配置的封装方式一致。       - 在 QinQ 接口应用复杂流分类策略的时候，可以指定 PVLAN 和 CVLAN 来应用，请执行如下步骤：a. 执行命令 **interface** interface-type interface-number.subinterfacenumber ，进入子接口视图。b. 执行命令 **encapsulation** **qinq-termination** [ **local-switch** | **rt-****protocol** ] ，设定终结子接口的 VLAN ID 和对带两层 Tag 的用户报文进行终结。c. 执行命令 **qinq termination pe-vid** pe-vid [ **to** high-pe-vid ] **ce-vid** ce-vid[ **to** high-ce-vid ] [ **vlan-group** group-id ] ，配置 QinQ 子接口终结功能。d. 执行命令 **traffic-policy** policy-name { **inbound** | **outbound** } **pe-vid** pe-vid**ce-vid** ce-vid1 [ **to** ce-vid2 ] [ **all-layer** | **link-layer** | **mpls-layer** ] ，在QINQ 接口应用流量策略。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 84HUAWEI NetEngine40E配置指南 1 QoS       - 在 VBDIF 接口应用复杂流分类策略，请执行如下步骤：a. 执行命令 **bridge-domain** bd-id ，创建广播域桥域 BD 。b. 执行命令 **quit** ，返回系统视图。c. 执行命令 **interface vbdif** bd-id ，创建 VBDIF 接口，并进入该 VBDIF 接口视图。d. 执行命令 **traffic-policy** policy-name { **inbound** | **outbound** } ，在 VBDIF 接口应用流量策略。       - （可选）在 VPN 实例视图下应用复杂流分类策略，请执行如下步骤：a. 执行命令 **ip vpn-instance** vpn-instance-name ，进入 VPN 实例视图。b. 执行命令 **traffic-policy** policy-name **network** **inbound** ，在 VPN 实例视图下应用流量策略。       - （可选）在 VSI 视图下应用复杂流分类策略，请执行如下步骤：a. 执行命令 **vsi** vsi-name ，进入 VSI 视图。b. 在 VSI 视图下应用流量策略，请根据需要选择如下配置。#### ▪ 执行命令 traffic-policy policy-name network inbound link-layer ，在VSI 视图下应用网络侧流量策略。#### ▪ 执行命令 traffic-policy policy-name ac-mode { inbound |**outbound** } [ **link-layer** | **all-layer** ] ，在 VSI 视图下应用用户侧流量策略。       - （可选）在 EVPN 视图下应用复杂流分类策略，请执行如下步骤：a. 执行命令 **evpn vpn-instance** vpn-instance-name **bd-mode** ，进入 EVPN 视图。b. 执行命令 **traffic-policy** policy-name **network** **inbound** ，在 EVPN 视图下应用流量策略。步骤 **5** （可选）在 Eth-Trunk 接口视图执行命令 **qos traffic-car member-link-scheduler****distribute** ，配置在将复杂流分类的 CAR 动作应用到 Trunk 口时，将限速总带宽以成员口为单位分配限速带宽。步骤 **6** 执行命令 **commit** ，提交配置。**----**结束##### 1.5.3.5 检查配置结果基于类的 QoS 配置成功后，可以查看设备配置的类信息、流行为信息、指定策略中指定类及与类关联的行为的配置信息、策略的配置信息和运行情况、流量策略信息、队列配置信息和运行情况等内容。##### 操作步骤       - 使用 **display interface** [ interface-type [ interface-number ] ] 命令查看接口的流量信息。       - 使用 **display traffic behavior** { **system-defined** | **user-defined** } [ behaviorname ] 命令查看流行为的配置信息。       - 使用 **display traffic classifier** { **system-defined** | **user-defined** } [ classifiername ] 命令查看流分类的配置信息。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 85HUAWEI NetEngine40E配置指南 1 QoS       - 使用 **display traffic policy** { **system-defined** | **user-defined** } [ policy-name[ **classifier** classifier-name ] ] 命令查看流策略中所有流分类与流行为的关联信息或特定流分类与流行为的关联信息。       - 使用 **display traffic policy** **statistics** **interface** interface-type interfacenumber [ .sub-interface ] { **inbound** | **outbound** } [ **verbose** { **classifier-****based** [ **class** class-name ] | **rule-based** [ **class** class-name ] [ **filter** ] } ] 命令查看接口的流量策略统计信息。       - 使用 **display flow-car** [ **ipv6** ] **statistics** { **source-ip** | **destination-ip** } [ ipaddress ] **slot** slot-id { **inbound** | **outbound** } ，根据源或目的 IP 查询某槽位指定方向的 flow-car 统计计数。       - 使用命令 **display traffic policy vpn-instance** **brief** ，查询 VPN 实例和复杂流分类的绑定关系。**----**结束#### 1.5.4 配置 IP 报文的优先级映射配置 IP 报文的 DSCP 值和 QoS 服务等级、颜色之间的映射关系，实现 IP 报文的 QoS 调度。##### 应用环境配置基于简单流分类的优先级映射可以将一种网络流量中的优先级映射到另外一种网络流量中，使流量在另外一种网络中按照原来的或用户配置的优先级传送。当 NE40E 作为不同网络之间的边缘路由器时，所有进入 NE40E 的 IP 报文，其原先的外部优先级标记即 DSCP 将被映射为内部优先级（以 Diff-Serv 的服务等级和颜色表示）；NE40E 发出报文时，将内部优先级映射为某种外部业务优先级。简单流分类通常配置在网络的核心位置。简单流分类不止应用在物理端口，由于逻辑端口在企业组网中将有更加广阔的应用，为了限制逻辑端口成员的报文拥塞，增加对逻辑端口报文优先级的限制，逻辑端口也需要支持简单流分类的配置及应用。Diff-Serv （ Different Service ）域由一组采用相同的服务提供策略和实现了相同 PHB 组集合的相连 Diff-Serv 节点组成。在网络的核心路由器上，一般直接接受或重定义报文中的服务等级。在 IP 域和 MPLS 域的边界路由器上，也需要将 DSCP 和 EXP 进行映射。简单流分类能够实现从外部优先级到内部优先级及从内部优先级到外部优先级的映射，但是在同种网络流量之间（如 IP 流量之间或 MPLS 流量之间）不支持映射。说明如果需要对接口上的所有上行流量进行统一调度，可以通过命令 **qos default-service-class** 配置接口上的上行流量进入特定的队列，根据队列的服务等级从而提供相应的服务。如果配置了该命令，则不能使能其他相关的报文进入队列的配置命令，不能使能简单流分类。##### 前置任务在配置 IP 报文的优先级映射之前，需要完成以下任务：       - 配置相关接口的物理参数       - 配置相关接口的链路层属性，保证接口的正常工作       - 配置相关接口的 IP 地址文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 86HUAWEI NetEngine40E配置指南 1 QoS       - 使能路由协议，实现互通##### 操作步骤       - 配置 IP 报文的 DSCP 值和 QoS 服务等级、颜色之间的映射关系。a. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **diffserv domain** { ds-domain-name | **default** | **5p3d** } [ **domain-****id** domain-id-value ] ，定义 DS 域并进入 DS 域视图。c. 请根据实际情况对路由器的流量策略进行定义。#### ▪ 如果同时对入方向的 IPv4 和 IPv6 流量定义流量策略，执行命令 ip-dscp**inbound** dscp-value **phb** service-class [ color ] 或命令 **ip-dscp-****inbound** dscp-value1 **to** dscp-value2 **phb** service-class [ color ][ **exclude-user-defined** ] 。#### ▪ 如果同时对出方向的 IPv4 和 IPv6 流量定义流量策略，执行命令 ip-dscp**outbound** service-class color **map** dscp-value 。#### ▪ 如果单独对入方向的 IPv4 流量定义流量策略，执行命令 ipv4-dscp**inbound** dscp-value **phb** service-class [ color ] 或命令 **ipv4-dscp-****inbound** dscp-value1 **to** dscp-value2 **phb** service-class [ color ][ **exclude-user-defined** ] 。#### ▪ 如果单独对出方向的 IPv4 流量定义流量策略，执行命令 ipv4-dscp**outbound** service-class color **map** dscp-value 。#### ▪ 如果单独对入方向的 IPv6 流量定义流量策略，执行命令 ipv6-dscp**inbound** dscp-value **phb** service-class [ color ] 或命令 **ipv6-dscp-****inbound** dscp-value1 **to** dscp-value2 **phb** service-class [ color ][ **exclude-user-defined** ] 。#### ▪ 如果单独对出方向的 IPv6 流量定义流量策略，执行命令 ipv6-dscp**outbound** service-class color **map** dscp-value 。说明当同时配置了 **ipv4-dscp-inbound** （或者 **ipv6-dscp-inbound** ）和 **ip-dscp-inbound**命令时， **ipv4-dscp-inbound** （或者 **ipv6-dscp-inbound** ）命令配置的流量策略优先生效，未单独配置的流量按照 **ip-dscp-inbound** 命令配置的流量策略生效。出方向叠加配置的生效规则与入方向相同。对于 IP 报文，系统预先为用户定义了 default 域。 default 域是系统默认存在的，不允许用户删除。如果用户在 DS 域中没有配置 **3.** 请根据实际情况对的流量策略进行定义。 中的优先级映射，系统将采用缺省的映射关系。 default 域分别描述了缺省情况下IP 报文 DSCP 值和 QoS 服务等级、颜色之间的映射关系，用户可以对域中的映射关系进行修改。 default 域中对于来自上游设备的报文，需要将报文的 DSCP值映射到 QoS 服务等级、颜色，具体映射关系如 表 **1-10** 所示；对于流向下游设备的报文，需要将报文的 QoS 服务等级、颜色映射到 DSCP 值，其映射关系如 表 **1-11** 所示。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 87HUAWEI NetEngine40E配置指南 1 QoS表 **1-10** default 域中 DSCP 与服务类型之间缺省的映射表|DSCP|Service|Color|DSCP|Service|Color||---|---|---|---|---|---||00|BE|Green|32|AF4|Green||01|BE|Green|33|BE|Green||02|BE|Green|34|AF4|Green||03|BE|Green|35|BE|Green||04|BE|Green|36|AF4|Yellow||05|BE|Green|37|BE|Green||06|BE|Green|38|AF4|Red||07|BE|Green|39|BE|Green||08|AF1|Green|40|EF|Green||09|BE|Green|41|BE|Green||10|AF1|Green|42|BE|Green||11|BE|Green|43|BE|Green||12|AF1|Yellow|44|BE|Green||13|BE|Green|45|BE|Green||14|AF1|Red|46|EF|Green||15|BE|Green|47|BE|Green||16|AF2|Green|48|CS6|Green||17|BE|Green|49|BE|Green||18|AF2|Green|50|BE|Green||19|BE|Green|51|BE|Green||20|AF2|Yellow|52|BE|Green||21|BE|Green|53|BE|Green||22|AF2|Red|54|BE|Green||23|BE|Green|55|BE|Green||24|AF3|Green|56|CS7|Green||25|BE|Green|57|BE|Green||26|AF3|Green|58|BE|Green||27|BE|Green|59|BE|Green||28|AF3|Yellow|60|BE|Green|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 88HUAWEI NetEngine40E配置指南 1 QoS|DSCP|Service|Color|DSCP|Service|Color||---|---|---|---|---|---||29|BE|Green|61|BE|Green||30|AF3|Red|62|BE|Green||31|BE|Green|63|BE|Green|系统缺省的内部服务等级和颜色到 IP 报文的 DSCP 值的映射关系如 表 **1-11** 所示。表 **1-11** 服务等级与 DSCP 之间缺省的映射表|Service|Color|DSCP||---|---|---||BE|Green|0||AF1|Green|10||AF1|Yellow|12||AF1|Red|14||AF2|Green|18||AF2|Yellow|20||AF2|Red|22||AF3|Green|26||AF3|Yellow|28||AF3|Red|30||AF4|Green|34||AF4|Yellow|36||AF4|Red|38||EF|Green|46||CS6|Green|48||CS7|Green|56|d. 执行命令 **commit** ，提交配置。e. 执行命令 **quit** ，退回到系统视图f. 执行命令 **interface** interface-type interface-number ，进入接口视图。g. 执行命令 **trust upstream** { **5p3d** | ds-domain-name | **default** } [ **inbound**| **outbound** ] ，在接口上绑定 DS 域，使能简单流分类。h. 执行命令 **commit** ，提交配置。**----**结束文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 89HUAWEI NetEngine40E配置指南 1 QoS##### 检查配置结果完成配置后，可以按以下指导来检查配置结果。       - 使用 **display diffserv domain** [ ds-domain-name ] [ **8021p** | **dscp** [ **ipv4** |**ipv6** ] | **exp** | **ip-precedence** ] [ **inbound** | **outbound** ] 命令查看 DS 域的配置信息。       - 使用 **display diffserv domain application** ds-domain-name 命令查询指定 DS 域下应用的接口列表。#### 1.5.5 配置 VLAN 报文复杂流分类的流量策略介绍如何配置 VLAN 报文复杂流分类的流量策略。##### 应用环境如果需要对进入网络的流量或网络中的流量进行分类管理和限制（例如对语音、视频、数据等业务分别对待，分配不同的带宽、保证不同的时延；对来自不同用户的流量分别对待，保证不同的带宽和优先级），则需要根据报文的 VLAN 属性对不同分类的业务提供差别服务，配置基于复杂流分类的 QoS 流量策略。使进入 VLAN 网络中的流量，可以继承或改变上层网络的 QoS 特性，在 VLAN 网络中继续传送。通常是在相对边界的路由器上配置复杂流分类的流量策略，在相对核心的路由器上配置简单流分类的流量策略。##### 前置任务在配置 VLAN 报文复杂流分类的流量策略之前，需完成以下任务：       - 配置接口的物理参数       - 配置接口的链路层属性       - 配置接口的 IP 地址##### ******* 配置匹配 VLAN 帧优先级的规则介绍如何配置 VLAN 帧优先级的匹配规则。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **traffic classifier** classifier-name [ **operator** { **and** | **or** } ] ，定义流分类并进入流分类视图。步骤 **3** 执行命令 **if-match 8021p** 8021p-value ，定义匹配 VLAN 优先级 8021p 的规则。步骤 **4** 执行命令 **commit** ，提交配置。**----**结束##### ******* 配置 VLAN 优先级的值介绍如何配置 VLAN 优先级的值。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 90HUAWEI NetEngine40E配置指南 1 QoS##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **traffic behavior** behavior-name ，定义行为并进入行为视图。步骤 **3** 请根据实际情况选择一种方式配置 VLAN 优先级 802.1p 的值。       - 如果要同时修改两层 VLAN 报文的优先级，执行命令 **remark 8021p** 8021pvalue ，配置 VLAN 优先级 802.1p 的值。       - 如果要分别修改两层 VLAN 报文的优先级，请进行如下配置：– 执行命令 **remark inner-8021p** 8021p-value ，重新标记封装了两层 VLAN 报文的第二层 VLAN 的 802.1p 值。– 执行命令 **remark outer-8021p** 8021p-value ，重新标记 VLAN 报文的最外层VLAN 的 802.1p 值。步骤 **4** 执行命令 **commit** ，提交配置。**----**结束##### 1.5.5.3 定义流量策略定义了流分类和动作后，需要配置流量策略，为定义的流关联动作。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **traffic policy** policy-name ，定义流量策略并进入流策略视图。步骤 **3** 执行命令 **classifier** classifier-name **behavior** behavior-name [ **precedence**precedence-value ] ，在流量策略中为流分类指定采用的行为并设定策略匹配的优先级。步骤 **4** （可选）执行命令 **step** step-value ，配置策略间的步长。步骤 **5** （可选）执行命令 **statistics enable** ，使能流量策略的统计功能。为节省内存资源，系统缺省不使能流量策略的统计功能。当用户需要查看流量策略的统计数据时，可使能流量策略的统计功能。步骤 **6** 执行命令 **commit** ，提交配置。**----**结束##### ******* 应用流量策略配置好的基于类的策略，需要应用到接口才能生效。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** （可选）执行命令 **qos match-type qos-local-id enhance** ，使能 qos-local-id 增强模式。报文上行方向应用复杂流分类对其执行 remark qos-local-id 动作或者在 Route-Policy 视图下为报文设置 qos-local-id ，叠加 CGN 或 IPsec 业务后，报文被引流至增值业务板进行文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 91HUAWEI NetEngine40E配置指南 1 QoS处理。这种场景下，如果需要在接口下行方向匹配 qos-local-id 信息对报文应用流策略，需要使能 qos-local-id 增强模式，否则下行接口无法匹配 qos-local-id 信息。步骤 **3** 请根据流量策略应用的接口选择下面的配置步骤。       - 在三层接口应用复杂流分类策略，请执行如下步骤。a. 执行命令 **interface** interface-type interface-number ，进入接口视图。b. 执行命令 **traffic-policy** policy-name { **inbound** | **outbound** } [ **all-layer** |**link-layer** | **mpls-layer** ] ，在三层接口应用流量策略。说明如果指定 **link-layer** 参数，设备将根据报文的二层信息进行规则匹配并执行相应的动作。如果指定 **mpls-layer** 参数，设备将根据指定 MPLS 报文的标签头信息进行复杂流分类。如果指定 **all-layer** 参数，设备将根据报文的二层信息进行规则匹配并执行相应的动作；如果报文的二层信息没有匹配上流分类规则，则继续根据报文的三层信息进行规则匹配并执行相应的动作。       - 在二层接口应用复杂流分类策略的时候，可以指定 VLAN 范围来应用，请执行如下步骤。a. 执行命令 **interface** interface-type interface-number ，进入接口视图。b. 执行命令 **portswitch** ，切换到二层端口视图。c. 执行命令 **port trunk allow-pass vlan** { { vlan-id1 [ **to** vlan-id2 ] }&<1-10> | **all** } ，配置二层端口以 tagged 方式加入到指定的 VLAN 中。d. 执行命令 **traffic-policy** policy-name { **inbound** | **outbound** } **vlan** { vlanid1 [ **to** vlan-id2 ] | **all** } [ **all-layer** | **link-layer** | **mpls-layer** ] ，在二层接口应用流量策略。       - 在 EVC 二层子接口应用复杂流分类策略的时候，可以指定带宽分配方式，请执行如下步骤。a. 执行命令 **interface** interface-type interface-number.subnum **mode l2** ，进入 EVC 二层子接口视图。b. 执行命令 **traffic-policy** policy-name { **inbound** | **outbound** } **identifier**{ **none** | **vid** | **ce-vid** | **vid-ce-vid** } [ **all-layer** | **link-layer** ] ，在 EVC 二层子接口应用流量策略。说明**identifier** 指定的分配方式必须与 EVC 二层子接口配置的封装方式一致。       - 在 QinQ 接口应用复杂流分类策略的时候，可以指定 PVLAN 和 CVLAN 来应用，请执行如下步骤。a. 执行命令 **interface** interface-type interface-number.subinterfacenumber ，进入子接口视图。b. 执行命令 **encapsulation** **qinq-termination** [ **local-switch** | **rt-****protocol** ] ，设定终结子接口的 VLAN ID 和对带两层 Tag 的用户报文进行终结。c. 执行命令 **qinq termination pe-vid** pe-vid [ **to** high-pe-vid ] **ce-vid** ce-vid[ **to** high-ce-vid ] [ **vlan-group** group-id ] ，配置 QinQ 子接口终结功能。d. 执行命令 **traffic-policy** policy-name { **inbound** | **outbound** } **pe-vid** pe-vid**ce-vid** ce-vid1 [ **to** ce-vid2 ] [ **all-layer** | **link-layer** | **mpls-layer** ] ，在QINQ 接口应用流量策略。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 92HUAWEI NetEngine40E配置指南 1 QoS说明在 QinQ 接口应用复杂流分类策略的时候，也可以不指定 PVLAN 和 CVLAN ，直接通过**traffic-policy** policy-name { **inbound** | **outbound** } [ **all-layer** | **link-layer** |**mpls-layer** ] 命令配置即可。步骤 **4** 执行命令 **commit** ，提交配置。**----**结束##### 1.5.5.5 检查配置结果VLAN QoS 配置成功后，可以查看设备配置的类信息、流行为信息、指定策略中指定类及与类关联的行为的配置信息、策略的配置信息和运行情况、流量策略信息、队列配置信息和运行情况等内容。##### 操作步骤       - 使用 **display interface** [ interface-type [ interface-number ] ] 命令查看接口的流量信息。       - 使用 **display traffic behavior** { **system-defined** | **user-defined** } [ behaviorname ] 命令查看流行为的配置信息。       - 使用 **display traffic classifier** { **system-defined** | **user-defined** } [ classifiername ] 命令查看流分类的配置信息。       - 使用 **display traffic policy** { **system-defined** | **user-defined** } [ policy-name[ **classifier** classifier-name ] ] 命令查看流策略中所有流分类与流行为的关联信息或特定流分类与流行为的关联信息。       - 使用 **display traffic policy** **statistics** **interface** interface-type interfacenumber [ .sub-interface ] { **inbound** | **outbound** } [ **verbose** { **classifier-****based** [ **class** class-name ] | **rule-based** [ **class** class-name ] [ **filter** ] } ] 命令查看接口的流量策略统计信息。       - 使用 **display traffic policy statistics** **interface** interface-type interfacenumber [ **vlan** vlan-id | **pe-vid** pe-vid **ce-vid** ce-vid ] { **inbound** | **outbound** }[ **verbose** { **classifier-based** [ **class** class-name ] | **rule-based** [ **class** classname ] [ **filter** ] } ] 命令，查看接口的流量策略统计信息。       - 使用 **display traffic policy not-support policy** policy-name **interface**interface-type interface-number **outbound** 命令，查看指定流策略模板在指定接口的下行方向不支持的规则。       - 使用 **display traffic policy not-support policy** policy-name **slot** slot-num{ **inbound** | **outbound** } **vsi-acl** **ac-mode** 命令，查看指定流策略模板在指定单板上不支持的规则和动作。**----**结束#### 1.5.6 配置 VLAN 报文的优先级映射介绍如何配置 VLAN 报文的优先级映射和该功能的应用场景。##### 应用环境配置基于简单流分类的优先级映射可以将一种网络流量中的优先级映射到另外一种网络流量中，使流量在另外一种网络中按照原来的或用户配置的优先级传送。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 93HUAWEI NetEngine40E配置指南 1 QoS当 NE40E 作为不同网络之间的边缘路由器时，所有进入 NE40E 的 VLAN 报文，其原先的外部优先级标记即 802.1p 将被映射为内部优先级（以 Diff-Serv 的服务等级和颜色表示）； NE40E 发出报文时，将内部优先级映射为 802.1p 。说明如果需要对接口上的所有上行流量进行统一调度，可以执行命令 **qos default-service-class** 配置接口上的上行流量进入特定的队列，根据队列的服务等级从而提供相应的服务。如果配置了该命令，则不能使能其他相关的报文进入队列的配置命令，不能使能简单流分类。##### 前置任务在配置 VLAN 报文的优先级映射之前，需要完成以下任务：       - 配置相关接口的物理参数       - 配置相关接口的链路层属性，保证接口的正常工作       - 配置相关接口的 IP 地址       - 使能路由协议，实现互通VS 模式下，该配置仅在 Admin VS 支持。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **diffserv domain** { ds-domain-name | **default** | **5p3d** } [ **domain-id**domain-id-value ] ，定义 DS 域并进入 DS 域视图。步骤 **3** 请根据实际情况对路由器的流量策略进行定义。       - 如果对入方向的 VLAN 报文流量定义流量策略，执行命令 **8021p-inbound** 8021pvalue **phb** service-class [ color ] 。       - 如果对出方向的 VLAN 报文流量定义流量策略，执行命令 **8021p-outbound**service-class color **map** 8021p-value 。对于 VLAN 报文系统预先定义了以下 DS 域模板： 5p3d 域和 default 域。       - 5p3d 域描述了 VLAN 报文 802.1p 优先级和 QoS 服务等级、颜色之间特定的一种的映射关系，用户可以对 5p3d 域中的映射关系进行修改。对于来自上游设备的报文，需要将报文的 802.1p 优先级映射到 QoS 服务等级、颜色，其映射关系如 表 **1-12** 所示；对于流向下游设备的报文，需要将报文的 QoS 服务等级、颜色映射到 802.1p优先级，其映射关系如 表 **1-13** 所示。表 **1-12** 802.1p 到服务等级的映射表|802.1p|Service|Color||---|---|---||0|BE|Yellow||1|BE|Green||2|AF2|Yellow||3|AF2|Green||4|AF4|Yellow|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 94HUAWEI NetEngine40E配置指南 1 QoS|802.1p|Service|Color||---|---|---||5|AF4|Green||6|CS6|Green||7|CS7|Green|表 **1-13** 服务等级到 802.1p 的映射表|Service|Color|802.1p||---|---|---||BE|Green|1||BE|Yellow|0||BE|Red|0||AF1|Green|1||AF1|Yellow|0||AF1|Red|0||AF2|Green|3||AF2|Yellow|2||AF2|Red|2||AF3|Green|3||AF3|Yellow|2||AF3|Red|2||AF4|Green|5||AF4|Yellow|4||AF4|Red|4||EF|Green|5||EF|Yellow|4||EF|Red|4||CS6|Green、Yellow、Red|6||CS7|Green、Yellow、Red|7|       - default 域描述了缺省情况下， VLAN 报文 802.1p 优先级和 QoS 服务等级、颜色之间的映射关系，用户可以对 default 域中的映射关系进行修改，对于来自上游设备的报文，需要将报文的 802.1p 优先级映射到 QoS 服务等级、颜色，具体映射关系如表 **1-14** 所示；对于流向下游设备的报文，需要将报文的 QoS 服务等级、颜色映射到 802.1p 优先级，其映射关系如 表 **1-15** 所示。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 95HUAWEI NetEngine40E配置指南 1 QoS表 **1-14** IP Precedence/MPLS EXP/802.1p 到服务等级的缺省映射表|IP Precedence/MPLS<br>EXP/802.1p|Service|Color||---|---|---||0|BE|Green||1|AF1|Green||2|AF2|Green||3|AF3|Green||4|AF4|Green||5|EF|Green||6|CS6|Green||7|CS7|Green|表 **1-15** 服务等级到 IP Precedence/MPLS EXP/802.1p 的缺省映射表|Service|Color|IP Precedence/MPLS EXP/802.1p||---|---|---||BE|Green、<br>Yellow、Red|0||AF1|Green、<br>Yellow、Red|1||AF2|Green、<br>Yellow、Red|2||AF3|Green、<br>Yellow、Red|3||AF4|Green、<br>Yellow、Red|4||EF|Green、<br>Yellow、Red|5||CS6|Green、<br>Yellow、Red|6||CS7|Green、<br>Yellow、Red|7|步骤 **4** （可选）在接口视图执行命令 **field dei enable vlan** { { vlan-id1 [ **to** vlanid2 ] }&<1-10> | **all** } ，使能接口指定 VLAN 范围内报文的 DEI 能力。使能后，简单流分类按照服务优先级进入队列，并且根据报文的 CFI 字段进行着色。说明DEI 功能与简单流分类同时配置且信任 802.1p 优先级时才能生效。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 96HUAWEI NetEngine40E配置指南 1 QoS步骤 **5** 执行命令 **commit** ，提交配置。步骤 **6** 执行命令 **quit** ，退回系统视图。步骤 **7** 请根据流量策略应用的接口选择下面的配置步骤。说明对于非 LPUF-50/LPUF-50-L/LPUI-21-L/LPUI-51-L/LPUF-51/LPUF-51-B/LPUI-51/LPUI-51-B/LPUS-51/LPUF-101/LPUF-101-B/LPUI-101/LPUI-101-B/LPUS-101 单板， L3VPN 差分模式为PIPE 的场景下，希望按照 8021P 值进行流量调度时，需要在系统视图下执行 **diffserv-mode pipe****mapping-8021p mpls-pop** 命令使能对 MPLS 出 PE 设备上接口出方向报文回填 8021P 值。       - 在三层接口上对 VLAN 报文应用流量策略a. 执行命令 **interface** **gigabitethernet** interface-number.subnumber ，进入子接口视图。b. 配置绑定 DS 域，根据使用场景选择如下配置：说明两种配置应用场景有所不同，配置 **qos phb enable** 只在下行生效，实现优先级映射；如果需要在上行和下行都实现优先级映射，则要配置 **trust upstream** 。#### ▪ 执行命令 trust upstream { 5p3d | ds-domain-name | default } ，在子接口上绑定 DS 域。#### ▪ 执行命令 qos phb enable { ds-domain-name | default } ，在下行子接口配置，用于绑定 DS 域。说明在接口下配置 **qos phb enable** 时，与该接口下配置的 **trust upstream** 命令、**qos phb disable** 命令互斥。c. 执行命令 **trust** { **8021p** | **inner-8021p** | **outer-8021p** } [ **inbound** |**outbound** ] ，使能根据 802.1p 的简单流分类。说明#### ▪ 在配置 trust 8021p 命令之前，必须先在接口上绑定 DS 域，即配置 trust**upstream** 命令，该配置才生效。#### ▪ 在接口加入到 Diff-Serv 域后， Diff-Serv 域所定义的流量策略将会自动对出入接口的流量起作用。       - 在二层接口上对 VLAN 报文应用流量策略a. 执行命令 **interface** **gigabitethernet** interface-number ，进入接口视图。b. 执行命令 **portswitch** ，进入二层接口视图。c. 执行命令 **port trunk allow-pass vlan** { { vlan-id1 [ **to** vlan-id2 ] }&<1-10> | **all** } ，配置二层端口以 tagged 方式加入到指定的 VLAN 中。d. 配置绑定 DS 域，根据使用场景选择如下配置：说明两种配置应用场景有所不同，配置 **qos phb enable vlan** 只在下行生效，实现优先级映射；如果需要在上行和下行都实现优先级映射，则要配置 **trust upstream vlan** 。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 97HUAWEI NetEngine40E配置指南 1 QoS#### ▪ 执行命令 trust upstream { 5p3d | ds-domain-name | default }[ **inbound** | **outbound** ] **vlan** { vlan-id1 [ **to** vlan-id2 ] } &<1-10> ，在下行接口配置，用于绑定 DS 域，实现优先级映射。#### ▪ 执行命令 qos phb enable { ds-domain-name | default | 5p3d } vlan{ vlan-id1 [ **to** vlan-id2 ] } &<1-10> ，在下行接口配置，用于绑定 DS域，实现优先级映射。说明在接口下配置 **qos phb enable** 时，与该接口下配置的 **trust upstream vlan** 命令、 **qos phb disable** 命令互斥。e. 执行命令 **trust 8021p** [ **inbound** | **outbound** ] **vlan** { vlan-id1 [ **to** vlanid2 ] } &<1-10> ，使能根据 802.1p 进行简单流分类。步骤 **8** 执行命令 **commit** ，提交配置。**----**结束##### 检查配置结果完成配置后，可以按以下指导来检查配置结果。       - 使用 **display diffserv domain** [ ds-domain-name ] [ **8021p** | **dscp** | **exp** ][ **inbound** | **outbound** ] 命令查看 DS 域的配置信息。       - 使用 **display diffserv domain application** ds-domain-name 命令查询指定 DS 域下应用的接口列表。#### 1.5.7 配置 MPLS 报文的优先级映射介绍如何配置 MPLS 报文的优先级映射和该功能的应用场景。##### 应用环境配置基于简单流分类的优先级映射可以将一种网络流量中的优先级映射到另外一种网络流量中，使流量在另外一种网络中按照原来的或用户配置的优先级传送。当 NE40E 作为不同网络之间的边缘路由器时，所有进入 NE40E 的 MPLS 报文，其原先的外部优先级标记即 EXP 将被映射为内部优先级（以 Diff-Serv 的服务等级和颜色表示）；NE40E 发出报文时，将内部优先级映射为某种外部业务优先级。MPLS 报文的优先级映射通常配置在网络的核心位置。##### 前置任务在配置 MPLS 报文的优先级映射之前，需要完成以下任务：       - 配置相关接口的物理参数       - 配置相关接口的链路层属性，保证接口的正常工作       - 配置相关接口的 IP 地址       - 使能路由协议，实现互通VS 模式下，该配置仅在 Admin VS 支持。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 98HUAWEI NetEngine40E配置指南 1 QoS##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **diffserv domain** ds-domain-name ，定义 DS 域并进入 DS 域视图。步骤 **3** 请根据实际情况对路由器的流量策略进行定义。       - 如果对入方向的 MPLS 流量定义流量策略，执行命令 **mpls-exp-inbound** exp **phb**service-class [ color ] 。       - 如果对出方向的 MPLS 流量定义流量策略，执行命令 **mpls-exp-outbound**service-class color **map** exp-value 。系统预先为用户定义了 default 域，如果用户在 DS 域中没有配置步骤 3 中的优先级映射，系统将采用缺省的映射关系。 default 域描述了缺省情况下 MPLS 报文 EXP 值和 QoS服务等级、颜色之间的映射关系，用户可以对 default 域中的映射关系进行修改，对于来自上游设备的报文，需要将报文的 EXP 值映射到 QoS 服务等级、颜色，具体映射关系如 表 **1-16** 所示；对于流向下游设备的报文，需要将报文的 QoS 服务等级、颜色映射到EXP 值，其映射关系如 表 **1-17** 所示。系统缺省的 MPLS 报文的 EXP 域值到服务类型映射关系如 表 **1-16** 所示。表 **1-16** EXP 与服务类型之间缺省的映射表|EXP|Service|Color|EXP|Service|Color||---|---|---|---|---|---||0|BE|Green|4|AF4|Green||1|AF1|Green|5|EF|Green||2|AF2|Green|6|CS6|Green||3|AF3|Green|7|CS7|Green|系统缺省的 MPLS 报文服务等级到 EXP 值的映射关系如 表 **1-17** 所示。表 **1-17** 服务等级与 EXP 之间缺省的映射表|Service|Color|MPLS EXP||---|---|---||BE|Green|0||AF1|Green、Yellow、Red|1||AF2|Green、Yellow、Red|2||AF3|Green、Yellow、Red|3||AF4|Green、Yellow、Red|4||EF|Green|5||CS6|Green|6||CS7|Green|7|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 99HUAWEI NetEngine40E配置指南 1 QoS步骤 **4** 执行命令 **commit** ，提交配置。步骤 **5** 执行命令 **quit** ，退回系统视图。步骤 **6** 执行命令 **interface** interface-type interface-number ，进入接口视图。步骤 **7** 执行命令 **trust upstream** { ds-domain-name | **default** } ，在接口上绑定 DS 域，使能简单流分类。步骤 **8** （可选）执行命令 **mpls l2vc diffserv domain** { **5p3d** | domain-name | **default** } ，在 VLL 接口上绑定 DS 域，指定获取私网标签优先级使用的 DS 域。步骤 **9** （可选）执行命令 **quit** ，退回系统视图。步骤 **10** （可选）执行命令 **slot** slot-id ，进入槽位视图。步骤 **11** （可选）执行命令 **mpls-inner-exp phb disable vll** ，去使能 VLL 场景下， PW 接口下行映射获取私网标签优先级的功能。步骤 **12** 执行命令 **commit** ，提交配置。**----**结束##### 检查配置结果完成配置后，可以按以下指导来检查配置结果。       - 使用 **display diffserv domain** [ ds-domain-name ] [ **8021p** | **dscp** | **exp** ][ **inbound** | **outbound** ] 命令查看 DS 域的配置信息。       - 使用 **display diffserv domain application** ds-domain-name 命令查询指定 DS 域下应用的接口列表。#### 1.5.8 配置 VXLAN 报文复杂流分类的流量策略介绍 VXLAN 报文复杂流分类的应用场景和配置过程。##### 应用环境基于 VXLAN 报文复杂流分类的流量策略支持根据报文的 DSCP 值、协议类型、 IP 地址、端口号等参数对不同分类的业务提供差别服务，以满足不同业务对网络带宽、时延等的应用需求。通常是在相对边界的路由器上配置复杂流分类的流量策略，在相对核心的路由器上配置简单流分类的流量策略。##### 前置任务在配置 VXLAN 报文复杂流分类的流量策略之前，需要完成以下任务：       - 配置 VXLAN 隧道       - BD/VPN 下绑定 VNI##### ******* 定义流分类要对网络中的流量进行基于类的 QoS 配置，就需要先定义流分类，流量的分类可以按照 ACL 规则、报文优先级等进行定义。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 100HUAWEI NetEngine40E配置指南 1 QoS##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** （可选）执行命令 **qos remark vxlan-qos-local-id enable** ，使能在 VXLAN 头中回填QoS 策略 ID 的功能。说明NetEngine 40E-X8AK 不支持此配置。步骤 **3** 执行命令 **traffic classifier** classifier-name [ **operator** { **and** | **or** } ] ，定义流分类并进入流分类视图。如果在一个流分类中配置了多个匹配规则，可以通过指定参数 operator 来设置这些规则之间的关系，其中：       - **and** ：指定类下的规则之间是逻辑“与”的关系，即数据包必须匹配全部规则才属于该类。       - **or** ：指定类下的规则之间是逻辑“或”的关系，即数据包只要匹配其中任何一个规则就属于该类。步骤 **4** 请根据实际情况对流分类的匹配规则进行定义。       - 如果定义 ACL 匹配规则，执行命令 **if-match** **acl** { acl-number | **name** acl-name }[ **precedence** precedence-value ] 。说明如果在高级 ACL 的 rule 规则中配置了虚匹配拟扩展局域网规则的命令 **rule** [ rule-id ] { **deny**| **permit** } { protocol | **udp** } **vxlan** **vni** vni 和匹配报文长度的命令 **rule** [ rule-id ] { **deny** |**permit** } protocol [ **packet-length** length-operation length-value ] ，会降低可配置的rule 规则的资源。ACL 匹配规则可以根据用户的不同需求定义不同的访问控制列表，包括协议类型、源地址、目的地址或则报文中的优先级字段等。 **if-match acl** 命令根据 **rule** 命令定义的 ACL 规则过滤报文，然后进行对应的流行为。       - 如果定义 DSCP 匹配规则，执行命令 **if-match** **dscp** { dscp-value | **af11** | **af12** |**af13** | **af21** | **af22** | **af23** | **af31** | **af32** | **af33** | **af41** | **af42** | **af43** | **cs1** | **cs2** |**cs3** | **cs4** | **cs5** | **cs6** | **cs7** | **ef** | **default** } 。       - 如果定义 IP 报文优先级的匹配规则，执行命令 **if-match** [ **ipv6** ] **ip-precedence**ip-precedence 。       - 如果定义匹配所有数据包的规则，执行命令 **if-match** **any** 。       - 如果定义基于 VXLAN 报文的 QoS 策略 ID 的匹配规则，执行命令 **if-match** [ **ipv6** ]**vxlan-qos-local-id** qos-local-id-value 。说明NetEngine 40E-X8AK 不支持定义基于 VXLAN 报文的 QoS 策略 ID 的匹配规则。**----**结束##### ******* 定义流行为并配置动作介绍设备支持的流行为及如何配置。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 101HUAWEI NetEngine40E配置指南 1 QoS##### 背景信息设备支持设置的流行为动作类型比较丰富，可以根据实际需要选择下面的一种或多种。##### 操作步骤       - 配置流量调度a. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **traffic behavior** behavior-name ，定义流行为并进入流行为视图。c. 执行命令 **car** { **cir** cir-value [ **pir** pir-value ] } [ **cbs** cbs-value [ **pbs** pbsvalue ] ] [ **adjust** adjust-value ] [ **green** { **discard** | **pass** [ **remark dscp**dscp | **service-class** class **color** color ] } | **yellow** { **discard** | **pass**[ **remark dscp** dscp | **service-class** class **color** color ] } | **red** { **discard** |**pass** [ **remark dscp** dscp | **service-class** class **color** color ] } ] [*][ **summary** ] [ **color-aware** ] [ **limit-type pps** ] ，配置流量监管动作。本命令为覆盖式命令，即在同一个策略的类上多次进行该配置后，按最后一次配置生效。说明报文被 remark 为 ef 、 be 、 cs6 和 cs7 服务等级后，报文颜色只能被 remark 为 green 。d. 执行命令 **commit** ，提交配置。       - 配置强制流分类a. 执行命令 **service-class** service-class **color** color ，配置指定服务等级的报文标记颜色。b. （可选）执行命令 **service-class** service-class **color** color **track** { **master** |**slave** } **bfd-session** **session-name** bfd-session-name ，配置依据指定的BFD 的会话状态来标记匹配流策略后的报文服务等级和颜色。c. 执行命令 **commit** ，提交配置。       - 设置报文的优先级a. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **traffic behavior** behavior-name ，定义流行为并进入流行为视图。c. 请根据实际情况进行如下配置。#### ▪ 如果重新设置 VXLAN 报文的 DSCP 值，执行命令 remark dscp dscpvalue 。#### ▪ 如果重新设置 VXLAN 报文的 QoS 策略 ID 值，执行命令 remark qos-local**id** qos-local-id-value 。说明NetEngine 40E-X8AK 不支持重新设置 VXLAN 报文的 QoS 策略 ID 值。d. 执行命令 **commit** ，提交配置。       - 配置级联流策略流行为中一般使用 ACL 规则进行重定向，但是 ACL 规则是有一定的规格限制的，复杂流分类所能定义的 ACL 规则无法满足实际场景的需求时，可以将流行为重定向到一个已经配置完成的流策略中，从而实现级联复杂流分类。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 102HUAWEI NetEngine40E配置指南 1 QoSa. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **traffic behavior** behavior-name ，定义流行为并进入流行为视图。c. 执行命令 **traffic-policy** policy-name ，配置级联下一个流策略。#### ▪ 配置级联流策略会造成设备转发性能下降。 ▪ 接口流量匹配级联的流策略时：           - 转发行为按照级联的流策略行为执行           - 非重复的流策略行为可以分别执行           - 重复的流策略行为按照级联的流策略行为执行#### ▪ 流策略接口上应用时所指定的 inbound 或 outbound 方向， link-layer 、mpls-layer 和 all-layer 等参数被级联的流策略继承。#### ▪ 两级 ACL 的流行为都是 service-class 时，以第一级 service-class 优先生效；但是如果第一级 service-class 带有 no-remark ，还是以第二级service-class 优先生效。d. （可选）执行命令 **hierarchical-car enable** ，在复杂流分类级联策略场景中使能层次化 CAR 动作。在流行为中嵌套流策略后，流量策略中可以配置 CAR 动作，实现对流量的层次化 CAR 限速。e. 执行命令 **commit** ，提交配置。**----**结束##### 1.5.8.3 （可选）使能匹配 VXLAN 报文的 QoS 策略 ID 模式定义流分类时如果定义了基于 VXLAN 报文的 QoS 策略 ID 的匹配规则，需要使能匹配VXLAN 报文的 QoS 策略 ID 的模式功能，匹配规则才能生效。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **traffic policy** policy-name ，进入已经定义的流策略视图。步骤 **3** 执行命令 **match-type vxlan-qos-local-id enable** ，使能匹配 VXLAN 报文的 QoS 策略ID 模式。说明NetEngine 40E-X8AK 不支持此配置。步骤 **4** 执行命令 **commit** ，提交配置。**----**结束##### 1.5.8.4 定义流量策略定义了流分类和动作后，需要配置流量策略，为定义的流关联动作。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 103HUAWEI NetEngine40E配置指南 1 QoS##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **traffic policy** policy-name ，定义流量策略并进入流策略视图。步骤 **3** 执行命令 **classifier** classifier-name **behavior** behavior-name [ **precedence**precedence-value ] ，在流量策略中为流分类指定采用的行为并设定策略匹配的优先级。步骤 **4** （可选）执行命令 **step** step-value ，配置策略间的步长。步骤 **5** （可选）执行命令 **statistics enable** ，使能流量策略的统计功能。为节省内存资源，系统缺省不使能流量策略的统计功能。当用户需要查看流量策略的统计数据时，可使能流量策略的统计功能。步骤 **6** 执行命令 **commit** ，提交配置。**----**结束##### 1.5.8.5 应用流量策略已配置的基于类的策略，需要应用到 BD/VPN 才能生效。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 请根据流量策略应用的实例类型选择下面的配置步骤。       - 在 BD 下应用复杂流分类策略，请执行如下步骤：a. 执行命令 **bridge-domain** bd-id ，进入 BD 视图。b. 执行命令 **traffic-policy** policy-name { **inbound** [ **link-layer** ] | **outbound** }**vxlan-mode** ，在 BD 下应用流量策略。c. （可选）执行命令 **traffic-policy-action car disable** [ **exclude** ] **slot** slot-id**vxlan-mode** ，去使能指定槽位号单板 VXLAN 模式的复杂流策略的 CAR 功能。说明为避免出现配置矛盾，相同视图下是否带 **exclude** 的配置不能同时存在。       - 在 VPN 下应用复杂流分类策略，请执行如下步骤：a. 执行命令 **ip** [ **dcn** ] **vpn-instance** vpn-instance-name ，进入 VPN 视图。b. 执行命令 **traffic-policy** policy-name { **inbound** [ **link-layer** ] | **outbound** }**vxlan-mode** ，在 VPN 下应用流量策略。c. （可选）执行命令 **traffic-policy-action car disable** [ **exclude** ] **slot** slot-id**vxlan-mode** ，去使能指定槽位号单板 VXLAN 模式的复杂流策略的 CAR 功能。说明为避免出现配置矛盾，相同视图下是否带 **exclude** 的配置不能同时存在。步骤 **3** 执行命令 **commit** ，提交配置。**----**结束文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 104HUAWEI NetEngine40E配置指南 1 QoS##### ******* 检查配置结果VXLAN 报文复杂流分类配置成功后，可以查看设备配置的类信息、流行为信息、指定策略中指定类及与类关联的行为的配置信息、策略的配置信息和运行情况、流量策略信息、队列配置信息和运行情况等内容。##### 操作步骤       - 使用 **display traffic behavior** { **system-defined** | **user-defined** } [ behaviorname ] 命令查看流行为的配置信息。       - 使用 **display traffic classifier** { **system-defined** | **user-defined** } [ classifiername ] 命令查看流分类的配置信息。       - 使用 **display traffic policy** { **system-defined** | **user-defined** } [ policy-name[ **classifier** classifier-name ] ] 命令查看流策略中所有流分类与流行为的关联信息或特定流分类与流行为的关联信息。       - 使用 **display traffic policy** **statistics** { **bridge-domain** bdid **vxlan-mode** |**vpn-instance** vpn-name **vxlan-mode** } { **inbound** | **outbound** } [ **verbose**{ **classifier-based** [ **class** class-name ] | **rule-based** [ **class** class-name ][ **filter** ] } ] 命令查看接口的流量策略统计信息。VS 模式下，该命令仅在 Admin VS 支持。**----**结束#### 1.5.9 （可选）设置协议报文的优先级配置主机报文的优先级，可以指定配置本机发送的管理协议报文或控制协议报文的DSCP （ Differentiated Services Code Point ）值，并根据这些协议报文的 DSCP 值让协议报文进入指定的内部优先级队列及获取对应的内部优先级颜色。##### 背景信息目前 NE40E 对协议报文进行内部调度时，默认指定为 CS6 队列，没有颜色，优先级是固定的。如果用户对 CS6 队列有特殊的用处，或者不用于业务转发，就会影响业务。同时，在下游设备中，指定的协议报文可能会因为进入低优先级的 QoS 队列而不满足调度需求，所以需要指定协议报文可以进入其他的队列进行灵活调度。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 请根据需要报文的类型，配置管理协议报文或控制协议报文的 DSCP 值。       - 执行命令 **host-packet type** { **management-protocol** | **control-protocol** } **dscp**dscp-value ，配置 IPv4 管理协议报文或控制协议报文的 DSCP 值。       - 执行命令 **host-packet ipv6 type** { **management-protocol** | **control-protocol** }**dscp** dscp-value ，配置 IPv6 管理协议报文或控制协议报文的 DSCP 值。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 105HUAWEI NetEngine40E配置指南 1 QoS说明一般情况下，各协议都有默认 DSCP 值，且部分协议的 DSCP 值可以通过 **host-packet type** 命令和各协议自己修改 DSCP 值的命令配置，这种情况下， DSCP 的配置生效规则如下：          - 当协议有自己的修改命令时，不管是否受 **host-packet type** 命令控制，都按协议自己的修改命令配置的 DSCP 值生效；          - 当协议没有自己的修改命令时，且受 **host-packet type** 命令控制，则按 **host-packet type** 命令配置的 DSCP 值生效；          - 当协议没有自己的修改命令时，且不受 **host-packet type** 命令控制，则按照默认 DSCP 值生效。如果需要了解各 PHB 对应的 DSCP 取值及含义请参考 DSCP 与 PHB 。IPv4 协议的 ToS/DSCP 值与 IPv6 协议的 Traffic Class/DSCP 值的修改方法如 表 **1-18** 和 表**1-19** 所示。表 **1-18** IPv4 协议的 ToS/DSCP 值及修改方法|协议|默认ToS/<br>DSCP值|是否受host-packet<br>type命令控制|各协议自己的修改命令||---|---|---|---||ICMP_ECHO|0|否|**ping** **-dscp** dscp-value||ICMP_ECHO<br>_REPLY|0|否|NA||ICMP差错|48|否|NA||DNS|0|否|NA||FTP|48|是，**host-packet type**<br>**management-protocol**|NA||TFTP|48|是，**host-packet type**<br>**management-protocol**|NA||SNMP|48|是，**host-packet type**<br>**management-protocol**|**snmp-agent packet-**<br>**priority** **snmp** priority-<br>level||SSH|48|是，**host-packet type**<br>**management-protocol**|**ssh server dscp** value||TELNET|48|是，**host-packet type**<br>**management-protocol**|**telnet server dscp**<br>value||SYSLOG<br>（UDP）|0|是，**host-packet type**<br>**management-protocol**|**info-center syslog**<br>**packet-priority**<br>priority-level<br>**info-center syslog**<br>**packet-priority**<br>priority-level命令优先级<br>高于**host-packet type**<br>**management-protocol**<br>命令。|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 106HUAWEI NetEngine40E配置指南 1 QoS|协议|默认ToS/<br>DSCP值|是否受host-packet<br>type命令控制|各协议自己的修改命令||---|---|---|---||SYSLOG<br>（TCP）|0|否|**info-center syslog**<br>**packet-priority**<br>priority-level||HWTACACS|48|是，**host-packet type**<br>**management-protocol**|NA||Radius|48|否|NA||NTP|48|是，**host-packet type**<br>**control-protocol**|NA||BFD|56|否|**tos-exp** tos-value<br>（BFD会话视图）<br>**tos-exp** tos-value<br>{ **dynamic** |**static** }<br>（BFD视图）||IGMP|48|否|NA||PIM|48|否|NA||CUSP|48|是，**host-packet type**<br>**control-protocol**|NA||BGP|48|是，**host-packet type**<br>**control-protocol**|NA||LDP|48|是，**host-packet type**<br>**control-protocol**|NA||OSPF|48|是，**host-packet type**<br>**control-protocol**|NA||DHCP<br>Server/<br>DHCP Relay|48|否|**dhcp dscp-outbound**<br>value||DHCP<br>Snooping|0|否|NA||GRE|内层IP ToS如<br>果有效则继承<br>内层IP报文<br>ToS/DSCP<br>值，否则设置<br>为48|否|NA||IKE|48|否|NA|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 107HUAWEI NetEngine40E配置指南 1 QoS|协议|默认ToS/<br>DSCP值|是否受host-packet<br>type命令控制|各协议自己的修改命令||---|---|---|---||VXLAN|内层IP ToS如<br>果有效则继承<br>内层IP报文<br>ToS/DSCP<br>值，否则设置<br>为48|否|NA||RSVP-TE|48|否|NA||MSDP|48|否|NA||PCEPv4|48|否|**dscp**dscp-val|表 **1-19** IPv6 协议的 Traffic Class/DSCP 值及修改方法|协议|默认Traffic<br>Class/DSCP值|是否受host-packet<br>type命令控制|各协议自己的修改命令||---|---|---|---||ICMP6_ECH<br>O|0|否|**ping ipv6** **-tc** traffic-<br>class-value||ICMP6_ECH<br>O_REPLY|复制<br>ICMP6_ECHO<br>报文TC/DSCP<br>值|否|NA||ICMP6差错|复制<br>ICMP6_ECHO<br>报文TC/DSCP<br>值|否|NA||ND<br>（NS/NA/RS<br>/RA）|48|否|NA||TNL6（IPv6<br>over IPv4）|0|否|NA||TNL6（IPv4<br>over IPv6）|0|否|**tunnel ipv4-ipv6**<br>**trafc-class** class-<br>value||DNSv6|0|否|NA||FTPv6|0|是，**host-packet ipv6**<br>**type management-**<br>**protocol**|NA||TFTPv6<br>SERVER|NA|否|NA|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 108HUAWEI NetEngine40E配置指南 1 QoS|协议|默认Traffic<br>Class/DSCP值|是否受host-packet<br>type命令控制|各协议自己的修改命令||---|---|---|---||TFTPv6<br>CLIENT|0|是，**host-packet ipv6**<br>**type management-**<br>**protocol**|NA||SNMPv6|48|否|**snmp-agent packet-**<br>**priority** **snmp** priority-<br>level||SSHv6|0|是，**host-packet ipv6**<br>**type management-**<br>**protocol**|NA||TELNETv6|0|是，**host-packet ipv6**<br>**type management-**<br>**protocol**|NA||SYSLOG<br>（UDP）|0|是，**host-packet ipv6**<br>**type management-**<br>**protocol**|**info-center syslog**<br>**packet-priority**<br>priority-level||SYSLOG<br>（TCP）|0|否|**info-center syslog**<br>**packet-priority**<br>priority-level||HWTACACS|48|否|NA||Radius|48|否|NA||NTPv6|0|是，**host-packet ipv6**<br>**type management-**<br>**protocol**|NA||BFDv6|56|否|**tos-exp** tos-value<br>（BFD会话视图）||BFDv6|56|否|**tos-exp** tos-value<br>{ **dynamic** |**static** }<br>（BFD视图）||MLD|48|否|NA||PIMv6|48|否|NA||BGP4+|48|是，**host-packet ipv6**<br>**type control-protocol**|NA||OSPFv3|48|是，**host-packet ipv6**<br>**type control-protocol**|NA||DHCPv6|48|否|NA|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 109HUAWEI NetEngine40E配置指南 1 QoS|协议|默认Traffic<br>Class/DSCP值|是否受host-packet<br>type命令控制|各协议自己的修改命令||---|---|---|---||GRE|内层IP TC如果<br>有效则继承内<br>层IP报文TC/<br>DSCP值，否<br>则设置为48|否|NA||VXLAN|内层IP TC如果<br>有效则继承内<br>层IP报文TC/<br>DSCP值，否<br>则设置为48|否|NA||PCEPv6|48|否|**dscp**dscp-val|步骤 **3** 执行命令 **host-packet dscp** dscp-value **map local-service** cos-value [ **color**color ] ，配置协议报文 DSCP 值和内部优先级及内部优先级颜色的对应关系。说明**host-packet type** 命令中的 DSCP 值只用来表示协议报文的优先级，二层协议的 802.1P 值是由该DSCP 值依据出接口配置的 DS 作用域（ DS-domain ）映射出来的， MPLS 报文的 EXP 值取的是该DSCP 值的高三位。如果不使用该命令，最终发送出去的协议报文优先级是设备预先固定设置的。步骤 **4** 执行命令 **commit** ，提交配置。**----**结束#### 1.5.10 维护基于类的 QoS介绍如何清空流量策略的统计信息。##### ******** 清空流量策略的统计信息介绍清空流量策略统计信息的命令。##### 背景信息须知清除统计信息后，以前的统计信息将无法恢复，务必仔细确认。##### 操作步骤       - 执行命令 **reset traffic** **policy** [ [ **name** ] policy-name ] **statistics** **interface**{ interface-name | interface-type interface-number } [ **vlan** vlan-id | **pe-vid**pe-vid **ce-vid** ce-vid | **vid** vid | **ce-vid** ce-vid | **vid** vid **ce-vid** ce-vid ] { **inbound**| **outbound** } ，清空指定接口下的流量策略统计数据。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 110HUAWEI NetEngine40E配置指南 1 QoS       - 执行命令 **reset traffic policy statistics** **bridge-domain** bdid { **inbound** |**outbound** } ，清空指定 BD 域下的流量策略统计数据。       - 执行命令 **reset traffic policy** [ **name** policy-name ] **statistics** { **vsi** vsiinstance-name [ **ac-mode** ] | **vpn-instance** vpn-instance-name } [ **slot** slotid ] { **inbound** | **outbound** } ，清空 VSI 实例绑定的 AC 接口的流量策略统计数据。       - 执行命令 **reset flow-car** [ **ipv6** ] [ { **source-ip** | **destination-ip** } ip-address ]**slot** slot-id ，清除指定单板的 flow-car 表。       - 执行命令 **reset flow-car** [ **ipv6** ] **all** ，清除整机的 flow-car 表。       - 执行命令 **reset flow-car** [ **ipv6** ] **statistics** { **source-ip** | **destination-ip** } [ ipaddress ] **slot** slot-id { **inbound** | **outbound** } ，根据源或目的 IP ，清除某槽位指定方向的 flow-car 统计计数。       - 执行命令 **reset user-queue statistics** **interface** { interface-name | interfacetype interface-number } { **pe-vid** pe-vid **ce-vid** ce-vid | **vlan** vlan-id }{ **inbound** | **outbound** } [ **policy** policy-name ] **classifier** classifier-name 或**reset user-queue statistics** **bridge-domain** bd-id { **inbound** | **outbound** }[ **policy** policy-name ] **classifier** classifier-name ，清空指定接口上用户队列的统计计数。**----**结束#### 1.5.11 配置举例介绍几种典型的流分类 QoS 策略的配置举例。##### ******** 配置 IP 报文复杂流分类示例以 IP 报文为例，介绍复杂流分类的配置。##### 组网需求如 图 **1-9** 所示，通过在 DeviceC 上配置复杂流分类，可实现对 DeviceA 与 DeviceB 之间的访问控制，并可以通过流量统计验证报文的收发情况。说明本例中 Interface1 ， Interface2 分别代表 GE1/0/0 ， GE2/0/0 。图 **1-9** 配置基于复杂流分类的流量策略示例图##### 配置思路采用如下的思路配置基于复杂流分类的流量策略：文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 111HUAWEI NetEngine40E配置指南 1 QoS1. 定义 ACL 规则。2. 定义流分类。3. 定义流行为。4. 定义流量策略。5. 应用流量策略。##### 数据准备为完成此配置举例，需准备如下的数据：       - ACL 编号。       - 流分类、流行为、流量策略的名字以及应用的接口号。##### 操作步骤步骤 **1** 定义 ACL 规则。<HUAWEI> **system-view**[ ~ HUAWEI] **sysname DeviceC**[*HUAWEI] **commit**[ ~ DeviceC] **acl number 3333**[*DeviceC-acl-advance-3333] **rule 5 permit ip source ******* 0 destination ******* 0**[*DeviceC-acl-advance-3333] **rule 10 permit ip source ******* 0 destination ******* 0**[*DeviceC-acl-advance-3333] **commit**[ ~ DeviceC-acl-advance-3333] **quit**步骤 **2** 定义流分类。[ ~ DeviceC] **traffic classifier c1**[*DeviceC-classifier-c1] **if-match acl 3333**[*DeviceC-classifier-c1] **commit**[ ~ DeviceC-classifier-c1] **quit**步骤 **3** 定义流行为。[ ~ DeviceC] **traffic behavior b1**[*DeviceC-behavior-b1] **permit**[*DeviceC-behavior-b1] **commit**[ ~ DeviceC-behavior-b1] **quit**步骤 **4** 定义流量策略。[ ~ DeviceC] **traffic policy p1**[*DeviceC-trafficpolicy-p1] **classifier c1 behavior b1**[*DeviceC-trafficpolicy-p1] **share-mode**[*DeviceC-trafficpolicy-p1] **statistics enable**[*DeviceC-trafficpolicy-p1] **commit**[ ~ DeviceC-trafficpolicy-p1] **quit**步骤 **5** 应用流量策略。[ ~ DeviceC] **interface gigabitethernet 1/0/0**[ ~ DeviceC-GigabitEthernet1/0/0] **traffic-policy p1 inbound**[*DeviceC-GigabitEthernet1/0/0] **traffic-policy p1 outbound**[*DeviceC-GigabitEthernet1/0/0] **commit**[ ~ DeviceC-GigabitEthernet1/0/0] **quit**[ ~ DeviceC] **interface gigabitethernet 2/0/0**[ ~ DeviceC-GigabitEthernet2/0/0] **traffic-policy p1 inbound**[*DeviceC-GigabitEthernet2/0/0] **traffic-policy p1 outbound**[*DeviceC-GigabitEthernet2/0/0] **commit**[ ~ DeviceC-GigabitEthernet2/0/0] **quit**步骤 **6** 检查配置结果。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 112
最终截取: ##### ******** 清空流量策略的统计信息


介绍清空流量策略统计信息的命令。

##### 背景信息


须知


清除统计信息后，以前的统计信息将无法恢复，务必仔细确认。

##### 操作步骤

       - 执行命令 **reset traffic** **policy** [ [ **name** ] policy-name ] **statistics** **interface**
{ interface-name | interface-type interface-number } [ **vlan** vlan-id | **pe-vid**
pe-vid **ce-vid** ce-vid | **vid** vid | **ce-vid** ce-vid | **vid** vid **ce-vid** ce-vid ] { **inbound**
| **outbound** } ，清空指定接口下的流量策略统计数据。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 110



HUAWEI NetEngine40E
配置指南 1 QoS


       - 执行命令 **reset traffic policy statistics** **bridge-domain** bdid { **inbound** |
**outbound** } ，清空指定 BD 域下的流量策略统计数据。

       - 执行命令 **reset traffic policy** [ **name** policy-name ] **statistics** { **vsi** vsiinstance-name [ **ac-mode** ] | **vpn-instance** vpn-instance-name } [ **slot** slotid ] { **inbound** | **outbound** } ，清空 VSI 实例绑定的 AC 接口的流量策略统计数据。

       - 执行命令 **reset flow-car** [ **ipv6** ] [ { **source-ip** | **destination-ip** } ip-address ]
**slot** slot-id ，清除指定单板的 flow-car 表。

       - 执行命令 **reset flow-car** [ **ipv6** ] **all** ，清除整机的 flow-car 表。

       - 执行命令 **reset flow-car** [ **ipv6** ] **statistics** { **source-ip** | **destination-ip** } [ ipaddress ] **slot** slot-id { **inbound** | **outbound** } ，根据源或目的 IP ，清除某槽位指
定方向的 flow-car 统计计数。


       - 执行命令 **reset user-queue statistics** **interface** { interface-name | interfacetype interface-number } { **pe-vid** pe-vid **ce-vid** ce-vid | **vlan** vlan-id }
{ **inbound** | **outbound** } [ **policy** policy-name ] **classifier** classifier-name 或
**reset user-queue statistics** **bridge-domain** bd-id { **inbound** | **outbound** }

[ **policy** policy-name ] **classifier** classifier-name ，清空指定接口上用户队列的
统计计数。


**----**
结束

