

系统支持两种配置生效模式：立即生效模式和两阶段生效模式。缺省为两阶段生效模
式。系统还支持单板离线方式对接口进行配置。

##### 配置生效模式


系统支持两种配置生效模式：立即生效模式和两阶段生效模式。缺省为两阶段生效模
式。


       - 立即生效模式是传统的生效模式。


此模式使用命令 **system-view** **immediately** 进入系统视图。用户在输入命令行并
输入回车键后，系统执行语法检查，如果语法检查通过则配置立即生效。


说明


立即生效模式下，不支持配置回退功能。


       - 两阶段生效模式将系统配置分为两个阶段。


此模式使用命令 **system-view** 进入系统视图。第一阶段用户输入配置命令，系统
在候选数据集执行命令语法和语义检查，对于有错误的配置语句，系统通过命令
行终端提醒用户配置错误及错误原因。用户完成系列配置命令的输入后，输入
**commit** 命令提交配置，系统进入第二阶段，即配置的提交阶段。此时系统将候选
数据集上的配置下发到业务，如果业务生效则将候选数据集的配置合并到当前系


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 186


HUAWEI NetEngine40E
配置指南 1 基础配置


统的运行数据集。在配置的提交阶段，系统会进行检查，发现配置相同时会产生
提示信息。


从下表可以看出立即生效模式和两阶段生效模式的优缺点：


表 **1-32** 立即生效模式和两阶段生效模式的优缺点






|生效模式|优点|缺点|
|---|---|---|
|立即生效模式|配置产生的业务影响能够<br>立即反映在系统上。|由于配置是立即生效的，<br>用户在配置错误时会直接<br>对运行业务产生影响，且<br>不能将业务作为一个整体<br>直接丢弃，用户需要逐条<br>撤销配置。|
|两阶段生效模式|●用户要求对业务的配置<br>能够整体生效。<br>●用户可以预览候选数据<br>集里面的配置。<br>●用户在预览配置后发现<br>业务配置产生错误或配<br>置不符合用户预期时，<br>能够立即清除未生效的<br>配置。<br>●用户要求把配置过程对<br>现有业务的影响降到最<br>低。|需要输入**commit**命令配<br>置才可以生效。<br>说明<br>在两阶段生效模式中，如果<br>用户存在未提交的配置时，<br>在对应的视图中用*标识。如<br>果用户所有配置均已提交<br>时，在对应的视图中用~标<br>识。此种情况，用户视图除<br>外。|


##### 离线配置

当前系统支持离线配置。即当在接口上配置了命令后，拔出单板，此时单板配置不丢
失，并且仍然能够对接口进行配置。


此时当在单板槽位插入其他单板时，对原有配置的影响如下：


       - 同一槽位插入和之前类型相同的单板


系统会自动恢复该单板上所有接口的配置。此时用户又可以看到该单板上的接
口，也能对它们进行配置。


       - 同一槽位插入和之前接口类型不同的单板


系统会先删除原来单板的接口配置。即使用户不对现在的单板进行配置，就再拔
出，然后再插入原来拔出的单板，这些配置也不会再被恢复。


例如：单板 A 的接口类型为 P ，单板 A 上有配置信息。


a. 先把单板 A 拔出后，插入接口类型为 E 的单板 C 。


b. 不对单板 C 进行任何配置，再拔出单板 C 。在单板 C 的接口视图下通过 **display**
**this** 命令，不能看到原来在单板 A 上的配置信息了。


c. 再插入单板 A 或接口类型为 P 的其他单板。在单板 A 或接口类型为 P 的其他单板
的接口视图下通过 **display this** 命令，也不能看到原来在单板 A 上的配置信
息。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 187


HUAWEI NetEngine40E
配置指南 1 基础配置


       - 插入接口数目不同但接口类型相同的单板


–
如果插入的单板上的接口数目比之前拔出的单板上的接口数目多，则系统会
进行如下处理：

#### ▪ 与之前单板相同的接口：直接恢复之前保留的接口配置信息。 ▪ 其余接口：接口下只保留默认配置信息。


–
如果插入的单板上的接口数目比之前拔出的单板上的接口数目少，则系统会
进行如下处理：

#### ▪ 与之前单板相同的接口：直接恢复之前保留的接口配置信息。 ▪ 其余接口：全部删除接口及配置信息。


通过 **display current-configuration inactive** 或 **display current-configuration all**
命令可以查看到设备中的离线配置信息。
