

用户可以配置是否使能兼容低版本 SSH 协议，配置或变更 SFTP 服务器侦听端口号以及
配置服务器密钥对更新时间等。

##### 背景信息


SSH 服务器参数如 表 **1-29** 所示。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 169


HUAWEI NetEngine40E
配置指南 1 基础配置


表 **1-29** SSH 服务器参数






|SFTP服务器参<br>数|说明|
|---|---|
|SFTP服务器侦<br>听端口号|如果SFTP服务器端登录设备时侦听标准端口号，可能会有攻击者不<br>断访问此端口，导致带宽和服务器性能的下降，造成其他正常用户<br>无法访问。所以可以重新配置SFTP服务器的侦听端口号，攻击者不<br>知道SSH侦听端口号的更改，有效防止攻击者对SSH服务标准端口<br>的访问，确保了安全性。|
|服务器密钥对<br>更新时间|当SFTP服务器的更新周期到达时，自动更新服务器密钥对，从而保<br>证安全性。|
|连接认证超时<br>时间|当设置的SSH认证超时时间到达后，如果用户还未登录成功，则终<br>止当前连接，确保安全性。|
|服务器最大客<br>户端数|指定的连接数比当前连接数小，已连接上的用户不会下线，但是同<br>时，服务器也不再接受新的连接。|
|服务器访问控<br>制列表|基于IPv6协议的SSH服务器设置访问控制列表，允许指定客户端访<br>问，有效防止非法用户登录SSH服务器，确保了安全性。|
|服务器<br>keepalive特性|使能keepalive特性后，服务器将发送一个keepalive回应给SSH客<br>户端，检测对端是否可达，以便尽早发现网络故障。|
|服务器和客户<br>端之间断开连<br>接的超时时间|当SFTP用户连接的空闲时间超过设定的阈值后，系统会自动断开此<br>用户的连接，从而有效避免用户长期占用连接而不进行任何操作。|
|指定SSH服务<br>器的源接口|成功指定SSH服务器端的源接口后，系统只允许SFTP用户通过指定<br>的源接口登录服务器，通过其他接口登录的SFTP用户都将被拒绝。|
|SSH服务器认<br>证方式的伪列<br>表模式|去使能SSH服务器认证方式的伪列表模式，可减少某些SFTP用户使<br>用password认证方式登录服务器的认证时长。|


##### 操作步骤

步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 根据需要，可执行如 表 **1-30** 中的一个或多个操作。


表 **1-30** SFTP 服务器参数






|SFTP服务器参数|操作|
|---|---|
|（可选）使能兼容低<br>版本功能|执行命令**ssh server compatible-ssh1x enable**<br>说明<br>如果SSH协议使能兼容低版本功能，系统会提示存在安全风险。|
|（可选）配置SFTP<br>服务器侦听端口号|执行命令**ssh server port** port-number<br>如果配置了新的侦听端口号，SFTP服务器端先断开当前已经<br>建立的所有连接，然后使用新的端口号开始侦听。|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 170


HUAWEI NetEngine40E
配置指南 1 基础配置






|SFTP服务器参数|操作|
|---|---|
|（可选）配置服务器<br>密钥对更新时间|执行命令**ssh server rekey-interval** hours|
|（可选）配置连接认<br>证超时时间|执行命令**ssh server timeout** seconds|
|（可选）配置SSH服<br>务器最大客户端数|执行命令**sftp max-sessions** max-session-count<br>如果设置的最大连接数小于当前登录设备的用户数，那么系<br>统将拒绝新的连接请求，当前连接不会断开。|
|（可选）配置SSH服<br>务器访问控制列表|执行命令**ssh [ ipv6 ] server acl** { acl-number | acl6-<br>name }|
|（可选）使能SSH服<br>务器上的keepalive<br>特性|执行命令**ssh server keepalive enable**|
|（可选）配置服务器<br>和客户端之间断开连<br>接的超时时间|执行命令**sftp idle-timeout** minutes [ seconds ]<br>执行命令**sftp idle-timeout** **0** **0**将关闭服务器和客户端之间<br>超时断开连接功能。|
|指定SSH服务器的源<br>接口或者源地址|●执行命令**ssh server-source** **-i** { interface-type<br>interface-number | interface-name }，指定SSH服务器<br>的源接口。<br>●执行命令**ssh server-source -a** ip-address [**-vpn-**<br>**instance** vpnName ]，指定SSH服务端的IPv4源地址。<br>●执行命令**ssh server-source** **all-interface**，指定SSH服务<br>器的源接口为设备上所有接口。<br>●执行命令**ssh ipv6 server-source** **-a** ipv6-address [**-**<br>**vpn-instance** vpn-instance-name ]，指定SSH服务器的<br>源IPv6地址。<br>●执行命令**ssh ipv6 server-source** **all-interface**，指定<br>SSH服务器的源接口为设备上所有IPv6接口。<br>●执行命令**ssh server-source** **physic-isolate** **-i**<br>{ interface-type interface-number | interface-name }**-a**<br>ip-address，指定SSH服务器的源接口，并设置SSH服务<br>器的接口隔离属性。<br>●执行命令**ssh ipv6 server-source** **physic-isolate** **-i**<br>{ interface-type interface-number | interface-name }**-a**<br>ipv6-address，指定SSH服务器的源IPv6接口，并设置<br>SSH服务器的接口隔离属性。<br>说明<br>配置**all-interface**参数后，将不会指定SSH服务器的源接口，用户可<br>从所有有效接口登录，增加系统安全风险，建议用户取消该配置。<br>成功设置接口隔离属性后，只能通过配置的物理口连接设备，即报<br>文只能从配置的物理口上送，通过其他接口上送的报文会被丢弃。|
|（可选）SSH服务器<br>认证方式的伪列表模<br>式|执行命令**ssh server authentication method bogus-list**<br>**disable**|



文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 171


HUAWEI NetEngine40E
配置指南 1 基础配置


步骤 **3** 执行命令 **commit** ，提交配置。


**----**
结束
