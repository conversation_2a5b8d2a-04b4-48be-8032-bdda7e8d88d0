

用户可以通过 HTTPS 进行版本包下载操作。

##### 前提条件


已通过 HTTPS 方式登录 HTTPS 服务器，具体可参考 **1.8.12** 通过 **HTTP** 登录其他设备 。

##### 背景信息


当设备作为客户端时，可以通过 HTTPS 方式从远端 HTTPS 服务器获取版本包保存到本
地，用于版本升级。


说明


仅支持 HTTPS 方式，不支持普通 HTTP 。

##### 操作步骤


步骤 **1** 执行命令 **download** file-url [ **save-as** file-path | [ **ssl-policy** policy-name [ **ssl-**
**verify** **peer** [ **verify-dns** ] ] | **verify-dns** ] | **vpn-instance** vpn-name ] | **source-ip**
ip-address ] [*] ，下载指定 URL 的版本包到设备的对应路径。


说明


只支持下载以 .cc 结尾的版本包。


source-ip 可以支持 IPv4/IPv6 地址。


**----**
结束
