

命令行接口是用户对命令行操作的常用工具。通过命令行接口输入命令，可以对路由
器进行配置和管理。


须知


当用户执行需要输入密码的命令时，有些密码会以明文方式显示在屏幕上，为了防止
密码泄露，请用户及时清屏。

##### 命令行接口


命令行接口 CLI （ Command Line Interface ）是用户与路由器进行交互的常用工具。用
户登录到路由器出现命令行提示符后，即进入命令行接口。系统向用户提供一系列命
令，用户可以通过命令行接口输入命令，由命令行接口对命令进行解析，实现用户对
路由器的配置和管理。


CLI 是一种传统的配置工具，大多数通信产品都提供了 CLI 功能。随着通信产品在全球
范围的应用越来越广，用户对 CLI 的可用性、灵活性、界面友好性提出了更高的要求。


设备在切换为 UP 形态后，会自动建立 cp-config 视图。视图内会保存 CP 下发到 UP 的模
板名，此模板内的数据不支持本地修改。


命令行接口有如下特性：


       - 允许通过 Console 口进行本地配置。


       - 允许通过 Telnet 、 SSH 进行本地或远程配置。


       - 提供 User-interface 视图，为不同的终端用户提供个性化配置管理。


       - 命令分级保护，不同级别的用户只能执行相应级别的命令。


       - 用户可以随时键入“ ? ”来获得在线帮助。


       - 提供网络测试命令，如 **tracert** 、 **ping** 等，迅速诊断网络是否正常。


       - 提供种类丰富、内容详尽的调试信息，帮助诊断网络故障。


       - 用 **telnet** 命令直接登录并管理其它路由器。


       - 提供 FTP 服务，方便用户上传、下载文件。


       - 可以执行某条历史命令。


       - 命令行解释器提供不完全匹配和上下文关联等多种智能命令解析方法，方便用户
输入。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 14


HUAWEI NetEngine40E
配置指南 1 基础配置
