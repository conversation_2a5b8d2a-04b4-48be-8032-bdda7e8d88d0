

在本示例中，通过配置 VTY 用户界面的最大个数、呼入呼出限制、终端属性、用户级
别、验证方式和验证密码，实现通过 Telnet 或 SSH （ Stelnet ）方式使用 AAA 验证登录设
备。

##### 组网需求


当用户需要通过 Telnet 或 SSH 方式进行本地或远程配置和管理设备时可以配置相应的
VTY 用户界面，包括 VTY 用户界面的最大个数、呼入呼出限制、终端属性、用户级别以


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 51


HUAWEI NetEngine40E
配置指南 1 基础配置


及用户验证方式等。用户可以根据使用需求以及出于对设备安全性的考虑配置相应的
参数。

##### 配置思路


采用如下的思路配置 VTY 用户界面：


1. 配置 VTY 用户界面的最大数。


2. 配置 VTY 用户界面的呼入呼出限制。


3. 配置 VTY 用户界面的终端属性。


4. 配置 VTY 用户界面的用户级别。


5. 配置 VTY 用户界面的验证方式和验证密码。

##### 数据准备


为完成此配置举例，需准备如下的数据：


       - VTY 用户界面的最大个数为 21 。


       - VTY 用户界面呼入限制的 ACL 号为 2000 。


       - VTY 用户界面断开连接的时间为 30 分钟。


       - VTY 用户界面的终端屏幕每屏显示的行数为 30 。


       - VTY 用户界面的历史命令缓冲区大小为 20 。


       - VTY 用户界面的用户级别为 15 。


       - VTY 用户界面的用户验证方式为 AAA 。


说明


以上各数据除对 VTY 类型用户界面呼入呼出进行限制的 ACL 号、用户名和密码外设备均有缺省值，一
般不需要单独配置。

##### 操作步骤


步骤 **1** 配置 VTY 用户界面的最大个数


<HUAWEI> **system-view**

[ ~ HUAWEI] **user-interface maximum-vty 21**

[*HUAWEI] **commit**


步骤 **2** 配置 VTY 用户界面的呼入呼出限制类型


[ ~ HUAWEI] **acl 2000**

[*HUAWEI-acl4-basic-2000] **rule deny source ******** 0**

[*HUAWEI-acl4-basic-2000] **quit**

[*HUAWEI] **user-interface vty 0 17**

[*HUAWEI-ui-vty0-17] **acl 2000 inbound**

[*HUAWEI-ui-vty0-17] **commit**


步骤 **3** 配置 VTY 用户界面的终端属性


[ ~ HUAWEI-ui-vty0-17] **shell**

[*HUAWEI-ui-vty0-17] **idle-timeout 30**

[*HUAWEI-ui-vty0-17] **screen-length 30**

[*HUAWEI-ui-vty0-17] **history-command max-size 20**

[*HUAWEI-ui-vty0-17] **commit**


步骤 **4** 配置 VTY 用户界面的用户级别


[ ~ HUAWEI-ui-vty0-17] **user privilege level 3**

[*HUAWEI-ui-vty0-17] **commit**


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 52


HUAWEI NetEngine40E
配置指南 1 基础配置


步骤 **5** 配置 VTY 用户界面的用户验证方式为 aaa 验证


[ ~ HUAWEI-ui-vty0-17] **authentication-mode aaa**

[*HUAWEI-ui-vty0-17] **commit**

[ ~ HUAWEI-ui-vty0-17] **quit**


VTY 用户界面配置完成后，用户可以通过 Telnet 或 SSH （ STelnet ）使用 Password 方式
登录设备，实现本地或者远程维护。用户通过 Telnet 或 SSH （ STelnet ）登录设备的具
体过程请参见 配置用户通过 **Telnet** 登录系统 或 配置用户通过 **STelnet** 登录系统 。


步骤 **6** 配置 VTY 用户的相关信息


[ ~ HUAWEI] **aaa**

[ ~ HUAWEI-aaa] **local-user admin1234 password irreversible-cipher YsHsjx_202206**

[*HUAWEI-aaa] **local-user admin1234 service-type ssh**

[*HUAWEI-aaa] **local-user admin1234 level 3**

[*HUAWEI-aaa] **commit**


步骤 **7** 验证配置结果


上述配置完成后，执行命令 **display user-interface** ，可以查看 VTY 用户界面的状态。


以 VTY14 为例：


[ ~ HUAWEI] **display user-interface vty 14**
Idx Type   Tx/Rx   Modem Privi ActualPrivi Auth Int
48  VTY 14       -   3   -      A   
+  : Current UI is active.
F  : Current UI is active and work in async mode.
Idx : Absolute index of UIs.
Type : Type and relative index of UIs.
Privi: The privilege of UIs.
ActualPrivi: The actual privilege of user-interface.
Auth : The authentication mode of UIs.

A: Authenticate use AAA.
P: Authenticate use current UI's password.
Int : The physical location of UIs.


**----**
结束

##### 配置文件


#

acl number 2000
rule 5 deny source ******** 0
#
user-interface maximum-vty 21
#

aaa
local-user admin123 password irreversible-cipher $1d$+,JS+))\\2$KVNj(.
3`_5x0FCKGv}H&.kUTI`Ff&H*eBqO.ua>)$
local-user admin123 service-type ssh
local-user admin123 level 3

#
user-interface vty 0 17
authentication-mode aaa
user privilege level 3
history-command max-size 20
idle-timeout 30 0
screen-length 30
acl 2000 inbound

return
