

设备上有些命令，如果用户误操作会引发比较严重的后果。为防止用户误操作，设备
默认需要进行二次确认。

##### 背景信息


设备上有些 **undo** 命令，如果用户误操作会关联删除相关特性的配置，导致业务中断，
造成用户网络中断。缺省情况下，为了防止用户误操作，执行这些 undo 命令时，需要
用户进行二次交互确认，命令范围包含： **undo mpls** 、 **undo mpls te** 、 **undo mpls**
**rsvp** 、 **undo mpls ldp** 、 **undo mpls l2vpn** 、 **undo multicast ipv6 routing-**
**enable** 、 **undo multicast routing-enable** 、 **undo pim** 、 **undo igmp** 、 **undo bfd** 、
**undo stp enable** 。


为防止误操作导致某些业务不可用，建议使能二次交互确认功能。

##### 操作步骤


步骤 **1** 执行命令 **system-view** ，进入系统视图。


步骤 **2** 执行命令 **configuration prevent-misoperation disable** ，关闭二次确认功能。


步骤 **3** 执行命令 **commit** ，提交配置。


**----**
结束


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 193


HUAWEI NetEngine40E
配置指南 1 基础配置
