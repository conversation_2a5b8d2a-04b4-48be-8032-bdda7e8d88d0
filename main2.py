import fitz  # PyMuPDF
import pdfplumber
import re
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class TitleInfo:
    """标题信息数据类"""
    text: str
    level: int
    page_num: int
    bbox: Optional[Dict[str, float]] = None
    font_size: Optional[float] = None
    font_name: Optional[str] = None
    is_bold: bool = False

@dataclass
class ContentBlock:
    """内容块数据类"""
    text: str
    page_num: int
    bbox: Dict[str, float]
    font_size: float
    font_name: str
    is_bold: bool = False

class PDFToMarkdownConverter:
    """高精度PDF转Markdown转换器"""
    
    def __init__(self, pdf_path: str):
        self.pdf_path = pdf_path
        self.fitz_doc = None
        self.plumber_doc = None
        self.titles: List[TitleInfo] = []
        self.content_blocks: List[ContentBlock] = []
        
    def __enter__(self):
        self.fitz_doc = fitz.open(self.pdf_path)
        self.plumber_doc = pdfplumber.open(self.pdf_path)
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.fitz_doc:
            self.fitz_doc.close()
        if self.plumber_doc:
            self.plumber_doc.close()
    
    def escape_markdown_chars(self, text: str) -> str:
        """转义Markdown特殊字符，防止影响结构"""
        if not text:
            return ""
        
        # 转义Markdown特殊字符，但保留我们要生成的标题#
        escape_chars = ['*', '_', '`', '[', ']', '(', ')', '\\', '|', '~']
        for char in escape_chars:
            text = text.replace(char, f'\\{char}')
        
        # 特殊处理#号 - 只转义不在行首的#号
        lines = text.split('\n')
        escaped_lines = []
        for line in lines:
            # 如果#不在行首，则转义
            if '#' in line and not line.strip().startswith('#'):
                line = line.replace('#', '\\#')
            escaped_lines.append(line)
        
        return '\n'.join(escaped_lines)
    
    def clean_text(self, text: str) -> str:
        """清理文本内容"""
        if not text:
            return ""

        # 处理连字符换行
        text = re.sub(r'(\w+)-\s*\n\s*(\w+)', r'\1\2', text)

        # 移除目录相关的点号引导线
        text = re.sub(r'\.{3,}', '', text)

        # 移除孤立的页码数字
        text = re.sub(r'\s+\d+\s+', ' ', text)

        # 合并多个空白字符
        text = re.sub(r'\s+', ' ', text)

        # 去除首尾空白
        text = text.strip()

        return text
    
    def is_header_footer(self, text: str, page_height: float, y_pos: float) -> bool:
        """判断是否为页眉页脚"""
        if not text or len(text.strip()) < 3:
            return True

        # 页眉：在页面顶部10%区域
        if y_pos < page_height * 0.1:
            return True

        # 页脚：在页面底部10%区域
        if y_pos > page_height * 0.9:
            return True

        # 常见页眉页脚模式
        header_footer_patterns = [
            r'^\d+$',  # 纯数字页码
            r'^第\s*\d+\s*页',  # 中文页码
            r'^Page\s+\d+',  # 英文页码
            r'^\d+\s*/\s*\d+$',  # 页码格式 1/10
            r'^Copyright\s+',  # 版权信息
            r'^©\s*\d{4}',  # 版权符号
        ]

        for pattern in header_footer_patterns:
            if re.match(pattern, text.strip(), re.IGNORECASE):
                return True

        return False

    def is_table_of_contents(self, text: str) -> bool:
        """判断是否为目录内容"""
        if not text:
            return False

        # 目录特征模式
        toc_patterns = [
            r'\.{3,}',  # 连续的点号（目录中的引导线）
            r'\d+\.\d+.*\.{3,}.*\d+',  # 章节号...页码格式
            r'^目\s*录$',  # 目录标题
            r'^\s*\d+\.\d+.*\d+\s*$',  # 纯章节号和页码
        ]

        for pattern in toc_patterns:
            if re.search(pattern, text):
                return True

        return False
    
    def extract_titles_from_toc(self) -> List[TitleInfo]:
        """从目录提取标题信息"""
        titles = []
        toc = self.fitz_doc.get_toc()
        
        if not toc:
            logger.warning("PDF没有目录，将使用字体分析方法")
            return self.extract_titles_by_font_analysis()
        
        logger.info(f"从目录中找到 {len(toc)} 个标题")
        
        for level, title_text, page_num in toc:
            # 清理标题文本
            clean_title = self.clean_text(title_text)
            if not clean_title:
                continue
                
            title_info = TitleInfo(
                text=clean_title,
                level=level,
                page_num=page_num
            )
            
            # 尝试在页面中找到标题的精确位置
            try:
                page_idx = page_num - 1
                if 0 <= page_idx < len(self.plumber_doc.pages):
                    page = self.plumber_doc.pages[page_idx]
                    bbox = self.find_text_bbox(page, clean_title)
                    if bbox:
                        title_info.bbox = bbox
                        # 获取字体信息
                        font_info = self.get_font_info_at_position(page_idx, bbox)
                        if font_info:
                            title_info.font_size = font_info.get('size')
                            title_info.font_name = font_info.get('name')
                            title_info.is_bold = font_info.get('bold', False)
                    else:
                        # 如果找不到精确位置，尝试通过字体分析找到类似的标题
                        # logger.warning(f"无法找到标题 '{clean_title}' 的精确位置，尝试字体分析")
                        estimated_bbox = self.estimate_title_position(page_idx, clean_title, level)
                        if estimated_bbox:
                            title_info.bbox = estimated_bbox
                        else:
                            # 最后使用页面估计位置
                            page_height = page.height
                            title_info.bbox = {
                                "x0": 0, "top": page_height * 0.1,  # 估计在页面上方10%位置
                                "x1": page.width, "bottom": page_height * 0.15
                            }
            except Exception as e:
                # logger.warning(f"无法找到标题 '{clean_title}' 的位置: {e}")
                pass
        
            titles.append(title_info)
        
        return titles
    
    def extract_titles_by_font_analysis(self) -> List[TitleInfo]:
        """通过字体分析提取标题"""
        titles = []
        font_sizes = []
        
        # 第一遍：收集所有字体大小信息
        for page_num in range(len(self.fitz_doc)):
            page = self.fitz_doc[page_num]
            blocks = page.get_text("dict")["blocks"]
            
            for block in blocks:
                if "lines" in block:
                    for line in block["lines"]:
                        for span in line["spans"]:
                            font_sizes.append(span["size"])
        
        # 分析字体大小分布，确定标题阈值
        if not font_sizes:
            return titles
            
        font_sizes.sort(reverse=True)
        unique_sizes = sorted(list(set(font_sizes)), reverse=True)
        
        # 假设最大的几个字体大小是标题
        title_sizes = unique_sizes[:min(6, len(unique_sizes))]
        
        logger.info(f"检测到的标题字体大小: {title_sizes}")
        
        # 第二遍：提取标题
        for page_num in range(len(self.fitz_doc)):
            page = self.fitz_doc[page_num]
            plumber_page = self.plumber_doc.pages[page_num]
            page_height = plumber_page.height
            
            blocks = page.get_text("dict")["blocks"]
            
            for block in blocks:
                if "lines" in block:
                    for line in block["lines"]:
                        line_text = ""
                        line_font_size = 0
                        line_font_name = ""
                        line_is_bold = False
                        line_bbox = None
                        
                        for span in line["spans"]:
                            if span["size"] in title_sizes:
                                line_text += span["text"]
                                line_font_size = max(line_font_size, span["size"])
                                line_font_name = span["font"]
                                line_is_bold = line_is_bold or ("Bold" in span["font"])
                                
                                # 计算行的bbox
                                if line_bbox is None:
                                    line_bbox = span["bbox"]
                                else:
                                    line_bbox = (
                                        min(line_bbox[0], span["bbox"][0]),
                                        min(line_bbox[1], span["bbox"][1]),
                                        max(line_bbox[2], span["bbox"][2]),
                                        max(line_bbox[3], span["bbox"][3])
                                    )
                        
                        if line_text.strip() and line_bbox:
                            # 检查是否为页眉页脚
                            if self.is_header_footer(line_text, page_height, line_bbox[1]):
                                continue
                            
                            # 根据字体大小确定标题级别
                            level = title_sizes.index(line_font_size) + 1
                            
                            title_info = TitleInfo(
                                text=self.clean_text(line_text),
                                level=level,
                                page_num=page_num + 1,
                                bbox={
                                    "x0": line_bbox[0], "top": line_bbox[1],
                                    "x1": line_bbox[2], "bottom": line_bbox[3]
                                },
                                font_size=line_font_size,
                                font_name=line_font_name,
                                is_bold=line_is_bold
                            )
                            titles.append(title_info)
        
        return titles
    
    def find_text_bbox(self, page, text_to_find: str) -> Optional[Dict[str, float]]:
        """在页面中查找文本的边界框"""
        # 清理搜索文本
        clean_text = text_to_find.strip()

        # 获取所有词语
        words = page.extract_words()
        text_words = clean_text.split()

        if not text_words:
            return None

        # 查找所有可能的匹配位置
        potential_matches = []

        # 查找连续的词组（允许一些变化）
        for i in range(len(words) - len(text_words) + 1):
            match_score = 0
            matched_words = []

            for j, target_word in enumerate(text_words):
                if i + j < len(words):
                    word = words[i + j]["text"]
                    # 移除标点符号进行比较
                    clean_word = re.sub(r'[^\w\s]', '', word.lower())
                    clean_target = re.sub(r'[^\w\s]', '', target_word.lower())

                    if clean_word == clean_target or clean_target in clean_word:
                        match_score += 1
                        matched_words.append(words[i + j])
                    elif j == 0:  # 第一个词必须匹配
                        break

            # 如果匹配度足够高
            if match_score >= len(text_words) * 0.8 and matched_words:
                first_word = matched_words[0]
                last_word = matched_words[-1]
                bbox = {
                    "x0": first_word["x0"],
                    "top": first_word["top"],
                    "x1": last_word["x1"],
                    "bottom": last_word["bottom"]
                }
                potential_matches.append(bbox)

        if not potential_matches:
            return None

        # 如果只有一个匹配，直接返回
        if len(potential_matches) == 1:
            return potential_matches[0]

        # 如果有多个匹配，需要选择正确的那个
        # 对于"1.1 前言"这样的标题，选择后面跟着真正内容的那个
        if "1.1" in clean_text and "前" in clean_text:
            return self.select_correct_preface_title(page, potential_matches)

        # 对于其他标题，选择第一个匹配
        return potential_matches[0]

    def select_correct_preface_title(self, page, potential_matches: List[Dict[str, float]]) -> Dict[str, float]:
        """为1.1前言选择正确的标题位置"""
        words = page.extract_words()

        best_match = None
        best_score = -1

        for bbox in potential_matches:
            # 检查这个标题后面的内容
            following_words = []
            start_y = bbox["bottom"]

            for word in words:
                if word["top"] > start_y and word["top"] < start_y + 100:  # 后续100像素内的内容
                    following_words.append(word["text"].lower())

            following_text = " ".join(following_words)

            # 计算得分：真正的前言后面应该有"概述"、"本文档"等关键词
            score = 0
            preface_keywords = ["概述", "本文档", "介绍", "基础配置", "配置过程"]

            for keyword in preface_keywords:
                if keyword in following_text:
                    score += 1

            # 减分：如果后面有其他章节标题，说明这不是真正的前言
            chapter_keywords = ["1.2", "1.3", "首次登录", "命令行接口"]
            for keyword in chapter_keywords:
                if keyword in following_text:
                    score -= 2

            logger.info(f"前言候选位置 y={bbox['top']:.1f}, 得分={score}, 后续内容: {following_text[:100]}")

            if score > best_score:
                best_score = score
                best_match = bbox

        return best_match if best_match else potential_matches[0]
    
    def get_font_info_at_position(self, page_num: int, bbox: Dict[str, float]) -> Optional[Dict[str, Any]]:
        """获取指定位置的字体信息"""
        try:
            page = self.fitz_doc[page_num]
            blocks = page.get_text("dict")["blocks"]
            
            for block in blocks:
                if "lines" in block:
                    for line in block["lines"]:
                        for span in line["spans"]:
                            span_bbox = span["bbox"]
                            # 检查是否重叠
                            if (span_bbox[0] <= bbox["x1"] and span_bbox[2] >= bbox["x0"] and
                                span_bbox[1] <= bbox["bottom"] and span_bbox[3] >= bbox["top"]):
                                return {
                                    "size": span["size"],
                                    "name": span["font"],
                                    "bold": "Bold" in span["font"]
                                }
        except Exception as e:
            logger.warning(f"获取字体信息失败: {e}")
        
        return None

    def estimate_title_position(self, page_num: int, title_text: str, level: int) -> Optional[Dict[str, float]]:
        """通过字体分析估计标题位置"""
        try:
            page = self.fitz_doc[page_num]
            blocks = page.get_text("dict")["blocks"]

            # 查找相似级别的标题字体大小
            similar_titles = []
            for block in blocks:
                if "lines" in block:
                    for line in block["lines"]:
                        line_text = ""
                        max_font_size = 0
                        line_bbox = None

                        for span in line["spans"]:
                            line_text += span["text"]
                            max_font_size = max(max_font_size, span["size"])
                            if line_bbox is None:
                                line_bbox = span["bbox"]
                            else:
                                line_bbox = (
                                    min(line_bbox[0], span["bbox"][0]),
                                    min(line_bbox[1], span["bbox"][1]),
                                    max(line_bbox[2], span["bbox"][2]),
                                    max(line_bbox[3], span["bbox"][3])
                                )

                        # 检查是否包含标题的部分文字
                        if line_text.strip() and any(word in line_text for word in title_text.split()[:2]):
                            similar_titles.append({
                                "text": line_text,
                                "bbox": line_bbox,
                                "font_size": max_font_size
                            })

            # 选择最匹配的标题
            if similar_titles:
                # 按文本相似度排序
                best_match = max(similar_titles, key=lambda x: len(set(title_text.split()) & set(x["text"].split())))
                if best_match["bbox"]:
                    return {
                        "x0": best_match["bbox"][0],
                        "top": best_match["bbox"][1],
                        "x1": best_match["bbox"][2],
                        "bottom": best_match["bbox"][3]
                    }
        except Exception as e:
            logger.warning(f"估计标题位置失败: {e}")

        return None

    def extract_content_between_titles(self) -> List[Dict[str, Any]]:
        """提取标题之间的内容"""
        if not self.titles:
            logger.error("没有找到标题，无法提取内容")
            return []

        content_sections = []

        for i, title in enumerate(self.titles):
            logger.info(f"处理标题: {title.text}")

            # 确定内容提取的范围
            start_page = title.page_num - 1
            start_y = title.bbox["bottom"] if title.bbox else 0

            # 更精确的结束位置确定逻辑
            end_page = start_page
            end_y = float('inf')

            # 查找下一个标题作为结束边界
            next_title = self.titles[min(i + 1, len(self.titles) - 1)]

            print(next_title)
            # 设置结束位置
            if next_title:
                if next_title.page_num == title.page_num:
                    end_page = start_page
                    end_y = next_title.bbox["top"] if next_title.bbox else float('inf')
                else:
                    end_page = next_title.page_num - 1
                    end_y = float('inf')

            logger.info(f"内容提取范围: 页码{start_page+1}(y>{start_y:.1f}) 到 页码{end_page+1}(y<{end_y:.1f})")

            # 提取内容
            content_parts = []

            for page_num in range(start_page, end_page + 1):
                if page_num >= len(self.plumber_doc.pages):
                    break

                page = self.plumber_doc.pages[page_num]
                page_height = page.height

                # 确定当前页面的Y坐标范围
                current_start_y = start_y if page_num == start_page else 0
                current_end_y = end_y if page_num == end_page else page_height

                # 使用更精确的文本提取方法
                content_text = self.extract_text_in_range(page, current_start_y, current_end_y)

                if content_text:
                    content_parts.append(content_text)

            # 合并和清理内容
            full_content = " ".join(content_parts)
            cleaned_content = self.clean_text(full_content)

            # 验证内容质量
            if len(cleaned_content) < 10:  # 内容太短，可能提取错误
                logger.warning(f"标题 '{title.text}' 的内容太短: {len(cleaned_content)} 字符")

            content_sections.append({
                "title": title,
                "content": cleaned_content
            })
            
            if(i>3):
                break

        return content_sections

    def extract_text_in_range(self, page, start_y: float, end_y: float) -> str:
        """在指定Y坐标范围内提取文本"""
        words = page.extract_words()
        page_height = page.height

        # 按Y坐标排序，然后按X坐标排序
        words_in_range = []
        for word in words:
            if start_y <= word["top"] <= end_y:
                # 过滤页眉页脚
                if self.is_header_footer(word["text"], page_height, word["top"]):
                    continue

                # 过滤目录内容
                if self.is_table_of_contents(word["text"]):
                    continue

                words_in_range.append(word)

        # 按位置排序：先按Y坐标，再按X坐标
        words_in_range.sort(key=lambda w: (w["top"], w["x0"]))

        # 组织成行
        lines = []
        current_line = []
        current_y = None

        for word in words_in_range:
            # 如果Y坐标差异较大，开始新行
            if current_y is None or abs(word["top"] - current_y) > 5:
                if current_line:
                    lines.append(" ".join(w["text"] for w in current_line))
                current_line = [word]
                current_y = word["top"]
            else:
                current_line.append(word)

        # 添加最后一行
        if current_line:
            lines.append(" ".join(w["text"] for w in current_line))

        return "\n".join(lines)

    def generate_markdown(self, content_sections: List[Dict[str, Any]]) -> str:
        """生成Markdown格式的文档"""
        markdown_lines = []

        for section in content_sections:
            title = section["title"]
            content = section["content"]

            # 生成标题标记
            title_prefix = "#" * min(title.level, 6)  # Markdown最多支持6级标题
            escaped_title = self.escape_markdown_chars(title.text)
            markdown_lines.append(f"{title_prefix} {escaped_title}")
            markdown_lines.append("")  # 空行

            # 添加内容
            if content:
                escaped_content = self.escape_markdown_chars(content)
                # 将长段落分成合理的行
                paragraphs = self.split_into_paragraphs(escaped_content)
                for paragraph in paragraphs:
                    if paragraph.strip():
                        markdown_lines.append(paragraph)
                        markdown_lines.append("")  # 段落间空行

            markdown_lines.append("")  # 章节间额外空行

        return "\n".join(markdown_lines)

    def split_into_paragraphs(self, text: str) -> List[str]:
        """将长文本分割成段落"""
        if not text:
            return []

        # 首先按自然段落分割（双换行或特定模式）
        natural_paragraphs = re.split(r'\n\s*\n|\s{4,}', text)

        result_paragraphs = []

        for paragraph in natural_paragraphs:
            paragraph = paragraph.strip()
            if not paragraph:
                continue

            # 如果段落太长，按句子分割
            if len(paragraph) > 300:
                # 按句号、问号、感叹号分割，但保留标点
                sentences = re.split(r'([.!?。！？])', paragraph)

                current_para = ""
                for i in range(0, len(sentences), 2):
                    sentence = sentences[i].strip()
                    punctuation = sentences[i + 1] if i + 1 < len(sentences) else ""

                    if not sentence:
                        continue

                    full_sentence = sentence + punctuation

                    # 如果当前段落加上新句子超过250字符，开始新段落
                    if len(current_para) + len(full_sentence) > 250 and current_para:
                        result_paragraphs.append(current_para.strip())
                        current_para = full_sentence
                    else:
                        current_para += " " + full_sentence if current_para else full_sentence

                if current_para.strip():
                    result_paragraphs.append(current_para.strip())
            else:
                result_paragraphs.append(paragraph)

        return result_paragraphs

    def convert_to_markdown(self) -> str:
        """主转换方法"""
        logger.info("开始PDF转Markdown转换")

        # 1. 提取标题
        self.titles = self.extract_titles_from_toc()

        if not self.titles:
            logger.error("未能提取到任何标题")
            return ""

        logger.info(f"成功提取 {len(self.titles)} 个标题")

        # 2. 按页码和位置排序标题，处理没有bbox的情况
        # 对于没有bbox的标题，使用目录中的顺序作为排序依据
        def sort_key(title):
            if title.bbox:
                return (title.page_num, title.bbox["top"])
            else:
                # 对于没有bbox的标题，使用一个较大的值但保持相对顺序
                # 通过在原始TOC中的索引来确定顺序
                toc = self.fitz_doc.get_toc()
                for i, (level, text, page) in enumerate(toc):
                    if text.strip() == title.text.strip() and page == title.page_num:
                        return (title.page_num, 1000 + i)  # 1000确保在有bbox的标题之后
                return (title.page_num, 9999)  # 兜底值

        self.titles.sort(key=sort_key)

        # 3. 提取内容
        content_sections = self.extract_content_between_titles()

        # 4. 生成Markdown
        markdown_content = self.generate_markdown(content_sections)

        logger.info("PDF转Markdown转换完成")
        return markdown_content


def convert_pdf_to_markdown(pdf_path: str, output_path: str = None) -> str:
    """
    将PDF转换为Markdown格式

    Args:
        pdf_path: PDF文件路径
        output_path: 输出文件路径（可选）

    Returns:
        Markdown格式的字符串
    """
    try:
        with PDFToMarkdownConverter(pdf_path) as converter:
            markdown_content = converter.convert_to_markdown()

            if output_path:
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(markdown_content)
                logger.info(f"Markdown文件已保存到: {output_path}")

            return markdown_content

    except Exception as e:
        logger.error(f"转换失败: {e}")
        raise


def main():
    """主函数，支持命令行参数"""
    import sys

    # 默认参数
    pdf_file = "2.pdf"
    output_file = "output_markdown.md"

    # 如果提供了命令行参数
    if len(sys.argv) > 1:
        pdf_file = sys.argv[1]
    if len(sys.argv) > 2:
        output_file = sys.argv[2]

    try:
        print(f"开始转换PDF文件: {pdf_file}")
        markdown_result = convert_pdf_to_markdown(pdf_file, output_file)
        print("转换成功！")
        print(f"生成的Markdown文件: {output_file}")
        print(f"生成的Markdown长度: {len(markdown_result)} 字符")

        # 统计标题数量
        title_count = len([line for line in markdown_result.split('\n') if line.strip().startswith('#')])
        print(f"提取的标题数量: {title_count}")

        # 显示前500字符作为预览
        print("\n--- Markdown预览 ---")
        preview = markdown_result[:500]
        print(preview)
        if len(markdown_result) > 500:
            print("...")

        print(f"\n完整内容已保存到: {output_file}")
        print("转换完成！")

    except Exception as e:
        print(f"转换失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
