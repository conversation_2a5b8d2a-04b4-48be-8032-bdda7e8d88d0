

用户可以清除本次启动时加载的配置文件。用户可以清除当前设备上加载的配置文
件，也可以一键式清除接口下配置信息。

##### 背景信息


在以下情况下需要清除配置文件：


       - 路由器软件升级之后，配置文件与软件不匹配。


       - 配置文件遭到破坏，或加载了错误的配置文件。

##### 操作步骤


       - 清除当前加载的配置文件


执行命令 **reset saved-configuration** ，清除设备当前加载的配置文件。


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 206


HUAWEI NetEngine40E
配置指南 1 基础配置


说明


系统在清除路由器配置文件前会比较当前启动与下次启动的配置文件：


            - 如果一致，执行该命令将同时清除这两个配置文件。此时可以在路由器上设定下次启动
文件，否则下次启动时配置文件为空。


            - 如果不一致，执行该命令将清除下次启动的配置文件。


            - 如果路由器当前启动的配置文件为空，执行该命令后，系统将提示配置文件不存在。


慎重执行该命令，建议在技术支持人员指导下使用。


       - 一键式清除指定接口下配置信息


用户可根据 表 **1-34** 选择操作一键式清除指定接口下配置信息。


表 **1-34** 一键式清除指定接口下配置信息










|视图|操作|说明|注意事项|
|---|---|---|---|
|系统<br>视图|**clear confguration**<br>**interface** interface-<br>type interface-<br>number|选择该操作将清除指<br>定接口下配置信息，<br>用户需要从当前接口<br>视图返回到系统视<br>图，并记住需要清除<br>的接口类型和编号。|无论是在系统视图下<br>还是在接口视图下执<br>行命令清除指定接口<br>下配置信息，使用时<br>请慎重。|
|接口<br>视图|**clear confguration**<br>**this**|选择该操作将清除接<br>口下配置信息，用户<br>可直接在当前接口视<br>图下执行该操作，简<br>化用户操作。<br>说明<br>该操作不支持在<br>Controller类型接口视<br>图下执行。|选择该操作将清除接<br>口下配置信息，用户<br>可直接在当前接口视<br>图下执行该操作，简<br>化用户操作。<br>说明<br>该操作不支持在<br>Controller类型接口视<br>图下执行。|




       - 清除默认配置文件


执行命令 **reset default-configuration** ，清除设备中的默认配置文件。


说明


慎重执行该命令，建议在技术支持人员指导下使用。


       - 清除指定的不注册框或者不在位单板的非激活配置信息


a. 执行命令 **system-view** ，进入系统视图。

b. 执行命令 **clear inactive-configuration** { **chassis** chassis-id | **slot** slot-id

[ **card** card-number ] | **all** } ，清除指定的非激活配置信息。


**----**
结束
