头标题: 1.10.1 用户接入QoS配置注意事项
尾标题: 1.10.2 配置基于UCL的复杂流分类
内容: HUAWEI NetEngine40E配置指南 1 QoS步骤 **5** 验证配置结果执行命令 **display ip policy-based-route** 命令查看已使能的策略。<DeviceA> **display ip policy-based-route**policy Name           Interfaceaaa             GigabitEthernet1/0/0aaa             GigabitEthernet2/0/0执行命令 **display policy-based-route** 命令查看已创建的策略内容。<DeviceA> **display policy-based-route**----------------------------------------------------User Defined policy-based-route Policy Information:----------------------------------------------------Total: 100 Used: 1 Free: 99Policy: aaaNode: 5   MapInstance: 5if-match acl name a3001apply output-interface Tunnel30Node: 10  MapInstance: 10if-match acl name a3002apply output-interface Tunnel40**----**结束##### 配置文件       - DeviceA 的配置文件#sysname DeviceA#acl number 3001rule 5 permit ip source *********** *********acl number 3002rule 5 permit ip source *********** *********#interface Tunnel30#interface Tunnel40#policy-based-route aaa permit node 5 map-instance 5if-match acl name a3001apply output-interface Tunnel30policy-based-route aaa permit node 10 map-instance 10if-match acl name a3002apply output-interface Tunnel40#interface GigabitEthernet1/0/0undo shutdownip address *********** *************ip policy-based-route aaa#interface GigabitEthernet2/0/0undo shutdownip address *********** *************ip policy-based-route aaa#return       - DeviceB 的配置文件#sysname DeviceB#interface GigabitEthernet1/0/0undo shutdownip address *********** *************#文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 156HUAWEI NetEngine40E配置指南 1 QoSip route-static ******** ************* ***********#return       - DeviceC 的配置文件#sysname DeviceC#interface GigabitEthernet1/0/0undo shutdownip address *********** *************#ip route-static ******** ************* ***********#return### 1.7 QPPB 配置介绍了 QPPB 的基本原理、配置过程和配置举例。#### 1.7.1 QPPB 概述应用 QPPB 技术可以由 BGP 路由发送者通过设置 BGP 属性预先对路由进行分类。在部署大型复杂组网环境中，需要执行大量的复杂流分类，而且无法按照团体属性、ACL 、 Prefix 或 AS-Path 对报文进行分类。在网络结构不稳定，需经常变化网络结构时，配置修改路由分类策略的工作量非常大甚至难以实施。在这种情况下，可以通过部署 QPPB 减少配置修改的工作量，只需要修改 BGP 路由发送者上的路由策略就可以满足需求。QPPB （ Qos Policy Propagation Through the Border Gateway Protocol ）是通过 BGP传播 QoS 策略的简称，优势是通过 BGP 路由发送者设置 BGP 属性，预先对路由进行分类； BGP 路由接收者可以依据 BGP 路由发送者设置属性对 BGP 路由应用不同的本地 QoS策略。QPPB 实现机制如下：       - 在 BGP 路由发送端，首先通过匹配路由策略，为发送到路由接收端的不同路由信息设置不同的 BGP 路由属性，包括 AS_PATH 、团体属性、扩展团体属性等。       - 在 BGP 路由接收端，主要有以下流程：a. 根据接收到的 BGP 路由属性信息，包括 AS_PATH 、团体属性、扩展团体属性等，通过匹配路由接收策略，对匹配的 BGP 路由设置关联的 QoS 策略 ID （ QoSLocal-ID ）或 IP 优先级。b. 报文转发时，根据关联的 QoS 策略 ID 使用不同的流行为。c. 创建 QPPB 本地策略，为 BGP 路由设置关联的 QoS 策略。d. 在接口应用，对于所有符合匹配规则的报文实施 QPPB 本地策略。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 157HUAWEI NetEngine40E配置指南 1 QoS#### 1.7.2 QPPB 配置注意事项##### 特性限制表 **1-20** 本特性的使用限制|特性限制|系列|涉及产品||---|---|---||全局复杂流支持源和目的qos-local-id重定向不支持<br>LPUF-50/LPUF-50-L/LPUI-21-L/LPUI-51-L/LPUF-51/<br>LPUF-51-B/LPUI-51/LPUI-51-B/LPUS-51/LPUF-101/<br>LPUF-101-B/LPUI-101/LPUI-101-B/LPUS-101单板。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||接口上复杂流支持源和目的qos-local-id不支持<br>LPUF-50/LPUF-50-L/LPUI-21-L/LPUI-51-L/LPUF-51/<br>LPUF-51-B/LPUI-51/LPUI-51-B/LPUS-51/LPUF-101/<br>LPUF-101-B/LPUI-101/LPUI-101-B/LPUS-101单板。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||接口复杂流不支持outbound方向同时匹配源和目的<br>qos-local-id。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 158HUAWEI NetEngine40E配置指南 1 QoS|特性限制|系列|涉及产品||---|---|---||全局复杂流不支持outbound方向同时匹配源和目的<br>qos-local-id。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||当QPPB流动作配置为remark dscp时，仅修改报文<br>DSCP值但是不按照修改后的DSCP值入队列。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK|#### 1.7.3 配置全局 QPPB##### 应用环境配置全局 QPPB 可以对不同接口接收的同一类型报文实现相同的行为策略。如 图 **1-20** 所示， Device A 可以从多个接口接收需要转发给 Device D 的具有某一相同属性的报文。客户希望对拥有此相同属性的报文应用相同的流策略，对其进行 QoS 调整。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 159HUAWEI NetEngine40E配置指南 1 QoS图 **1-20** 配置全局 QPPB 应用组网图##### 前置任务在配置全局 QPPB 之前，需要完成以下任务：       - 配置 BGP 的基本功能       - 配置 BGP 发布的本地网络路由       - 配置建立 BGP 连接所使用的接口##### 1.7.3.1 配置 BGP 路由发送端的路由发布策略配置发送端的路由策略信息。##### 背景信息在 BGP 路由的发送端进行如下配置。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** （可选）配置基本 ACL 规则。1. 执行命令 **acl** { **name** basic-acl-name { **basic** | [ **basic** ] **number** basic-aclnumber } | [ **number** ] basic-acl-number } [ **match-order** { **config** | **auto** } ] ，创建基本 ACL ，并进入基本 ACL 视图。2. 执行命令 **rule** [ rule-id ] { **deny** | **permit** } [ **fragment-type** { **fragment** | **non-****fragment** | **non-subseq** | **fragment-subseq** | **fragment-spe-first** } | **source**{ source-ip-address { source-wildcard | **0** } | **any** } | **time-range** time-name |**vpn-instance** vpn-instance-name ] [*] ，配置基本 ACL 规则。对于报文是否匹配 ACL 规则，采用如下策略：       - 如果匹配上 ACL 规则，且 ACL 规则的动作是 permit ，则报文会被命中。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 160HUAWEI NetEngine40E配置指南 1 QoS       - 如果匹配上 ACL 规则，且 ACL 规则的动作是 deny ，则报文会被丢弃。       - 如果没有匹配上 ACL 规则，则报文不会被命中，只进行正常转发。       - 如果引用的 ACL 规则不存在，或者 ACL 存在但是 ACL 中没有定义规则，则报文不会被命中，只进行正常转发。步骤 **3** 执行命令 **quit** ，退回到系统视图。步骤 **4** 执行命令 **route-policy** route-policy-name matchMode **node** node ，创建路由策略的节点，并进入路由策略视图。步骤 **5** 选择执行下列命令，配置路由策略中的匹配原则。       - 如果匹配 ACL ，执行命令 **if-match acl** { acl-number | acl-name } 。说明路由策略只支持匹配 2000 ～ 2999 的基本 acl 。       - 如果匹配 BGP 路由信息的 AS 路径列表，执行命令 **if-match as-path-filter** { aspath-filter-number | as-path-filter-name } &<1-16> 。       - 如果匹配 BGP 路由信息的团体属性列表，执行命令 **if-match community-filter**{ basic-comm-filter-num [ **whole-match** ] | adv-comm-filter-num }* &<1-16>或 **if-match community-filter** comm-filter-name [ **whole-match** ] 。       - 如果匹配路由信息的路由权值，执行命令 **if-match cost** cost 。       - 如果匹配地址前缀列表，执行命令 **if-match ip-prefix** ip-prefix 。步骤 **6** 选择执行下列命令，通过匹配路由策略为 BGP 路由设置路由属性。       - 如果设置 AS_Path 属性，执行命令 **apply as-path** as-number &<1-128>[ **additive** ] 。       - 如果设置团体属性，执行命令 **apply community** { [ community-number |aa:nn ] &<1-32> | **internet** | **no-advertise** | **no-export** | **no-export-****subconfed** } * [ **additive** ] 。       - 如果设置路由信息的路由权值，执行命令 **apply cost** { [ **+** | **-** ] cost | **inherit** } 。AS_Path 、团体属性、扩展团体属性是为匹配上路由策略的路由设置不同的 BGP 路由属性，配置其中一种既可。步骤 **7** 执行命令 **quit** ，退回到系统视图。步骤 **8** 执行命令 **bgp** as-number ，进入 BGP 视图。步骤 **9** 执行命令 **peer** { ipv4-address | group-name } **route-policy** route-policy-name**export** ，配置向对等体发布的路由应用路由策略。说明在应用路由策略前请确认 BGP 的对等体已经建立。步骤 **10** 执行命令 **peer** peerIpv4Addr **advertise-community** ，将团体属性发布给对等体。步骤 **11** 执行命令 **commit** ，提交配置。**----**结束##### ******* 配置接收端应用路由策略配置接收端的路由策略，定义规则并应用规则。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 161HUAWEI NetEngine40E配置指南 1 QoS##### 背景信息在 BGP 路由的接收端进行如下配置。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **route-policy** route-policy-name matchMode **node** node ，创建路由策略的节点，并进入路由策略视图。步骤 **3** 选择执行下列命令，配置接收端路由接收策略匹配规则。       - 如果匹配 BGP 路由信息的 AS 路径列表，执行命令 **if-match as-path-filter** aspath-acl-number &<1-16> 。       - 如果匹配 BGP 路由信息的团体属性列表，执行命令 **if-match community-filter**{ basic-comm-filter-num [ **whole-match** ] | ext-comm-filter-num }&<1-16> 。       - 如果匹配路由信息的路由权值，执行命令 **if-match cost** value 。说明BGP 路由接收的路由属性必须为 BGP 路由发送端通告的路由属性。步骤 **4** 选择执行下列命令，配置匹配路由策略的关联属性：       - 执行命令 **apply qos-local-id** qos-local-id ，对匹配的路由策略，设置关联的 QoS策略 ID 。说明在路由策略中 **apply qos-local-id** qos-local-id 关联 QoS 策略 ID 被应用于 QPPB 时，所配置的ID 不应该超出 QPPB 策略下 **qos-local-id** qos-local-id **behavior** behavior-name 配置的 QoS策略 ID 范围。       - 执行命令 **apply ip-precedence** ip-precedence ，对匹配的路由策略，设置关联的IP 优先级。一个路由策略（ route-policy ）由多个节点（ node ）构成。一个节点包括多个 **if-****match** 和 **apply** 子句。 **if-match** 子句用来定义该节点的匹配条件， **apply** 子句用来定义通过过滤的路由行为。在一个节点中，可以同时配置多个 **if-match** 匹配规则，这些过滤规则之间的关系是逻辑“与”，即所有 **if-match** 子句都必须匹配，才能通过该路由策略。路由策略节点间的过滤关系是逻辑“或”，即只要通过了一个节点的过滤，就可通过该路由策略。如果没有通过任何一个节点的过滤，路由信息将无法通过该路由策略。步骤 **5** 执行命令 **quit** ，退回到系统视图。步骤 **6** 执行命令 **bgp** as-number ，启动 BGP ，进入 BGP 视图。步骤 **7** 执行命令 **peer** ipv4-address **route-policy** route-policy-name **import** ，对从对等体（路由发送端）来的路由应用接收端路由策略。说明在应用路由策略前请确认 BGP 的对等体已经建立。步骤 **8** 执行命令 **ipv6-family** **unicast** ，进入 BGP-IPv6 单播地址族视图。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 162HUAWEI NetEngine40E配置指南 1 QoS步骤 **9** 执行命令 **ipv6 qppb** ，使能 BGP 的邻居入口策略支持 IPv6 QPPB 属性功能。说明配置本步骤可以使下发给 RM 的 BGP IPv6 路由携带 Qos 属性， BGP IPv6 路由支持流量统计等功能。步骤 **10** 执行命令 **commit** ，提交配置。**----**结束##### ******* 配置全局应用流策略并使能全局 QPPB为实现全局 QPPB 功能，必须先配置全局应用流策略。##### 背景信息为实现全局 QPPB 功能，必须先配置全局应用流策略，且该流策略定义的流分类中需要配置基于 QoS 策略 ID 的匹配规则。流策略相关配置可参见 **1.5** 基于类的 **QoS** 配置 章节。VS 模式下，该配置仅在 Admin VS 支持。##### 操作步骤步骤 **1** 定义基于三层 / 四层信息的流分类1. 执行命令 **system-view** ，进入系统视图。2. 执行命令 **traffic classifier** classifier-name [ **operator** { **and** | **or** } ] ，定义流分类并进入流分类视图。3. 执行命令 **if-match qos-local-id** qos-local-id ，定义基于源或目的 QoS 策略 ID 的匹配规则。4. 执行命令 **if-match qos-local-id** **source** source-local-id **destination**destination-local-id ，定义基于源和目的 QoS 策略 ID 的匹配规则。5. 执行命令 **commit** ，提交配置。步骤 **2** 定义流行为并配置流动作具体配置请参考 **1.5.3.2** 定义流行为并配置动作 。步骤 **3** 定义流量策略并在策略中为类指定行为具体配置请参考 **1.5.3.3** 定义流量策略 。步骤 **4** 执行命令 **quit** ，退出到系统视图。步骤 **5** 执行命令 **traffic-policy** policy-name { **inbound** | **outbound** } **global-acl** ，配置全局应用流策略。步骤 **6** 执行命令 **qppb qos-local-id** { **source** | **destination** | **both inbound** } ，使能全局QPPB 。步骤 **7** 执行命令 **commit** ，提交配置。**----**结束文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 163HUAWEI NetEngine40E配置指南 1 QoS#### 1.7.4 配置基于源的 QPPB配置基于源的 QPPB 策略对来自不同发送端的路由进行区别对待，实现差异的行为策略。##### 应用环境用配置 QPPB 特性的方式通过 BGP 传播 QoS 策略同时适用于 IBGP 和 EBGP ，可以在同一个自治系统内部或者不同的自治系统之间进行配置。如 图 **1-21** 所示，运营商 B 的网络 (AS200) 和运营商 C 的网络 (AS300) 到达运营商 D 的网络(AS400) 的流量需要经过运营商 A 的网络 (AS100) ，运营商 B 和运营商 C 是 BGP 路由的发送方，运营商 A 是 BGP 路由的接收方，根据运营商 A 和运营商 B 、运营商 C 签署的流量控制策略，运营商 A 需要对来自运营商 B 和运营商 C 流经的流量进行限速。运营商 B 和运营商 C 向运营商 A 通告带有团体属性的 BGP 路由，运营商 A 收到后，通过匹配 BGP 团体列表、 ACL 、 BGP AS path list ，为 BGP 路由设置关联的流行为和 QoS 策略ID ，在流量的接口上使能，所有流经运营商 A 的流量就会应用相应的 QPPB 本地策略。设备支持在流量的上行或下行部署基于源的 QPPB 特性。图 **1-21** 配置基于源的 QPPB 应用组网图##### 前置任务在配置 QPPB 之前，需要完成以下任务：       - 配置 BGP 的基本功能       - 配置 BGP 发布的本地网络路由       - 配置建立 BGP 连接所使用的接口##### 1.7.4.1 配置路由发送端的路由策略配置发送端的路由策略信息。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 164HUAWEI NetEngine40E配置指南 1 QoS##### 背景信息在 BGP 路由的发送端进行如下配置。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** （可选）配置基本 ACL 规则。1. 执行命令 **acl** { **name** basic-acl-name { **basic** | [ **basic** ] **number** basic-aclnumber } | [ **number** ] basic-acl-number } [ **match-order** { **config** | **auto** } ] ，创建基本 ACL ，并进入基本 ACL 视图。2. 执行命令 **rule** [ rule-id ] { **deny** | **permit** } [ **fragment-type** { **fragment** | **non-****fragment** | **non-subseq** | **fragment-subseq** | **fragment-spe-first** } | **source**{ source-ip-address { source-wildcard | **0** } | **any** } | **time-range** time-name |**vpn-instance** vpn-instance-name ] [*] ，配置基本 ACL 规则。对于报文是否匹配 ACL 规则，采用如下策略：       - 如果匹配上 ACL 规则，且 ACL 规则的动作是 permit ，则报文会被命中。       - 如果匹配上 ACL 规则，且 ACL 规则的动作是 deny ，则报文会被丢弃。       - 如果没有匹配上 ACL 规则，则报文不会被命中，只进行正常转发。       - 如果引用的 ACL 规则不存在，或者 ACL 存在但是 ACL 中没有定义规则，则报文不会被命中，只进行正常转发。步骤 **3** 执行命令 **quit** ，退回到系统视图。步骤 **4** 执行命令 **route-policy** route-policy-name matchMode **node** node ，创建路由策略的节点，并进入路由策略视图。步骤 **5** 选择执行下列命令，配置路由策略中的匹配原则。       - 如果匹配 ACL ，执行命令 **if-match acl** { acl-number | acl-name } 。说明路由策略只支持匹配 2000 ～ 2999 的基本 acl 。       - 如果匹配 BGP 路由信息的 AS 路径列表，执行命令 **if-match as-path-filter** { aspath-filter-number | as-path-filter-name } &<1-16> 。       - 如果匹配 BGP 路由信息的团体属性列表，执行命令 **if-match community-filter**{ basic-comm-filter-num [ **whole-match** ] | adv-comm-filter-num }* &<1-16>或 **if-match community-filter** comm-filter-name [ **whole-match** ] 。       - 如果匹配路由信息的路由权值，执行命令 **if-match cost** cost 。       - 如果匹配地址前缀列表，执行命令 **if-match ip-prefix** ip-prefix 。步骤 **6** 选择执行下列命令，通过匹配路由策略为 BGP 路由设置路由属性。       - 如果设置 AS_Path 属性，执行命令 **apply as-path** as-number &<1-128>[ **additive** ] 。       - 如果设置团体属性，执行命令 **apply community** { [ community-number |aa:nn ] &<1-32> | **internet** | **no-advertise** | **no-export** | **no-export-****subconfed** } * [ **additive** ] 。       - 如果设置路由信息的路由权值，执行命令 **apply cost** { [ **+** | **-** ] cost | **inherit** } 。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 165HUAWEI NetEngine40E配置指南 1 QoSAS_Path 、团体属性、扩展团体属性是为匹配上路由策略的路由设置不同的 BGP 路由属性，配置其中一种既可。步骤 **7** 执行命令 **quit** ，退回到系统视图。步骤 **8** 执行命令 **bgp** as-number ，进入 BGP 视图。步骤 **9** 执行命令 **peer** { ipv4-address | group-name } **route-policy** route-policy-name**export** ，配置向对等体发布的路由应用路由策略。说明在应用路由策略前请确认 BGP 的对等体已经建立。步骤 **10** 执行命令 **peer** peerIpv4Addr **advertise-community** ，将团体属性发布给对等体。步骤 **11** 执行命令 **commit** ，提交配置。**----**结束##### ******* 配置接收端应用路由策略配置接收端的路由策略，定义规则并应用规则。##### 背景信息在 BGP 路由的接收端进行如下配置。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **route-policy** route-policy-name matchMode **node** node ，创建路由策略的节点，并进入路由策略视图。步骤 **3** 选择执行下列命令，配置接收端路由接收策略匹配规则。       - 如果匹配 BGP 路由信息的 AS 路径列表，执行命令 **if-match as-path-filter** aspath-acl-number &<1-16> 。       - 如果匹配 BGP 路由信息的团体属性列表，执行命令 **if-match community-filter**{ basic-comm-filter-num [ **whole-match** ] | ext-comm-filter-num }&<1-16> 。       - 如果匹配路由信息的路由权值，执行命令 **if-match cost** value 。说明BGP 路由接收的路由属性必须为 BGP 路由发送端通告的路由属性。步骤 **4** 选择执行下列命令，配置匹配路由策略的关联属性：       - 执行命令 **apply qos-local-id** qos-local-id ，对匹配的路由策略，设置关联的 QoS策略 ID 。说明在路由策略中 **apply qos-local-id** qos-local-id 关联 QoS 策略 ID 被应用于 QPPB 时，所配置的ID 不应该超出 QPPB 策略下 **qos-local-id** qos-local-id **behavior** behavior-name 配置的 QoS策略 ID 范围。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 166HUAWEI NetEngine40E配置指南 1 QoS       - 执行命令 **apply ip-precedence** ip-precedence ，对匹配的路由策略，设置关联的IP 优先级。一个路由策略（ route-policy ）由多个节点（ node ）构成。一个节点包括多个 **if-****match** 和 **apply** 子句。 **if-match** 子句用来定义该节点的匹配条件， **apply** 子句用来定义通过过滤的路由行为。在一个节点中，可以同时配置多个 **if-match** 匹配规则，这些过滤规则之间的关系是逻辑“与”，即所有 **if-match** 子句都必须匹配，才能通过该路由策略。路由策略节点间的过滤关系是逻辑“或”，即只要通过了一个节点的过滤，就可通过该路由策略。如果没有通过任何一个节点的过滤，路由信息将无法通过该路由策略。步骤 **5** 执行命令 **quit** ，退回到系统视图。步骤 **6** 执行命令 **bgp** as-number ，启动 BGP ，进入 BGP 视图。步骤 **7** 执行命令 **peer** ipv4-address **route-policy** route-policy-name **import** ，对从对等体（路由发送端）来的路由应用接收端路由策略。说明在应用路由策略前请确认 BGP 的对等体已经建立。步骤 **8** 执行命令 **ipv6-family** **unicast** ，进入 BGP-IPv6 单播地址族视图。步骤 **9** 执行命令 **ipv6 qppb** ，使能 BGP 的邻居入口策略支持 IPv6 QPPB 属性功能。说明配置本步骤可以使下发给 RM 的 BGP IPv6 路由携带 Qos 属性， BGP IPv6 路由支持流量统计等功能。步骤 **10** 执行命令 **commit** ，提交配置。**----**结束##### ******* 配置接收端流行为对接收端匹配上流分类的流量，提供有区别的流行为服务。##### 背景信息在 BGP 路由的接收端进行如下配置。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **traffic behavior** behavior-name ，定义行为进入流行为视图。步骤 **3** 请根据实际情况进行如下配置。       - 如果配置流量监管动作，执行命令 **car** { **cir** cir-value [ **pir** pir-value ] } [ **cbs** cbsvalue [ **pbs** pbs-value ] ] [ **adjust** adjust-value ] [ **green** { **discard** | **pass**[ **remark dscp** dscp-value | **service-class** class **color** color ] } | **yellow**{ **discard** | **pass** [ **remark dscp** dscp-value | **service-class** class **color** color ] } |**red** { **discard** | **pass** [ **remark dscp** dscp-value | **service-class** class **color**color ] } ] [*] [ **summary** ] [ **color-aware** ] [ **limit-type pps** ] 。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 167HUAWEI NetEngine40E配置指南 1 QoS       - 如果重新标记 IP 报文的 DSCP 值，执行命令 **remark** [ **ipv6** ] **dscp** { dscp-value |**af11** | **af12** | **af13** | **af21** | **af22** | **af23** | **af31** | **af32** | **af33** | **af41** | **af42** | **af43** |**cs1** | **cs2** | **cs3** | **cs4** | **cs5** | **cs6** | **cs7** | **default** | **ef** } 。       - 如果重新设置 IP 报文的优先级，执行命令 **remark ip-precedence** ipprecedence 。       - 如果设置满足规则的所有报文通过，执行命令 **permit** 。       - 如果设置满足规则的所有报文禁止通过，执行命令 **deny** 。       - 如果设置对指定服务等级的报文标记颜色，执行命令 **service-class** service-class**color** color 。       - 如果配置用户队列的调度参数对用户业务进行 HQoS 调度，执行命令 **user-queue****cir** cir-value [ [ **pir** pir-value ] | [ **flow-queue** flow-queue-name ] | [ **flow-****mapping** mapping-name ] | [ **user-group-queue** group-name ] | [ **service-****template** service-template-name ] ]*       - 如果将报文重定向到指定的 VPN 组，执行命令 **redirect vpn-group** vpn-groupname 。       - 如果设置级联下一个流策略，执行命令 **traffic-policy** policy-name 。说明在流行为视图中同时执行命令 **hierarchical-car enable** ，可以在复杂流分类级联策略场景中使能层次化 CAR 动作。–配置级联流策略会造成设备转发性能下降。–接口流量匹配级联的流策略时：#### ▪ 转发行为按照级联的流策略行为执行 ▪ 非重复的流策略行为可以分别执行 ▪ 重复的流策略行为按照级联的流策略行为执行–一个接口的一个方向上只能应用一个流量策略。如果该流策略级联了另外一个流策略，那么这个接口该方向上相当于同时应用了多个流量策略。– 流策略接口上应用时所指定的 inbound 或 outbound 方向， link-layer 、 mplslayer 和 all-layer 等参数被级联的流策略继承。– 两级 ACL 的流行为都是 service-class 时，以第一级 service-class 优先生效；但是如果第一级 service-class 带有 no-remark ，还是以第二级 service-class 优先生效。       - 如果配置提升流行为动作的优先级，执行命令 **increase-priority** 。设备上同时配置了 BGP Flow Specification 和 QPPB 的流动作，如果需要 QPPB 的流动作优先生效，可以执行该命令实现。步骤 **4** 执行命令 **commit** ，提交配置。**----**结束##### 1.7.4.4 配置 QPPB 本地策略通过匹配 BGP 团体列表、 ACL 、 BGP AS path list ，为 BGP 路由设置关联的 QoS 策略，在流量的入接口和出接口上使能 QPPB 后，流量就会应用相应的 QoS 策略。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 168HUAWEI NetEngine40E配置指南 1 QoS##### 背景信息在 BGP 路由的接收端进行如下配置。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **qppb local-policy** policy-name ，创建 QPPB 策略，并进入 QPPB 策略视图。步骤 **3** ( 可选 ) 执行命令 **statistics enable** ，使能 QPPB 的统计功能。步骤 **4** （可选）执行命令 **service-class outbound enable** ，使能 QPPB 本地策略下行 serviceclass 功能步骤 **5** 执行命令 **qos-local-id** qos-local-id **behavior** behavior-name ，将 QoS 策略 ID 和behavior 绑定。步骤 **6** 执行命令 **commit** ，提交配置。**----**结束##### 1.7.4.5 在接口下应用 QPPB在接口应用 QPPB 策略，对于所有符合匹配规则的报文实施流行为。##### 背景信息可配置基于流量的上行或者下行的 QPPB ，达到相同的实现效果。QPPB 中的 BGP 路由只针对公网 BGP 路由（私网下的 QPPB 扩展为 L3VPN 中应用QPPB ）。在 BGP 路由的接收端进行如下配置。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** （可选）执行命令 **qos match-type qos-local-id enhance** ，使能 qos-local-id 增强模式。报文上行方向应用复杂流分类对其执行 remark qos-local-id 动作或者在 Route-Policy 视图下为报文设置 qos-local-id ，叠加 CGN 或 IPsec 业务后，报文被引流至增值业务板进行处理。这种场景下，如果需要在接口下行方向匹配 qos-local-id 信息对报文应用 QPPB策略，需要使能 qos-local-id 增强模式，否则无法匹配 qos-local-id 信息。步骤 **3** 执行命令 **interface** interface-type interface-number ，进入接口视图。步骤 **4** 应用 QPPB 策略，请根据实际情况选择如下命令行。       - 配置基于上行 QoS 策略的 QPPB ，执行命令 **qppb-policy** policy-name **source****inbound** ，在接口入方向应用 QPPB 策略。       - 配置基于下行 QoS 策略的 QPPB ，执行命令 **qppb-policy** **qos-local-id** **source****inbound** 和 **qppb-policy** policy-name **outbound** ，分别在入接口和出接口应用QPPB 策略。       - 配置基于 IP 优先级的 QPPB ，执行命令 **qppb-policy ip-precedence** **source** ，根据路由策略指定流行为，并对源地址实施 QPPB 动作。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 169HUAWEI NetEngine40E配置指南 1 QoS说明**source** 关键字表示所发布的携带路由属性的路由方向作为流量的源地址方向。**----**结束##### ******* 检查配置结果QPPB 功能配置成功后，可以查看 QPPB 信息等内容。##### 背景信息在所有视图下执行下面的 **display** 命令，可以查看 QPPB 的运行信息，检查配置的效果。运行信息的详细解释请参见《 HUAWEI NetEngine40E 路由器 命令参考》的“ QoS 命令”。##### 操作步骤步骤 **1** 使用 **display qppb local-policy configuration** policy-name 命令查看 qppb 本地策略的配置。步骤 **2** 使用 **display qppb local-policy statistics** **interface** interface-type interfacenumber [ **qos-local-id** qos-local-id ] { **inbound** | **outbound** } 命令查看指定 qppb 本地策略的统计信息。**----**结束#### 1.7.5 配置基于目的的 QPPB配置基于目的的 QPPB 策略对去往不同接收端的路由进行区别对待，实现差异的行为策略。##### 应用环境用配置 QPPB 特性的方式通过 BGP 传播 QoS 策略同时适用于 IBGP 和 EBGP ，可以在同一个自治系统内部或者不同的自治系统之间进行配置。如 图 **1-22** 所示，运营商 B 的网络 (AS200) 和运营商 C 的网络 (AS300) 到达运营商 D 的网络（ AS400 ）的流量需要经过运营商 A 的网络 (AS100) ，运营商 B 和运营商 C 是 BGP 路由的发送方，运营商 A 是 BGP 路由的接收方，根据运营商 A 和运营商 D 签署的流量控制策略，运营商 A 需要对所有流入运营 D 的流量都进行限速。运营商 B 和运营商 C 向运营商 A 通告带有团体属性的 BGP 路由，运营商 A 收到后，通过匹配 BGP 团体列表、 ACL 、 BGP AS path list ，为 BGP 路由设置关联的流行为和 QoS 策略ID ，在流量的接口上使能，所有流经运营商 A 的流量就会应用相应的 QPPB 本地策略。设备支持在流量的上行或下行部署基于目的的 QPPB 特性。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 170HUAWEI NetEngine40E配置指南 1 QoS图 **1-22** 配置基于目的的 QPPB 应用组网图##### 前置任务在配置 QPPB 之前，需要完成以下任务：       - 配置 BGP 的基本功能       - 配置 BGP 发布的本地网络路由       - 配置建立 BGP 连接所使用的接口##### 1.7.5.1 配置 BGP 路由发送端的路由发布策略配置发送端的路由策略信息。##### 背景信息在 BGP 路由的发送端进行如下配置。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** （可选）配置基本 ACL 规则。1. 执行命令 **acl** { **name** basic-acl-name { **basic** | [ **basic** ] **number** basic-aclnumber } | [ **number** ] basic-acl-number } [ **match-order** { **config** | **auto** } ] ，创建基本 ACL ，并进入基本 ACL 视图。2. 执行命令 **rule** [ rule-id ] { **deny** | **permit** } [ **fragment-type** { **fragment** | **non-****fragment** | **non-subseq** | **fragment-subseq** | **fragment-spe-first** } | **source**{ source-ip-address { source-wildcard | **0** } | **any** } | **time-range** time-name |**vpn-instance** vpn-instance-name ] [*] ，配置基本 ACL 规则。对于报文是否匹配 ACL 规则，采用如下策略：       - 如果匹配上 ACL 规则，且 ACL 规则的动作是 permit ，则报文会被命中。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 171HUAWEI NetEngine40E配置指南 1 QoS       - 如果匹配上 ACL 规则，且 ACL 规则的动作是 deny ，则报文会被丢弃。       - 如果没有匹配上 ACL 规则，则报文不会被命中，只进行正常转发。       - 如果引用的 ACL 规则不存在，或者 ACL 存在但是 ACL 中没有定义规则，则报文不会被命中，只进行正常转发。步骤 **3** 执行命令 **quit** ，退回到系统视图。步骤 **4** 执行命令 **route-policy** route-policy-name matchMode **node** node ，创建路由策略的节点，并进入路由策略视图。步骤 **5** 选择执行下列命令，配置路由策略中的匹配原则。       - 如果匹配 ACL ，执行命令 **if-match acl** { acl-number | acl-name } 。说明路由策略只支持匹配 2000 ～ 2999 的基本 acl 。       - 如果匹配 BGP 路由信息的 AS 路径列表，执行命令 **if-match as-path-filter** { aspath-filter-number | as-path-filter-name } &<1-16> 。       - 如果匹配 BGP 路由信息的团体属性列表，执行命令 **if-match community-filter**{ basic-comm-filter-num [ **whole-match** ] | adv-comm-filter-num }* &<1-16>或 **if-match community-filter** comm-filter-name [ **whole-match** ] 。       - 如果匹配路由信息的路由权值，执行命令 **if-match cost** cost 。       - 如果匹配地址前缀列表，执行命令 **if-match ip-prefix** ip-prefix 。步骤 **6** 选择执行下列命令，通过匹配路由策略为 BGP 路由设置路由属性。       - 如果设置 AS_Path 属性，执行命令 **apply as-path** as-number &<1-128>[ **additive** ] 。       - 如果设置团体属性，执行命令 **apply community** { [ community-number |aa:nn ] &<1-32> | **internet** | **no-advertise** | **no-export** | **no-export-****subconfed** } * [ **additive** ] 。       - 如果设置路由信息的路由权值，执行命令 **apply cost** { [ **+** | **-** ] cost | **inherit** } 。AS_Path 、团体属性、扩展团体属性是为匹配上路由策略的路由设置不同的 BGP 路由属性，配置其中一种既可。步骤 **7** 执行命令 **quit** ，退回到系统视图。步骤 **8** 执行命令 **bgp** as-number ，进入 BGP 视图。步骤 **9** 执行命令 **peer** { ipv4-address | group-name } **route-policy** route-policy-name**export** ，配置向对等体发布的路由应用路由策略。说明在应用路由策略前请确认 BGP 的对等体已经建立。步骤 **10** 执行命令 **peer** peerIpv4Addr **advertise-community** ，将团体属性发布给对等体。步骤 **11** 执行命令 **commit** ，提交配置。**----**结束##### ******* 配置接收端应用路由策略配置接收端的路由策略，定义规则并应用规则。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 172HUAWEI NetEngine40E配置指南 1 QoS##### 背景信息在 BGP 路由的接收端进行如下配置。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **route-policy** route-policy-name matchMode **node** node ，创建路由策略的节点，并进入路由策略视图。步骤 **3** 选择执行下列命令，配置接收端路由接收策略匹配规则。       - 如果匹配 BGP 路由信息的 AS 路径列表，执行命令 **if-match as-path-filter** aspath-acl-number &<1-16> 。       - 如果匹配 BGP 路由信息的团体属性列表，执行命令 **if-match community-filter**{ basic-comm-filter-num [ **whole-match** ] | ext-comm-filter-num }&<1-16> 。       - 如果匹配路由信息的路由权值，执行命令 **if-match cost** value 。说明BGP 路由接收的路由属性必须为 BGP 路由发送端通告的路由属性。步骤 **4** 请进行如下配置：       - 执行命令 **apply qos-local-id** qos-local-id ，对匹配的路由策略，设置关联的 QoS策略 ID 。说明在路由策略中 **apply qos-local-id** qos-local-id 关联 QoS 策略 ID 被应用于 QPPB 时，所配置的ID 不应该超出 QPPB 策略下 **qos-local-id** qos-local-id **behavior** behavior-name 配置的 QoS策略 ID 范围。       - 执行命令 **apply ip-precedence** ip-precedence ，对匹配的路由策略，设置关联的IP 优先级。说明需要预先配置好对匹配上路由策略的路由设置的关联 IP 优先级。一个路由策略（ route-policy ）由多个节点（ node ）构成。一个节点包括多个 **if-****match** 和 **apply** 子句。 **if-match** 子句用来定义该节点的匹配条件， **apply** 子句用来定义通过过滤的路由行为。在一个节点中，可以同时配置多个 **if-match** 匹配规则，这些过滤规则之间的关系是逻辑“与”，即所有 **if-match** 子句都必须匹配，才能通过该路由策略。路由策略节点间的过滤关系是逻辑“或”，即只要通过了一个节点的过滤，就可通过该路由策略。如果没有通过任何一个节点的过滤，路由信息将无法通过该路由策略。步骤 **5** 执行命令 **quit** ，退回到系统视图步骤 **6** 执行命令 **bgp** as-number ，启动 BGP ，进入 BGP 视图。步骤 **7** 执行命令 **peer** ipv4-address **route-policy** route-policy-name **import** ，对从对等体（路由发送端）来的路由应用接收端路由策略。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 173HUAWEI NetEngine40E配置指南 1 QoS说明在应用路由策略前请确认 BGP 的对等体已经建立。步骤 **8** 执行命令 **commit** ，提交配置。**----**结束##### ******* 配置接收端 QPPB 的流行为对接收端匹配上流分类的流量，提供有区别的流行为服务。##### 背景信息在 BGP 路由的接收端进行如下配置。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **traffic behavior** behavior-name ，定义行为进入流行为视图。步骤 **3** 请根据实际情况进行如下配置。       - 如果配置流量监管动作，执行命令 **car** { **cir** cir-value [ **pir** pir-value ] } [ **cbs** cbsvalue [ **pbs** pbs-value ] ] [ **adjust** adjust-value ] [ **green** { **discard** | **pass**[ **remark dscp** dscp-value | **service-class** class **color** color ] } | **yellow**{ **discard** | **pass** [ **remark dscp** dscp-value | **service-class** class **color** color ] } |**red** { **discard** | **pass** [ **remark dscp** dscp-value | **service-class** class **color**color ] } ] [*] [ **summary** ] [ **color-aware** ] [ **limit-type pps** ] 。       - 如果重新标记 IP 报文的 DSCP 值，执行命令 **remark** [ **ipv6** ] **dscp** { dscp-value |**af11** | **af12** | **af13** | **af21** | **af22** | **af23** | **af31** | **af32** | **af33** | **af41** | **af42** | **af43** |**cs1** | **cs2** | **cs3** | **cs4** | **cs5** | **cs6** | **cs7** | **default** | **ef** } 。       - 如果重新设置 IP 报文的优先级，执行命令 **remark ip-precedence** ipprecedence 。       - 如果设置满足规则的所有报文通过，执行命令 **permit** 。       - 如果设置满足规则的所有报文禁止通过，执行命令 **deny** 。       - 如果设置对指定服务等级的报文标记颜色，执行命令 **service-class** service-class**color** color 。       - 如果配置用户队列的调度参数对用户业务进行 HQoS 调度，执行命令 **user-queue****cir** cir-value [ [ **pir** pir-value ] | [ **flow-queue** flow-queue-name ] | [ **flow-****mapping** mapping-name ] | [ **user-group-queue** group-name ] | [ **service-****template** service-template-name ] ]*       - 如果将报文重定向到指定的 VPN 组，执行命令 **redirect vpn-group** vpn-groupname 。       - 如果设置级联下一个流策略，执行命令 **traffic-policy** policy-name 。说明在流行为视图中同时执行命令 **hierarchical-car enable** ，可以在复杂流分类级联策略场景中使能层次化 CAR 动作。–配置级联流策略会造成设备转发性能下降。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 174HUAWEI NetEngine40E配置指南 1 QoS–接口流量匹配级联的流策略时：#### ▪ 转发行为按照级联的流策略行为执行 ▪ 非重复的流策略行为可以分别执行 ▪ 重复的流策略行为按照级联的流策略行为执行–一个接口的一个方向上只能应用一个流量策略。如果该流策略级联了另外一个流策略，那么这个接口该方向上相当于同时应用了多个流量策略。– 流策略接口上应用时所指定的 inbound 或 outbound 方向， link-layer 、 mplslayer 和 all-layer 等参数被级联的流策略继承。– 两级 ACL 的流行为都是 service-class 时，以第一级 service-class 优先生效；但是如果第一级 service-class 带有 no-remark ，还是以第二级 service-class 优先生效。       - 如果配置提升流行为动作的优先级，执行命令 **increase-priority** 。设备上同时配置了 BGP Flow Specification 和 QPPB 的流动作，如果需要 QPPB 的流动作优先生效，可以执行该命令实现。步骤 **4** 执行命令 **commit** ，提交配置。**----**结束##### 1.7.5.4 配置 QPPB 本地策略通过匹配 BGP 团体列表、 ACL 、 BGP AS path list ，为 BGP 路由设置关联的 QoS 策略，在流量的入接口和出接口上使能 QPPB 后，流量就会应用相应的 QoS 策略。##### 背景信息在 BGP 路由的接收端进行如下配置。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **qppb local-policy** policy-name ，创建 QPPB 策略，并进入 QPPB 策略视图。步骤 **3** ( 可选 ) 执行命令 **statistics enable** ，使能 QPPB 的统计功能。步骤 **4** （可选）执行命令 **service-class outbound enable** ，使能 QPPB 本地策略下行 serviceclass 功能步骤 **5** 执行命令 **qos-local-id** qos-local-id **behavior** behavior-name ，创建 QoS 策略并且将QoS 策略 ID 和 behavior 绑定。步骤 **6** 执行命令 **commit** ，提交配置。**----**结束##### ******* 在接口下应用 QPPB在流量的入接口应用 QPPB 策略，对于所有符合匹配规则的报文实施流行为。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 175HUAWEI NetEngine40E配置指南 1 QoS##### 背景信息可配置基于流量的上行或者下行的 QPPB ，达到相同的实现效果。QPPB 中的 BGP 路由只针对公网 BGP 路由（私网下的 QPPB 扩展为 L3VPN 中应用QPPB ）。说明在 Ingress 节点，不支持公网或私网 IP 入 MPLS 隧道的下行 Qppb 功能。在 BGP 路由的接收端进行如下配置。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** （可选）执行命令 **qos match-type qos-local-id enhance** ，使能 qos-local-id 增强模式。报文上行方向应用复杂流分类对其执行 remark qos-local-id 动作或者在 Route-Policy 视图下为报文设置 qos-local-id ，叠加 CGN 或 IPsec 业务后，报文被引流至增值业务板进行处理。这种场景下，如果需要在接口下行方向匹配 qos-local-id 信息对报文应用 QPPB策略，需要使能 qos-local-id 增强模式，否则无法匹配 qos-local-id 信息。步骤 **3** 执行命令 **interface** interface-type interface-number ，进入接口视图。步骤 **4** 应用 QPPB 策略，请根据实际情况选择如下配置步骤。       - 配置基于上行 QoS 策略的 QPPB ，执行命令 **qppb-policy** policy-name **destination****inbound** ，在接口入方向应用 QPPB 策略。       - 配置基于下行 QoS 策略的 QPPB ，执行命令 **qppb-policy** **qos-local-id****destination inbound** 和 **qppb-policy** policy-name **outbound** ，分别在入接口和出接口应用 QPPB 策略。       - 配置基于 IP 优先级的 QPPB ，执行命令 **qppb-policy ip-precedence****destination** ，根据路由策略指定流行为，并对目的地址实施 QPPB 动作。说明**destination** 关键字表示对目的地址实施 QPPB 动作。**----**结束##### ******* 检查配置结果QPPB 功能配置成功后，可以查看 QPPB 信息等内容。##### 背景信息在所有视图下执行下面的 **display** 命令，可以查看 QPPB 的运行信息，检查配置的效果。运行信息的详细解释请参见《 HUAWEI NetEngine40E 路由器 命令参考》的“ QoS 命令”。##### 操作步骤步骤 **1** 使用 **display qppb local-policy configuration** policy-name 命令查看 qppb 本地策略的配置。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 176HUAWEI NetEngine40E配置指南 1 QoS步骤 **2** 使用 **display qppb local-policy statistics** **interface** interface-type interfacenumber [ **qos-local-id** qos-local-id ] { **inbound** | **outbound** } 命令查看指定 qppb 本地策略的统计信息。**----**结束#### 1.7.6 维护 QPPB介绍如何清空 QPPB 本地策略的统计信息。##### 1.7.6.1 清空 QPPB 本地策略的统计信息介绍如何清空接口下 QPPB 本地策略的统计信息。##### 背景信息须知清除统计信息后，以前的统计信息将无法恢复，务必仔细确认。##### 操作步骤步骤 **1** 在确认需要清空接口下的 QPPB 本地策略统计信息后，请在用户视图下执行 **reset qppb****local-policy statistics interface** interface-type interface-number [ **qos-local-id**qos-local-id ] { **inbound** | **outbound** } 命令，清空指定接口下的 QPPB 本地策略的统计数据。**----**结束#### 1.7.7 配置举例从具体应用场景、配置命令等方面对 QPPB 的应用进行了详细的描述。##### 1.7.7.1 配置 QPPB 示例（ BGP ）以特性的典型配置场景为例，介绍通过 BGP 传播 QoS 策略的配置。##### 组网需求如 图 **1-23** 所示， DeviceB 向 DeviceA 通告带有团体属性的 BGP 路由， DeviceA 收到后，通过匹配 BGP 团体列表为 BGP 路由设置关联的流行为和 QoS 策略 ID ，对流经 DeviceA 的流量应用相应的 QPPB 本地策略。DeviceB 的网络到达 DeviceC 网络的流量需要经过 DeviceA 的网络， DeviceB 是 BGP 路由的发送方， DeviceA 是 BGP 路由的接收方。要求在 DeviceA 的上行部署基于源的 QPPB 特性。说明本例中 interface1 ， interface2 分别代表 GE1/0/0 ， GE2/0/0 。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 177HUAWEI NetEngine40E配置指南 1 QoS图 **1-23** QPPB 配置组网图##### 配置思路采用如下的思路配置 QPPB 。1. 配置 BGP 的基本功能。2. 在 DeviceB 上配置路由策略，为发送的路由设置团体属性，并通过 BGP 发布路由策略。3. 在 DeviceA 上应用路由策略，匹配路由属性，设置关联的 QoS 策略 ID （ QoS LocalID ）。4. 在 DeviceA 的入接口上应用 QPPB 策略。##### 数据准备为完成此配置举例，需准备如下的数据：       - 各接口的 IP 地址       - 路由策略的名称、匹配规则和路由属性       - QPPB 策略的名称##### 操作步骤步骤 **1** 配置 DeviceA 和 DeviceB 的 BGP 基本功能# 配置 DeviceA 和 DeviceB 的 Loopback 接口。<HUAWEI> **system-view**[ ~ HUAWEI] **sysname DeviceA**[*HUAWEI] **commit**[ ~ DeviceA] **interface loopback 0**[*DeviceA-LoopBack0] **ip address ******* *****************[*DeviceA-LoopBack0] **commit**[ ~ DeviceA-LoopBack0] **return**<HUAWEI> **system-view**[ ~ HUAWEI] **sysname DeviceB**[*HUAWEI] **commit**[ ~ DeviceB] **interface loopback 0**[*DeviceB-LoopBack0] **ip address ******* *****************[*DeviceB-LoopBack0] **commit**[ ~ DeviceB-LoopBack0] **return**# 配置 DeviceA 和 DeviceB 直连的接口， DeviceA 和 DeviceC 直连的端口。<DeviceA> **system-view**[ ~ DeviceA] **interface GigabitEthernet 2/0/0**文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 178HUAWEI NetEngine40E配置指南 1 QoS[ ~ DeviceA-GigabitEthernet2/0/0] **undo shutdown**[ ~ DeviceA-GigabitEthernet2/0/0] **ip address ********* ***************[*DeviceA-GigabitEthernet2/0/0] **commit**[ ~ DeviceA-GigabitEthernet2/0/0] **quit**[ ~ DeviceA] **interface gigabitethernet 1/0/0**[ ~ DeviceA-GigabitEthernet1/0/0] **undo shutdown**[ ~ DeviceA-GigabitEthernet1/0/0] **ip address ********* ***************[*DeviceA-GigabitEthernet1/0/0] **commit**[ ~ DeviceA-GigabitEthernet1/0/0] **return**<DeviceB> **system-view**[ ~ DeviceB] **interface GigabitEthernet 2/0/0**[ ~ DeviceB-GigabitEthernet1/0/0] **undo shutdown**[ ~ DeviceB-GigabitEthernet1/0/0] **ip address ********* ***************[*DeviceB-GigabitEthernet1/0/0] **commit**[ ~ DeviceB-GigabitEthernet1/0/0] **return**<HUAWEI> **system-view**[ ~ HUAWEI] **sysname DeviceC**[*HUAWEI] **commit**[ ~ DeviceC] **interface gigabitethernet 1/0/0**[ ~ DeviceC-GigabitEthernet1/0/0] **undo shutdown**[ ~ DeviceC-GigabitEthernet1/0/0] **ip address ********* ***************[*DeviceC-GigabitEthernet1/0/0] **commit**[ ~ DeviceC-GigabitEthernet1/0/0] **return**# 启动 OSPF 协议，发布接口地址的路由。<DeviceA> **system-view**[ ~ DeviceA] **ospf**[*DeviceA-ospf-1] **area 0**[*DeviceA-ospf-1-area-0.0.0.0] **network ******* 0.0.0.0**[*DeviceA-ospf-1-area-0.0.0.0] **network ********* ***********[*DeviceA-ospf-1-area-0.0.0.0] **network ********* ***********[*DeviceA-ospf-1-area-0.0.0.0] **commit**[ ~ DeviceA-ospf-1-area-0.0.0.0] **return**<DeviceB> **system-view**[ ~ DeviceB] **ospf**[*DeviceB-ospf] **area 0**[*DeviceB-ospf-1-area-0.0.0.0] **network ******* 0.0.0.0**[*DeviceB-ospf-1-area-0.0.0.0] **network ********* ***********[*DeviceB-ospf-1-area-0.0.0.0] **commit**[ ~ DeviceB-ospf-1-area-0.0.0.0] **return**<DeviceC> **system-view**[ ~ DeviceC] **ospf**[*DeviceC-ospf] **area 0**[*DeviceC-ospf-1-area-0.0.0.0] **network ********* ***********[*DeviceC-ospf-1-area-0.0.0.0] **commit**[ ~ DeviceC-ospf-1-area-0.0.0.0] **return**# 配置 BGP 协议， DeviceA 和 DeviceB 建立 EBGP 连接。<DeviceA> **system-view**[ ~ DeviceA] **bgp 100**[*DeviceA-bgp] **peer ******* as-number 200**[*DeviceA-bgp] **peer ******* ebgp-max-hop 2**[*DeviceA-bgp] **peer ******* connect-interface loopback 0**[*DeviceA-bgp] **import-route direct**[*DeviceA-bgp] **commit**[ ~ DeviceA-bgp] **return**<DeviceB> **system-view**[ ~ DeviceB] **bgp 200**[*DeviceB-bgp] **peer ******* as-number 100**[*DeviceB-bgp] **peer ******* ebgp-max-hop 2**[*DeviceB-bgp] **peer ******* connect-interface loopback 0**[*DeviceB-bgp] **import-route direct**[*DeviceB-bgp] **commit**[ ~ DeviceB-bgp] **return**# 配置 BGP 协议， DeviceA 和 DeviceC 建立 IBGP 连接。<DeviceA> **system-view**文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 179HUAWEI NetEngine40E配置指南 1 QoS[ ~ DeviceA] **bgp 100**[*DeviceA-bgp] **peer ********* as-number 100**[*DeviceA-bgp] **import-route direct**[*DeviceA-bgp] **commit**[ ~ DeviceA-bgp] **quit**<DeviceC> **system-view**[ ~ DeviceC] **bgp 100**[*DeviceC-bgp] **peer ********* as-number 100**[*DeviceC-bgp] **import-route direct**[*DeviceC-bgp] **commit**[ ~ DeviceC-bgp] **quit**配置完成后可以看到 DeviceA 和 DeviceB ， DeviceC 之间可以实现互通。步骤 **2** 配置路由发送端 DeviceB 的路由策略，并应用路由策略# 配置路由发送端的 ip-prefix 。<DeviceB> **system-view**[ ~ DeviceB] **ip ip-prefix bb permit ******** 32**[*DeviceB] **commit**[ ~ DeviceB] **return**# 配置路由发送端的路由策略。<DeviceB> **system-view**[ ~ DeviceB] **route-policy aa permit node 10**[*DeviceB-route-policy] **if-match ip-prefix bb**[*DeviceB-route-policy] **apply community 10:10**[*DeviceB-route-policy] **commit**[ ~ DeviceB-route-policy] **return**# 在路由发送端通过 BGP 发布路由策略。<DeviceB> **system-view**[ ~ DeviceB] **bgp 200**[*DeviceB-bgp] **peer ******* route-policy aa export**[*DeviceB-bgp] **peer ******* advertise-community**[*DeviceB-bgp] **commit**[ ~ DeviceB-bgp] **return**步骤 **3** 配置路由接收端 DeviceA 的路由接收策略，对匹配路由属性的流量应用关联的流行为# 配置 QoS 策略，即关联的流行为。[ ~ DeviceA] **traffic behavior dd**[*DeviceA-behavior-dd] **remark dscp af11**[*DeviceA-behavior-dd] **commit**[ ~ DeviceA-behavior-dd] **return**# 配置路由接收策略，使匹配团体属性的路由应用关联的流行为。<DeviceA> **system-view**[ ~ DeviceA] **ip community-filter 10 permit 10:10**[*DeviceA] **route-policy aa permit node 10**[*DeviceA-route-policy] **if-match community-filter 10**[*DeviceA-route-policy] **apply qos-local-id 1**[*DeviceA-route-policy] **commit**[ ~ DeviceA-route-policy] **return**# 在 DeviceA 上配置 QPPB 策略。<DeviceA> **system-view**[ ~ DeviceA] **qppb local-policy ac**[*DeviceA-localpolicy-ac] **qos-local-id 1 behavior dd**[*DeviceA-localpolicy-ac] **commit**[ ~ DeviceA-localpolicy-ac] **return**# 在 DeviceA 上对从对等体 DeviceB 发布过来的路由应用接收端路由策略。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 180HUAWEI NetEngine40E配置指南 1 QoS<DeviceA> **system-view**[ ~ DeviceA] **bgp 100**[*DeviceA-bgp] **peer ******* route-policy aa import**[*DeviceA-bgp] **commit**[ ~ DeviceA-bgp] **return**步骤 **4** 在接收端 DeviceA 上，流量的入接口上应用 QPPB<DeviceA> **system-view**[ ~ DeviceA] **interface GigabitEthernet 2/0/0**[ ~ DeviceA-GigabitEthernet2/0/0] **qppb-policy ac source inbound**[*DeviceA-GigabitEthernet2/0/0] **commit**[ ~ DeviceA-GigabitEthernet2/0/0] **return**步骤 **5** 检查配置结果# 在 DeviceA 上显示 QPPB 本地策略的信息如下。< ~ DeviceA> **display qppb local-policy configuration ac**qppb local-policy: acstatistics disableservice-class outbound disableis-used yesqos-local-id 1 behavior dd**----**结束##### 配置文件       - DeviceA 的配置文件#sysname DeviceA#traffic behavior ddremark dscp af11#qppb local-policy acqos-local-id 1 behavior dd#interface GigabitEthernet1/0/0undo shutdownip address ********* *************#interface GigabitEthernet2/0/0undo shutdownip address ********* *************qppb-policy ac source inbound#interface LoopBack0ip address ******* ***************#bgp 100peer ******* as-number 200peer ******* ebgp-max-hop 2peer ******* connect-interface LoopBack0peer ********* as-number 100#ipv4-family unicastundo synchronizationimport-route directpeer ******* enablepeer ******* route-policy aa importpeer ********* enable#ospf 1area 0.0.0.0network ******* 0.0.0.0network ********* *********network ********* *********文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 181HUAWEI NetEngine40E配置指南 1 QoS#ip community-filter 10 index 10 permit 10:10#route-policy aa permit node 10if-match community-filter 10apply qos-local-id 1#return       - DeviceB 的配置文件#sysname DeviceB#interface GigabitEthernet2/0/0undo shutdownip address ********* *************#interface LoopBack0ip address ******* ***************#bgp 200peer ******* as-number 100peer ******* ebgp-max-hop 2peer ******* connect-interface LoopBack0#ipv4-family unicastundo synchronizationimport-route directpeer ******* enablepeer ******* route-policy aa exportpeer ******* advertise-communityquit#ospf 1area 0.0.0.0network ******* 0.0.0.0network ********* *********#route-policy aa permit node 10if-match ip-prefix bbapply community 10:10#ip ip-prefix bb index 10 permit ******** 32#return       - DeviceC 的配置文件#sysname DeviceC#interface GigabitEthernet1/0/0undo shutdownip address ********* *************#bgp 100peer ********* as-number 100#ipv4-family unicastundo synchronizationimport-route directpeer ********* enable#ospf 1area 0.0.0.0network ********* *********#return文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 182HUAWEI NetEngine40E配置指南 1 QoS##### ******* 配置 QPPB 示例（ ISIS ）以特性的典型配置场景为例，介绍通过 ISIS 路由传播 QoS 策略的配置。##### 组网需求如 图 **1-24** 所示， DeviceA 与 DeviceB 之间建立起 ISIS 邻居关系，并通过 ISIS 路由传递路由属性，对流经 DeviceA 的流量应用相应的 QPPB 本地策略。其中， DeviceB 是 QPPB 策略的发送端， DeviceA 是 QPPB 策略的接收端。说明本例中 interface1 代表 GE1/0/0 。图 **1-24** QPPB 配置组网图##### 配置注意事项无。##### 配置思路采用如下的思路配置 QPPB 。1. DeviceA 上配置路由策略，匹配路由属性，设置关联的 QoS 策略 ID （ QoS LocalID ）。2. 配置 ISIS 的基本功能。3. 在 DeviceA 的入接口上应用 QPPB 策略。##### 数据准备为完成此配置举例，需准备如下的数据：       - 各接口的 IP 地址       - 路由策略的名称、匹配规则和路由属性       - QPPB 策略的名称##### 操作步骤步骤 **1** 配置各接口的 IP 地址（略）。请根据 图 **1-24** 配置接口的 IP 地址。步骤 **2** 配置路由接收端 DeviceA 的路由接收策略，对匹配路由属性的流量应用关联的流行为。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 183HUAWEI NetEngine40E配置指南 1 QoS# 在 DeviceA 上配置路由策略。<DeviceA> **system-view**[ ~ DeviceA] **route-policy ac permit node 123**[*DeviceA-route-policy] **apply qos-local-id 1**[*DeviceA-route-policy] **commit**[ ~ DeviceA-route-policy] **return**# 配置 QoS 策略，即关联的流行为。[ ~ DeviceA] **traffic behavior dd**[*DeviceA-behavior-dd] **remark dscp af11**[*DeviceA-behavior-dd] **commit**[ ~ DeviceA-behavior-dd] **return**# 在 DeviceA 上配置 QPPB 策略。<DeviceA> **system-view**[ ~ DeviceA] **qppb local-policy ac**[*DeviceA-localpolicy-ac] **qos-local-id 1 behavior dd**[*DeviceA-localpolicy-ac] **commit**[ ~ DeviceA-localpolicy-ac] **return**步骤 **3** 配置 IS-IS 基本功能。# 配置 DeviceA 。<DeviceA> **system-view**[ ~ DeviceA] **isis 1**[*DeviceA-isis-1] **is-level level-1**[*DeviceA-isis-1] **network-entity 10.0000.0000.0001.00**[*DeviceA-isis-1] **cost-style wide**[*DeviceA-isis-1] **filter-policy route-policy ac import**[*DeviceA-isis-1] **traffic-eng level-1**[*DeviceA-isis-1] **quit**[*DeviceA] **interface gigabitethernet 1/0/0**[*DeviceA-GigabitEthernet1/0/0] **isis enable 1**[*DeviceA-GigabitEthernet1/0/0] **commit**[ ~ DeviceA-GigabitEthernet1/0/0] **quit**# 配置 DeviceB 。<DeviceB> **system-view**[ ~ DeviceB] **isis 1**[*DeviceB-isis-1] **network-entity 10.0000.0000.0002.00**[*DeviceB-isis-1] **cost-style wide**[*DeviceB-isis-1] **quit**[*DeviceB] **interface gigabitethernet 1/0/0**[*DeviceB-GigabitEthernet1/0/0] **isis enable 1**[*DeviceB-GigabitEthernet1/0/0] **commit**[ ~ DeviceB-GigabitEthernet1/0/0] **quit**步骤 **4** 在接收端 DeviceA 上，流量的入接口上应用 QPPB 。[ ~ DeviceA] **interface gigabitethernet 1/0/0**[ ~ DeviceA-GigabitEthernet1/0/0] **qppb-policy ac source inbound**[*DeviceA-GigabitEthernet1/0/0] **commit**[ ~ DeviceA-GigabitEthernet1/0/0] **quit**步骤 **5** 检查配置结果。# 在 DeviceA 上显示 QPPB 本地策略的信息如下。[ ~ DeviceA] **display qppb local-policy configuration ac**qppb local-policy: acstatistics disableservice-class outbound disableis-used yesqos-local-id 1 behavior dd**----**结束文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 184HUAWEI NetEngine40E配置指南 1 QoS##### 配置文件       - DeviceA 的配置文件。#sysname DeviceA#traffic behavior ddremark dscp af11#qppb local-policy acqos-local-id 1 behavior dd#isis 1is-level level-1cost-style widenetwork-entity 10.0000.0000.0001.00filter-policy route-policy ac importtraffic-eng level-1#interface GigabitEthernet1/0/0undo shutdownip address ********* *************isis enable 1qppb-policy ac source inbound#route-policy ac permit node 123apply qos-local-id 1#return       - DeviceB 的配置文件。#sysname DeviceB#isis 1cost-style widenetwork-entity 10.0000.0000.0002.00#interface GigabitEthernet1/0/0undo shutdownip address ********* *************isis enable 1#return### 1.8 HQoS 配置传统的 QoS 基于端口进行流量调度，无法区分用户和业务。 HQoS 可以针对每个用户的业务流进行队列调度。#### 1.8.1 HQoS 简介HQoS 能为高级用户提供精细化的服务质量保证，本处介绍 HQoS 的基本概念和原理。##### ******* HQoS 概述与传统的 QoS 基于端口进行流量调度不同， HQoS 在单个端口下能区分用户和不同用户的业务。HQoS 即层次化 QoS （ Hierarchical Quality of Service ），是一种通过多级队列调度机制，解决 Diffserv 模型下多用户多业务带宽保证的技术。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 185HUAWEI NetEngine40E配置指南 1 QoS传统的 QoS 采用一级调度，单个端口只能区分业务优先级，无法区分用户。只要属于同一优先级的流量，使用同一个端口队列，不同用户的流量彼此之间竞争同一个队列资源，无法对端口上单个用户的单个流量进行区分服务。 HQoS 采用多级调度的方式，可以精细区分不同用户和不同业务的流量，提供区分的带宽管理。##### NE40E 的调度结构在 NE40E 上，没有配置 HQoS 的情况下，只有类队列 CQ （ Class Queue ）和 Port 调度器，其调度结构如 图 **1-25** 。图 **1-25** 没有配置 HQoS 时的队列调度结构在配置 HQoS 的情况下，路由器另外划分缓存，用于缓存需要层次化调度的业务流队列，并对这些流队列先进行一轮多层次调度，再将 HQoS 流与非 HQoS 流统一入目的端口，进行统一调度，如 图 **1-26** 。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 186HUAWEI NetEngine40E配置指南 1 QoS图 **1-26** HQoS 流队列调度       - 叶子节点： FQ （ Flow Queue ）队列用于暂存一个用户各个优先级中的一个优先级的数据流。每个用户的数据流都可以划分为 1~8 个优先级，即每个用户可以使用 1~8 个 FQ 。不同用户之间不能共享FQ 。每个 FQ 可以配置 shaping 值来限制该队列的最大带宽。FQ 队列具有的属性：–队列优先级、队列权重– 队列整形速率 PIR– 报文丢弃策略，尾丢弃（ Tail-drop ）或 WRED       - 中间节点： SQ （ Subscriber Queue ）一个 SQ 代表一个用户（例如，一个 VLAN ，或一个 LSP ，或一个 PVC ），每个 SQ 可定义其 CIR 和 PIR 。每个 SQ 固定对应 8 种 FQ 业务优先级，这 1 ～ 8 个 FQ 共享该 SQ 的带宽。如果哪个 FQ空闲，则其他 FQ 可以占用空闲出来的带宽，但是受限于 FQ 自己的 PIR ，最大不能超过 FQ 自己的 PIR 。SQ 既是调度器，又是虚队列作为被调度对象。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 187HUAWEI NetEngine40E配置指南 1 QoS– 作为调度器：对多个 FQ 队列进行调度。 FQ 队列可以配置为 PQ 、 WFQ 和LPQ 。优先级为 EF 、 CS6 和 CS7 的 FQ 队列默认采用 SP 调度算法；优先级为BE 、 AF1 、 AF2 、 AF3 、 AF4 的流队列默认采用 WFQ 调度算法，调度权重为10:10:10:15:15 。– 作为被调度对象的虚队列：被赋予了两个属性， CIR 和 PIR 。通过流量测速（ Metering ），将输入流量分流成“ <=CIR ”与“ >CIR ”两部分；“ <=CIR ”的部分是指用户付费的部分，“ >CIR ”的那部分流量称为 EIR ， EIR=PIRCIR 。 EIR 是指突发量，允许用户流量突发至最大值 PIR 。       - 根节点： GQ （ Group Queue ）为了简便处理，可以把多个用户定义为一个用户组 GQ ，类似于在配置 BGP 对等体时把多个具有共同特点的对等体配置为一个组。例如，可以把相同总带宽需求的用户归为一个 GQ ，或把所有金牌级用户归为一个 GQ 。一个 GQ 可以绑定多个 SQ ，但一个 SQ 最多只能绑定到一个 GQ 内。GQ 作为调度器，对多个 SQ 队列进行调度。先采用 DRR 算法在 SQ 之间调度小于 CIR的那部分流量，不同 SQ 之间采用 DRR 调度。如果有剩余带宽，再采用 DRR 算法调度超过 CIR 但小于 PIR 的那部分流量（即 EIR ）。优先保证调度 CIR 的流量，如果有剩余带宽再调度 EIR 的流量，超过 PIR 的流量将被丢弃。因此，整个 GQ 的带宽如果达到了 PIR ，则 GQ 下每个 SQ 的 CIR 带宽都可以得到保证，且 SQ 最大可获得的带宽是每个 SQ 自己的 PIR 。此外， GQ 作为根节点，可以赋予最大带宽 PIR 属性，用于对多个用户的流量进行整体限速。该 GQ 下的所有用户都受限于这个最大带宽。 GQ 的 PIR 值只是用于整体限速，并不用来保证带宽， GQ 的 PIR 值建议不小于 GQ 中所有 SQ 的 CIR 之和，否则单个用户（ SQ ）的流量无法得到保证。为了帮助理解，下面举例说明 FQ 、 SQ 、 GQ 及其关系。假设一栋楼有 20 个家庭，每个家庭购买的带宽是 20M ，则只需要为每个家庭创建一个SQ ，设置 CIR 和 PIR 均为 20M 即可，满足了保证每个家庭 20M 带宽的要求，同时这里的PIR 也是限定用户最大可使用的带宽为 20M 。但随着 VoIP 、 IPTV 业务的开通，加上已有的个人上网 HSI 业务，运营商实时推出了新的带宽套餐，带宽仍然为 20M ，其中包含了VoIP ， IPTV 等增值业务。这样，每个家庭可以使用 VoIP 电话，机顶盒看电视（ IPTV ），同时还使用电脑上网冲浪（ HSI ）。此需求的 HQoS 配置方法是：       - 配置 3 个 FQ ，对应 3 种业务（ VoIP 、 IPTV 、 HSI ），并为每种业务设置自己的 CIR 和PIR ；       - 配置 20 个 SQ ，分别对应 20 个家庭用户。每个 SQ 配置 CIR 和 PIR ， CIR 保证带宽，PIR 限定最大带宽。       - 配置 1 个 GQ 对应一栋楼，将 20 个用户的带宽汇总，将 20 个用户视为“用户组”，整合 20 个用户的总带宽作为 GQ 的 PIR ，让这 20 个用户实现带宽共享。 20 个用户相对独立，但是总带宽又受到 GQ 的限制。分层模型为：       - FQ 用于对用户的各种业务进行细分，控制用户的业务类型和带宽在各个业务之间的分配；       - SQ 用于区分用户，对每个用户的带宽进行限速；       - GQ 用于区分用户组，对 20 个 SQ 的流量进行整体限速。FQ 的作用是保证各类业务的带宽分配关系； SQ 的作用是将每一个用户单独标识出来；而 GQ 的作用是使得各个用户之间的 CIR 能够得到保证，同时又达到带宽共享的目的。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 188HUAWEI NetEngine40E配置指南 1 QoS超过 CIR 部分的带宽是不保证的，这是合理的，因为超过 CIR 部分的带宽实际上是用户并未付费的部分，是属于额外的。而重要的是，必须保证 CIR ，因为 CIR 的带宽是客户购买的。按照 图 **1-26** 的 SQ 调度可以看到，用户的 CIR 总是可以保证的，因为用户的 CIR是被单独标识出来优先被调度的，因此不会被其它用户超过 CIR 部分的流量抢占。NE40E 上， HQoS 可实现上、下行方向的调度，且上、下行的调度结构不同。##### 1.8.1.2 HQoS 基本概念HQoS 需要用到的基本概念包括：流队列、用户队列、用户组队列、类队列、低优先级队列。##### 流队列 FQ （ Flow Queue ）HQoS 可以针对每个用户的业务流进行队列调度。通过配置峰值带宽（ PIR ）， HQoS 可以对每个用户进行带宽限制。每个用户都可以细分为 8 个流队列， 8 个流队列可以配置PQ 、 WFQ 和 LPQ 队列调度；每个流队列可以配置 WRED 丢弃机制；可以配置流量整形的速率。##### 用户队列 SQ （ Subscriber Queue ）SQ 为虚拟队列。所谓虚拟队列，是指队列不存在实际的缓存单元，不暂存数据，数据进入和离开队列没有延迟，仅作为层次化调度中的其中一级队列参与输出调度。每个 SQ 固定对应 8 种 FQ 业务优先级，可配置 1 ～ 8 个 FQ 。空闲 FQ 不能被其它 SQ 利用，即这 1 ～ 8 个 FQ 共享该 SQ 的带宽。实际应用中，一个 SQ 对应一个用户（ VLAN 、 VPN等），一个用户可使用 1 ～ 8 个 FQ 。每个 SQ 可定义其 CIR 和 PIR 。##### 用户组队列 GQ （ Group Queue ）HQoS 可以将多个 SQ 绑定到一个 GQ 实现第三级队列调度。GQ 用来对多个用户的流量进行整体限速。设置的 shaping 值建议不要小于 GQ 中所有SQ 的 CIR 之和，否则单个用户（ SQ ）的流量就无法得到保证。GQ 也是虚拟队列。每个 SQ 最多只能绑定到一个 GQ 内，也可以不绑定 GQ ，跨过第三级队列调度。GQ 可以实现流量整形，配置整形速率。##### 类队列 CQ （ Class Queue ）HQoS 调度时，流队列报文经过用户队列调度后，要同普通报文同时进入端口中的CQ 。缺省情况下， SQ 中 8 个等级的 FQ 同端口的 CQ 是一一对应的映射关系。如果用户需要自行指定 FQ 与 CQ 的映射关系，可以通过配置 flow-mapping ，创建流队列映射对象来实现。具体操作步骤请参考： 配置流队列的映射关系。##### 低优先级队列 LPQ （ Low Priority Queue ）说明LPQ 是在以太网接口上实现的队列调度机制。在以太网接口的 HQoS 中共有三种队列调度方式： PQ ， WFQ ， LPQ 。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 189HUAWEI NetEngine40E配置指南 1 QoS调度的时候，优先调度 PQ 队列的报文，其次是 WFQ ，最后是 LPQ 。在 PQ 和 WFQ 队列调度结束后，如果还有剩余带宽，这些剩余带宽就可以分配给 LPQ 。LPQ 内部的队列调度方式与 PQ 一样。不同在于拥塞时 PQ 可以抢占 WFQ 的带宽，而 LPQ不可以抢占 WFQ 的带宽。实际应用中，可以将 BE 流用 LPQ 进行调度。这样，在网络负载较重时， BE 流可以完全被限制，优先满足其它业务。#### 1.8.2 HQoS 配置注意事项##### 特性限制表 **1-21** 本特性的使用限制|特性限制|系列|涉及产品||---|---|---||队列优先级CS7>CS6>EF>AF4>AF3>AF2>AF1>BE，设<br>备实现时优先保证三个较高优先级(CS7~EF)队列的低<br>时延特性，因此在部署低时延业务时需要部署在三个高<br>优先级队列中，如果部署在其余五个低优先级队列中，<br>需要保证三个高优先级队列不拥塞，否则无法保证五个<br>低优先级做low-latency的最大时延|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||多点到多点IP硬管道的公网侧不支持隧道负载分担，公<br>网出口必须保证惟一。否则VPLS硬管道失效，带宽无<br>法保证。<br>建议网管端到端部署VPLS硬管道，则没有上述要求。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 190HUAWEI NetEngine40E配置指南 1 QoS|特性限制|系列|涉及产品||---|---|---||1、后置TM子卡或单板情况下，如果配置了带宽HQOS<br>时，此时TE普通流量统计由于HQOS功能由后置TM完<br>成，而Telemetry统计由NP完成，则Telemetry采集和<br>普通流量统计可能不一致。<br>2、对于TE HSB切换、出接口变更等涉及跨板场景，出<br>接口切换后，Telemetry采集计数重新会从零重新开始<br>计数，和普通TE流量统计不同。<br>3、当TE普通流量统计在CAR和统计ID方式之间切换<br>时，Telemetry采集和普通TE统计计数可能不一致。<br>4、TE FRR保护，主Tunnel故障，流量走Bypass<br>Tunnel后，主Tunnel的Telemetry上报信息不包含FRR<br>切换后流量，和主Tunnel统计查询结果不一致。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||轨道交通多点到多点IP硬管道与AC口下的端口HQoS互<br>斥。AC口下配置了接口Hqos的情况下无法配置VPLS硬<br>管道。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||IP硬管道和接口下差分服务模式（difserv-mode）功能<br>互斥。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 191HUAWEI NetEngine40E配置指南 1 QoS|特性限制|系列|涉及产品||---|---|---||多点到多点IP硬管道的主备TE隧道软硬属性需要一致。<br>命令行配置硬管道迭代软硬管道属性不一致的隧道会导<br>致PW不生效，业务不通。<br>建议网管端到端部署VPLS硬管道，则没有上述要求。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||多点到多点IP硬管道的公网隧道必须是静态双向共路TE<br>隧道，并且公网口必须配置硬管道带宽。命令行配置硬<br>管道迭代其他隧道会导致PW不生效，业务不通。<br>建议网管端到端部署VPLS硬管道，没有上述要求。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||多点到多点IP硬管道涉及多个AC和多个PW互通，无法<br>支持AC与PW之间的硬管道带宽校验。AC带宽总和超<br>PW带宽时，会出现丢包。<br>在部署的时候PW的硬管道带宽必须大于同一个VSI实例<br>下所有AC口的带宽总和。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||对于同一主接口下VLL硬管道和多点到多点IP硬管道共<br>存的情况，VLL硬管道基于端口带宽校验可能不准。<br>建议VLL硬管道和多点到多点IP硬管道不在同一接口<br>下。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 192HUAWEI NetEngine40E配置指南 1 QoS|特性限制|系列|涉及产品||---|---|---||多点到多点IP硬管道支持TE的TPOAM检测链路故障和<br>TE的APS触发链路保护，不支持VPLS PW的TPOAM检<br>测和PW APS保护。配置VPLS PW的TPOAM检测和PW<br>APS保护后不保证功能可用性。<br>建议硬管道的链路保护可以使用TE隧道的检测和保护。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||1、设备上配置了MLDP over GRE，则整机无法节能减<br>排。<br>2、设备转发模块先配置了节能减排，后配置MLDP<br>over GRE，则节能减排功能失效。<br>3、模块退出节能且处于唤醒状态后，业务才会恢复。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||对于PW公网出接口为Trunk并且Trunk成员口跨NP模块<br>或者跨板的情况，存在NP下行CAR带宽翻倍的情况。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||VPLS配置多点到多点IP硬管道属性后，默认为MPLS的<br>差分服务模式为pipe增强模式，不支持配置携带参数<br>pipe或shortpipe的difserv-mode命令。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 193HUAWEI NetEngine40E配置指南 1 QoS|特性限制|系列|涉及产品||---|---|---||在GRE Tunnel接口配置端口级HQoS功能，如果入GRE<br>Tunnel的流量是从多块接口板或多个TM进入设备，会<br>存在HQoS限速翻倍问题。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||端口队列（port-queue）支持CIR与share-shaping互<br>斥。同一个端口下，如果有端口队列配置了CIR，则该<br>端口下不能再配置share-shaping，同一个端口下，如<br>果配置了share-shaping，则不能再配置端口队列的<br>CIR。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||仅下行支持fq和port-queue的cir功能。|NE40E|NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||足够的CIR带宽是保证低时延的一个必要条件。端口的<br>默认CIR配置是10M，如果需要保证低时延的port-<br>queue流量大于10M，则需要通过"qos default user-<br>queue"修改端口默认CIR配置, 使得其配置值不低于低<br>时延port-queue的流量带宽。低时延业务流量带宽大于<br>CIR，不能保证业务的低时延。<br>建议通过qos default user-queue命令行调整接口默认<br>SQ的CIR带宽。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 194HUAWEI NetEngine40E配置指南 1 QoS|特性限制|系列|涉及产品||---|---|---||Trunk接口配置port shaping或port-queue后，会按照<br>成员口个数带宽翻倍。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||CR5D00EEGF71子卡配置set service-mode port-<br>extended命令时，下行报文才能根据复杂流分类的结果<br>入队列。|NE40E|NetEngine<br>40E-X8AK||qos channel-profle模板配置的总带宽小于120G不允许<br>应用的Slot视图。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||多队列拥塞耗尽片内缓存资源时，无法保证不拥塞队列<br>的带宽隔离和线速，比如信道化子接口场景。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X16A|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 195HUAWEI NetEngine40E配置指南 1 QoS|特性限制|系列|涉及产品||---|---|---||1、vlan-type dot1q子接口HQoS同vlan-type dot1q信<br>道化子接口共享微波链路带宽。由于微波带宽是通过Y.<br>1731协议触发学习的，配置界面无法感知学习结果，<br>无法做带宽校验。需要用户根据微波链路相对稳定的带<br>宽，事先规划好信道化业务与非信道化业务的带宽，满<br>足“信道化业务带宽和 +非信道化业务带宽 <=相对稳<br>定的微波带宽”，否则会导致信道化业务带宽得不到保<br>证，在下游的微波设备上出现丢包。<br>2、已经按照微波链路相对稳定的带宽规划好信道化业<br>务与非信道化业务的带宽了，但是，信道化业务带宽和<br>是静态配置好的，微波带宽则是动态变化的，如果微波<br>带宽下降的比较厉害，出现“信道化业务带宽和 >=微<br>波通告带宽”，则仍然会导致承载的信道化业务带宽得<br>不到保证，在下游的微波设备上出现丢包。此时系统会<br>产生告警，需要运维人员根据实际情况进行处理，比如<br>将部分信道化业务切换到备份的微波链路上。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||1、Slot VE SR LPU1T单板不支持上行HQoS功能。<br>2、Slot VE下行只在硬环回模式下支持HQoS。<br>3、Global VE SR LPU1T单板不支持上行HQoS功能。<br>4、Globale VE下行支持软/硬环回两种模式下的<br>HQoS，如果是软环回模式，下行HQoS会环回到上行<br>实现，例如L2入L3场景，L2VE下行环回到L3VE上行实<br>现，如果L3VE上行配置了HQoS，L2VE下行HQoS则不<br>生效。<br>5、LPUF200C/LPUF200D/LPUF240F单板硬/软环回都<br>使用单板上行TM资源。（可以使用"display QoS<br>resource user-queue slot <slot-id> { inbound |<br>outbound } "查询资源使用情况）。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||端口扩展场景下行不支持配置流动作SQ。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 196HUAWEI NetEngine40E配置指南 1 QoS|特性限制|系列|涉及产品||---|---|---||仅LPUI-21-L/LPUI-51-L/LPUF-51/LPUF-51-B/<br>LPUI-51/LPUI-51-B/LPUI-51-S/LPUS-51/LPUF-101/<br>LPUF-101-B/LPUI-101/LPUI-101-B/LPUS-101/<br>LPUF-51-E/LPUI-51-E/LPUF-120/LPUF-120-B/<br>LPUF-120-E/LPUI-102-E/LPUI-120/LPUI-120-B/<br>LPUI-120-L/LPUI-52-E/LPUI-120-E/LPUF-240/<br>LPUF-240-B/LPUF-240-E/LPUI-240/LPUI-240-B/<br>LPUI-240-L支持通过PM统计接口下的SQ、GQ、PQ数<br>据。（仅支持统计物理口且该物理口不能是Trunk接口<br>的成员口）|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A||如下单板支持FQ和port-queue的cir功能：LPUF-53A/<br>LPUF-243A/LPUI-242A/LPUF-483A/LPUI-52C/<br>LPUI-402C/LPUF-400-E/LPUI-2TA/LPUF-1T2A/<br>LPUI-1T2A-CM/LPUI-483A-CM/LPUI-243A-CM/<br>LPUF-243-K/LPUF-53-K/VSUI-400-K/LPUI-2T-K/<br>LPUF-53D/LPUF-243D/LPUF-483D/VSUI-400A单板<br>（仅下行支持）。不支持低优先级队列的CIR调度，低<br>优先级队列的带宽无法得到保障。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||接口下配置了连续的VLAN和qos-profle模板，如果同<br>时删除2个或2个以上不连续VLAN并一起提交，可能会<br>导致业务震荡和统计数据丢失。<br>建议接口下删除连续VLAN和qos-profle模板，一次删<br>除一个或连续的一段VLAN后提交。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||接口上配置了fow-queue、user-queue、user-group-<br>queue或sub-port-queue后，在流量跨硬件资源组（硬<br>件资源组指的是硬件限速单元）的场景下，会存在带宽<br>翻倍的问题。例如，Trunk接口存在跨转发模块的多个<br>成员口，user-queue在多个转发模块上限速导致带宽翻<br>倍。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 197HUAWEI NetEngine40E配置指南 1 QoS|特性限制|系列|涉及产品||---|---|---||Trunk主接口执行命令qos schedule-tree distribute-<br>mode outbound配置成调度树拆分模式后，每个trunk<br>成员口为1个硬件资源组，配置FQ、SQ、GQ和VI限速<br>时会按照成员口个数进行带宽翻倍。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||上行缓存功能弱，FQ通过shaper桶深来做输入突发控<br>制，在流量突发大的且用户FQ业务间的权重和比较大<br>（权重值）的时候，如果shaper桶深配置过小会造成丢<br>包或WFQ调度不准确<br>需要配置较大的桶深，可以使用指定的PBS配置工具计<br>算出合理的PBS值。|NE40E|NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||部署带宽扩展license可以限制物理接口的带宽，但是当<br>物理接口加入Trunk接口，且在该Trunk接口存在多个成<br>员口时，Trunk接口下部署hqos功能后，Trunk成员口<br>的带宽将不再受带宽扩展license限制。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A||内联口trunk上不支持配置"qos member-link-<br>scheduler distribute"命令行。<br>建议合理规划配置。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 198HUAWEI NetEngine40E配置指南 1 QoS|特性限制|系列|涉及产品||---|---|---||外联口不支持配置"qos hard-pipe bandwidth"、"user-<br>group-queue"、"sub-port-queue"、"user-queue"、<br>"shaping service-template"、"port-queue-alarm"、<br>"qos link-adjustment"、"qos queueu-resource<br>pool"命令行。<br>建议合理规划配置。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||LPUI-21-L/LPUI-51-L/LPUF-51/LPUF-51-B/LPUI-51/<br>LPUI-51-B/LPUI-51-S/LPUS-51/LPUF-101/LPUF-101-<br>B/LPUI-101/LPUI-101-B/LPUS-101/LPUF-51-E/<br>LPUI-51-E/LPUF-120/LPUF-120-B/LPUF-120-E/<br>LPUI-102-E/LPUI-120/LPUI-120-B/LPUI-120-L/<br>LPUI-52-E/LPUI-120-E/LPUF-240/LPUF-240-B/<br>LPUF-240-E/LPUI-240/LPUI-240-B/LPUI-240-L单板默<br>认关闭SQ动态带宽调整，该单板上的SQ PIR带宽总和<br>存在600G的配置限制。当配置带宽总和超过600G时，<br>BRAS用户带宽转CAR，非BRAS用户带宽限制不生效。<br>用户开启单板的SQ动态带宽调整功能后，当SQ带宽和<br>超过480G时，BRAS用户SQ带宽根据用户实际流量动态<br>调整。当在线用户量大且用户流量变化引起大量用户带<br>宽调整的情况下，用户带宽调整期间无法达到配置带<br>宽，带宽调整时间最大到100分钟。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X16A||用户侧带宽拆分功能仅对接入用户流量生效，对DAA、<br>EDSG业务流量不生效，DAA和EDSG业务流量仍按照配<br>置带宽生效。如果用户从Trunk口上线，当接入用户的<br>流量跨TM模块时，会导致DAA，EDSG业务限速流量翻<br>倍。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 199HUAWEI NetEngine40E配置指南 1 QoS|特性限制|系列|涉及产品||---|---|---||LPUF120/LPUI120/LPUF240/LPUI240非-E单板上，同<br>一主接口下，不能同时配置port-queue cs6的限速调度<br>和信道化子接口。换插子卡或配置回退出现同时配置<br>port-queue cs6的限速调度和信道化场景，无法保证信<br>道化带宽和时延。建议合理规划业务。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A||在LPUI-21-L/LPUI-51-L/LPUF-51/LPUF-51-B/<br>LPUI-51/LPUI-51-B/LPUI-51-S/LPUS-51/LPUF-101/<br>LPUF-101-B/LPUI-101/LPUI-101-B/LPUS-101/<br>LPUF-51-E/LPUI-51-E/LPUF-120/LPUF-120-B/<br>LPUF-120-E/LPUI-102-E/LPUI-120/LPUI-120-B/<br>LPUI-120-L/LPUI-52-E/LPUI-120-E/LPUF-240/<br>LPUF-240-B/LPUF-240-E/LPUI-240/LPUI-240-B/<br>LPUI-240-L单板的物理主接口下，CS6或CS7队列流量<br>拥塞时，无法保证信道化带宽和时延。<br>在LPUI-21-L/LPUI-51-L/LPUF-51/LPUF-51-B/<br>LPUI-51/LPUI-51-B/LPUI-51-S/LPUS-51/LPUF-101/<br>LPUF-101-B/LPUI-101/LPUI-101-B/LPUS-101/<br>LPUF-51-E/LPUI-51-E/LPUF-120/LPUF-120-B/<br>LPUF-120-E/LPUI-102-E/LPUI-120/LPUI-120-B/<br>LPUI-120-L/LPUI-52-E/LPUI-120-E/LPUF-240/<br>LPUF-240-B/LPUF-240-E/LPUI-240/LPUI-240-B/<br>LPUI-240-L单板的Trunk主接口下，EF、CS6或CS7队列<br>流量拥塞时，无法保证信道化带宽和时延。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A||一个主接口下配置了信道化子接口后，这个主接口及其<br>下面的所有子接口上都不允许配置"sub-port-queue"。<br>建议合理规划业务。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 200HUAWEI NetEngine40E配置指南 1 QoS|特性限制|系列|涉及产品||---|---|---||组播剪枝场景下，LPUF120/LPUI120/LPUF240/<br>LPUI240非-E单板，配置信道化子接口后，主接口及其<br>子接口下二层组播、Bier组播流量无法达到端口带宽，<br>最大影响一半带宽。同一主接口下，同时配置二层组<br>播/Bier组播和信道化子接口时，会出现二层组播/Bier<br>组播业务无法达到端口实际带宽。<br>同一主接口下，不建议同时配置二层组播/Bier组播业务<br>和信道化子接口。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A||如果主接口下存在信道化子接口，那么主接口不允许配<br>置qos default { sub-port-queue | user-group-queue }<br>命令。建议合理规划功能使用。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||trunk信道化子接口的带宽之和不能超过Eth-Trunk接口<br>带宽（所有up口带宽和）的98%减去给te隧道最大预留<br>带宽。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A||信道化子接口仅支持配置在GE、XGE、10GE、25GE、<br>40GE、50GE、100GE、200GE、400GE的以太物理端<br>口、FlexE物理口和Eth-Trunk接口的vlan-type dot1q子<br>接口上。建议合理规划功能使用。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 201HUAWEI NetEngine40E配置指南 1 QoS|特性限制|系列|涉及产品||---|---|---||同一个主接口下存在信道化子接口，这个主接口及所有<br>子接口都不允许配置sub-port-queue和user-group-<br>queue。建议合理规划功能使用。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||如果信道化子接口下应用的流策略中配置了user-<br>queue，user-queue的带宽不受信道化子接口带宽约<br>束，而是受主接口下非信道化部分的剩余带宽约束，所<br>以在信道化子接口下应用的流策略不建议配置下行<br>user-queue。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||Global-VE接口下，下行方向配置的流SQ不生效。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||Eth-Trunk信道化子接口与Eth-Trunk调度树拆分互斥。<br>建议合理规划功能使用。|NE40E|NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 202HUAWEI NetEngine40E配置指南 1 QoS|特性限制|系列|涉及产品||---|---|---||信道化子接口不能基于业务做精准补偿，如果按照最大<br>补偿和精准shaper处理，会导致端口使用率不能达到<br>100%。建议合理规划功能使用。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||LPUI-21-L/LPUI-51-L/LPUF-51/LPUF-51-B/LPUI-51/<br>LPUI-51-B/LPUI-51-S/LPUS-51/LPUF-101/LPUF-101-<br>B/LPUI-101/LPUI-101-B/LPUS-101/LPUF-51-E/<br>LPUI-51-E/LPUF-120/LPUF-120-B/LPUF-120-E/<br>LPUI-102-E/LPUI-120/LPUI-120-B/LPUI-120-L/<br>LPUI-52-E/LPUI-120-E/LPUF-240/LPUF-240-B/<br>LPUF-240-E/LPUI-240/LPUI-240-B/LPUI-240-L单板上<br>的公网子接口同时配置SRv6业务和HQoS时，HQoS限<br>速不生效。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||LPUF120/LPUI120/LPUF240/LPUI240非-E单板上，主<br>接口下cs6或cs7队列流量拥塞时，无法保证信道化带宽<br>和时延。建议合理规划业务。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 203HUAWEI NetEngine40E配置指南 1 QoS|特性限制|系列|涉及产品||---|---|---||在LPUI-21-L/LPUI-51-L/LPUF-51/LPUF-51-B/<br>LPUI-51/LPUI-51-B/LPUI-51-S/LPUS-51/LPUF-101/<br>LPUF-101-B/LPUI-101/LPUI-101-B/LPUS-101/<br>LPUF-51-E/LPUI-51-E/LPUF-120/LPUF-120-B/<br>LPUF-120-E/LPUI-102-E/LPUI-120/LPUI-120-B/<br>LPUI-120-L/LPUI-52-E/LPUI-120-E/LPUF-240/<br>LPUF-240-B/LPUF-240-E/LPUI-240/LPUI-240-B/<br>LPUI-240-L、LPUF-480/LPUF-480-B/LPUI-480/<br>LPUI-480-B/LPUI-480-L/LPUF-480-E/LPUI-480-CM/<br>LPUI-200/LPUI-200-L/LPUF-200/LPUF-200-B/<br>LPUI-1T/LPUI-1T-B/LPUI-1T-L/LPUI-241/LPUI-241-B/<br>LPUI-241-CM/LPUF-241/LPUF-241-E/LPUF-52单板的<br>信道化子接口上部署SRv6、二层组播或BIER组播业务<br>时，不支持按照信道化限速进行业务限制。建议合理规<br>划功能使用。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A||LPUF120/LPUI120/LPUF240/LPUI240非-E单板的信道<br>化子接口上部署SRv6、二层组播或Bier组播业务时，不<br>支持按照信道化限速进行业务限制。信道化子接口上不<br>建议配置SRv6业务、二层组播或Bier组播业务。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A||信道化子接口和同一个主接口的IP硬管道互斥。建议合<br>理规划业务。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 204HUAWEI NetEngine40E配置指南 1 QoS|特性限制|系列|涉及产品||---|---|---||信道化子接口不允许配置"qos-profle"和"user-group-<br>queue"。建议合理规划业务。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||同一个主接口下存在信道化子接口，信道化子接口不可<br>以配置IP硬管道。接口配置IP硬管道后，不允许配置为<br>信道化子接口。建议合理规划功能使用。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||Eth-Trunk成员口同板但TM类型不同，不能配置Eth-<br>Trunk信道化子接口。建议合理规划功能使用。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||配置Eth-Trunk信道化子接口与在Eth-Trunk成员口配置<br>port shaping互斥。建议合理规划功能使用。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 205HUAWEI NetEngine40E配置指南 1 QoS|特性限制|系列|涉及产品||---|---|---||信道化子接口不支持配置user-group-queue。建议合理<br>规划功能使用。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||在信道化场景下，对于LPUI-21-L/LPUI-51-L/LPUF-51/<br>LPUF-51-B/LPUI-51/LPUI-51-B/LPUI-51-S/LPUS-51/<br>LPUF-101/LPUF-101-B/LPUI-101/LPUI-101-B/<br>LPUS-101/LPUF-51-E/LPUI-51-E/LPUF-120/<br>LPUF-120-B/LPUF-120-E/LPUI-102-E/LPUI-120/<br>LPUI-120-B/LPUI-120-L/LPUI-52-E/LPUI-120-E/<br>LPUF-240/LPUF-240-B/LPUF-240-E/LPUI-240/<br>LPUI-240-B/LPUI-240-LLPUF-480/LPUF-480-B/<br>LPUI-480/LPUI-480-B/LPUI-480-L/LPUF-480-E/<br>LPUI-480-CM/LPUI-200/LPUI-200-L/LPUF-200/<br>LPUF-200-B/LPUI-1T/LPUI-1T-B/LPUI-1T-L/LPUI-241/<br>LPUI-241-B/LPUI-241-CM/LPUF-241/LPUF-241-E/<br>LPUF-52单板配套非-E子卡时，为了保证时延，默认补<br>偿值只能匹配单个典型场景。例如TM补偿值设置为58<br>字节，在某些场景可能会导致报文膨胀，从而导致满带<br>宽流量丢包，如IP转发场景，而且字节越小影响越大。<br>建议合理规划功能使用。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A||信道化子接口不支持配置qos-profle、user-queue和<br>user-queue shaping ldp-trafc outbound命令。建议<br>合理规划功能使用。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 206HUAWEI NetEngine40E配置指南 1 QoS|特性限制|系列|涉及产品||---|---|---||在Eth-Trunk成员口跨TM场景下，信道化的配置带宽默<br>认按照同TM的权重和等比拆分方式下发，带宽不翻<br>倍，Hash不均情况下会达不到信道化限速带宽和时<br>延，会有丢包情况。建议合理规划功能使用。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||同一物理口上配置的信道化子接口的带宽之和不能超过<br>物理口带宽的98%减去给te隧道最大预留带宽。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||信道化子接口仅支持配置在GE、XGE、10GE、25GE、<br>40GE、50GE、100GE、200GE、400GE的以太物理端<br>口、FlexE物理口和Eth-Trunk接口的vlan-type dot1q子<br>接口或encapsulation qinq-termination子接口上。建<br>议合理规划功能使用。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||硬管道在网络侧使用的转发接口不建议再部署限速类<br>QoS功能。硬管道转发使用的主接口或者子接口上的<br>QoS配置（入出方向接口CAR，入出方向接口ACL）对<br>硬管道流量也生效。例如：P节点，入口方向配置<br>CAR，那么硬管道流量会被CAR限速，可能导致硬管道<br>丢包。<br>建议不要在开启了硬管道的网络侧接口上部署QoS<br>car、含car/user-queue/deny动作的trafc-policy。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 207HUAWEI NetEngine40E配置指南 1 QoS|特性限制|系列|涉及产品||---|---|---||GRE Tunnel支持端口级HQoS功能，在LPUI-1T单板不<br>生效。<br>建议使用其它单板。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X16A||仅LPUF-50/LPUF-50-L支持通过PM统计接口下的SQ、<br>GQ、PQ数据。（仅支持统计物理口且该物理口不能是<br>Trunk接口的成员口）|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A||P51-E/P101-E/LPUI-51-E子卡，不支持Bufer相关的统<br>计。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A||1、HQoS支持端口默认SQ和端口PQ队列统计信息的<br>Telemetry采集。<br>2、HQoS仅支持以太物理接口/Trunk接口的Telemetry<br>采集。<br>3、HQoS支持慢速秒级的Telemetry采集。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||LPUI-1T/LPUI-1T-B/LPUI-1T-L单板在设备转发模式为<br>compatible模式时上行无HQoS调度功能；在设备转发<br>模式为enhance模式时下行无HQOS调度能力。<br>建议合理规划配置。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 208HUAWEI NetEngine40E配置指南 1 QoS|特性限制|系列|涉及产品||---|---|---||Trunk成员口存在跨TM/NP场景并且Trunk成员口流量<br>HASH不均或流量没有按照负载分担方式通过时，带宽<br>拆分后，单个TM/NP上的限速带宽可能小于配置值。<br>Trunk成员口负载分担不均时，可能导致限速内丢包。<br>建议Trunk成员口不跨子卡。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||如果已在Eth-trunk口配置命令"qos schedule-tree<br>distribute-mode outbound"，并且该Eth-Trunk有成员<br>口，进行配置回退时，业务会中断。配置回退结束后，<br>业务自动恢复。<br>删除调度树拆分时，Trunk中成员口要退出才能删除，<br>业务会受影响。回退调度树拆分时，是一个逆向过程，<br>也需要成员口退出Trunk，业务同样会受影响。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||每个SQ下最多有2个FQ队列支持cir配置；每个端口下<br>只有2个port-queue支持cir配置。不支持低优先级队列<br>的CIR调度，低优先级队列的带宽无法得到保障。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||Eth-Trunk信道化子接口默认按照TM权重等比拆分信道<br>化带宽，在成员口加入或退出时，由于Eth-Trunk各成<br>员口所在TM的权重比例发生变化，会刷新信道化的限<br>速带宽，在这期间信道化流量达不到限速带宽和时延，<br>会出现丢包的情况。建议合理规划功能使用。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 209HUAWEI NetEngine40E配置指南 1 QoS|特性限制|系列|涉及产品||---|---|---||在Eth-Trunk接口下，信道化的配置带宽默认按照成员<br>口权重等比拆分方式下发，带宽不翻倍，Hash不均情<br>况下会达不到信道化限速带宽和时延，会有丢包情况。<br>建议合理规划功能使用。|NE40E|NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||流队列（fow-queue）支持CIR与share-shaping互斥。<br>同一个用户队列（user-queue）下，如果有流队列配置<br>了CIR，则该用户队列下不能再配置share-shaping，同<br>一个用户队列下，如果配置了share-shaping，则不能<br>再配置流队列的CIR。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||配置了CIR的端口队列与配置了低时延的端口队列，会<br>进行流量抢占，同一个端口下，如果有端口队列配置了<br>CIR，则该端口队列的流量会与同一端口下配置了低时<br>延的端口队列抢占流量，流量抢占的优先级与端口队列<br>本身的优先级一致；同一个端口队列不支持同时配置<br>CIR和低时延。建议合理规划业务。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||2T-B单板不支持HQOS功能。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 210HUAWEI NetEngine40E配置指南 1 QoS|特性限制|系列|涉及产品||---|---|---||上行缓存功能弱，FQ通过shaper桶深来做输入突发控<br>制，在流量突发大的且用户FQ业务间的权重和比较大<br>（权重值）的时候，如果shaper桶深配置过小会造成丢<br>包或WFQ调度不准确<br>需要配置较大的桶深，可以使用指定的PBS配置工具计<br>算出合理的PBS值。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||LPUF-53A/LPUF-243A/LPUI-242A/LPUF-483A/<br>LPUI-52C/LPUI-402C/LPUF-400-E/LPUI-2TA/<br>LPUF-1T2A/LPUI-1T2A-CM/LPUI-483A-CM/<br>LPUI-243A-CM/LPUF-243-K/LPUF-53-K/VSUI-400-K/<br>LPUI-2T-K/LPUF-53D/LPUF-243D/LPUF-483D/<br>VSUI-400A单板上行缓存能力弱，通过FQ的CBS来调节<br>抗输入突发的大小，为抗输入突发不丢包需要调大<br>CBS，同时会带来输出突发也随之增大。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||如下单板支持FQ和port-queue的cir功能：LPUI-2T/<br>LPUI-2T-B/LPUI-2T-CM单板。不支持低优先级队列的<br>CIR调度，低优先级队列的带宽无法得到保障。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A||如下单板支持FQ和port-queue的cir功能：LPUF-241/<br>LPUF-241-R/LPUF-52/LPUI-241/LPUI-241-B/<br>LPUI-241-CM。不支持低优先级队列的CIR调度，低优<br>先级队列的带宽无法得到保障。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 211HUAWEI NetEngine40E配置指南 1 QoS|特性限制|系列|涉及产品||---|---|---||Eth-Trunk信道化子接口流量在负载分担不均匀时无法<br>保证信道化子接口带宽，Eth-Trunk信道化子接口下配<br>置命令行mode channel bandwidth maximize使能扩<br>展模式后，能够提高信道化带宽，在端口不拥塞时，能<br>够同时保证时延；但如果端口拥塞，则无法保证时延。<br>请调整信道化带宽配置比实际大些，解决拥塞问题。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||如下单板支持FQ和port-queue的cir功能：LPUI-2T/<br>LPUI-2T-B/LPUI-2T-CM/LPUF-481/LPUF-402-E/<br>LPUF-1T-E/LPUI-1T-CM单板（仅下行支持）。不支持<br>低优先级队列的CIR调度，低优先级队列的带宽无法得<br>到保障。|NE40E|NE40E-<br>X8A/<br>NE40E-<br>X16A||trunk接口port shaping带宽拆分，流量负载分担不均<br>时端口总流量可能达不到port shaping配置带宽。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||在LPUI-21-L/LPUI-51-L/LPUF-51/LPUF-51-B/<br>LPUI-51/LPUI-51-B/LPUI-51-S/LPUS-51/LPUF-101/<br>LPUF-101-B/LPUI-101/LPUI-101-B/LPUS-101/<br>LPUF-51-E/LPUI-51-E/LPUF-120/LPUF-120-B/<br>LPUF-120-E/LPUI-102-E/LPUI-120/LPUI-120-B/<br>LPUI-120-L/LPUI-52-E/LPUI-120-E/LPUF-240/<br>LPUF-240-B/LPUF-240-E/LPUI-240/LPUI-240-B/<br>LPUI-240-L单板的同一物理主接口下，不能同时配置<br>port-queue cs6和信道化子接口功能。<br>在LPUI-21-L/LPUI-51-L/LPUF-51/LPUF-51-B/<br>LPUI-51/LPUI-51-B/LPUI-51-S/LPUS-51/LPUF-101/<br>LPUF-101-B/LPUI-101/LPUI-101-B/LPUS-101/<br>LPUF-51-E/LPUI-51-E/LPUF-120/LPUF-120-B/<br>LPUF-120-E/LPUI-102-E/LPUI-120/LPUI-120-B/<br>LPUI-120-L/LPUI-52-E/LPUI-120-E/LPUF-240/<br>LPUF-240-B/LPUF-240-E/LPUI-240/LPUI-240-B/<br>LPUI-240-L单板的同一Trunk主接口下，不能同时配置<br>port-queue ef、port-queue cs6、port-queue cs7和信<br>道化子接口功能。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 212HUAWEI NetEngine40E配置指南 1 QoS|特性限制|系列|涉及产品||---|---|---||内联口Trunk场景下，子卡上所有接口退出内联口Trunk<br>后，内联口Trunk对应的外联口在该子卡上HQoS统计<br>数据会丢失。<br>统计命令行："display port-queue statistics"和<br>"display QoS-profle statistics"|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||LPUI-21-L/LPUI-51-L/LPUF-51/LPUF-51-B/LPUI-51/<br>LPUI-51-B/LPUI-51-S/LPUS-51/LPUF-101/LPUF-101-<br>B/LPUI-101/LPUI-101-B/LPUS-101/LPUF-51-E/<br>LPUI-51-E/LPUF-120/LPUF-120-B/LPUF-120-E/<br>LPUI-102-E/LPUI-120/LPUI-120-B/LPUI-120-L/<br>LPUI-52-E/LPUI-120-E/LPUF-240/LPUF-240-B/<br>LPUF-240-E/LPUI-240/LPUI-240-B/LPUI-240-L单板，<br>硬管道和软管道流量共享CQ队列，当软管道优先级为<br>cs7的流量拥塞时会影响硬管道时延或导致硬管道流量<br>丢包。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A||用户侧带宽拆分功能要求Trunk成员口负载分担均匀，<br>否则会小于用户预期限速带宽。建议合理规划用户流<br>量。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||内联口配置port-queue、port-queue-alarm、QoS<br>default user-queue命令行在内联口的默认队列生效。<br>外联口流量不会经过内联口默认队列调度，所以配置对<br>于外联口转发的流量不生效。<br>需要在外联口配置端口限速和端口调度业务，内联口可<br>以通过配置"port shaping"规划端口带宽。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK|文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 213HUAWEI NetEngine40E配置指南 1 QoS|特性限制|系列|涉及产品||---|---|---||LPUF-480-E、LPUF-480、LPUF-480-B、LPUI-480、<br>LPUI-480-B、LPUI-480-L、LPUF-481、LPUF-402-E、<br>SPUI-402、LPUF-1T、LPUI1T单板上，LNS入隧道场景<br>的HQoS，不支持最后一公里的cell模式。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK||配置了CIR的流队列与配置了低时延的流队列，会进行<br>流量抢占，同一个用户队列（user-queue）下，如果有<br>流队列配置了CIR，则该流队列的流量会与同一用户队<br>列下配置了低时延的流队列抢占流量，流量抢占的优先<br>级与流队列本身的优先级一致；同一个流队列不支持同<br>时配置CIR和低时延。建议合理规划业务。|NE40E|NE40E-<br>X16C/<br>NE40E-<br>X8C/<br>NE40E-<br>X8A/<br>NE40E-<br>X3A/<br>NE40E-<br>X16A/<br>NetEngine<br>40E-X8AK|#### 1.8.3 激活 HQoS 端口 License##### 应用环境需要激活 HQoS 端口 License 才可以使用 HQoS 功能。##### 前置任务在配置 HQoS 端口 License 之前，需要完成以下任务：1. 执行命令 **license active** file-name ，激活主用主控板上的 License 文件。2. 执行命令 **active port-basic** **slot** slotid **card** cardid **port** port-list [ portband ] ，批量激活设备端口基本硬件 License 。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **license** ，进入 License 视图。步骤 **3** 执行命令 **active port-hqos** **slot** slotid **card** cardid **port** port-list ，激活端口 HQoS 功能 License 。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 214HUAWEI NetEngine40E配置指南 1 QoS在 License 视图下，执行命令 **undo active port-hqos** **slot** slotid **card** cardid **port**port-list ，可以去激活端口 HQoS 功能 License 。步骤 **4** 执行命令 **commit** ，提交配置。说明激活操作仅支持在物理主接口上执行，若端口的 HQoS License 资源不足，端口进入分配未激活状态。**----**结束##### 检查配置结果端口 HQoS License 激活后，通过检查配置结果查看端口 HQoS License 激活是否成功。在所有视图下，执行 **display license resource usage port-hqos** { **all** | **slot** slotid }[ **active** | **deactive** ] 查看 HQoS 端口 License 的激活情况。#### 1.8.4 配置基于 8 队列普通模式流队列的 HQoS基于 8 队列普通模式流队列的 HQoS 可以保证不同优先级的业务流量按照优先级进行统一调度。##### 背景信息当一个用户拥有多种不同优先级的业务时，需要根据业务优先级进行流量的统一调度。每个用户最多拥有 8 种不同的业务流，也就是 8 种不同优先级的流队列（ FQ ）。它们分别为： BE 、 AF1 、 AF2 、 AF3 、 AF4 、 EF 、 CS6 、 CS7 。优先级为 EF 、 CS6 和 CS7 的流队列对应系统默认为 PQ 调度。优先级为 BE 、 AF1 、 AF2 、 AF3 、 AF4 的流队列系统默认为 WFQ 调度，调度权重为 10 ： 10 ： 10 ： 15 ： 15 。用户也可以根据网络需求来配置流队列的优先级和调度参数。##### 前置任务在配置 8 队列普通模式流队列的 HQoS 之前，需完成以下任务：       - 配置各接口的物理参数、链路属性，保证接口的正常工作。       - 配置接口的 IP 地址。       - 配置设备的 IP 路由，保证链路连通。##### ******* （可选）配置基于 8 队列普通模式的流队列模板用户可以根据网络需求配置基于 8 队列普通模式流队列的调度参数、流量整形和队列缓存资源。##### 背景信息用户可以采用非缺省的流队列（ flow-wred ）模板，并根据网络需求来配置流队列的调度参数，也可在系统中同时创建多个 flow-wred 对象，供流队列引用，并为创建的flow-wred 模板设定高低门限百分比和丢弃概率，当队列的实际长度占流队列的长度百分比小于低门限百分比时，不丢弃报文；当队列的实际长度占流队列的长度百分比在低门限百分比和高门限百分比之间时，开始随机丢弃报文（队列的长度越长，报文被丢弃的概率越高）；当队列的实际长度占流队列的长度百分比大于高门限百分比时，文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 215HUAWEI NetEngine40E配置指南 1 QoS丢弃所有的报文。各队列间共享队列缓存，如果队列缓存耗尽，会存在队列不拥塞但是丢包的问题，因此需要配置队列缓存资源。说明          - 红色报文丢弃优先级的高低门限可以配置得最小，黄色报文丢弃优先级的高低门限可以配置得稍大些，绿色报文丢弃优先级的高低门限可以配置得最大。          - 用户在实际配置时，低门限百分比建议从 50% 开始取值，根据不同颜色的丢弃优先级逐级调整，丢弃概率建议取值为 100% 。          - 如果用户不配置流队列的 WRED 对象，系统采用缺省的尾丢弃策略。##### 操作步骤       - 配置流队列 WRED 对象。a. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **flow-wred** wred-name ，创建流队列 WRED 对象，进入流队列WRED 视图。c. 执行命令 **color** { **green** | **yellow** | **red** } **low-limit** low-limit-percentage**high-limit** high-limit-percentage **discard-percentage** discardpercentage ，设置不同颜色报文的高低门限百分比和丢弃概率。d. 执行命令 **queue-depth** queue-depth-value ，配置队列的深度。e. 执行命令 **commit** ，提交配置。f. 执行命令 **quit** ，返回系统视图。       - 配置流队列的调度参数。a. 执行命令 **flow-queue** flow-queue-name ，进入流队列视图。b. 执行命令 **queue** cos-value { { **pq** | **wfq** **weight** weight-value | **lpq** } |{ **shaping** { shaping-value | **shaping-percentage** shaping-percentagevalue } [ **pbs** pbs-value ] | **car** { car-value | **car-percentage** carpercentage-value } [ **pbs** pbs-value ] } | **flow-wred** wred-name | **low-****latency** | **low-jitter** } * 或 **queue** cos-value **cir** { { cir-value [ **cbs** cbs-value ]**cir-schedule pq** **pir** pir-value } | { **cir-percentage** cir-percentage-value[ **cbs** cbs-value ] **cir-schedule pq** **pir** **pir-percentage** pir-percentagevalue } } [ **pbs** pbs-value ] **pir-schedule** { **pq** | **wfq weight** weight-value |**lpq** } [ **flow-wred** wred-name ] ，修改流队列的调度参数和调度策略。c. 执行命令 **queue** cos-value **random-discard** random-discard-value ，配置流队列的随机丢弃值。d. 执行命令 **commit** ，提交配置。       - 配置联合流量整形。a. 执行命令 **share-shaping** [ shap-id ] { **af1** | **af2** | **af3** | **af4** | **be** | **cs6** | **cs7** |**ef** } [*] [ **pq** | **wfq** **weight** weight-value | **lpq** ] shaping-value [ **pbs** pbsvalue ] ，配置多个流队列的联合流量整形。配置联合流量整形后，联合流量整形中的队列将先做整形，再与其它的用户队列一起做调度。b. 执行命令 **share-shaping** { **be** | **af1** | **af2** | **af3** | **af4** | **ef** | **cs6** | **cs7** } [ *]**random-discard** random-discard-value ，配置流队列的联合流量整形随机丢弃值。c. 执行命令 **commit** ，提交配置。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 216HUAWEI NetEngine40E配置指南 1 QoSd. 执行命令 **quit** ，返回系统视图。       - 使能流队列下所有 PQ 调度队列的低时延功能。a. 执行命令 **qos flow-queue low-latency enable** ，使能流队列下所有 PQ 调度队列的低时延功能，以保证 PQ 调度队列的时延。       - 配置 PQ 调度优先级映射、桶深预备值和全局缓存。a. 执行命令 **qos cos** { **be** | **af1** | **af2** | **af3** | **af4** | **ef** | **cs6** | **cs7** } **burst-size**buffer-size-value ，配置队列的桶深预借值。b. 执行命令 **qos cos all pack-size** pack-size-value ，配置后置 eTM 子卡的报文数据块容量阈值。c. 执行命令 **slot** slot-id ，进入槽位视图。d. 执行命令 **qos pq-scheduler priority** { **high** | **low** } { **inbound** |**outbound** } ，更改单板的 PQ 调度优先级映射，改变原有优先级默认映射关系。e. 执行命令 **qos pq-scheduler priority** { **high** | **low** } **outbound** [ **card**card_id ] ，更改子卡的 PQ 调度优先级映射，改变原有优先级默认映射关系。f. 执行命令 **qos global-buffer** { **share-threshold** share-value | { **be** | **af1** |**af2** | **af3** | **af4** | **ef** | **cs6** | **cs7** } **share** } { **inbound** | **outbound** } ，控制整个单板的队列缓存资源。为了避免队列缓存被部分队列大量占用，需要通过配置限制队列最大可以使用的队列缓存。当通过 flow-wred 来限制时，如果配置的 flow-wred 过小，会导致突发流量丢包的问题，此时可以通过命令 **qos global-buffer** 设置全局队列缓存，在共享的队列缓存资源耗尽前， flow-wred 配置不生效，从而避免流量突发导致的丢包问题。g. 执行命令 **commit** ，提交配置。h. 执行命令 **quit** ，返回系统视图。       - 配置流队列的映射关系。a. 执行命令 **flow-mapping** mapping-name ，进入流队列映射视图。用户可以在一个流队列映射模板中，分别配置 8 个 flow-queue 到 port-queue的映射关系，也可以根据需要，创建多个 flow-mapping 映射模板，供用户队列引用，系统最多可以配置 15 个 flow-mapping 映射模板。b. 执行命令 **map flow-queue** cos-value **to** **port-queue** cos-value ，设置用户队列中某个业务进入端口队列的优先级。如果用户不配置流队列到端口队列的映射关系，系统将采取缺省的一一对应映射关系。c. 执行命令 **commit** ，提交配置。d. 执行命令 **quit** ，返回系统视图。**----**结束##### 1.8.4.2 （可选）配置基于 8 队列普通模式的用户组队列模板配置并应用基于 8 队列普通模式的用户组队列模板可以使设备以均匀的速度向外发送流量。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 217HUAWEI NetEngine40E配置指南 1 QoS##### 背景信息基于 8 队列普通模式的用户组队列模板支持配置队列的整形值、权重值、承诺信息速率（ CIR ）和峰值信息速率（ PIR ），以此用来限制用户组队列流量与突发，且保证了指定的用户组队列能够按配置的比例进行调度。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **user-group-queue** group-name ，进入用户组队列视图。步骤 **3** 执行命令 **mode template** ，使能 GQ （ Group Queue ）按照 QoS 模板的实例共享组共享 QoS 资源。说明GQ 创建完成后只有在 **qos-profile** 中应用时才会共享 QoS 资源。配置该命令后， GQ 在申请 QoS 资源时按照 **qos-profile** 中的实例共享组关键字（ group-name ）共享资源；如果不配置该命令，则所有 GQ 共享 QoS 资源，即当多个 **qos-profile** 中的 **group** 不同时，所有 **group** 共享 QoS 资源。步骤 **4** 执行命令 **shaping** shaping-value [ **pbs** pbs-value ] { **inbound** | **outbound** } ，配置用户组的整形值。步骤 **5** 执行命令 **weight** weight-value **outbound** ，配置用户组队列的权重值。步骤 **6** 执行命令 **cir** cir-value [ **cbs** cbs-value ] [ **pir** pir-value [ **pbs** pbs-value ] ]**outbound** ，配置用户组队列的调度参数。步骤 **7** 执行命令 **commit** ，提交配置。**----**结束##### 1.8.4.3 （可选）配置业务模板在业务模板中配置精度调整长度，对报文长度进行补偿，补偿报文在设备上处理后的差值，从而精确地进行流量控制。##### 背景信息用户报文的长度会因为调度模块不同的处理方式而变化，影响流量整形的准确性。此时用户可以通过配置业务模板中的精度调整长度来补偿报文在设备上处理后的差值，从而提高流量整形的准确性。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **service-template** service-template-name ，进入业务模板视图。步骤 **3** 执行命令 **network-header-length** network-header-length { **inbound** |**outbound** } ，设置业务模板的精度补偿长度。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 218HUAWEI NetEngine40E配置指南 1 QoS说明如需同时指定前置 TM 和后置 TM （ ETM ）的精度补偿值，需要在全局的业务模板中针对前后置TM 分别进行配置，具体操作如下：          - 在业务模板视图下执行命令 **network-header-length** network-header-length **outbound** ，设置业务模板的精度补偿长度，在 QoS 模板中应用该业务模板后，精度补偿值会在出方向的前置 TM 生效。          - 在业务模板视图下执行命令 **network-header-length** network-header-length **outbound** ，设置业务模板的精度补偿长度，在 QoS 模板中应用该业务模板并使用参数 **adjust-on-card** 指定其在后置 TM 生效时，精度补偿值将会在出方向的后置 TM 生效。          - 在业务模板视图下执行命令 **network-header-length** network-header-length **outbound****adjust-on-etm** ，设置业务模板的精度补偿长度，并指定其在后置 TM 生效。在 QoS 模板中应用该业务模板后，精度补偿值会在出方向的后置 TM 生效。步骤 **4** 执行命令 **commit** ，提交配置。**----**结束##### 1.8.4.4 配置基于 QoS 模板的用户队列QoS 模板是 QoS 调度参数的集合，可用来配置用户队列的调度参数和带宽值，也可以用来绑定流队列模板。##### 背景信息当通过多个接口接入的流量需要作为一个用户进行统一调度时，需要实现用户级别的流量管理。基于接口配置的 HQoS 只支持每个接口的流量进入一个 SQ 队列进行调度，不支持对多个接口的流量进行统一调度时。基于模板的 HQoS 支持多个接口的流量进入同一个 SQ 队列进行调度，通过定义 QoS 调度模板，并应用到不同的接口上实现了多个接口流量的统一调度。说明用户队列的调度参数可选择在非基于时间的 QoS 模板下或基于时间的 QoS 模板下配置。基于时间的 QoS 模板下支持配置 2 条带 time-range 的 user-queue 。          - qos-profile time-range 与 qos-profile 下配置的模板名 qos-profile-name 不能相同。          - 如果配置了 1 条带 time-range 的 user-queue ，建议同时配置 1 条不带 time-range 的 userqueue 。在 time-range 时间内，按照带 time-range 的 user-queue 生效；非 time-range 时间内，按照不带 time-range 的 user-queue 的生效。          - 如果配置了 2 条带 time-range 的 user-queue ，建议 time-range 时间范围叠加在一起能够覆盖24 小时。如果时间不能覆盖 24 小时，没有覆盖的时间内 user-queue 会失效。##### 操作步骤       - 配置 QoS 模板中用户队列的调度参数。a. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **qos-profile** profile-name ，定义 QoS 模板并进入 QoS 模板视图。c. （可选）执行命令 **description** description-info ，配置 QoS 模板的描述信息。d. 执行命令 **user-queue** { { **cir** cir-value [ **cbs** cbs-value ] [ [ **pir** pir-value[ **pbs** pbs-value ] [ **pir-priority high** ] ] | [ **flow-queue** flow-queuename ] | [ **queue-4cos-mapping** queue-mapping-name ] | [ **flow-****mapping** mapping-name ] | [ **user-group-queue** group-name ] ] *文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 219HUAWEI NetEngine40E配置指南 1 QoS[ **inbound** | **outbound** ] [ **service-template** template-name [ **adjust-on-****card** ] ] } | { **cir-percentage** cir-percentage-value [ **cbs** cbs-value ] [ [ **pir-****percentage** pir-percentage-value [ **pbs** pbs-value ] [ **pir-priority high** ] ]| [ **flow-queue** flow-queue-name ] | [ **queue-4cos-mapping** queuemapping-name ] | [ **flow-mapping** mapping-name ] | [ **user-group-****queue** group-name ] ] * [ **inbound** | **outbound** ] [ **service-template**template-name [ **adjust-on-card** ] ] } } ，配置用户队列的调度参数。说明对于 LPUI-21-L/LPUI-51-L/LPUF-51/LPUF-51-B/LPUI-51/LPUI-51-B/LPUI-51-S/LPUS-51/LPUF-101/LPUF-101-B/LPUI-101/LPUI-101-B/LPUS-101/LPUF-51-E/LPUI-51-E/LPUF-120/LPUF-120-B/LPUF-120-E/LPUI-102-E/LPUI-120/LPUI-120-B/LPUI-120-L/LPUI-52-E/LPUI-120-E/LPUF-240/LPUF-240-B/LPUF-240-E/LPUI-240/LPUI-240-B/LPUI-240-L 单板，用户可以在系统视图下配置 **qos user-queue pir-****exhaust alarm enable** 命令，开启 PIR 资源超限告警功能。当同一 TM 模块下所有接口上配置的 pir 之和超过阈值时设备会打印告警提示用户进行带宽调整，避免带宽不足影响正常业务运行。e. 执行命令 **weight** weight-value [ **inbound** | **outbound** ] ，配置用户队列权重值。f. 执行命令 **commit** ，提交配置。g. 执行命令 **quit** ，返回系统视图。h. 执行命令 **qos-profile** qos-profile-name **time-range** ，定义基于时间的 QoS模板并进入 qos-profile time-range 视图。i. 执行命令 **user-queue** **cir** cir-value [ **cbs** cbs-value ] [ [ **pir** pir-value [ **pbs**pbs-value ] [ **pir-priority** **high** ] ] | [ **flow-queue** flow-queue-name ] |[ flow-mapping <mapping-name> ] | [ **user-group-queue** user-groupname ] ] [ **inbound** | **outbound** ] [ **service-template** template-name[ **adjust-on-card** ] ] [ **time-range** time-range-name ] ，配置用户队列的调度参数。j. 执行命令 **commit** ，提交配置。k. 执行命令 **quit** ，返回系统视图。       - （可选）配置通道模板中的带宽粒度和带宽数量。a. 执行命令 **qos channel-profile** channel-profile-name ，创建通道模板，并进入通道模板视图。b. 执行命令 **channel** channel-id **bandwidth** bandwidth-value **quantity**quantity-value ，配置每一个指定编号的通道的带宽粒度和带宽数量。用户队列的配置带宽在设备上是通过一系列的离散带宽组合而来，每种离散带宽的带宽值和带宽数量可以通过手工指定，以达到更合理的匹配现网业务配置的目的。若默认配置的通道带宽太小，则不能满足低速卡的 PPP 和 MP 业务的 QoS 的资源规格。因此需要首先执行命令 **display qos channel-profile****slot** slot-id [ **verbose** ] ， 查看当前资源是否能满足规格要求，若不满足，则需要进行静态通道带宽配置。c. 执行命令 **commit** ，提交配置。d. 执行命令 **quit** ，返回系统视图。**----**结束##### 1.8.4.5 应用 QoS 模板用户可以通过定义 QoS 模板并应用到不同接口上，来实现多个接口流量的统一调度。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 220HUAWEI NetEngine40E配置指南 1 QoS##### 背景信息当通过多个接口接入的流量需要作为一个用户进行统一调度时，要实现用户级别的流量管理。基于接口的 HQoS 只支持每个接口的流量进入一个 SQ 队列进行调度，不支持对多个接口的流量进行统一调度。基于模板的 HQoS 支持多个接口的流量进入同一个SQ 队列进行调度，通过定义 QoS 调度模板，并把它应用到不同的接口上来实现多个接口流量的统一调度。##### 操作步骤       - 应用 QoS 模板。a. 执行命令 **system-view** ，进入系统视图。b. （可选）执行命令 **qos tm-schedule traffic-statistics adjustment-packet****slot** slot-id **card** card-id ，使能按照精度补偿后的报文长度进行报文统计的功能。说明只支持对逐包补偿的场景进行控制，无法支持分片补偿场景。c. 执行命令 **interface** interface-type interface-number ，进入接口视图。d. 请根据接口类型选择不同的命令行应用 QoS 模板。#### ▪ 执行命令 qos-profile qos-profile-name { inbound | outbound }[ **identifier** **none** ] [ **group** group-name ] ，在 IP-Trunk 接口、 Dot1Q接口、端口扩展接口上应用 QoS 模板。#### ▪ 执行命令 qos-profile qos-profile-name { inbound | outbound } vlanvlan-id-begin [ **to** vlan-id-end ] [ **identifier** { **vlan-id** | **none** } ][ **group** group-name ] ，在二层接口或者 Dot1Q 终结子接口上应用 QoS模板。#### ▪ 执行命令 qos-profile qos-profile-name { inbound | outbound } pe**vid** pe-vlan-id { **ce-vid** ce-vlan-id-begin [ **to** ce-vlan-id-end ] }[ **identifier** { **pe-vid** | **ce-vid** | **pe-ce-vid** | **none** } ] [ **group** groupname ] ，在 QinQ 终结子接口上应用 QoS 模板。#### ▪ 执行命令 qos-profile qos-profile-name { inbound | outbound }[ **identifier** { **none** | **vid** | **ce-vid** | **vid-ce-vid** } ] [ **group** groupname ] ，在 EVC 二层子接口上应用 QoS 模板。#### ▪ 执行命令 qos-profile qos-profile-name { inbound | outbound } vnivni-id **source** sourceip **peer** peerip ，在 NVE 接口上应用 QoS 模板。e. （可选）执行命令 **user-queue shaping bgp-local-ifnet-traffic****outbound** ，使能接口对 BGP Local Ifnet 隧道承载流量的 HQoS 功能。在 BGP Local Ifnet 隧道沿途的接口配置 HQoS 后，当出接口为下行非后置 TM场景时，如果要使 HQoS 生效，需要执行此步骤。f. （可选）执行命令 **qos user-queue member-link-scheduler distribute**[ **inbound** | **outbound** ] ，配置在 qos-profile 的 user-queue 应用到 Trunk 口时，使 Trunk 成员口带宽按照权重分配。g. 执行命令 **commit** ，提交配置。h. 执行命令 **quit** ，退回到系统视图。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 221HUAWEI NetEngine40E配置指南 1 QoS       - （可选）应用通道模板。a. 执行命令 **slot** slot-id ，进入槽位视图。b. 执行命令 **qos channel-profile** channel-profile-name ，将配置参数后的通道模板应用到单板上。说明配置此命令后需要重启单板功能才能生效。c. 执行命令 **commit** ，提交配置。d. 执行命令 **quit** ，退回到系统视图。e. 执行命令 **quit** ，退回到用户视图。f. 执行命令 **reset** **slot** slot-id ，重启单板。**----**结束##### 1.8.4.6 检查配置结果基于模板的 HQoS 配置成功后，可以查看接口上队列模板的配置情况和统计信息等内容。##### 背景信息完成上述配置后，请执行下面的命令检查配置结果。##### 操作步骤       - 使用 **display flow-mapping configuration** [ **verbose** [ mapping-name ] ] 命令，查看流队列映射对象的配置参数和该对象的引用关系。       - 使用 **display flow-queue configuration** [ **verbose** [ flow-queue-name ] ] 命令，查看流队列模板配置信息。       - 使用 **display flow-wred configuration** [ **verbose** [ flow-wred-name ] ] 命令，查看流队列 WRED 对象的配置参数。       - 使用 **display user-group-queue configuration** [ **verbose** [ group-name ] ] 命令，查看用户组队列的配置信息以及引用关系。       - 使用 **display user-group-queue statistics** 命令，查看用户组队列的统计计数信息。       - 使用 **display qos-profile application** 命令，查看 QoS 模板的应用信息。       - 使用 **display qos-profile statistics** 命令，查看 QoS 模板的统计信息。       - 使用 **monitor qos-profile statistics** 命令，监控 QoS 模板的统计信息。       - 使用 **display qos information user-id** user-id { **inbound** | **outbound** } 命令，查看指定用户的 CAR 和队列流量监管方式下的统计数据。**----**结束#### 1.8.5 配置基于 8 队列优先级模式流队列的 HQoS基于优先级模式流队列的 HQoS 支持用户根据网络需求配置流队列的优先级映射、流量整形及调度权重。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 222HUAWEI NetEngine40E配置指南 1 QoS##### 背景信息当一个用户拥有多种不同优先级的业务时，需要根据业务优先级进行流量的统一调度。每个用户最多拥有 8 种不同的业务流，也就是 8 种不同优先级的流队列（ FQ ）。它们分别为： BE 、 AF1 、 AF2 、 AF3 、 AF4 、 EF 、 CS6 、 CS7 。表 **1-22** 流队列模板的默认配置##### 前置任务|流队列|priority值|weight||---|---|---||BE|3|10||AF1|2|10||AF2|2|10||AF3|1|10||AF4|1|10||CS6|0|10||CS7|0|10||EF|0|10|在配置基于优先级模式流队列的 HQoS 之前，需完成以下任务：       - 配置各接口的物理参数、链路属性，保证接口的正常工作       - 配置接口的 IP 地址       - 配置设备的 IP 路由，保证链路连通##### ******* 配置基于 8 队列优先级模式的流队列模板用户可以根据网络需求配置流队列的优先级、调度参数和流量整形。##### 背景信息基于优先级模式流队列的 HQoS 是指在优先级流队列模板中再次赋予 8 个队列不同的优先级（ priority 值），优先级取值从高到低依次为 0 ， 1 ， 2 ， 3 。根据优先级取值来配置不同优先级队列的流量整形、整形速率和调度权重。说明缺省情况下，各队列的优先级是默认 priority 值。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** （可选）执行命令 **flow-wred** wred-name ，创建流队列 WRED 对象，进入 flow-wred视图。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 223HUAWEI NetEngine40E配置指南 1 QoS步骤 **3** （可选）执行命令 **color** { **green** | **yellow** | **red** } **low-limit** low-limit-percentage**high-limit** high-limit-percentage **discard-percentage** discard-percentage ，设置不同颜色的报文的高低门限百分比和丢弃概率。步骤 **4** （可选）执行命令 **queue-depth** queue-depth-value ，配置队列的深度。步骤 **5** 执行命令 **commit** ，提交配置。步骤 **6** 执行命令 **quit** ，返回系统视图。步骤 **7** 执行命令 **flow-queue** flow-queue-name **priority-mode** ，进入优先级模式流队列视图。步骤 **8** 执行命令 **priority** priority-value { **pq** | **wfq** } ，配置相同调度器上用户的优先级流队列采用 PQ 调度方式或者 WFQ 调度方式。步骤 **9** 执行命令 **queue** cos-value { **priority** priority-value [ **weight** weight-value ] |{ **shaping** { shaping-value | **shaping-percentage** shaping-percentage-value } } |**flow-wred** wred-name } * ，修改流队列的队列优先级、权重值和整形值，并绑定flow-wred 模板。步骤 **10** 执行命令 **share-shaping** [ shap-id ] { **af1** | **af2** | **af3** | **af4** | **be** | **cs6** | **cs7** | **ef** } *[ **pq** | **wfq weight** weight-value | **lpq** ] shaping-value [ **pbs** pbs-value ] ，配置多个流队列的联合流量整形。配置联合流量整形后，联合流量整形中的队列将先做整形，再与其它的用户队列一起做调度。在优先级模式流队列视图下，若联合流量整形不指定调度方式，则联合联合流量整形与子调度器的调度方式保持一致。**----**结束##### 1.8.5.2 配置基于 8 队列优先级模式的用户组队列模板优先级模式用户组队列支持用户配置某个优先级的用户组队列的整形参数和令牌桶深度。##### 背景信息GQ 用户组业务场景中，当需要对整个 GQ 限速和需要针对各业务的某个优先级进行整形配置时，可以配置优先级模式用户组队列的整形值。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **user-group-queue** group-name **priority-mode** ，进入优先级模式用户组队列视图。步骤 **3** 执行命令 **priority** priority-value **shaping** shaping-value [ **pbs** pbs-value ]**outbound** ，在优先级模式用户组队列视图下配置用户组队列的调度整形参数和令牌桶深度。步骤 **4** 执行命令 **commit** ，提交配置。**----**结束文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 224HUAWEI NetEngine40E配置指南 1 QoS##### 1.8.5.3 （可选）配置业务模板在业务模板中配置精度调整长度，对报文长度进行补偿，补偿报文在设备上处理后的差值，从而精确地进行流量控制。##### 背景信息用户报文的长度会因为调度模块不同的处理方式而变化，影响流量整形的准确性。此时用户可以通过配置业务模板中的精度调整长度来补偿报文在设备上处理后的差值，从而提高流量整形的准确性。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **service-template** service-template-name ，进入业务模板视图。步骤 **3** 执行命令 **network-header-length** network-header-length { **inbound** |**outbound** } ，设置业务模板的精度补偿长度。说明如需同时指定前置 TM 和后置 TM （ ETM ）的精度补偿值，需要在全局的业务模板中针对前后置TM 分别进行配置，具体操作如下：          - 在业务模板视图下执行命令 **network-header-length** network-header-length **outbound** ，设置业务模板的精度补偿长度，在 QoS 模板中应用该业务模板后，精度补偿值会在出方向的前置 TM 生效。          - 在业务模板视图下执行命令 **network-header-length** network-header-length **outbound** ，设置业务模板的精度补偿长度，在 QoS 模板中应用该业务模板并使用参数 **adjust-on-card** 指定其在后置 TM 生效时，精度补偿值将会在出方向的后置 TM 生效。          - 在业务模板视图下执行命令 **network-header-length** network-header-length **outbound****adjust-on-etm** ，设置业务模板的精度补偿长度，并指定其在后置 TM 生效。在 QoS 模板中应用该业务模板后，精度补偿值会在出方向的后置 TM 生效。步骤 **4** 执行命令 **commit** ，提交配置。**----**结束##### 1.8.5.4 配置基于 QoS 模板的用户队列QoS 模板是 QoS 调度参数的集合，可用来配置用户队列的调度参数和带宽值，也可以用来绑定流队列模板。##### 背景信息当通过多个接口接入的流量需要作为一个用户进行统一调度时，需要实现用户级别的流量管理。基于接口配置的 HQoS 只支持每个接口的流量进入一个 SQ 队列进行调度，不支持对多个接口的流量进行统一调度时。基于模板的 HQoS 支持多个接口的流量进入同一个 SQ 队列进行调度，通过定义 QoS 调度模板，并应用到不同的接口上实现了多个接口流量的统一调度。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 225HUAWEI NetEngine40E配置指南 1 QoS说明用户队列的调度参数可选择在非基于时间的 QoS 模板下或基于时间的 QoS 模板下配置。基于时间的 QoS 模板下支持配置 2 条带 time-range 的 user-queue 。          - qos-profile time-range 与 qos-profile 下配置的模板名 qos-profile-name 不能相同。          - 如果配置了 1 条带 time-range 的 user-queue ，建议同时配置 1 条不带 time-range 的 userqueue 。在 time-range 时间内，按照带 time-range 的 user-queue 生效；非 time-range 时间内，按照不带 time-range 的 user-queue 的生效。          - 如果配置了 2 条带 time-range 的 user-queue ，建议 time-range 时间范围叠加在一起能够覆盖24 小时。如果时间不能覆盖 24 小时，没有覆盖的时间内 user-queue 会失效。##### 操作步骤       - 配置 QoS 模板中用户队列的调度参数。a. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **qos-profile** profile-name ，定义 QoS 模板并进入 QoS 模板视图。c. （可选）执行命令 **description** description-info ，配置 QoS 模板的描述信息。d. 执行命令 **user-queue** { { **cir** cir-value [ **cbs** cbs-value ] [ [ **pir** pir-value[ **pbs** pbs-value ] [ **pir-priority high** ] ] | [ **flow-queue** flow-queuename ] | [ **queue-4cos-mapping** queue-mapping-name ] | [ **flow-****mapping** mapping-name ] | [ **user-group-queue** group-name ] ] *[ **inbound** | **outbound** ] [ **service-template** template-name [ **adjust-on-****card** ] ] } | { **cir-percentage** cir-percentage-value [ **cbs** cbs-value ] [ [ **pir-****percentage** pir-percentage-value [ **pbs** pbs-value ] [ **pir-priority high** ] ]| [ **flow-queue** flow-queue-name ] | [ **queue-4cos-mapping** queuemapping-name ] | [ **flow-mapping** mapping-name ] | [ **user-group-****queue** group-name ] ] * [ **inbound** | **outbound** ] [ **service-template**template-name [ **adjust-on-card** ] ] } } ，配置用户队列的调度参数。说明对于 LPUI-21-L/LPUI-51-L/LPUF-51/LPUF-51-B/LPUI-51/LPUI-51-B/LPUI-51-S/LPUS-51/LPUF-101/LPUF-101-B/LPUI-101/LPUI-101-B/LPUS-101/LPUF-51-E/LPUI-51-E/LPUF-120/LPUF-120-B/LPUF-120-E/LPUI-102-E/LPUI-120/LPUI-120-B/LPUI-120-L/LPUI-52-E/LPUI-120-E/LPUF-240/LPUF-240-B/LPUF-240-E/LPUI-240/LPUI-240-B/LPUI-240-L 单板，用户可以在系统视图下配置 **qos user-queue pir-****exhaust alarm enable** 命令，开启 PIR 资源超限告警功能。当同一 TM 模块下所有接口上配置的 pir 之和超过阈值时设备会打印告警提示用户进行带宽调整，避免带宽不足影响正常业务运行。e. 执行命令 **weight** weight-value [ **inbound** | **outbound** ] ，配置用户队列权重值。f. 执行命令 **commit** ，提交配置。g. 执行命令 **quit** ，返回系统视图。h. 执行命令 **qos-profile** qos-profile-name **time-range** ，定义基于时间的 QoS模板并进入 qos-profile time-range 视图。i. 执行命令 **user-queue** **cir** cir-value [ **cbs** cbs-value ] [ [ **pir** pir-value [ **pbs**pbs-value ] [ **pir-priority** **high** ] ] | [ **flow-queue** flow-queue-name ] |[ flow-mapping <mapping-name> ] | [ **user-group-queue** user-groupname ] ] [ **inbound** | **outbound** ] [ **service-template** template-name[ **adjust-on-card** ] ] [ **time-range** time-range-name ] ，配置用户队列的调度参数。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 226HUAWEI NetEngine40E配置指南 1 QoSj. 执行命令 **commit** ，提交配置。k. 执行命令 **quit** ，返回系统视图。       - （可选）配置通道模板中的带宽粒度和带宽数量。a. 执行命令 **qos channel-profile** channel-profile-name ，创建通道模板，并进入通道模板视图。b. 执行命令 **channel** channel-id **bandwidth** bandwidth-value **quantity**quantity-value ，配置每一个指定编号的通道的带宽粒度和带宽数量。用户队列的配置带宽在设备上是通过一系列的离散带宽组合而来，每种离散带宽的带宽值和带宽数量可以通过手工指定，以达到更合理的匹配现网业务配置的目的。若默认配置的通道带宽太小，则不能满足低速卡的 PPP 和 MP 业务的 QoS 的资源规格。因此需要首先执行命令 **display qos channel-profile****slot** slot-id [ **verbose** ] ， 查看当前资源是否能满足规格要求，若不满足，则需要进行静态通道带宽配置。c. 执行命令 **commit** ，提交配置。d. 执行命令 **quit** ，返回系统视图。**----**结束##### 1.8.5.5 应用 QoS 模板用户可以通过定义 QoS 模板并应用到不同接口上，来实现多个接口流量的统一调度。##### 背景信息当通过多个接口接入的流量需要作为一个用户进行统一调度时，要实现用户级别的流量管理。基于接口的 HQoS 只支持每个接口的流量进入一个 SQ 队列进行调度，不支持对多个接口的流量进行统一调度。基于模板的 HQoS 支持多个接口的流量进入同一个SQ 队列进行调度，通过定义 QoS 调度模板，并把它应用到不同的接口上来实现多个接口流量的统一调度。##### 操作步骤       - 应用 QoS 模板。a. 执行命令 **system-view** ，进入系统视图。b. （可选）执行命令 **qos tm-schedule traffic-statistics adjustment-packet****slot** slot-id **card** card-id ，使能按照精度补偿后的报文长度进行报文统计的功能。说明只支持对逐包补偿的场景进行控制，无法支持分片补偿场景。c. 执行命令 **interface** interface-type interface-number ，进入接口视图。d. 请根据接口类型选择不同的命令行应用 QoS 模板。#### ▪ 执行命令 qos-profile qos-profile-name { inbound | outbound }[ **identifier** **none** ] [ **group** group-name ] ，在 IP-Trunk 接口、 Dot1Q接口、端口扩展接口上应用 QoS 模板。#### ▪ 执行命令 qos-profile qos-profile-name { inbound | outbound } vlanvlan-id-begin [ **to** vlan-id-end ] [ **identifier** { **vlan-id** | **none** } ][ **group** group-name ] ，在二层接口或者 Dot1Q 终结子接口上应用 QoS模板。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 227HUAWEI NetEngine40E配置指南 1 QoS#### ▪ 执行命令 qos-profile qos-profile-name { inbound | outbound } pe**vid** pe-vlan-id { **ce-vid** ce-vlan-id-begin [ **to** ce-vlan-id-end ] }[ **identifier** { **pe-vid** | **ce-vid** | **pe-ce-vid** | **none** } ] [ **group** groupname ] ，在 QinQ 终结子接口上应用 QoS 模板。#### ▪ 执行命令 qos-profile qos-profile-name { inbound | outbound }[ **identifier** { **none** | **vid** | **ce-vid** | **vid-ce-vid** } ] [ **group** groupname ] ，在 EVC 二层子接口上应用 QoS 模板。#### ▪ 执行命令 qos-profile qos-profile-name { inbound | outbound } vnivni-id **source** sourceip **peer** peerip ，在 NVE 接口上应用 QoS 模板。e. （可选）执行命令 **user-queue shaping bgp-local-ifnet-traffic****outbound** ，使能接口对 BGP Local Ifnet 隧道承载流量的 HQoS 功能。在 BGP Local Ifnet 隧道沿途的接口配置 HQoS 后，当出接口为下行非后置 TM场景时，如果要使 HQoS 生效，需要执行此步骤。f. （可选）执行命令 **qos user-queue member-link-scheduler distribute**[ **inbound** | **outbound** ] ，配置在 qos-profile 的 user-queue 应用到 Trunk 口时，使 Trunk 成员口带宽按照权重分配。g. 执行命令 **commit** ，提交配置。h. 执行命令 **quit** ，退回到系统视图。       - （可选）应用通道模板。a. 执行命令 **slot** slot-id ，进入槽位视图。b. 执行命令 **qos channel-profile** channel-profile-name ，将配置参数后的通道模板应用到单板上。说明配置此命令后需要重启单板功能才能生效。c. 执行命令 **commit** ，提交配置。d. 执行命令 **quit** ，退回到系统视图。e. 执行命令 **quit** ，退回到用户视图。f. 执行命令 **reset** **slot** slot-id ，重启单板。**----**结束##### 1.8.5.6 检查配置结果基于优先级模式的 HQoS 配置成功后，可以查看接口上队列配置情况和统计信息等内容。##### 背景信息完成上述配置后，请执行下面的命令检查配置结果。##### 操作步骤       - 使用 **display flow-mapping configuration** [ **verbose** [ mapping-name ] ] 命令，查看流队列映射对象的配置参数和该对象的引用关系。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 228HUAWEI NetEngine40E配置指南 1 QoS       - 使用 **display user-group-queue statistics** [ **name** ] user-group-name**statistics** [ **group** group-name ] [ **slot** slot-id ] { **outbound** | **inbound** } 命令，查看用户组队列的统计计数信息。       - 使用 **display qos-profile configuration** [ qos-profile-name ] 命令，查看 QoS 模板的配置。       - 使用 **display qos-profile statistics** { **vni** vni-id | **interface** { interface-name |interface-type interface-number } [ **vlan** vlan-id | **pe-vid** pe-vid **ce-vid** ce-vid |**vid** vid | **ce-vid** ce-vid | **vid** vid **ce-vid** ce-vid ] } { **inbound** | **outbound** } 命令，查看 QoS 模板的统计信息。       - 使用 **display user-group-queue statistics interface** { interface-name |interface-type interface-number } [ **vlan** vlan-id | **pe-vid** pe-vid **ce-vid** ce-vid ]{ **inbound** | **outbound** } 命令，查看用户组队列的统计信息。       - 使用 **display qos scheduling-mode** **slot** slot-id 命令，查询指定单板当前使用的带宽调度模式。       - 使用 **display qos bandwidth-adjustment information** **slot** slot-id 命令，查询指定单板的资源使用情况及带宽调整情况。VS 模式下，该命令仅在 Admin VS 支持。       - 使用 **display qos information user-id** user-id { **inbound** | **outbound** } 命令，查看指定用户的 CAR 和队列流量监管方式下的统计数据。       - 使用 **display qos-profile statistics vpn-instance** vpnname { **inbound** |**outbound** } [ **verbose** ] 命令，查看 VPN 实例上所有成员口的 QoS 模板统计信息。       - 使用 **display qos-profile statistics vsi** vsi-name { **inbound** | **outbound** }[ **verbose** ] 命令，查看指定 VSI 域的 QoS 流量统计信息。       - 使用 **display qos-profile statistics bridge-domain** bd-id { **inbound** |**outbound** } [ **verbose** ] 命令，查看指定 BD 的 QoS 流量统计信息。**----**结束#### 1.8.6 配置基于 8 队列增强模式流队列的 HQoS基于 8 队列增强模式的 HQoS 可以保证不同优先级业务的 CIR 和 PIR 流量按照优先级进行调度。##### 背景信息HQoS 层次化调度模型划分为：流队列（ FQ ）、用户队列（ SQ ）、用户组队列（ GQ ）三个调度层次。可以精细到区分不同用户和不同业务的流量，提供区分的带宽管理。一个 FQ 代表一种优先级的数据流，每个用户（ SQ ）固定对应 8 种 FQ 业务优先级。 8 队列增强模式流队列在 8 队列的基础上增加了对 CIR 和 PIR 流量进行区分、标记、根据标记再入队列等功能，优先保证整体业务的 CIR 带宽。 8 队列增强模式流队列采用 COS 加取值（取值范围为 0~7 ）来表示 8 个队列的服务等级，取值越大，服务等级越高，队列优先级也就越高。##### 前置任务在配置基于 8 队列增强模式的 HQoS 之前，需完成以下任务：       - 配置各接口的物理参数、链路属性，保证接口的正常工作。       - 配置接口的 IP 地址。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 229HUAWEI NetEngine40E配置指南 1 QoS       - 配置路由器的 IP 路由，保证链路连通。##### ******* 配置基于 8 队列增强模式的流队列模板8 队列增强模式流队列模板用来配置流队列的调度参数，从而为不同优先级的业务流量提供差异化的 QoS 保障。##### 背景信息此步骤用来配置流队列的承诺信息速率（ CIR ）、峰值信息速率（ PIR ）和调度权重，并使能 remark 功能，通过标记不同颜色来区分 CIR 流量与 PIR 流量，从而确保在后续过程中 CIR 业务被优先处理。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** （可选）配置流队列 WRED 对象。1. 执行命令 **flow-wred** wred-name ，创建流队列 WRED 对象，并进入流队列 WRED视图。2. 执行命令 **color** { **green** | **yellow** | **red** } **low-limit** low-limit-percentage **high-****limit** high-limit-percentage **discard-percentage** discard-percentage ，按颜色配置 WRED 的高低门限百分比与丢弃概率。3. 执行命令 **queue-depth** { queue-depth-value | **buffer-time** queue-depthtime } ，配置队列的深度，通过调整队列的深度可以减少报文时延。4. 执行命令 **quit** ，退回系统视图。步骤 **3** 执行命令 **flow-queue** flow-queue-name [ **8cos-enhance-mode** ] ，创建 8 队列增强模式的流队列，并进入 8 队列增强流队列模板视图。步骤 **4** 执行命令 **cos** cos-value ，进入队列优先级子视图。说明队列优先级子视图仅在如下形态支持：步骤 **5** 在 8 队列增强模式流队列模板中配置调度参数，实现对指定优先级流队列业务的带宽限制。可按照承诺突发尺寸和峰值突发尺寸的不同配置方式选择如下一种进行配置：       - 直接指定承诺突发尺寸值和峰值突发尺寸值的方式，请执行命令 **pir** { { pir-value[ **pbs** pbs-value ] [ **cir** cir-value [ **cbs** cbs-value ] ] } | { **percentage** pir-percent[ **pbs** pbs-value ] [ **cir-percentage** cir-percent [ **cbs** cbs-value ] ] } }       - 通过指定承诺突发时间和峰值突发时间计算承诺突发尺寸值和峰值突发尺寸值的方式，请执行命令 **pir** { { <pir-value> [ **pbs burst-time** <pbs-time> ] [ **cir** <cirvalue> [ **cbs burst-time** <cbs-time> ] ] } | { **percentage** <pir-percent> [ **pbs****burst-time** <pbs-time> ] [ **cir percentage** <cir-percent> [ **cbs burst-time**<cbs-time> ] ] } }步骤 **6** 执行命令 **cir-parent-priority** cir-priority-value [ **weight** cir-weight-value ] **pir-****parent-priority** pir-priority-value [ **weight** pir-weight-value ] ，配置指定服务等级流队列的调度优先级及调度权重。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 230HUAWEI NetEngine40E配置指南 1 QoS说明调度权重只在用户队列的调度模式为 WFQ 时生效。步骤 **7** 执行命令 **remark-color enable** ，使能 remark 功能，对 PIR 和 CIR 流量进行区分和标记。说明该功能只对上行方向的流量生效。步骤 **8** （可选）执行命令 **flow-wred** wred-name ，绑定丢弃策略模板。步骤 **9** （可选）执行命令 **car-mode enable**, 使能流队列的 CAR 模式。CAR 模式下，不对用户报文做缓存处理，可以达到降低时延的效果。说明仅 cos5 、 cos6 和 cos7 优先级的流队列支持使能 CAR 模式。步骤 **10** 执行命令 **commit** ，提交配置。步骤 **11** 执行命令 **quit** ，退回系统视图。**----**结束##### 1.8.6.2 配置基于 8 队列增强模式的用户队列模板用户可以通过用户队列模板来配置用户队列的调度模式、调度优先级、调度权重、整形等参数。##### 背景信息用户队列模板可以被不同的 QoS 模板引用，以提高 HQoS 的配置效率，实现不同用户队列调度器对业务流量进行差异化 QoS 处理。配置用户队列模板参数前，必须先创建用户队列模板。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** （可选）执行命令 **service-template** service-template-name ，创建业务模板，并进入业务模板视图。步骤 **3** （可选）执行命令 **network-header-length** network-header-length { **inbound** |**outbound** } ，设置业务模板的精度调整长度。步骤 **4** 执行命令 **quit** ，退回系统视图。步骤 **5** 执行命令 **user-queue** user-queue-name, 创建用户队列模板并进入用户队列模板视图。步骤 **6** 配置用户队列模板下用户队列的调度参数，实现对不同用户队列业务的带宽限制。可按照承诺突发尺寸和峰值突发尺寸的不同配置方式选择如下一种进行配置：       - 直接指定承诺突发尺寸值和峰值突发尺寸值的方式，请执行命令 **pir** { { pir-value[ **pbs** pbs-value ] [ **cir** cir-value [ **cbs** cbs-value ] ] } | { **percentage** pir-percent[ **pbs** pbs-value ] [ **cir-percentage** cir-percent [ **cbs** cbs-value ] ] } }文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 231HUAWEI NetEngine40E配置指南 1 QoS       - 通过指定承诺突发时间和峰值突发时间计算承诺突发尺寸值和峰值突发尺寸值的方式，请执行命令 **pir** { { <pir-value> [ **pbs burst-time** <pbs-time> ] [ **cir** <cirvalue> [ **cbs burst-time** <cbs-time> ] ] } | { **percentage** <pir-percent> [ **pbs****burst-time** <pbs-time> ] [ **cir percentage** <cir-percent> [ **cbs burst-time**<cbs-time> ] ] } }步骤 **7** （可选）执行命令 **priority** priority **scheduler-mode** { **pq** | **wfq** } ，配置指定优先级用户队列调度器的调度模式，实现不同用户队列调度器对经过流量的差异化处理。步骤 **8** （可选）执行命令 **priority** priority **cir-parent-priority** cir-priority-value **pir-parent-****priority** pir-priority-value ，配置指定优先级用户队列的调度器的调度优先级。步骤 **9** （可选）执行命令 **parent-priority** priority **weight** weight ，配置用户队列模板下指定优先级用户队列父调度器的调度权重。在流量拥塞时，根据用户队列之间的权重值比例关系，保证指定用户的业务按配置比例进行调度。步骤 **10** （可选）执行命令 **priority** priority **pir** pir-value [ **pbs** pbs-value ] ，配置用户队列模板下指定优先级用户队列调度器的限速值。步骤 **11** （可选）执行命令 **service-template** service-template-name ，关联业务模板，对报文长度进行精度补偿，使报文的流量整形更准确。步骤 **12** 执行命令 **commit** ，提交配置。**----**结束##### 1.8.6.3 （可选）配置基于 8 队列增强模式的用户组队列模板用户可通过设置用户组队列的整形值、权重值来限制用户组队列流量与突发，以均匀的速度向外发送流量。##### 背景信息层次化 QoS 调度，采用多级调度的方式，可以精细区分不同用户和不同业务的流量，提供区分的带宽管理。当出现上游路由器流量大于下行设备处理能力，致使缺省的流量带宽不足，造成网络拥塞时，可以通过配置用户组队列的调度参数和队列权重来实现对不同用户组队列的业务流量进行差异化 QoS 处理。确保优先级较高的用户组队列业务可享有足够的带宽。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **user-group-queue** group-name ，进入用户组队列视图。步骤 **3** 执行命令 **shaping** shaping-value [ **pbs** pbs-value ] { **inbound** | **outbound** } ，配置GQ （ Group Queue ）调度参数。说明该命令中的 shaping 值不能小于引用该 GQ 的所有 SQ 的 CIR 之和，否则 CIR 值无法保证。 CIR 值可通过 **user-queue** 命令进行配置。如果配置的 PBS 值比 MTU （最大传输元）小，或者比实际报文的包长小，会出现丢包现象。MTU （ Maximum Transfer Unit ）定义了一个接口在无需分片的情况下能够发送的最大长度的报文。如果 IP 报文的长度大于 MTU 的值，那么它会在发送出接口前被分片。用户可以通过 **mtu**mtu 命令来配置接口的最大传输单元。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 232HUAWEI NetEngine40E配置指南 1 QoS步骤 **4** 执行命令 **mode template** ，使能 GQ （ Group Queue ）按照 QoS 模板中的实例共享组共享 QoS 资源。说明GQ 创建完成后只有在 **qos-profile** 中应用时才会共享 QoS 资源。配置该命令后， GQ 在申请 QoS 资源时按照 **qos-profile** 中的实例共享组关键字（ group-name ）共享资源；如果不配置该命令，则所有 GQ 共享 QoS 资源，即当多个 **qos-profile** 中的 group 不同时，所有 group 共享 QoS 资源。步骤 **5** 执行命令 **weight** weight-value **outbound** ，设置用户组队列权重值，保证指定用户组的业务按配置比例进行调度。步骤 **6** 执行命令 **cir** cir-value [ **cbs** cbs-value ] [ **pir** pir-value [ **pbs** pbs-value ] ]**outbound** ，设置用户组队列的承诺信息速率（ CIR ）和峰值信息速率（ PIR ），限制用户组队列流量，使用户组队列以均匀的速度向外发送流量。步骤 **7** 执行命令 **commit** ，提交配置。**----**结束##### 1.8.6.4 配置基于 QoS 模板的用户队列QoS 模板是 QoS 调度参数的集合，可配置用户队列的调度参数。##### 背景信息QoS 模板下可绑定用户队列模板、流队列模板和用户组队列模板。此处以用户队列模板为例，将提前配置好的用户队列模板绑定到 8 队列增强模式的 QoS 模板中。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **qos-profile** profile-name **8cos-enhance-mode** ，创建 8 队列增强模式的QoS 模板。步骤 **3** 执行命令 **user-queue** user-queue-name [ **flow-queue** flow-queue-name ] [ **user-****group-queue** user-group-queue-name ] [ **inbound** | **outbound** ] ，在 8 队列增强模式的 QoS 模板中绑定用户队列模板、流队列模板和用户组队列模板，从而使 QoS 模板能被应用到接口上进行 QoS 处理。步骤 **4** 执行命令 **commit** ，提交配置。**----**结束##### 1.8.6.5 应用 QoS 模板用户可以通过定义 QoS 模板并应用到不同接口上，来实现多个接口流量的统一调度。##### 背景信息将用户队列模板绑定到 8 队列增强模式的 QoS 模板后，可以通过在接口下应用此模板来实现对不同用户队列的业务流量进行差异化 QoS 处理。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 233HUAWEI NetEngine40E配置指南 1 QoS##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 配置用户队列资源大小。1. 执行命令 **slot** slot-id ，进入指定的槽位视图。2. 执行命令 **qos user-queue resource 8cos-enhance** 8cos-enhance-size{ **inbound** | **outbound** } ， 配置 8 队列增强模式用户队列的队列资源。3. 执行命令 **quit** ，退回系统视图。步骤 **3** 执行命令 **interface** { interface-name | interface-type interface-number } ，进入接口视图。步骤 **4** 请根据接口类型选择不同的命令行应用 QoS 模板。       - 执行命令 **qos-profile** qos-profile-name { **inbound** | **outbound** } [ **identifier****none** ] [ **group** group-name ] ，在 IP-Trunk 接口、 Dot1Q 接口、端口扩展接口上应用 QoS 模板。       - 执行命令 **qos-profile** qos-profile-name { **inbound** | **outbound** } **vlan** vlan-idbegin [ **to** vlan-id-end ] [ **identifier** { **vlan-id** | **none** } ] [ **group** groupname ] ，在二层接口或者 Dot1Q 终结子接口上应用 QoS 模板。       - 执行命令 **qos-profile** qos-profile-name { **inbound** | **outbound** } **pe-vid** pevlan-id **ce-vid** { ce-vlan-id-begin [ **to** ce-vlan-id-end ] } [ **identifier** { **pe-vid** |**ce-vid** | **pe-ce-vid** | **none** } ] [ **group** group-name ] ，在 QinQ 终结子接口上应用 QoS 模板。       - 执行命令 **qos-profile** qos-profile-name { **inbound** | **outbound** } [ **identifier**{ **none** | **vid** | **ce-vid** | **vid-ce-vid** } ] [ **group** group-name ] ，在 EVC 二层子接口上应用 QoS 模板。       - 执行命令 **qos-profile** qos-profile-name { **inbound** | **outbound** } **vni** vni-id**source** sourceip **peer** peerip ，在 NVE 接口上应用 QoS 模板。步骤 **5** 执行命令 **commit** ，提交配置。**----**结束##### ******* 检查配置结果基于 8 队列增强模式流队列的 HQoS 配置成功后，可以查看接口上队列配置情况、模板的应用信息和配置信息。##### 操作步骤       - 使用 **display flow-queue configuration** [ **verbose** [ flow-queue-name ] ] 命令来查看流队列模板信息。包括流队列的调度方式， wfq 调度的用户配置权重以及实际生效权重， shaping 带宽，应用的流队列 WRED 对象。       - 使用 **display qos-profile application** { qos-profile-name } 命令来查看 qosprofile 模板的应用信息。       - 使用 **display qos-profile configuration** [ qos-profile-name ] 命令来查看 qosprofile 模板的配置信息。       - 使用 **display qos-profile statistics vpn-instance** vpnname { **inbound** |**outbound** } [ **verbose** ] 命令来查看 VPN 实例上所有成员口的 QoS 模板统计信息。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 234HUAWEI NetEngine40E配置指南 1 QoS       - 使用 **display qos-profile statistics vsi** vsi-name { **inbound** | **outbound** }[ **verbose** ] 命令来查看指定 VSI 域的 QoS 流量统计信息。       - 使用 **display qos-profile statistics bridge-domain** bd-id { **inbound** |**outbound** } [ **verbose** ] 命令，查看指定 BD 的 QoS 流量统计信息。**----**结束#### 1.8.7 配置基于接口的 HQoS基于接口的 HQoS 支持用户根据网络需要在接口下配置用户队列的调度参数和带宽的分配方式。##### 1.8.7.1 （可选）配置流队列调度参数 背景信息用户可以采用非缺省的流队列（ flow-wred ）模板，并根据网络需求来配置流队列的调度参数，也可在系统中同时创建多个 flow-wred 对象，供流队列引用，并为创建的flow-wred 模板设定高低门限百分比和丢弃概率，当队列的实际长度占流队列的长度百分比小于低门限百分比时，不丢弃报文；当队列的实际长度占流队列的长度百分比在低门限百分比和高门限百分比之间时，开始随机丢弃报文（队列的长度越长，报文被丢弃的概率越高）；当队列的实际长度占流队列的长度百分比大于高门限百分比时，丢弃所有的报文。各队列间共享队列缓存，如果队列缓存耗尽，会存在队列不拥塞但是丢包的问题，因此需要配置队列缓存资源。说明          - 红色报文丢弃优先级的高低门限可以配置得最小，黄色报文丢弃优先级的高低门限可以配置得稍大些，绿色报文丢弃优先级的高低门限可以配置得最大。          - 用户在实际配置时，低门限百分比建议从 50% 开始取值，根据不同颜色的丢弃优先级逐级调整，丢弃概率建议取值为 100% 。          - 如果用户不配置流队列的 WRED 对象，系统采用缺省的尾丢弃策略。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** （可选）配置流队列 WRED 对象。1. 执行命令 **flow-wred** wred-name ，创建流队列 WRED 对象，进入流队列 WRED 视图。2. 执行命令 **color** { **green** | **yellow** | **red** } **low-limit** low-limit-percentage **high-****limit** high-limit-percentage **discard-percentage** discard-percentage ，设置不同颜色报文的高低门限百分比和丢弃概率。3. 执行命令 **queue-depth** queue-depth-value ，配置队列的深度。4. 执行命令 **commit** ，提交配置。5. 执行命令 **quit** ，返回系统视图。步骤 **3** 配置流队列调度参数。1. 执行命令 **flow-queue** flow-queue-name ，进入流队列视图。2. 执行命令 **queue** cos-value **cir** { { cir-value [ **cbs** cbs-value ] **cir-schedule pq pir**pir-value } | { **cir-percentage** cir-percentage-value [ **cbs** cbs-value ] **cir-**文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 235HUAWEI NetEngine40E配置指南 1 QoS**schedule pq pir pir-percentage** pir-percentage-value } } [ **pbs** pbs-value ]**pir-schedule** { **pq** | **wfq** **weight** weight-value | **lpq** } [ **flow-wred** wredname ] * 或者命令 **queue** cos-value { { **pq** | **wfq weight** weight-value | **lpq** } |{ **shaping** { shaping-value | **shaping-percentage** shaping-percentage-value }[ **pbs** pbs-value ] | **car** { car-value | **car-percentage** car-percentage-value }[ **pbs** pbs-value ] } | { **flow-wred** wred-name } | **low-latency** | **low-jitter** } * ，修改流队列的调度参数和调度策略。3. （可选）执行命令 **queue** cos-value **random-discard** random-discard-value ，配置流队列的随机丢弃值。4. 执行命令 **quit** ，返回系统视图。步骤 **4** （可选）配置流队列到端口队列的映射关系。1. 执行命令 **flow-mapping** mapping-name ，创建流队列映射对象并进入流队列映射视图。2. 执行命令 **map flow-queue** cos-value **to port-queue** cos-value ，配置流队列到端口队列的映射关系。通过配置流队列到端口队列的映射关系，可以灵活的控制流队列中某一服务等级的业务流量入端口队列的指定服务等级队列，从而参与该端口队列的调度。3. 执行命令 **quit** ，返回系统视图。步骤 **5** 执行命令 **commit** ，提交配置。**----**结束##### 1.8.7.2 （可选）配置用户组队列调度参数 背景信息用户组队列模板支持配置队列的整形值、权重值、承诺信息速率（ CIR ）和峰值信息速率（ PIR ），以此用来限制用户组队列流量与突发，保证了指定的用户组队列能够按配置的比例进行调度。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **user-group-queue** group-name ，进入用户组队列视图。步骤 **3** 执行命令 **mode template** ，使能 GQ （ Group Queue ）按照 QoS 模板的实例共享组共享 QoS 资源。说明GQ 创建完成后只有在 **qos-profile** 中应用时才会共享 QoS 资源。配置该命令后， GQ 在申请 QoS 资源时按照 **qos-profile** 中的实例共享组关键字（ group-name ）共享资源；如果不配置该命令，则所有 GQ 共享 QoS 资源，即当多个 **qos-profile** 中的 **group** 不同时，所有 **group** 共享 QoS 资源。步骤 **4** 执行命令 **shaping** shaping-value [ **pbs** pbs-value ] { **inbound** | **outbound** } ，配置用户组的整形值。步骤 **5** 执行命令 **weight** weight-value **outbound** ，配置用户组队列的权重值。步骤 **6** 执行命令 **cir** cir-value [ **cbs** cbs-value ] [ **pir** pir-value [ **pbs** pbs-value ] ]**outbound** ，配置用户组队列的调度参数。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 236HUAWEI NetEngine40E配置指南 1 QoS步骤 **7** 执行命令 **commit** ，提交配置。**----**结束##### 1.8.7.3 配置用户队列调度参数 背景信息缺省情况下，接口未使能层次化 QoS 功能，可通过配置用户队列的方式使能接口的层次化 QoS 功能。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **interface** interface-type interface-number ，进入接口视图。步骤 **3** 请依据接口类型。选择相应命令配置用户队列的调度参数。       - 对于 MP-Group 接口或配置了 HDLC 协议或 PPP 协议的串行接口，执行命令 **user-****queue cir** cir-value [ [ **pir** pir-value ] | [ **flow-queue** flow-queue-name ] |[ **flow-mapping** mapping-name ] | [ **user-group-queue** group-name ] ] *{ **inbound** | **outbound** } [ **service-template** template-name ] 或者命令 **user-****queue cir** cir-value [ [ **pir** pir-value ] | [ **flow-queue** flow-queue-name ] |[ **user-group-queue** group-name ] ] * { **inbound** | **outbound** } [ **service-****template** template-name [ **adjust-on-card** ] ]       - 对于配置了 FR 协议的 Serial 接口，执行命令 **user-queue** **cir** cir-value [ [ **pir** pirvalue ] | [ **flow-queue** flow-queue-name ] ] * { **inbound** | **outbound** }[ **service-template** template-name ]       - 对于其他类型的接口，执行命令 **user-queue cir** cir-value [ [ **pir** pir-value ] |[ **flow-queue** flow-queue-name ] | [ **user-group-queue** group-name ] ] *{ **inbound** | **outbound** } [ **service-template** template-name ] 或者命令 **user-****queue cir** cir-value **flow-mapping** mapping-name { **inbound** | **outbound** }[ **service-template** template-name ]步骤 **4** （可选）执行命令 **user-queue shaping ldp-traffic outbound** ，配置 HQoS 对接口上的 L3VPN 、 IP 、 VLL 、 VPLS 、 EVPN 流量生效。步骤 **5** （可选）执行命令 **user-queue shaping bgp-local-ifnet-traffic outbound** ，使能接口对 BGP Local Ifnet 隧道承载流量的 HQoS 功能。在 BGP Local Ifnet 隧道沿途的接口配置 HQoS 后，当出接口为下行非后置 TM 场景时，如果要使 HQoS 生效，需要执行此步骤。步骤 **6** （可选）执行命令 **qos default user-queue** { **cir** cir-value | **pir** pir-value | **cbs** cbsvalue | **pbs** pbs-value | **weight** weight-value } * **outbound** ，更改默认用户队列调度参数。步骤 **7** （可选）执行命令 **qos default user-group-queue** **shaping** shaping-value [ **pbs**pbs-value ] [ **weight** weight-value ] **outbound** ，配置缺省用户组队列的整形参数。步骤 **8** （可选）执行命令 **qos default user-group-queue** { **cir** cir-value [ **cbs** cbs-value ][ **pir** pir-value [ **pbs** pbs-value ] ] | [ **weight** weight-value ] } * **outbound** ，配置缺省用户组队列的调度参数。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 237HUAWEI NetEngine40E配置指南 1 QoS步骤 **9** （可选）执行命令 **qos user-group-queue member-link-scheduler distribute**{ **inbound** | **outbound** } ，配置 Trunk 成员口 User-group-queue 限速带宽按照成员口权重进行分配。Trunk 接口下配置了 User-group-queue 后，如果 Trunk 接口的成员口分布在不同网络处理器上，则每个成员口的限速带宽均为 Trunk 接口上配置的限速带宽。此时 Trunk 接口的流量为配置的限速带宽乘以成员口数量，达不到预期的限速效果。这种情况下，可以在 Trunk 接口下配置 **qos user-group-queue member-link-scheduler distribute** 命令使得 Trunk 成员口 User-group-queue 限速带宽按照成员口权重进行分配。说明Bras 场景建议同时配置命令 **bas-load-balance exclude sub-port-queue outbound** ，否则Trunk 接口的 User-group-queue 限速可能达不到配置的限速带宽。步骤 **10** 执行命令 **quit** ，退出接口视图。步骤 **11** （可选）执行命令 **slot** slot-id ，进入槽位视图。步骤 **12** （可选）执行命令 **qos pir-precision user-queue precision** precision-value ，配置用户队列带宽的实际生效值与配置值的误差范围。步骤 **13** （可选）执行命令 **qos user-queue burst-size bytes** min-bytes **time** burst-time ，配置用户队列默认突发尺寸的最小值及突发时间。步骤 **14** （可选）执行命令 **qos bandwidth-adjustment initial-grade** grade-id ，配置用户带宽的初始档位。步骤 **15** （可选）执行命令 **qos bandwidth-adjustment** { **degrade-cycle** cycle-num |**upgrade-cycle** cycle-num | **monitor-number** monitor_number | **adjust-number**adjust_number | **disable-percent** disable-value **enable-percent** enable-value |**upgrade-threshold** upgrade-value **degrade-threshold** degrade-value **inbound** |**upgrade-threshold** upgrade-value **degrade-threshold** degrade-value **outbound** |**enable** { **inbound** | **outbound** } } ，设置用户带宽调整的参数。说明由于下行带宽调整功能默认是关闭的，需要进行下行带宽调整时，要先执行 **qos bandwidth-****adjustment enable outbound** 使能下行带宽调整功能。步骤 **16** 执行命令 **commit** ，提交配置。**----**结束##### 1.8.7.4 （可选）配置 Trunk 接口的 HQoS 调度模式 背景信息Trunk 接口支持两种 HQoS 调度方模式：       - 基于成员口的 HQoS 调度（即拆分模式）：每个 Trunk 成员口单独进行调度，为每个成员口独立分配调度资源。当一个成员口业务出现拥塞时，不会影响其他接口上的业务。但是，此调度模式下调度资源消耗量较大，配置下行 HQoS 限速时会按照成员口个数进行带宽翻倍。       - 基于 TM 的 HQoS 调度（即合并模式）：对 Trunk 接口整体进行调度，统一分配调度资源。但是，此调度方式下，出现业务拥塞时成员口之间会相互影响。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 238HUAWEI NetEngine40E配置指南 1 QoS请根据实际网络情况选择配置 Trunk 接口的 HQoS 调度模式。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **interface** { **eth-trunk** | **ip-trunk** } trunk-number ，进入 Trunk 接口视图。步骤 **3** 执行命令 **qos schedule-tree distribute-mode outbound** ，配置 Trunk 接口的 HQoS 调度模式为拆分模式，带宽基于 Trunk 成员口进行 HQoS 调度。Trunk 接口带宽默认基于 TM 进行调度，当部分接口出现流量拥塞时，可能会导致其他未拥塞接口也出现报文丢失（此时设备会上报 hwXQoSTrunkTrafficCongestionAlarm告警，系统视图下执行命令 **qos trunk traffic-congestion-alarm disable** 可以关闭此告警功能）。为避免部分成员口流量拥塞导致其他未拥塞成员口报文丢失的问题，可以执行本配置调整 Trunk 接口带宽调度模式为基于 Trunk 成员口进行调度。步骤 **4** 执行命令 **commit** ，提交配置。**----**结束##### ******* 检查配置结果基于接口的 HQoS 配置成功后，可以查看接口上队列配置情况和统计信息等内容。##### 背景信息完成上述配置后，请执行下面的命令检查配置结果。##### 操作步骤       - 使用 **display qos resource user-queue** **slot** slot-id { **inbound** | **outbound** } 命令，查看接口板用户队列资源的使用情况。       - 使用 **display traffic buffer usage slot** slot-id 命令，查看当前 buffer 的使用情况。       - 使用 **display qos scheduling-mode** **slot** slot-id 命令，查询指定单板当前使用的带宽调度模式。       - 使用 **display qos bandwidth-adjustment information** **slot** slot-id 命令，查询指定单板的资源使用情况及带宽调整情况。       - 使用 **display qos default flow-queue statistics interface** { interface-name |interface-type interface-number } **outbound** 命令，查询指定信道化子接口下流队列的统计信息。       - 使用 **display sub-port-queue statistics** **interface** { interface-type interfacenumber | interface-name } **outbound** 命令，查看子接口队列的统计信息。       - 使用 **display qos resource sub-port-queue** **slot** slot-id { **inbound** |**outbound** } 命令，查看子接口队列资源的使用情况。**----**结束#### 1.8.8 配置信道化子接口的 HQoS配置信道化子接口的 HQoS 后，每个信道化子接口进行独立的 HQoS 调度。从而实现不同类型业务间的隔离承载。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 239HUAWEI NetEngine40E配置指南 1 QoS##### 背景信息为避免不同业务之间相互影响，需要有一种能够隔离不同类型业务的机制。不同业务流量可以归属到不同的 Dot1q 封装方式的 VLAN 信道化子接口上，每个信道化子接口可以实现独立的 HQoS 调度，从而实现不同类型业务之间的隔离。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **interface** interface-type interface-number ，进入接口视图。步骤 **3** 执行命令 **mode channel network-header-length** [ network-header-length-value ]**adjustment** **enable** ，配置信道化子接口的精度补偿值。说明仅支持信道化子接口的 Eth-Trunk 和 GE 主接口支持配置精度补偿值。步骤 **4** 执行命令 **quit** ，退回系统视图。步骤 **5** 执行命令 **interface** interface-type interface-number ，进入信道化子接口视图。了解信道化子接口的详细配置，请参见《接口与链路》分册中逻辑接口章节下的“配置信道化子接口”。步骤 **6** 执行命令 **qos default flow-queue** flow-queue-template-name **outbound** ，在信道化子接口下应用流队列模板。说明一个信道化子接口下只支持应用一个流队列模板。关于流队列模板的配置请参考 *********** （可选）配置基于 **8** 队列普通模式的流队列模板 。步骤 **7** 执行命令 **qos default service-template** service-template-name **outbound** ，在信道化子接口下应用业务模板。说明如果在信道化子接口下通过 **qos default service-template** 命令应用已经配置了精度补偿值的业务模板，同时又通过 **mode channel network-header-length adjustment enable** 命令在主接口下使能了信道化子接口的精度补偿功能并配置了精度补偿值，那么 **qos default service-****template** 模板中配置的精度补偿值会优先于 **mode channel network-header-length****adjustment enable** 命令中配置的精度补偿值生效。关于如何配置业务模板请参考 **1.8.4.3** （可选）配置业务模板 。步骤 **8** （可选）执行命令 **mode channel bandwidth maximize** ，使能信道化子接口的带宽扩展模式。说明该命令仅支持在 Eth-Trunk 信道化子接口下配置。步骤 **9** 执行命令 **commit** ，提交配置。**----**结束#### 1.8.9 维护 HQoS介绍如何清除 HQoS 的统计信息。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 240HUAWEI NetEngine40E配置指南 1 QoS##### 1.8.9.1 清空队列统计计数信息清空指定用户组队列、接口上用户队列和 QoS 模板的统计计数，以及指定用户的八个优先级队列的统计数据。##### 背景信息须知清除统计信息后，以前的统计信息将无法恢复，务必在执行该操作之前仔细确认。在确认需要清空队列统计计数信息后，请在用户视图下执行下面的 **reset** 命令，清除之前的统计信息。##### 操作步骤       - 执行命令 **reset user-group-queue** [ **name** ] group-name **statistics** [ **group**group-name ] **slot** { slot-id | all } { **inbound** | **outbound** } ，清空指定用户组队列的统计计数。       - 执行命令 **reset qos-profile statistics** **interface** { interface-name | interfacetype interface-number } [ **vlan** vlan-id | **pe-vid** pe-vid **ce-vid** ce-vid | **vid** vid |**ce-vid** ce-vid | **vid** vid **ce-vid** ce-vid ] { **inbound** | **outbound** } ，清空指定接口上的 QoS 模板的统计计数。       - 执行命令 **reset port-queue statistics** **interface** { interface-name | interfacetype interface-number } [ cos-value ] **outbound** 或 **reset port-queue statistics**[ **slot** slot-id ] [ cos-value ] **outbound** ，清除 port-queue 的统计信息。       - 执行命令 **reset qos user-id** user-id { **inbound** | **outbound** } ，清除指定用户的八个优先级队列和 CAR 流量监管的统计数据。VS 模式下，该命令仅在 Admin VS 支持。       - 执行命令 **reset user-group-queue statistics interface** { interface-name |interface-type interface-number } **pe-vid** pe-vid **ce-vid** ce-vid { **inbound** |**outbound** } ，清空指定接口用户组队列的统计计数。       - 执行命令 **reset sub-port-queue statistics** { interface-type interface-number |interface-name } **outbound** ，清除子接口队列的统计信息。       - 执行命令 **reset qos-profile statistics** **vni** vni-id { **inbound** | **outbound** } ，清除指定 vni 上 qos-profile 模板的统计计数。       - 执行命令 **reset qos-profile statistics** **vni** vni-id { **inbound** | **outbound** } **source**sourceip **peer** peerip ，清除 NVE 接口上 qos-profile 模板的统计计数。       - 执行命令 **reset qos default flow-queue statistics interface** {interface-name |interface-type interface-number } **outbound** ， 清除指定信道化子接口下流队列的统计信息。       - 执行命令 **reset qos-profile statistics bridge-domain** bdid { **inbound** |**outbound** } ，清除指定广播域下所有成员口的 qos-profile 统计计数信息。       - 执行命令 **reset qos-profile statistics vsi** vsiname { **inbound** | **outbound** } ，清除指定 VSI 实例下所有成员口的 qos-profile 统计计数信息。**----**结束文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 241HUAWEI NetEngine40E配置指南 1 QoS#### 1.8.10 配置举例从具体应用场景、配置命令等方面对 HQoS 的应用进行了详细的描述。##### ******** 配置基于 8 队列普通模式流队列的 HQoS 示例根据不同的子接口接入不同业务的流量，以基于 QoS 模板的场景为例，介绍如何配置 8队列普通模式流队列的 HQoS 示例。##### 组网需求用户通过 DSLAM 接入设备，设备为骨干网的接入设备。如 图 **1-27** 所示。用户的三个业务映射到 DSLAM 的 3 个 PVC 中，在设备上同一用户的流量分别通过GE1/0/0.1 、 GE1/0/0.2 和 GE1/0/0.3 三个子接口上接入，不同的子接口接入不同业务的流量。流量进入设备时带有两层 tag ，内层 tag 表示用户，外层 tag 表示业务。要对用户的流量进行统一调度，保证带宽为 100M ，其中 EF 流量带宽为 30M 、 AF1 流量带宽为10M 。用户所属用户组的总带宽为 500M 。在接入设备下行接口 EF 类型的报文流量不能超过 120M 。同时，业务类型 PC 、 VOIP 和 IPTV 分别标记外层 VLAN tag 为 1 、 2 、 3 ；内层 VLAN tag 均为 1 ～ 100 。说明本例中 Subinterface1.1 ， Subinterface2.1 ， Subinterface3.1 ， interface2 分别代表 GE1/0/0.1 ，GE1/0/0.2 ， GE1/0/0.3 ， GE2/0/0 。图 **1-27** 配置基于 8 队列普通模式流队列的 HQoS 组网图##### 配置思路采用如下的思路配置基于 8 队列普通模式流队列的 HQoS 功能。1. 配置流队列 WRED 对象的报文丢弃的参数。2. 配置流队列的调度算法及参数。3. 配置用户组队列 shaping 值。4. 配置业务模板的精度调整长度。5. 配置 QoS 模板中用户队列调度参数。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 242HUAWEI NetEngine40E配置指南 1 QoS6. 配置端口队列 WRED 对象的报文丢弃的参数。7. 在接入设备下行接口配置端口队列。##### 数据准备完成此例配置，需准备以下数据：       - flow-wred 报文丢弃的参数       - flow-queue 调度算法及其参数       - user-group-queue 的 shaping 值       - QoS 模板中 user-queue CIR 、 PIR 和 network-header-length 的值       - QoS 模板应用的接口       - port-queue 引用的 port-wred 的参数       - port-queue 调度算法及其参数、 shaping 值##### 操作步骤步骤 **1** 配置流队列引用的 WRED 对象。# 配置 flow-wred 报文丢弃的参数。<HUAWEI> **system-view**[ ~ HUAWEI] **flow-wred test**[*HUAWEI-flow-wred-test] **color green low-limit 70 high-limit 100 discard-percentage 100**[*HUAWEI-flow-wred-test] **color yellow low-limit 60 high-limit 90 discard-percentage 100**[*HUAWEI-flow-wred-test] **color red low-limit 50 high-limit 80 discard-percentage 100**[*HUAWEI-flow-wred-test] **commit**[ ~ HUAWEI-flow-wred-test] **return**完成上述配置后，执行命令 **display flow-wred configuration verbose** ，可以查看流队列 WRED 对象的配置参数。<HUAWEI> **display flow-wred configuration verbose test**Flow wred name : test--------------------------------------------------Color  Low-limit  High-limit  Discard-percent--------------------------------------------------green  70      100      100yellow  60      90      100red   50      80      100Queue Depth(kbytes) : 1000Reference relationships : NULL步骤 **2** 配置流队列的调度算法及其参数。# 配置 flow-queue 的调度算法、 WRED 参数及 shaping 值。<HUAWEI> **system-view**[ ~ HUAWEI] **flow-queue test**[*HUAWEI-flow-queue-template-test] **queue af1 lpq flow-wred test shaping 10000**[*HUAWEI-flow-queue-template-test] **queue ef pq flow-wred test shaping 30000**[*HUAWEI-flow-queue-template-test] **commit**[ ~ HUAWEI-flow-queue-template-test] **return**完成上述配置后，执行命令 **display flow-queue configuration verbose** ，可以查看流队列模板的配置信息。<HUAWEI> **display flow-queue configuration verbose test**Codes: Arith(Schedule algorithm)U-Weight(Schedule weight configured by users)文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 243HUAWEI NetEngine40E配置指南 1 QoSI-Weight(Inverse schedule weight used by TM)A-Weight(Actual schedule weight obtained by users)Shp(Shaping value)Pct(The percentage of subscriber queue's PIR)Drop-Arith(The name of the WRED object used by the flow queue)Flow Queue Template : test-----------------------------------------------------------------Cos Arith U-Weight I-Weight A-Weight Shp   Pct Drop-Arith-----------------------------------------------------------------be  wfq  10    3     10.00   -    -  Tail Dropaf1 lpq  -     -     -     10000  -  testaf2 wfq  10    3     10.00   -    -  Tail Dropaf3 wfq  15    2     15.00   -    -  Tail Dropaf4 wfq  15    2     15.00   -    -  Tail Dropef  pq   -     -     -     30000  -  testcs6 pq   -     -     -     -    -  Tail Dropcs7 pq   -     -     -     -    -  Tail DropReference relationships : NULL步骤 **3** 配置用户组队列 shaping 值。# 配置 user-group-queue 。<HUAWEI> **system-view**[ ~ HUAWEI] **user-group-queue test**[*HUAWEI-user-group-queue-test-slot-all] **shaping 500000 inbound**[*HUAWEI-user-group-queue-test-slot-all] **commit**[ ~ HUAWEI-user-group-queue-test-slot-all] **return**完成上述配置后，执行命令 **display user-group-queue configuration verbose** ，可以查看用户组队列的配置信息以及引用关系。<HUAWEI> **display user-group-queue configuration verbose test**user-group-queue-name : testslot : all[current configuration]inboundshaping-value <kbps> : 500000pbs-value <byte> : 524288outboundshaping-value <kbps> : NApbs-value <byte> : NAweight-value : NA[reference relationship]步骤 **4** 配置业务模板的精度调整长度。# 配置 service-template 及 network-header-length 。<HUAWEI> **system-view**[ ~ HUAWEI] **service-template test**[*HUAWEI-service-template-test-slot-all] **network-header-length 12 inbound**[*HUAWEI-service-template-test-slot-all] **commit**[ ~ HUAWEI-service-template-test-slot-all] **return**完成上述配置后，执行命令 **display service-template configuration verbose** ，可以查看业务模板的配置信息， network-header-length 值以及引用关系。<HUAWEI> **display service-template configuration verbose**[service-template detail information]total number : 1slot all   : 1service-template-name : testslot : all[current configuration]inbound network-header-length: 12outbound network-header-length: NA文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 244HUAWEI NetEngine40E配置指南 1 QoS[reference relationship]NULL步骤 **5** 配置 QoS 模板调度参数并应用到接口上。# 配置 QoS 模板中 user-queue 的调度参数<HUAWEI> **system-view**[ ~ HUAWEI] **qos-profile test**[*HUAWEI-qos-profile-test] **user-queue cir 100000 flow-queue test user-group-queue test service-****template test**[*HUAWEI-qos-profile-test] **commit**[ ~ HUAWEI-qos-profile-test] **return**# 创建 QinQ 子接口并配置 QinQ Termination, 在接口 GE1/0/0.1 、 GE1/0/0.2 和GE1/0/0.3 上应用 QoS 模板。<HUAWEI> **system-view**[ ~ HUAWEI] **interface gigabitethernet 1/0/0.1**[*HUAWEI-GigabitEthernet1/0/0.1] **control-vid 1 qinq-termination**[*HUAWEI-GigabitEthernet1/0/0.1] **qinq termination pe-vid 1 ce-vid 1 to 100**[*HUAWEI-GigabitEthernet1/0/0.1] **ip address ********* 24**[*HUAWEI-GigabitEthernet1/0/0.1] **qos-profile test inbound pe-vid 1 ce-vid 1 to 100 identifier ce-vid****group group1**[*HUAWEI-GigabitEthernet1/0/0.1] **commit**[ ~ HUAWEI-GigabitEthernet1/0/0.1] **quit**[ ~ HUAWEI] **interface gigabitethernet 1/0/0.2**[*HUAWEI-GigabitEthernet1/0/0.2] **control-vid 2 qinq-termination**[*HUAWEI-GigabitEthernet1/0/0.2] **qinq termination pe-vid 2 ce-vid 1 to 100**[*HUAWEI-GigabitEthernet1/0/0.2] **ip address ********* 24**[*HUAWEI-GigabitEthernet1/0/0.2] **qos-profile test inbound pe-vid 2 ce-vid 1 to 100 identifier ce-vid****group group1**[*HUAWEI-GigabitEthernet1/0/0.2] **commit**[ ~ HUAWEI-GigabitEthernet1/0/0.2] **quit**[ ~ HUAWEI] **interface gigabitethernet 1/0/0.3**[*HUAWEI-GigabitEthernet1/0/0.3] **control-vid 3 qinq-termination**[*HUAWEI-GigabitEthernet1/0/0.3] **qinq termination pe-vid 3 ce-vid 1 to 100**[*HUAWEI-GigabitEthernet1/0/0.3] **ip address ********* 24**[*HUAWEI-GigabitEthernet1/0/0.3] **qos-profile test inbound pe-vid 3 ce-vid 1 to 100 identifier ce-vid****group group1**[*HUAWEI-GigabitEthernet1/0/0.3] **commit**[ ~ HUAWEI-GigabitEthernet1/0/0.3] **return**完成上述配置后，执行命令 **display qos-profile configuration** qos-profile-name 和**display qos-profile application** profile-name ，可以看到 QoS 模板的配置和应用信息。<HUAWEI> **display qos-profile configuration test**qos-profile: testinbound:outbound:both:user-queue cir 100000 pir 100000 flow-queue test user-group-queue test service-template test<HUAWEI> **display qos-profile application test**qos-profile test:GigabitEthernet1/0/0.1GigabitEthernet1/0/0.2GigabitEthernet1/0/0.3Reference number by access user ： [inbound] 0, [outbound] 0Reference number by VNI ： [inbound] 0, [outbound] 0执行命令 **display qos-profile statistics interface gigabitethernet1/0/0.1 pe-vid 1****ce-vid 1 inbound** 可以看到接口 GE1/0/0.1 上 QoS 模板的统计信息。<HUAWEI> **display qos-profile statistics interface gigabitethernet 1/0/0.1 pe-vid 1 ce-vid 1 inbound**GigabitEthernet1/0/0.1 inbound traffic statistics:[be]文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 245HUAWEI NetEngine40E配置指南 1 QoSPass:              248,785 packets,          249,780,140 bytesDiscard:             23,986,035 packets,         24,081,979,140 bytesRandom-discard:         0 packets,             0 bytesLast 5 minutes pass rate:12,460 pps,             100,071,104 bpsLast 5 minutes discard rate:1,209,158 pps,           9,711,951,656 bpsLast 5 minutes random discard rate:0 pps,               0 bpsbuffer size:           1600 kbytesused buffer size:        1600 kbyteslast packet was received at:   0000-00-00 00:00:00[af1]Pass:              0 packets,             0 bytesDiscard:             0 packets,             0 bytesRandom-discard:         0 packets,             0 bytesLast 5 minutes pass rate:0 pps,               0 bpsLast 5 minutes discard rate:0 pps,               0 bpsLast 5 minutes random discard rate:0 pps,               0 bpsbuffer size:           1600 kbytesused buffer size:        1600 kbyteslast packet was received at:   0000-00-00 00:00:00[af2]Pass:              0 packets,             0 bytesDiscard:             0 packets,             0 bytesRandom-discard:         0 packets,             0 bytesLast 5 minutes pass rate:0 pps,               0 bpsLast 5 minutes discard rate:0 pps,               0 bpsLast 5 minutes random discard rate:0 pps,               0 bpsbuffer size:           1600 kbytesused buffer size:        1600 kbyteslast packet was received at:   0000-00-00 00:00:00[af3]Pass:              0 packets,             0 bytesDiscard:             0 packets,             0 bytesRandom-discard:         0 packets,             0 bytesLast 5 minutes pass rate:0 pps,               0 bpsLast 5 minutes discard rate:0 pps,               0 bpsLast 5 minutes random discard rate:0 pps,               0 bpsbuffer size:           1600 kbytesused buffer size:        1600 kbyteslast packet was received at:   0000-00-00 00:00:00[af4]Pass:              0 packets,             0 bytesDiscard:             0 packets,             0 bytesRandom-discard:         0 packets,             0 bytesLast 5 minutes pass rate:0 pps,               0 bpsLast 5 minutes discard rate:0 pps,               0 bpsLast 5 minutes random discard rate:0 pps,               0 bpsbuffer size:           1600 kbytesused buffer size:        1600 kbyteslast packet was received at:   0000-00-00 00:00:00[ef]Pass:              0 packets,             0 bytesDiscard:             0 packets,             0 bytesRandom-discard:         0 packets,             0 bytesLast 5 minutes pass rate:文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 246HUAWEI NetEngine40E配置指南 1 QoS0 pps,               0 bpsLast 5 minutes discard rate:0 pps,               0 bpsLast 5 minutes random discard rate:0 pps,               0 bpsbuffer size:           1600 kbytesused buffer size:        1600 kbyteslast packet was received at:   0000-00-00 00:00:00[cs6]Pass:              0 packets,             0 bytesDiscard:             0 packets,             0 bytesRandom-discard:         0 packets,             0 bytesLast 5 minutes pass rate:0 pps,               0 bpsLast 5 minutes discard rate:0 pps,               0 bpsLast 5 minutes random discard rate:0 pps,               0 bpsbuffer size:           1600 kbytesused buffer size:        1600 kbyteslast packet was received at:   0000-00-00 00:00:00[cs7]Pass:              0 packets,             0 bytesDiscard:             0 packets,             0 bytesRandom-discard:         0 packets,             0 bytesLast 5 minutes pass rate:0 pps,               0 bpsLast 5 minutes discard rate:0 pps,               0 bpsLast 5 minutes random discard rate:0 pps,               0bpsbuffer size:           1600 kbytesused buffer size:        1600 kbyteslast packet was received at:   0000-00-00 00:00:00[total]Pass:              248,785 packets,          249,780,140 bytesDiscard:             23,986,035 packets,         24,081,979,140 bytesRandom-discard:         0 packets,             0 bytesLast 5 minutes pass rate:12,460 pps,             100,071,104 bpsLast 5 minutes discard rate:1,209,158 pps,           9,711,951,656 bpsLast 5 minutes random discard rate:0 pps,               0 bpsTraffic towards this interface:                    0 bpsConfigured CIR and PIR:100,000 kbps,            100,000 kbps步骤 **6** 配置端口队列引用的 WRED 对象# 配置端口队列引用的 port-wred 报文丢弃的参数。<HUAWEI> **system-view**[ ~ HUAWEI] **port-wred test**[*HUAWEI-port-wred-test] **color green low-limit 70 high-limit 100 discard-percentage 100**[*HUAWEI-port-wred-test] **color yellow low-limit 60 high-limit 90 discard-percentage 100**[*HUAWEI-port-wred-test] **color red low-limit 50 high-limit 80 discard-percentage 100**[*HUAWEI-port-wred-test] **commit**[ ~ HUAWEI-port-wred-test] **return**完成上述配置后，执行命令 **display port-wred configuration verbose** ，可以查看端口队列 WRED 对象的配置参数。<HUAWEI> **display port-wred configuration verbose test**Port wred name : test--------------------------------------------------Color  Low-limit  High-limit  Discard-percent--------------------------------------------------green  70      100      100文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 247HUAWEI NetEngine40E配置指南 1 QoSyellow  60      90      100red   50      80      100Queue Depth(kbytes) : 8000Reference relationships : NULL步骤 **7** 配置端口队列# 配置 port-queue 的调度算法、 WRED 参数及 shaping 值。<HUAWEI> **system-view**[ ~ HUAWEI] **interface gigabitethernet 2/0/0**[ ~ HUAWEI-GigabitEthernet2/0/0] **undo shutdown**[*HUAWEI-GigabitEthernet2/0/0] **port-queue ef pq shaping 120 port-wred test outbound**[*HUAWEI-GigabitEthernet2/0/0] **commit**[ ~ HUAWEI-GigabitEthernet2/0/0] **return**完成上述配置后，执行命令 **display port-queue configuration interface** ，可以查看端口队列的详细配置信息。<HUAWEI> **display port-queue configuration interface gigabitethernet 2/0/0 outbound**GigabitEthernet2/0/0 outbound current port-queue configuration:be : arithmetic: wfq        weight: 10     tm weight: 3fact weight: 10.00       shaping(kbps): NAport-wred: NAgreen (low-high limit) (kbytes)         12800 - 12800yellow(low-high limit) (kbytes)         12800 - 12800red  (low-high limit) (kbytes)         12800 - 12800current queue-length   (kbytes)         12800cir:123            cir-percentage:NAcir-arithmetic:pq       cir-weight:NApir:123            pir-percentage:NApir-arithmetic:lpq       pir-weight:NAaf1: arithmetic: wfq        weight: 10     tm weight: 3fact weight: 10.00       shaping(kbps): NAport-wred: NAgreen (low-high limit) (kbytes)         12800 - 12800yellow(low-high limit) (kbytes)         12800 - 12800red  (low-high limit) (kbytes)         12800 - 12800current queue-length   (kbytes)         12800cir:NA             cir-percentage:10cir-arithmetic:pq       cir-weight:NApir:NA             pir-percentage:20pir-arithmetic:wfq       pir-weight:15af2: arithmetic: wfq        weight: 10     tm weight: 3fact weight: 10.00       shaping(kbps): NAport-wred: NAgreen (low-high limit) (kbytes)         12800 - 12800yellow(low-high limit) (kbytes)         12800 - 12800red  (low-high limit) (kbytes)         12800 - 12800current queue-length   (kbytes)         12800cir:NA             cir-percentage:NAcir-arithmetic:NA       cir-weight:NApir:NA             pir-percentage:NApir-arithmetic:NA       pir-weight:NAaf3: arithmetic: wfq        weight: 15     tm weight: 2fact weight: 15.00       shaping(kbps): NAport-wred: NAgreen (low-high limit) (kbytes)         12800 - 12800yellow(low-high limit) (kbytes)         12800 - 12800red  (low-high limit) (kbytes)         12800 - 12800current queue-length   (kbytes)         12800cir:NA             cir-percentage:NAcir-arithmetic:NA       cir-weight:NApir:NA             pir-percentage:NApir-arithmetic:NA       pir-weight:NAaf4: arithmetic: wfq        weight: 15     tm weight: 2fact weight: 15.00       shaping(kbps): NAport-wred: NAgreen (low-high limit) (kbytes)         12800 - 12800yellow(low-high limit) (kbytes)         12800 - 12800文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 248HUAWEI NetEngine40E配置指南 1 QoSred  (low-high limit) (kbytes)         12800 - 12800current queue-length   (kbytes)         12800cir:NA             cir-percentage:NAcir-arithmetic:NA       cir-weight:NApir:NA             pir-percentage:NApir-arithmetic:NA       pir-weight:NAef : arithmetic: pq         weight: NA     tm weight: 0fact weight: 0.00       shaping(kbps): NAport-wred: NAgreen (low-high limit) (kbytes)         1280 - 1280yellow(low-high limit) (kbytes)         1280 - 1280red  (low-high limit) (kbytes)         1280 - 1280current queue-length   (kbytes)         1280cir:NA             cir-percentage:NAcir-arithmetic:NA       cir-weight:NApir:NA             pir-percentage:NApir-arithmetic:NA       pir-weight:NAcs6: arithmetic: pq         weight: NA     tm weight: 0fact weight: 0.00       shaping(kbps): NAport-wred: NAgreen (low-high limit) (kbytes)         1280 - 1280yellow(low-high limit) (kbytes)         1280 - 1280red  (low-high limit) (kbytes)         1280 - 1280current queue-length   (kbytes)         1280cir:NA             cir-percentage:NAcir-arithmetic:NA       cir-weight:NApir:NA             pir-percentage:NApir-arithmetic:NA       pir-weight:NAcs7: arithmetic: pq         weight: NA     tm weight: 0fact weight: 0.00       shaping(kbps): NAport-wred: NAgreen (low-high limit) (kbytes)         1280 - 1280yellow(low-high limit) (kbytes)         1280 - 1280red  (low-high limit) (kbytes)         1280 - 1280current queue-length   (kbytes)         1280cir:NA             cir-percentage:NAcir-arithmetic:NA       cir-weight:NApir:NA             pir-percentage:NApir-arithmetic:NA       pir-weight:NA步骤 **8** 检查配置结果当网络中有流量通过时，发现用户 1 的 AF1 、 EF 流量和用户 2 的 EF 流量均按照配置的保证带宽进行转发。在设备的下行接口 GE2/0/0 执行命令 **display port-queue statistics** ，可以看到 EF 流量迅速增长。<HUAWEI> **display port-queue statistics interface gigabitethernet 2/0/0 ef outbound**GigabitEthernet2/0/0 outbound traffic statistics:[ef]Current usage percentage of queue: 10Total pass:5,097,976 packets,        458,817,750 bytesTotal discard:0 packets,             0 bytesDrop tail discard:0 packets,             0 bytesWred discard:0 packets,             0 bytesLast 30 seconds pass rate:12,030 pps,           8,661,600 bpsLast 30 seconds discard rate:0 pps,               0 bpsDrop tail discard rate:0 pps,               0 bpsWred discard rate:0 pps,               0 bpsbuffer size:          10 kbytes文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 249HUAWEI NetEngine40E配置指南 1 QoSused buffer size:        0 kbytesPeak rate:2013-11-17 13:15:18         8,661,600 bps**----**结束##### 配置文件       - HUAWEI 的配置文件#flow-wred testcolor green low-limit 70 high-limit 100 discard-percentage 100color yellow low-limit 60 high-limit 90 discard-percentage 100color red low-limit 50 high-limit 80 discard-percentage 100#flow-queue testqueue af1 lpq shaping 10000 flow-wred testqueue ef pq shaping 30000 flow-wred test#user-group-queue testshaping 500000 inbound#service-template testnetwork-header-length 12 inbound#qos-profile testuser-queue cir 100000 pir 100000 flow-queue test user-group-queue test service-template test#port-wred testcolor green low-limit 70 high-limit 100 discard-percentage 100color yellow low-limit 60 high-limit 90 discard-percentage 100color red low-limit 50 high-limit 80 discard-percentage 100#interface GigabitEthernet1/0/0.1encapsulation qinq-terminationqinq termination pe-vid 1 ce-vid 1 to 100ip address ********* *************qos-profile test inbound pe-vid 1 ce-vid 1 to 100 identifier ce-vid group group1#interface GigabitEthernet1/0/0.2encapsulation qinq-terminationqinq termination pe-vid 2 ce-vid 1 to 100ip address ********* *************qos-profile test inbound pe-vid 2 ce-vid 1 to 100 identifier ce-vid group group1#interface GigabitEthernet1/0/0.3encapsulation qinq-terminationqinq termination pe-vid 3 ce-vid 1 to 100ip address ********* *************qos-profile test inbound pe-vid 3 ce-vid 1 to 100 identifier ce-vid group group1#interface GigabitEthernet2/0/0undo shutdownip address ********* *************port-queue ef pq shaping 120 port-wred test outbound#ospf 10area 0.0.0.0network ********* *********network ********* *********network ********* *********network ********* *********#return##### ******** 配置基于 8 队列增强模式流队列的 HQoS 示例以在指定接口应用 QoS 模板为例，介绍如何配置并应用 8 队列增强模式的 QoS 模板。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 250HUAWEI NetEngine40E配置指南 1 QoS##### 组网需求如 图 **1** 所示，以用户 1 和用户 2 为例，两个用户分别从 PE1 设备的接口 interface1 和interface2 接入。在 PE1 设备上配置 8 队列增强模式流队列模板的相关参数、指定队列并使能 remark-color 功能，可对指定队列的 CIR 与 PIR 流量进行区分和标记（本例中以使能 cos 5 队列的 remark-color 功能为例），并映射成对应的 DEI （ Drop EligibleIndicator ）值。在 PE1 节点， CIR 流量对应的 DEI=0 ， PIR 流量对应的 DEI=1 。 PE1 节点生成的 DEI 将会随报文被发送到下游 P 节点， P 节点通过 DEI 可识别报文的丢弃优先级别。P 节点上同样需要先配置 8 队列增强模式流队列模板的相关参数，并根据上游节点 PE1发来的报文中包含的 DEI 值来映射颜色，当 DEI=1 时，报文的内部丢弃优先级（ color ）被标记为 yellow ，如果 DEI=0 ，则被标记为 Green ，被标记为 Green 的报文有优先通过的权利。然后根据各队列的优先级、调度权重和报文的优先级，在 P 节点完成报文重新入队列的操作。说明本例中 interface1 ， interface2 ， interface3 ， interface4 ， interface5 分别代表 GE1/0/0 ，GE2/0/0 ， GE3/0/0 ， GE1/0/3 ， GE1/0/4 。图 **1-28** 配置基于 8 队列增强模式流队列的 HQoS 组网图##### 配置思路采用如下的思路配置基于 8 队列增强模式流队列的 HQoS 功能。1. 配置 8 队列增强模式 SQ 资源个数。2. 配置 8 队列增强模式流队列模板相关参数，并使能 remark-color 功能。3. 配置 8 队列增强模式用户队列模板及用户带宽。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 251HUAWEI NetEngine40E配置指南 1 QoS4. 配置 8 队列增强模式 QoS 模板。5. 配置流队列 WRED 模板及丢弃参数。6. 在接口下应用 QoS 模板。##### 数据准备完成此例配置，需准备以下数据：       - 8 队列增强模式 SQ 资源个数。       - QoS 模板中 flow-queue CIR 、 PIR 的值。       - QoS 模板中 user-queue CIR 、 PIR 的值。       - 流队列 WRED 模板的丢弃参数。##### 操作步骤       - 以下为在 PE1 节点的配置。a. 配置 8 队列增强模式的上下行 SQ 资源个数。<HUAWEI> **system-view**[ ~ HUAWEI] **slot 3**[ ~ HUAWEI-slot-3] **qos user-queue resource 8cos-enhance 1000 inbound**[*HUAWEI-slot-3] **qos user-queue resource 8cos-enhance 1000 outbound**[*HUAWEI-slot-3] **commit**[ ~ HUAWEI-slot-3] **quit**b. 创建名为 qos1 的 8 队列增强模式流队列模板，并配置 cos 5 队列的峰值信息速率（ PIR ）、承诺信息速率（ CIR ）和 remark-color 使能。[ ~ HUAWEI] **flow-queue qos1 8cos-enhance-mode**[*HUAWEI-flow-queue-template-qos1] **cos 5**[*HUAWEI-flow-queue-template-qos1-cos5] **remark-color enable**[*HUAWEI-flow-queue-template-qos1-cos5] **pir 20000 cir 20000**[*HUAWEI-flow-queue-template-qos1-cos5] **commit**[ ~ HUAWEI-flow-queue-template-qos1-cos5] **quit**[ ~ HUAWEI-flow-queue-template-qos1] **quit**创建名为 qos2 的 8 队列增强模式流队列模板，并配置 cos 5 队列的峰值信息速率（ PIR ）、承诺信息速率（ CIR ）和 remark-color 使能。[ ~ HUAWEI] **flow-queue qos2 8cos-enhance-mode**[*HUAWEI-flow-queue-template-qos2] **cos 5**[*HUAWEI-flow-queue-template-qos2-cos5] **remark-color enable**[*HUAWEI-flow-queue-template-qos2-cos5] **pir 200000 cir 100000**[*HUAWEI-flow-queue-template-qos2-cos5] **commit**[ ~ HUAWEI-flow-queue-template-qos2-cos5] **quit**[ ~ HUAWEI-flow-queue-template-qos2] **quit**完成上述配置后，执行命令 **display flow-queue configuration** ，可以查看 8队列增强模式的流队列模板信息（此处查看名为 qos1 的流队列模板信息）。[ ~ HUAWEI] **display flow-queue configuration verbose qos1**Codes: Cos(Priority of queue's)CIR-PCT(The percentage of committed information rate)CIR-P(The priority schedule of cir)CIR-W(Schedule weight of cir)PIR-PCT(The percentage of peak information rate)PIR-P(The priority schedule of pir)PIR-W(Schedule weight of pir)Drop-Arith(The name of the WRED object used by the flow queue)Flow Queue Template : qos1                     mode: 8cos-enhancemode文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 252HUAWEI NetEngine40E配置指南 1 QoS------------------------------------------------------------------------------------------Cos CIR/CIR-PCT/CBS/CIR-P/CIR-W  PIR/PIR-PCT/PBS/PIR-P/PIR-W CAR-MODE Remark DropArith------------------------------------------------------------------------------------------0  -/-/-/1/10          -/-/-/3/10          0    0    TailDrop1  -/-/-/1/10          -/-/-/3/10          0    0    TailDrop2  -/-/-/1/10          -/-/-/3/10          0    0    TailDrop3  -/-/-/1/15          -/-/-/3/15          0    0    TailDrop4  -/-/-/1/15          -/-/-/3/15          0    0    TailDrop5  20000/-/-/0/20        20000/-/-/2/20        0    1    TailDrop6  -/-/-/0/20          -/-/-/2/20          0    0    TailDrop7  -/-/-/0/20          -/-/-/2/20          0    0    TailDropReference relationships : NULLc. 创建名为 qos 的 8 队列增强模式用户队列模板并配置用户队列的峰值信息速率（ PIR ）和承诺信息速率（ CIR ）等调度参数。[ ~ HUAWEI] **user-queue qos**[*HUAWEI-user-queue-template-qos] **pir 800000 cir 400000**[*HUAWEI-user-queue-template-qos] **priority 0 cir-parent-priority 0 pir-parent-priority 3**[*HUAWEI-user-queue-template-qos] **priority 0 pir 800000**[*HUAWEI-user-queue-template-qos] **commit**[ ~ HUAWEI-user-queue-template-qos] **quit**d. 创建名为 qos1 的 8 队列增强模式 QoS 模板。[ ~ HUAWEI] **qos-profile qos1 8cos-enhance-mode**[*HUAWEI-qos-profile-qos1-8cos-enhance] **user-queue qos flow-queue qos1**[*HUAWEI-qos-profile-qos1-8cos-enhance] **commit**[ ~ HUAWEI-qos-profile-qos1-8cos-enhance] **quit**创建名为 qos2 的 8 队列增强模式 QoS 模板。[ ~ HUAWEI] **qos-profile qos2 8cos-enhance-mode**[*HUAWEI-qos-profile-qos2-8cos-enhance] **user-queue qos flow-queue qos2**[*HUAWEI-qos-profile-qos2-8cos-enhance] **commit**[ ~ HUAWEI-qos-profile-qos2-8cos-enhance] **quit**完成上述配置后，执行命令 **display qos-profile configuration** ，可以查看 8队列增强模式 QoS 模板的配置信息（此处查看名为 qos1 的 QoS 模板信息）。[ ~ HUAWEI] **display qos-profile configuration qos1**qos-profile: qos1  mode: 8cos-enhance-modeinbound:outbound:both:flow-queue: qos1user-queue: qosuser-group-queue: -------------------------------------------------------------service-template: CIR(kbps)/CIR-PCT/CBS(bytes): 400000/-/PIR(kbps)/PIR-PCT/PBS(bytes): 800000/-/-------------------------------------------------------------PRI SCH-MODE PIR     PBS     CIR-P/CIR-W PIR-P/PIR-W-------------------------------------------------------------0  pq    800000   -      0/10     3/101  wfq    -      -      1/10     3/102  pq    -      -      2/10     3/103  wfq    -      -      3/10     3/10e. 在 PE1 节点入接口 GE1/0/0.1 、 GE2/0/0.1 上应用 QoS 模板。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 253HUAWEI NetEngine40E配置指南 1 QoS[ ~ HUAWEI] **interface gigabitethernet 1/0/0.1**[*HUAWEI-GigabitEthernet1/0/0.1] **vlan-type dot1q 1**[*HUAWEI-GigabitEthernet1/0/0.1] **ip address ************ 24**[*HUAWEI-GigabitEthernet1/0/0.1] **trust upstream default**[*HUAWEI-GigabitEthernet1/0/0.1] **trust 8021p**[*HUAWEI-GigabitEthernet1/0/0.1] **qos-profile qos1 inbound identifier none**[*HUAWEI-GigabitEthernet1/0/0.1] **commit**[ ~ HUAWEI-GigabitEthernet1/0/0.1] **quit**[ ~ HUAWEI] **interface gigabitethernet 2/0/0.1**[*HUAWEI-GigabitEthernet2/0/0.1] **vlan-type dot1q 2**[*HUAWEI-GigabitEthernet2/0/0.1] **ip address ******** 24**[*HUAWEI-GigabitEthernet2/0/0.1] **trust upstream default**[*HUAWEI-GigabitEthernet2/0/0.1] **trust 8021p**[*HUAWEI-GigabitEthernet2/0/0.1] **qos-profile qos2 inbound identifier none**[*HUAWEI-GigabitEthernet2/0/0.1] **commit**[ ~ HUAWEI-GigabitEthernet2/0/0.1] **quit**在 PE1 节点出接口 GE3/0/0.1 上使能接口报文的 DEI 能力。[ ~ HUAWEI] **mpls**[*HUAWEI-mpls] **mpls** **ldp**[*HUAWEI-mpls-ldp] **commit**[ ~ HUAWEI-mpls-ldp] **quit**[ ~ HUAWEI] **interface gigabitethernet 3/0/0.1**[*HUAWEI-GigabitEthernet3/0/0.1] **vlan-type dot1q 11**[*HUAWEI-GigabitEthernet3/0/0.1] **ip address ************ 24**[*HUAWEI-GigabitEthernet3/0/0.1] **trust upstream default**[*HUAWEI-GigabitEthernet3/0/0.1] **mpls**[*HUAWEI-GigabitEthernet3/0/0.1] **mpls ldp**[*HUAWEI-GigabitEthernet3/0/0.1] **trust 8021p**[*HUAWEI-GigabitEthernet3/0/0.1] **field dei enable**[*HUAWEI-GigabitEthernet3/0/0.1] **commit**[ ~ HUAWEI-GigabitEthernet3/0/0.1] **quit**完成上述配置后，执行命令 **display qos-profile application** profilename ，可以看到 QoS 模板的应用信息（此处查看名为 qos2 的 qos-profile 模板应用信息）。[ ~ HUAWEI] **display qos-profile application qos2**qos-profile qos2:GigabitEthernet2/0/0.1Reference number by access user:[inbound] 0, [outbound] 0Reference number by VNI:[inbound] 0, [outbound] 0       - 以下为在 P 节点的配置。a. 配置 qos user-queue resource 8 队列增强模式上下行 SQ 资源个数。<HUAWEI> **system-view**[ ~ HUAWEI] **slot 3**[ ~ HUAWEI-slot-3] **qos user-queue resource 8cos-enhance 1000 inbound**[*HUAWEI-slot-3] **qos user-queue resource 8cos-enhance 1000 outbound**[*HUAWEI-slot-3] **commit**[ ~ HUAWEI-slot-3] **quit**b. 创建名为 network 的流队列 WRED 模板并配置丢弃参数。[ ~ HUAWEI] **flow-wred network**[*HUAWEI-flow-wred-network] **color yellow low-limit 50 high-limit 50 discard-percentage****100**[*HUAWEI-flow-wred-network] **commit**[ ~ HUAWEI-flow-wred-network] **quit**完成上述配置后，执行命令 **display flow-wred configuration** ，可以查看流队列 WRED 模板的配置参数。包括每种颜色报文的高低门限百分比，丢弃概率以及该对象的引用关系。[ ~ HUAWEI] **display flow-wred configuration verbose network**Flow wred name : network--------------------------------------------------Color  Low-limit  High-limit  Discard-percent--------------------------------------------------文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 254HUAWEI NetEngine40E配置指南 1 QoSgreen  100     100      100yellow  50      50      100red   100     100      100Queue Depth(kbytes) : 1000Reference relationships : NULLc. 创建名为 network 的 8 队列增强模式流队列模板，并配置 cos 5 队列的峰值信息速率（ PIR ）、承诺信息速率（ CIR ）和 WRED 模板。[ ~ HUAWEI] **flow-queue network 8cos-enhance-mode**[*HUAWEI-flow-queue-template-network] **cos 5**[*HUAWEI-flow-queue-template-network-cos5] **flow-wred network**[*HUAWEI-flow-queue-template-network-cos5] **pir 200000 cir 120000**[*HUAWEI-flow-queue-template-network-cos5] **commit**[ ~ HUAWEI-flow-queue-template-network-cos5] **quit**[ ~ HUAWEI-flow-queue-template-network] **quit**完成上述配置后，执行命令 **display flow-queue configuration verbose****network** ，可以查看名为 network 的 8 队列增强模式的流队列模板信息。[ ~ HUAWEI] **display flow-queue configuration verbose network**Codes: Cos(Priority of queue's)CIR-PCT(The percentage of committed information rate)CIR-P(The priority schedule of cir)CIR-W(Schedule weight of cir)PIR-PCT(The percentage of peak information rate)PIR-P(The priority schedule of pir)PIR-W(Schedule weight of pir)Drop-Arith(The name of the WRED object used by the flow queue)Flow Queue Template : network                   mode: 8cos-enhancemode------------------------------------------------------------------------------------------Cos CIR/CIR-PCT/CBS/CIR-P/CIR-W  PIR/PIR-PCT/PBS/PIR-P/PIR-W  CAR-MODE Remark DropArith------------------------------------------------------------------------------------------0  -/-/-/1/10          -/-/-/3/10          0    0    TailDrop1  -/-/-/1/10          -/-/-/3/10          0    0    TailDrop2  -/-/-/1/10          -/-/-/3/10          0    0    TailDrop3  -/-/-/1/15          -/-/-/3/15          0    0    TailDrop4  -/-/-/1/15          -/-/-/3/15          0    0    TailDrop5  120000/-/-/0/20        200000/-/-/2/20        0    0network6  -/-/-/0/20          -/-/-/2/20          0    0    TailDrop7  -/-/-/0/20          -/-/-/2/20          0    0    TailDropReference relationships : NULLd. 创建名为 network 的 8 队列增强模式用户队列模板，并配置用户队列的峰值信息速率（ PIR ）和承诺信息速率（ CIR ）等调度参数。[ ~ HUAWEI] **user-queue network**[*HUAWEI-user-queue-template-network] **pir 8000000 cir 4000000**[*HUAWEI-user-queue-template-network] **priority 0 cir-parent-priority 0 pir-parent-priority 3**[*HUAWEI-user-queue-template-network] **priority 0 pir 800000**[*HUAWEI-user-queue-template-network] **commit**[ ~ HUAWEI-user-queue-template-network] **quit**e. 创建名为 network 的 8 队列增强模式 QoS 模板。[ ~ HUAWEI] **qos-profile network 8cos-enhance-mode**[*HUAWEI-qos-profile-network-8cos-enhance] **user-queue network flow-queue network**[*HUAWEI-qos-profile-network-8cos-enhance] **commit**文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 255HUAWEI NetEngine40E配置指南 1 QoS[ ~ HUAWEI-qos-profile-network-8cos-enhance] **quit**f. 在 P 节点入接口 GE1/0/3.1 上使能接口报文的 DEI 能力。[ ~ HUAWEI] **mpls**[*HUAWEI-mpls] **mpls** **ldp**[*HUAWEI-mpls-ldp] **commit**[ ~ HUAWEI-mpls-ldp] **quit**[ ~ HUAWEI] **interface gigabitethernet** **1/0/3.1**[*HUAWEI-GigabitEthernet1/0/3.1] **vlan-type dot1q 11**[*HUAWEI-GigabitEthernet1/0/3.1] **ip address ************ 24**[*HUAWEI-GigabitEthernet1/0/3.1] **trust upstream default**[*HUAWEI-GigabitEthernet1/0/3.1] **mpls**[*HUAWEI-GigabitEthernet1/0/3.1] **mpls ldp**[*HUAWEI-GigabitEthernet1/0/3.1] **trust 8021p**[*HUAWEI-GigabitEthernet1/0/3.1] **field dei enable**[*HUAWEI-GigabitEthernet1/0/3.1] **commit**[ ~ HUAWEI-GigabitEthernet1/0/3.1] **quit**在 P 节点出接口 GE1/0/4.1 上应用 QoS 模板。[ ~ HUAWEI] **interface gigabitethernet 1/0/4.1**[*HUAWEI-GigabitEthernet1/0/4.1] **vlan-type dot1q 22**[*HUAWEI-GigabitEthernet1/0/4.1] **ip address ************ 24**[*HUAWEI-GigabitEthernet1/0/4.1] **trust upstream default**[*HUAWEI-GigabitEthernet1/0/4.1] **mpls**[*HUAWEI-GigabitEthernet1/0/4.1] **mpls ldp**[*HUAWEI-GigabitEthernet1/0/4.1] **trust 8021p**[*HUAWEI-GigabitEthernet1/0/4.1] **qos-profile network outbound identifier none**[*HUAWEI-GigabitEthernet1/0/4.1] **field dei enable**[*HUAWEI-GigabitEthernet1/0/4.1] **commit**[ ~ HUAWEI-GigabitEthernet1/0/4.1] **quit**完成上述配置后，执行命令 **display qos-profile application** profilename ，可以看到 QoS 模板的配置和应用信息。[ ~ HUAWEI] **display qos-profile application network**qos-profile network:GigabitEthernet1/0/4.1Reference number by access user:[inbound] 0, [outbound] 0Reference number by VNI:[inbound] 0, [outbound] 0g. 检查配置结果。在 GE1/0/4.1 接口查询 QoS 模板统计信息，可以看到 cos 5 队列限速结果。[ ~ HUAWEI] **display qos-profile statistics interface gigabitethernet 1/0/4.1 outbound**GigabitEthernet1/0/4.1 outbound traffic statistics:[COS0]Pass              0 packets,             0 bytesDiscard             0 packets,             0 bytesLast 5 minutes pass rate0 pps,               0 bpsLast 5 minutes discard rate0 pps,               0 bpsbuffer size           10496 kbytesused buffer size        0 kbytes[COS1]Pass              0 packets,             0 bytesDiscard             0 packets,             0 bytesLast 5 minutes pass rate0 pps,               0 bpsLast 5 minutes discard rate0 pps,               0 bpsbuffer size           10496 kbytesused buffer size        0 kbytes[COS2]Pass              0 packets,             0 bytesDiscard             0 packets,             0 bytesLast 5 minutes pass rate0 pps,               0 bps文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 256HUAWEI NetEngine40E配置指南 1 QoSLast 5 minutes discard rate0 pps,               0 bpsbuffer size           10496 kbytesused buffer size        0 kbytes[COS3]Pass              0 packets,             0 bytesDiscard             0 packets,             0 bytesLast 5 minutes pass rate0 pps,               0 bpsLast 5 minutes discard rate0 pps,               0 bpsbuffer size           10496 kbytesused buffer size        0 kbytes[COS4]Pass              0 packets,             0 bytesDiscard             0 packets,             0 bytesLast 5 minutes pass rate0 pps,               0 bpsLast 5 minutes discard rate0 pps,               0 bpsbuffer size           10496 kbytesused buffer size        0 kbytes[COS5]Pass              0 packets,             0 bytesDiscard             0 packets,             0 bytesLast 5 minutes pass rate0 pps,               0 bpsLast 5 minutes discard rate0 pps,               0 bpsbuffer size           2560 kbytesused buffer size        0 kbytes[COS6]Pass              0 packets,             0 bytesDiscard             0 packets,             0 bytesLast 5 minutes pass rate0 pps,               0 bpsLast 5 minutes discard rate0 pps,               0 bpsbuffer size           2560 kbytesused buffer size        0 kbytes[COS7]Pass              0 packets,             0 bytesDiscard             0 packets,             0 bytesLast 5 minutes pass rate0 pps,               0 bpsLast 5 minutes discard rate0 pps,               0 bpsbuffer size           2560 kbytesused buffer size        0 kbytes[total]Pass              0 packets,             0 bytesDiscard             0 packets,             0 bytesLast 5 minutes pass rate0 pps,               0 bpsLast 5 minutes discard rate0 pps,               0 bpsTraffic towards this interface                    0 bpsConfigured CIR and PIR400,000 kbps,            800,000 kbps**----**结束##### 配置文件       - PE1 节点配置：slot 3qos user-queue resource 8cos-enhance 1000 inboundqos user-queue resource 8cos-enhance 1000 outbound#文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 257HUAWEI NetEngine40E配置指南 1 QoSflow-queue qos1 8cos-enhance-modecos 5remark-color enablepir 20000 cir 20000#flow-queue qos2 8cos-enhance-modecos 5remark-color enablepir 200000 cir 100000#user-queue qospir 800000 cir 400000priority 0 cir-parent-priority 0 pir-parent-priority 3priority 0 pir 800000#qos-profile qos1 8cos-enhance-modeuser-queue qos flow-queue qos1#qos-profile qos2 8cos-enhance-modeuser-queue qos flow-queue qos2#interface gigabitethernet 1/0/0.1vlan-type dot1q 1ip address ************ 24trust upstream defaulttrust 8021pqos-profile qos1 inbound identifier none#interface gigabitethernet 2/0/0.1vlan-type dot1q 2ip address ******** 24trust upstream defaulttrust 8021pqos-profile qos2 inbound identifier none#mplsmpls ldp#interface gigabitethernet 3/0/0.1vlan-type dot1q 11ip address ************ 24trust upstream defaultmplsmpls ldptrust 8021pfield dei enable#       - P 节点配置：slot 3qos user-queue resource 8cos-enhance 1000 inboundqos user-queue resource 8cos-enhance 1000 outbound#flow-wred networkcolor yellow low-limit 50 high-limit 50 discard-percentage 100#flow-queue network 8cos-enhance-modecos 5flow-wred networkpir 200000 cir 120000#user-queue networkpir 8000000 cir 4000000priority 0 cir-parent-priority 0 pir-parent-priority 3priority 0 pir 800000#qos-profile network 8cos-enhance-modeuser-queue network flow-queue network#mpls文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 258HUAWEI NetEngine40E配置指南 1 QoSmpls ldp#interface gigabitethernet 1/0/3.1vlan-type dot1q 11ip address ************ 24trust upstream defaultmplsmpls ldptrust 8021pfield dei enable#interface gigabitethernet 1/0/4.1vlan-type dot1q 22ip address ************ 24trust upstream defaultmplsmpls ldptrust 8021pqos-profile network outbound identifier nonefield dei enable#### 1.9 MPLS DiffServ 模式配置介绍了 MPLS DiffServ 模式的基本原理、配置过程和配置举例。#### 1.9.1 MPLS DiffServ 模式概述##### MPLS DiffServ COS 处理模式DiffServ 体系结构允许 DS 域内的中间节点检查并修改 IP Precedence 、 DSCP 或 Exp 值，统称 COS （ Class of Service ）值，这会导致报文的 COS 值在 IP 网络和 MPLS 网络传输过程中都可能发生变化。图 **1-29** MPLS DiffServ COS 处理模式因此，在报文进入 MPLS 网络或从 MPLS 网络离开进入 IP 网络时，运营商需要在 MPLS 边缘路由器对 COS （ Class of Service ）处理做出选择：是否信任 IP/MPLS 报文已经携带的COS 信息。相关标准中定义了三种 COS 处理模式： Uniform 、 Pipe 和 Short Pipe 。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 259HUAWEI NetEngine40E配置指南 1 QoS#### 1.9.2 MPLS DiffServ 模式配置注意事项##### 特性限制表 **1-23** 本特性的使用限制|特性限制|系列|涉及产品||---|---|---||MPLS报文P节点和出PE节点，MPLS报文根据EXP<br>按默认域映射入队，不受入接口的简单流配置影<br>响。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK||不支持全局配置l3vpn difserv-mode short-pipe<br>enhance enable，本地vrf不支持按私网报文优先<br>级入队|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK||IP OVER RSVP-TE TTL的场景，egress节点TTL<br>mode为uniform时，IP的TTL继承外层MPLS TTL<br>- 1。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK||入PE节点，报文根据入口配置简单流的配置入<br>队，与vpn实例下difserv-mode配置和出口配置<br>无关|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|#### 1.9.3 MPLS DiffServ 配置解析##### BA 和 PHB 详解了解本节之情，请先了解 BA 和 PHB 的解析，详细信息请参见 BA 和 PHB 详解。##### MPLS DiffServ 场景的 BA 、 PHB 动作剖析在 MPLS diffserv 场景中，有三种模式： Uniform ， Pipe ， Short-Pipe 。       - Uniform ：按照报文原始标记设置穿越 MPLS 网络的优先级标记。从 MPLS 网出来的时候，用户报文的优先级标记按照要求重标记。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 260HUAWEI NetEngine40E配置指南 1 QoS       - Pipe 和 short-pipe ：不关心报文原始标记，在穿越骨干网络时，统一打一个固定的优先级标记。从骨干网出来的时候，用户报文的优先级标记要保持不变。–Pipe ：骨干网尾节点不按照报文原始优先级标记进行调度。–short-pipe ：尾节点按照报文原始优先级标记进行调度。图 **1-30** PE 节点的 BA 、 PHB 动作剖析在 PE 的网络侧接口要同时做 BA 和 PHB ，因此要配置 **trust upstream** 命令。在 PE 的用户侧接口，上行要做 BA ，下行方向：       - Uniform 模式：需执行 PHB 。即， Uniform 模式， PE 的用户侧接口要同时做 BA 和PHB ，因此要配置 **trust upstream** 命令。       - Pipe 和 short-pipe 模式： PE 的用户侧接口只做 BA 不做 PHB 。 **diffserv-mode** 命令与**trust upstream** 命令互斥。图 **1-31** P 节点的 BA 、 PHB 动作剖析P 节点两侧接口要同时做 BA 和 PHB ，因此要配置 **trust upstream** 命令。#### 1.9.4 配置 MPLS TE 的 Uniform/Pipe 模式介绍 MPLS TE 的 Uniform/Pipe 模式的配置过程。##### 应用环境在 MPLS 公网中，为了保证不同 MPLS TE 业务的优先级，可以配置 Uniform/Pipe 模式实现根据不同的服务等级进行队列调度。##### 前置任务在配置 MPLS TE 的 Uniform/Pipe 之前，需要完成以下任务：文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 261HUAWEI NetEngine40E配置指南 1 QoS       - 配置各接口的物理参数、链路属性，保证接口的正常工作。       - 配置 PE 间的 MPLS TE 隧道。请参见《 HUAWEI NetEngine40E 路由器 配置指南       MPLS 》中的“ MPLS TE 配置”。说明在配置 MPLS TE 的 Uniform/Pipe 模式之前必须确定 MPLS TE 的状态为 UP 。在 Ingress PE 的用户侧 Tunnel 接口上进行以下配置。配置后只对 Ingress PE 节点产生影响，对于倒数第二跳节点默认都是采用 Uniform 模式。可参考“配置 MPLS 倒数第二跳节点的 Uniform/Pipe 模式”来改变倒数第二跳节点的 DiffServ 模式。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **interface tunnel** interface-number ，进入用户侧的 Tunnel 接口视图。步骤 **3** 执行命令 **diffserv-mode** { **pipe** service-class color | **uniform** } ，设置 MPLS TE 的DiffServ 模式。步骤 **4** 执行命令 **commit** ，提交配置。**----**结束#### 1.9.5 配置 VPN 的 Pipe/Short Pipe 模式配置 VPN 的 DiffServ 模式来实现不同 VPN 根据不同的服务等级进行队列调度。##### 应用环境1. 本特性可以单独在 Ingress PE 、 Egress PE 或同时应用：–若在 Ingress PE 用户侧接口同时配置了简单流分类和 Pipe/Short Pipe 模式，优先支持 Pipe/Short Pipe 的 DiffServ 模式。– 若在 PE 上配置了支持 DiffServ 模式配置为 Pipe/Short Pipe 时，则不需要配置简单流分类。– 若在 PE 上配置了支持 DiffServ 模式配置为 Uniform 时，则需要同时配置简单流分类。2. 需要在符合以下配置的 L3VPN 中 DiffServ 模式才能生效：–在 Egress PE 节点下 DiffServ 模式配置为 Pipe 或 Short Pipe 模式时，且下行出接口配置简单流分类时，需在 Egress PE 节点的流量出接口配置 **qos phb****disable** 命令。– 在 Egress PE 节点下 DiffServ 模式配置为 Uniform 模式时，且下行出接口配置简单流分类时，在 Egress PE 节点的流量出接口不用配置 **qos phb disable** 命令。3. 在如下情况下，建议在 P 节点上也配置简单流分类，使 P 节点根据不同的服务等级对报文进行调度：– P 节点性能有限，可能发生拥塞。– 在 P 节点实现 LDP over TE ， TE 隧道接口在 P 节点上，且隧道配置了优先级。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 262HUAWEI NetEngine40E配置指南 1 QoS##### 前置任务在配置基于 VPN Pipe/Short Pipe 之前，需要完成以下任务：       - 配置 PE 间的 MPLS TE 隧道。请参见《 HUAWEI NetEngine40E 路由器 配置指南       MPLS 》中的“ MPLS TE 配置”。       - 配置 VPN 业务，可根据具体业务需要选用不同的 L3VPN 或 L2VPN ，请参见《 HUAWEI NetEngine40E 路由器 配置指南 -VPN 》。       - 在 Ingress PE 用户侧接口配置简单流分类或者复杂流分类。请参见《 HUAWEINetEngine40E 路由器 配置指南 -QoS 》中的“基于类的 QoS 配置”。##### 操作步骤       - 配置 L3VPN 支持 DiffServ 模式a. 执行命令 **system-view** ，进入系统视图。b. （可选）执行命令 **l3vpn diffserv-mode short-pipe enhance enable** ，配置 L3VPN 场景下 Short Pipe 增强模式。在差分服务模式为 Short Pipe 模式下执行 **l3vpn diffserv-mode short-pipe****enhance enable** 命令才能生效。c. 执行命令 **ip vpn-instance** vpn-instance-name ，进入 VPN 实例视图。d. 执行命令 **diffserv-mode** { **pipe** service-class [ color ] | **short-pipe** serviceclass [ color ] [ **domain** ds-name ] | **uniform** } 或 **diffserv-mode** **ingress**{ **uniform** | **pipe** service-class color | **short-pipe** service-class color }**egress** { **uniform** | **pipe** | **short-pipe** [ **domain** ds-name ] } ，设置 VPN 实例的 DiffServ 模式。**diffserv-mode** 命令行除支持单播场景外，同时支持组播 NGMVPN 场景。e. 执行命令 **commit** ，提交配置。       - 配置 VLL 支持 DiffServ 模式a. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **interface** interface-type interface-number ，进入用户侧接口的接口视图。说明该接口为绑定了 L2VPN 的用户侧接口。c. 执行命令 **diffserv-mode** { **pipe** service-class color | **short-pipe** serviceclass color [ **domain** ds-name ] | **uniform** } 或 **diffserv-mode** **ingress**{ **uniform** | **pipe** service-class color | **short-pipe** service-class color }**egress** { **uniform** | **pipe** | **short-pipe** [ **trust** { **inner-vlan-8021p** | **ip-****dscp** } ] [ **domain** ds-name ] } ，设置 VLL 的 DiffServ 模式。d. 执行命令 **commit** ，提交配置。       - 配置 VPLS 支持 DiffServ 模式a. 执行命令 **system-view** ，进入系统视图。b. 执行命令 **vsi** vsi-name ，进入 VSI 实例视图。c. 执行命令 **diffserv-mode** { **pipe** service-class color | **short-pipe** serviceclass color [ **domain** ds-name ] | **uniform** } 或 **diffserv-mode** **ingress**{ **uniform** | **pipe** service-class color | **short-pipe** service-class color }**egress** { **uniform** | **pipe** | **short-pipe** [ **trust** { **inner-vlan-8021p** | **ip-****dscp** } ] [ **domain** ds-name ] } ，设置 VPLS 的 DiffServ 模式。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 263HUAWEI NetEngine40E配置指南 1 QoSd. 执行命令 **commit** ，提交配置。       - 配置 EVPN 支持 DiffServ 模式– 在 EVPN 实例视图和 BD-EVPN 实例视图下配置 DiffServ 模式。i. 执行命令 **system-view** ，进入系统视图。ii. 配置 EVPN 实例，具体请参照配置 EVPN 实例。iii. 执行命令 **diffserv-mode** { **pipe** service-class color | **short-pipe**service-class color [ **domain** ds-name ] | **uniform** } 或 **diffserv-mode****ingress** { **uniform** | **pipe** service-class color | **short-pipe** service-classcolor } **egress** { **uniform** | **pipe** | **short-pipe** [ **trust** { **inner-****vlan-8021p** | **ip-dscp** } ] [ **domain** ds-name ] } ，设置 EVPN 的 DiffServ模式。iv. 执行命令 **commit** ，提交配置。– 在 EVPL 实例视图下配置 DiffServ 模式。i. 执行命令 **system-view** ，进入系统视图。ii. 配置 EVPL 实例，具体请参照配置 EVPL 实例。iii. 执行命令 **diffserv-mode** { **pipe** service-class color | **short-pipe**service-class color [ **domain** ds-name ] | **uniform** } 或 **diffserv-mode****ingress** { **uniform** | **pipe** service-class color | **short-pipe** service-classcolor } **egress** { **uniform** | **pipe** | **short-pipe** [ **trust** { **inner-****vlan-8021p** | **ip-dscp** } ] [ **domain** ds-name ] } ，设置 EVPN 的 DiffServ模式。iv. 执行命令 **commit** ，提交配置。**----**结束#### 1.9.6 配置举例介绍了配置 MPLS DiffServ 模式示例。##### ******* 配置 MPLS DiffServ 模式示例 组网需求如 图 **1-32** 所示， CE1 、 CE3 属于 VPN-A ， CE2 、 CE4 属于 VPN-B 。 VPN-A 使用的 VPNtarget 属性为 111:1 ， VPN-B 为 222:2 ，不同 VPN 用户之间不能互相访问。在 PE1 和 PE2上配置 MPLS DiffServ 模式为 Pipe ，使 VPN 中的数据业务在 MPLS 网络中以运营商配置的优先级转发，其中在 P 节点也要按优先级进行调度。说明本例中 interface1 ， interface2 ， interface3 分别代表 GE1/0/0 ， GE2/0/0 ， GE3/0/0 。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 264HUAWEI NetEngine40E配置指南 1 QoS图 **1-32** MPLS DiffServ 模式组网图##### 配置思路采用如下的思路配置 MPLS DiffServ 模式：1. 骨干网上配置 OSPF 实现 PE 之间的互通。2. 配置 MPLS 基本功能和 MPLS LDP ，建立 MPLS LSP 。3. PE 之间配置 MP-IBGP 交换 VPN 路由信息。4. PE 上配置 VPN 实例，并把与 CE 相连的接口和相应的 VPN 实例绑定。5. CE 与 PE 之间配置 EBGP 交换 VPN 路由信息。6. 在 VPN-A 和 VPN-B 分别使能 Pipe 模式，同时把不同的 Differ-serve 域应用到不同的VPN 实例中。7. 在 P 节点上配置简单流分类。##### 数据准备为完成此配置例，需准备如下的数据：       - PE 及 P 上的 MPLS LSR-ID       - VPN-A 与 VPN-B 的路由区分符 RD       - VPN-A 与 VPN-B 的收发路由属性 VPN-Target       - PE1 和 PE2 上配置不同的 Differ-serve 域##### 操作步骤步骤 **1** 在 MPLS 骨干网上配置 IGP 协议，实现骨干网 PE 和 P 的互通文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 265HUAWEI NetEngine40E配置指南 1 QoS# 配置 PE1 。<HUAWEI> **system-view**[ ~ HUAWEI] **sysname PE1**[*HUAWEI] **commit**[ ~ PE1] **interface loopback 1**[*PE1-LoopBack1] **ip address ******* 32**[*PE1-LoopBack1] **commit**[ ~ PE1-LoopBack1] **quit**[ ~ PE1] **interface gigabitethernet 3/0/0**[ ~ PE1-GigabitEthernet3/0/0] **ip address ********** 24**[*PE1-GigabitEthernet3/0/0] **commit**[ ~ PE1-GigabitEthernet3/0/0] **quit**[ ~ PE1] **ospf**[*PE1-ospf-1] **area 0**[*PE1-ospf-1-area-0.0.0.0] **network ********** ***********[*PE1-ospf-1-area-0.0.0.0] **network ******* 0.0.0.0**[*PE1-ospf-1-area-0.0.0.0] **commit**[ ~ PE1-ospf-1-area-0.0.0.0] **quit**[ ~ PE1-ospf-1] **quit**# 配置 P 。<HUAWEI> **system-view**[ ~ HUAWEI] **sysname P**[*HUAWEI] **commit**[ ~ P] **interface loopback 1**[*P-LoopBack1] **ip address ******* 32**[*P-LoopBack1] **commit**[ ~ P-LoopBack1] **quit**[ ~ P] **interface gigabitethernet 1/0/0**[ ~ P-GigabitEthernet1/0/0] **ip address ********** 24**[*P-GigabitEthernet1/0/0] **commit**[ ~ P-GigabitEthernet1/0/0] **quit**[ ~ P] **interface gigabitethernet 2/0/0**[ ~ P-GigabitEthernet2/0/0] **ip address ********** 24**[*P-GigabitEthernet2/0/0] **commit**[ ~ P-GigabitEthernet2/0/0] **quit**[ ~ P] **ospf**[*P-ospf-1] **area 0**[*P-ospf-1-area-0.0.0.0] **network ********** ***********[*P-ospf-1-area-0.0.0.0] **network ********** ***********[*P-ospf-1-area-0.0.0.0] **network ******* 0.0.0.0**[*P-ospf-1-area-0.0.0.0] **commit**[ ~ P-ospf-1-area-0.0.0.0] **quit**[ ~ P-ospf-1] **quit**# 配置 PE2 。<HUAWEI> **system-view**[ ~ HUAWEI] **sysname PE2**[*HUAWEI] **commit**[ ~ PE2] **interface loopback 1**[*PE2-LoopBack1] **ip address ******* 32**[*PE2-LoopBack1] **commit**[ ~ PE2-LoopBack1] **quit**[ ~ PE2] **interface gigabitethernet 3/0/0**[ ~ PE2-GigabitEthernet3/0/0] **ip address ********** 24**[*PE2-GigabitEthernet3/0/0] **commit**[ ~ PE2-GigabitEthernet3/0/0] **quit**[ ~ PE2] **ospf**[*PE2-ospf-1] **area 0**[*PE2-ospf-1-area-0.0.0.0] **network ********** ***********[*PE2-ospf-1-area-0.0.0.0] **network ******* 0.0.0.0**[*PE2-ospf-1-area-0.0.0.0] **commit**[ ~ PE2-ospf-1-area-0.0.0.0] **quit**[ ~ PE2-ospf-1] **quit**文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 266HUAWEI NetEngine40E配置指南 1 QoS配置完成后， PE1 、 P 、 PE2 之间应能建立 OSPF 邻居关系，执行 **display ospf peer** 命令可以看到邻居状态为 Full 。执行 **display ip routing-table** 命令可以看到 PE 之间学习到对方的 Loopback1 路由。以 PE1 的显示为例：[ ~ PE1] **display ospf peer**(M) Indicates MADJ neighborOSPF Process 1 with Router ID *******NeighborsArea 0.0.0.0 interface **********(GigabitEthernet3/0/0)'s neighborsRouter ID: *******      Address: **********State: **Full** Mode:Nbr is Master  Priority: 1DR: **********   BDR: **********    MTU: 0Dead timer due in 38 secRetrans timer interval: 5Neighbor is up for 00h02m45sNeighbor Up Time : 2020-08-15 01:41:57Authentication Sequence: [ 0 ][ ~ PE1] **display ip routing-table**Route Flags: R - relay, D - download to fib, T - to vpn-instance, B - black hole route-----------------------------------------------------------------------------Routing Table: _public_Destinations : 8    Routes : 8Destination/Mask Proto Pre Cost       Flags NextHop     Interface*******/32 Direct 0  0        D 127.0.0.1     LoopBack1*******/32 OSPF  10  1        D **********     GigabitEthernet3/0/0*******/32 OSPF  10  2        D **********     GigabitEthernet3/0/0127.0.0.0/8  Direct 0  0        D 127.0.0.1     InLoopBack0127.0.0.1/32 Direct 0  0        D 127.0.0.1     InLoopBack0**********/24 Direct 0  0        D **********     GigabitEthernet3/0/0**********/32 Direct 0  0        D 127.0.0.1     InLoopBack0**********/24 OSPF  10  2        D **********     GigabitEthernet3/0/0步骤 **2** 在 MPLS 骨干网上配置 MPLS 基本能力和 MPLS LDP ，建立 LDP LSP# 配置 PE1 。[ ~ PE1] **mpls lsr-id *********[*PE1] **mpls**[*PE1-mpls] **commit**[ ~ PE1-mpls] **quit**[ ~ PE1] **mpls ldp**[*PE1-mpls-ldp] **commit**[ ~ PE1-mpls-ldp] **quit**[ ~ PE1] **interface gigabitethernet 3/0/0**[ ~ PE1-GigabitEthernet3/0/0] **mpls**[*PE1-GigabitEthernet3/0/0] **mpls ldp**[*PE1-GigabitEthernet3/0/0] **commit**[ ~ PE1-GigabitEthernet3/0/0] **quit**# 配置 P 。[ ~ P] **mpls lsr-id *********[*P] **mpls**[*P-mpls] **commit**[ ~ P-mpls] **quit**[ ~ P] **mpls ldp**[*P-mpls-ldp] **commit**[ ~ P-mpls-ldp] **quit**[ ~ P] **interface gigabitethernet 1/0/0**[ ~ P-GigabitEthernet1/0/0] **mpls**[*P-GigabitEthernet1/0/0] **mpls ldp**[*P-GigabitEthernet1/0/0] **commit**[ ~ P-GigabitEthernet1/0/0] **quit**[ ~ P] **interface gigabitethernet 2/0/0**[ ~ P-GigabitEthernet2/0/0] **mpls**文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 267HUAWEI NetEngine40E配置指南 1 QoS[*P-GigabitEthernet2/0/0] **mpls ldp**[*P-GigabitEthernet2/0/0] **commit**[ ~ P-GigabitEthernet2/0/0] **quit**# 配置 PE2 。[ ~ PE2] **mpls lsr-id *********[*PE2] **mpls**[*PE2-mpls] **commit**[ ~ PE2-mpls] **quit**[ ~ PE2] **mpls ldp**[*PE2-mpls-ldp] **commit**[ ~ PE2-mpls-ldp] **quit**[ ~ PE2] **interface gigabitethernet 3/0/0**[ ~ PE2-GigabitEthernet3/0/0] **mpls**[*PE2-GigabitEthernet3/0/0] **mpls ldp**[*PE2-GigabitEthernet3/0/0] **commit**[ ~ PE2-GigabitEthernet3/0/0] **quit**上述配置完成后， PE1 与 P 、 P 与 PE2 之间应能建立 LDP 会话，执行 **display mpls ldp****session** 命令可以看到显示结果中 Status 项为“ Operational ”。执行 **display mpls ldp****lsp** 命令，可以看到 LDP LSP 的建立情况。以 PE1 的显示为例：[ ~ PE1] **display mpls ldp session**LDP Session(s) in Public NetworkCodes: LAM(Label Advertisement Mode), SsnAge Unit(DDDD:HH:MM)An asterisk (*) before a session means the session is being deleted.------------------------------------------------------------------------PeerID       Status   LAM SsnRole SsnAge   KASent/Rcv------------------------------------------------------------------------*******:0 **Operational** DU Passive 0000:00:01 5/5------------------------------------------------------------------------TOTAL: 1 Session(s) Found.[ ~ PE1] **display mpls ldp lsp**LDP LSP Information------------------------------------------------------------------------------Flag after Out IF: (I) - RLFA Iterated LSP, (I*) - Normal and RLFA Iterated LSP------------------------------------------------------------------------------DestAddress/Mask  In/OutLabel  UpstreamPeer  NextHop     OutInterface------------------------------------------------------------------------------*******/32     3/NULL    *******    127.0.0.1    Loopback1********/32     Liberal/48061        DS/**************/32     NULL/3    -       **********   GigabitEthernet3/0/0*******/32    48061/3    *******    **********   GigabitEthernet3/0/0*******/32     NULL/48062  -       **********   GigabitEthernet3/0/0*******/32    48062/48062   *******    **********   GigabitEthernet3/0/0------------------------------------------------------------------------------TOTAL: 5 Normal LSP(s) Found.TOTAL: 1 Liberal LSP(s) Found.TOTAL: 0 FRR LSP(s) Found.An asterisk (*) before an LSP means the LSP is not establishedAn asterisk (*) before a Label means the USCB or DSCB is staleAn asterisk (*) before an UpstreamPeer means the session is staleAn asterisk (*) before a DS means the session is staleAn asterisk (*) before a NextHop means the LSP is FRR LSP步骤 **3** 在 PE 之间建立 MP-IBGP 对等体关系# 配置 PE1 。[ ~ PE1] **bgp 100**[*PE1-bgp] **peer ******* as-number 100**[*PE1-bgp] **peer ******* connect-interface loopback 1**[*PE1-bgp] **ipv4-family vpnv4**[*PE1-bgp-af-vpnv4] **peer ******* enable**Warning: This operation will reset the peer session. Continue? [Y/N]: **y**文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 268HUAWEI NetEngine40E配置指南 1 QoS[*PE1-bgp-af-vpnv4] **commit**[ ~ PE1-bgp-af-vpnv4] **quit**[ ~ PE1-bgp] **quit**# 配置 PE2 。[ ~ PE2] **bgp 100**[*PE2-bgp] **peer ******* as-number 100**[*PE2-bgp] **peer ******* connect-interface loopback 1**[*PE2-bgp] **ipv4-family vpnv4**[*PE2-bgp-af-vpnv4] **peer ******* enable**Warning: This operation will reset the peer session. Continue? [Y/N]: **y**[*PE2-bgp-af-vpnv4] **commit**[ ~ PE2-bgp-af-vpnv4] **quit**[ ~ PE2-bgp] **quit**配置完成后，在 PE 设备上执行 **display bgp peer** 或 **display bgp vpnv4 all peer** 命令，可以看到 PE 之间的 BGP 对等体关系已建立，并达到 Established 状态。[ ~ PE1] **display bgp vpnv4 all peer**BGP local router ID : *******Local AS number : 100Total number of peers : 1         Peers in established state : 1Peer      V  AS MsgRcvd MsgSent  OutQ Up/Down  State    PrefRcv*******     4  100  12   18     0   00:09:38 **Established** 0步骤 **4** 在 PE 设备上配置 VPN 实例，将 CE 接入 PE# 配置 PE1 。[ ~ PE1] **ip vpn-instance vpna**[*PE1-vpn-instance-vpna] **route-distinguisher 100:1**[*PE1-vpn-instance-vpna-af-ipv4] **vpn-target 111:1 both**[*PE1-vpn-instance-vpna-af-ipv4] **commit**[ ~ PE1-vpn-instance-vpna-af-ipv4] **quit**[ ~ PE1-vpn-instance-vpna] **quit**[ ~ PE1] **ip vpn-instance vpnb**[*PE1-vpn-instance-vpnb] **route-distinguisher 100:2**[*PE1-vpn-instance-vpnb-af-ipv4] **vpn-target 222:2 both**[*PE1-vpn-instance-vpnb-af-ipv4] **commit**[ ~ PE1-vpn-instance-vpnb-af-ipv4] **quit**[ ~ PE1-vpn-instance-vpnb] **quit**[ ~ PE1] **interface gigabitethernet 1/0/0**[ ~ PE1-GigabitEthernet1/0/0] **ip binding vpn-instance vpna**[*PE1-GigabitEthernet1/0/0] **ip address ******** 24**[*PE1-GigabitEthernet1/0/0] **commit**[ ~ PE1-GigabitEthernet1/0/0] **quit**[ ~ PE1] **interface gigabitethernet 2/0/0**[ ~ PE1-GigabitEthernet2/0/0] **ip binding vpn-instance vpnb**[*PE1-GigabitEthernet2/0/0] **ip address ******** 24**[*PE1-GigabitEthernet2/0/0] **commit**[ ~ PE1-GigabitEthernet2/0/0] **quit**# 配置 PE2 。[ ~ PE2] **ip vpn-instance vpna**[*PE2-vpn-instance-vpna] **route-distinguisher 200:1**[*PE2-vpn-instance-vpna-af-ipv4] **vpn-target 111:1 both**[*PE2-vpn-instance-vpna-af-ipv4] **commit**[ ~ PE2-vpn-instance-vpna-af-ipv4] **quit**[ ~ PE2-vpn-instance-vpna] **quit**[ ~ PE2] **ip vpn-instance vpnb**[*PE2-vpn-instance-vpnb] **route-distinguisher 200:2**[*PE2-vpn-instance-vpnb-af-ipv4] **vpn-target 222:2 both**[*PE2-vpn-instance-vpnb-af-ipv4] **commit**[ ~ PE2-vpn-instance-vpnb-af-ipv4] **quit**[ ~ PE2-vpn-instance-vpnb] **quit**[ ~ PE2] **interface gigabitethernet 1/0/0**[ ~ PE2-GigabitEthernet1/0/0] **ip binding vpn-instance vpna**[*PE2-GigabitEthernet1/0/0] **ip address ******** 24**文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 269HUAWEI NetEngine40E配置指南 1 QoS[*PE2-GigabitEthernet1/0/0] **commit**[ ~ PE2-GigabitEthernet1/0/0] **quit**[ ~ PE2] **interface gigabitethernet 2/0/0**[ ~ PE2-GigabitEthernet2/0/0] **ip binding vpn-instance vpnb**[*PE2-GigabitEthernet2/0/0] **ip address ******** 24**[*PE2-GigabitEthernet2/0/0] **commit**[ ~ PE2-GigabitEthernet2/0/0] **quit**# 按 图 **1-32** 配置各 CE 的接口 IP 地址，配置过程略。配置完成后，在 PE 设备上执行 **display ip vpn-instance verbose** 命令可以看到 VPN 实例的配置情况。各 PE 能 ping 通自己接入的 CE 。说明当 PE 上有多个绑定了同一个 VPN 的接口，则使用 **ping -vpn-instance** 命令 ping 对端 PE 接入的 CE时，要指定源 IP 地址，即要指定 **ping -vpn-instance** vpn-instance-name **-a** source-ip-addressdest-ip-address 命令中的参数 **-a** source-ip-address ，否则可能 ping 不通。以 PE1 和 CE1 为例：[ ~ PE1] **display ip vpn-instance verbose**Total VPN-Instances configured : 2Total IPv4 VPN-Instances configured : 2Total IPv6 VPN-Instances configured : 0VPN-Instance Name and ID : vpna, 4Interfaces : GigabitEthernet1/0/0Address family ipv4Create date : 2020/09/21 11:30:35Up time : 0 days, 00 hours, 05 minutes and 19 secondsVrf Status : UPRoute Distinguisher : 100:1Export VPN Targets : 111:1Import VPN Targets : 111:1Label Policy : label per routeThe diffserv-mode Information is : uniformThe ttl-mode Information is : pipeLog Interval : 5Interfaces : GigabitEthernet1/0/0VPN-Instance Name and ID : vpnb, 5Interfaces : GigabitEthernet2/0/0Address family ipv4Create date : 2020/09/21 11:31:18Up time : 0 days, 00 hours, 04 minutes and 36 secondsVrf Status : UPRoute Distinguisher : 100:2Export VPN Targets : 222:2Import VPN Targets : 222:2Label Policy : label per routeThe diffserv-mode Information is : uniformThe ttl-mode Information is : pipeLog Interval : 5Interfaces : GigabitEthernet2/0/0[ ~ PE1] **ping -vpn-instance vpna **********PING ********: 56 data bytes, press CTRL_C to breakReply from ********: bytes=56 Sequence=1 ttl=255 time=56 msReply from ********: bytes=56 Sequence=2 ttl=255 time=4 msReply from ********: bytes=56 Sequence=3 ttl=255 time=4 msReply from ********: bytes=56 Sequence=4 ttl=255 time=52 msReply from ********: bytes=56 Sequence=5 ttl=255 time=3 ms--- ******** ping statistics --5 packet(s) transmitted5 packet(s) received0.00% packet lossround-trip min/avg/max = 3/23/56 ms文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 270HUAWEI NetEngine40E配置指南 1 QoS步骤 **5** 在 PE 与 CE 之间建立 EBGP 对等体关系，引入 VPN 路由# 配置 CE1 。[ ~ CE1] **bgp 65410**[*CE1-bgp] **peer ******** as-number 100**[*CE1-bgp] **import-route direct**[*CE1-bgp] **commit**说明另外 3 个 CE 设备（ CE2 ～ CE4 ）配置与 CE1 设备配置类似，配置过程省略。# 配置 PE1 。[ ~ PE1] **bgp 100**[*PE1-bgp] **ipv4-family vpn-instance vpna**[*PE1-bgp-vpna] **peer ******** as-number 65410**[*PE1-bgp-vpna] **import-route direct**[*PE1-bgp-vpna] **commit**[ ~ PE1-bgp-vpna] **quit**[ ~ PE1-bgp] **ipv4-family vpn-instance vpnb**[*PE1-bgp-vpnb] **peer ******** as-number 65420**[*PE1-bgp-vpnb] **import-route direct**[*PE1-bgp-vpnb] **commit**[ ~ PE1-bgp-vpnb] **quit**[ ~ PE1-bgp] **quit**说明PE2 的配置与 PE1 类似，配置过程省略。配置完成后，在 PE 设备上执行 **display bgp vpnv4 vpn-instance peer** 命令，可以看到PE 与 CE 之间的 BGP 对等体关系已建立，并达到 Established 状态。以 PE1 与 CE1 的对等体关系为例：[ ~ PE1] **display bgp vpnv4 vpn-instance vpna peer**BGP local router ID : *******Local AS number : 100VPN-Instance vpna, Router ID *******:Total number of peers : 1      Peers in established state : 1Peer      V  AS MsgRcvd MsgSent  OutQ Up/Down  State    PrefRcv********    4  65410 11   9     0   00:06:37 **Established** 1步骤 **6** 检查配置结果在 PE 设备上执行 **display ip routing-table vpn-instance** 命令，可以看到去往对端 CE的路由。以 PE1 的显示为例：[ ~ PE1] **display ip routing-table vpn-instance vpna**Route Flags: R - relay, D - download to fib-----------------------------------------------------------------------------Routing Table: vpnaDestinations : 3    Routes : 3Destination/Mask  Proto Pre Cost   Flags NextHop     Interface10.1.1.0/24  Direct 0  0    D   ********    GigabitEthernet1/0/0********/32  Direct 0  0    D   127.0.0.1    GigabitEthernet1/0/0**********/24** IBGP  255 0    RD  *******     GigabitEthernet3/0/0[ ~ PE1] **display ip routing-table vpn-instance vpnb**Route Flags: R - relay, D - download to fib-----------------------------------------------------------------------------Routing Table: vpnbDestinations : 3    Routes : 3文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 271HUAWEI NetEngine40E配置指南 1 QoSDestination/Mask  Proto Pre Cost   Flags NextHop     Interface10.2.1.0/24  Direct 0  0    D   ********    GigabitEthernet2/0/0********/32  Direct 0  0    D   127.0.0.1    GigabitEthernet2/0/0**********/24** IBGP  255 0    RD  *******     GigabitEthernet3/0/0同一 VPN 的 CE 能够相互 Ping 通，不同 VPN 的 CE 不能相互 Ping 通。例如： CE1 能够 Ping 通 CE3 （ ********/24 ），但不能 Ping 通 CE4 （ ********/24 ）。[ ~ CE1] **ping **********PING ********: 56 data bytes, press CTRL_C to breakReply from ********: bytes=56 Sequence=1 ttl=253 time=72 msReply from ********: bytes=56 Sequence=2 ttl=253 time=34 msReply from ********: bytes=56 Sequence=3 ttl=253 time=50 msReply from ********: bytes=56 Sequence=4 ttl=253 time=50 msReply from ********: bytes=56 Sequence=5 ttl=253 time=34 ms--- ******** ping statistics --5 packet(s) transmitted5 packet(s) received0.00% packet lossround-trip min/avg/max = 34/48/72 ms[ ~ CE1] **ping **********PING ********: 56 data bytes, press CTRL_C to breakRequest time outRequest time outRequest time outRequest time outRequest time out--- ******** ping statistics --5 packet(s) transmitted0 packet(s) received100.00% packet loss步骤 **7** 在 PE1 和 PE2 上配置 differ-serve 模式，同时将 Differ-serve 域应用到不同的 VPN 实例中# 配置 PE1 。[ ~ PE1] **ip vpn-instance vpna**[*PE1-vpn-instance-vpna] **ipv4-family**[*PE1-vpn-instance-vpna-af-ipv4] **diffserv-mode pipe af1 green**[*PE1-vpn-instance-vpna-af-ipv4] **commit**[ ~ PE1-vpn-instance-vpna-af-ipv4] **quit**[ ~ PE1-vpn-instance-vpna] **quit**[ ~ PE1] **ip vpn-instance vpnb**[*PE1-vpn-instance-vpnb] **ipv4-family**[*PE1-vpn-instance-vpnb-af-ipv4] **diffserv-mode pipe be yellow**[*PE1-vpn-instance-vpnb-af-ipv4] **commit**[ ~ PE1-vpn-instance-vpnb-af-ipv4] **quit**[ ~ PE1-vpn-instance-vpnb] **quit**# 配置 PE2 。[ ~ PE2] **ip vpn-instance vpna**[*PE2-vpn-instance-vpna] **ipv4-family**[*PE2-vpn-instance-vpna-af-ipv4] **diffserv-mode pipe af1 green**[*PE2-vpn-instance-vpna-af-ipv4] **commit**[ ~ PE2-vpn-instance-vpna-af-ipv4] **quit**[ ~ PE2-vpn-instance-vpna] **quit**[ ~ PE2] **ip vpn-instance vpnb**[*PE2-vpn-instance-vpnb] **ipv4-family**[*PE2-vpn-instance-vpnb-af-ipv4] **diffserv-mode pipe be yellow**[*PE2-vpn-instance-vpnb-af-ipv4] **commit**[ ~ PE2-vpn-instance-vpnb-af-ipv4] **quit**[ ~ PE2-vpn-instance-vpnb] **quit**步骤 **8** 在 P 节点上配置简单流分类# 配置 P 。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 272HUAWEI NetEngine40E配置指南 1 QoS[ ~ P] **interface gigabitethernet 1/0/0**[ ~ P-GigabitEthernet1/0/0] **trust upstream** **default**[*P-GigabitEthernet1/0/0] **quit**[*P] **interface gigabitethernet 2/0/0**[*P-GigabitEthernet2/0/0] **trust upstream** **default**[*P-GigabitEthernet2/0/0] **quit**[*P] **commit**步骤 **9** 检查配置结果在 PE 设备上执行 **display ip vpn-instance verbose vpna** 命令，可以看到该 VPN 实例下配置的 differ-serve 模式。以 PE1 的显示为例：[ ~ PE1] **display ip vpn-instance verbose vpna**VPN-Instance Name and ID : vpna, 23Address family ipv4Create date : 2020/09/21 11:08:12Up time : 0 days, 00 hours, 06 minutes and 32 secondsVrf Status : UPLabel Policy : label per route**The diffserv-mode Information is : pipe af1 green**The ttl-mode Information is : pipeLog Interval : 5**----**结束##### 配置文件       - PE1 的配置文件#sysname PE1#ip vpn-instance vpnaipv4-familyroute-distinguisher 100:1apply-label per-instancevpn-target 111:1 export-extcommunityvpn-target 111:1 import-extcommunitydiffserv-mode pipe af1 green#ip vpn-instance vpnbipv4-familyroute-distinguisher 100:2apply-label per-instancevpn-target 222:2 export-extcommunityvpn-target 222:2 import-extcommunitydiffserv-mode pipe be yellow#mpls lsr-id *******#mpls#mpls ldp#interface GigabitEthernet1/0/0undo shutdownip binding vpn-instance vpnaip address ******** *************#interface GigabitEthernet2/0/0undo shutdownip binding vpn-instance vpnbip address ******** *************#interface GigabitEthernet3/0/0undo shutdown文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 273HUAWEI NetEngine40E配置指南 1 QoSip address ********** *************mplsmpls ldp#interface LoopBack1ip address ******* ***************#bgp 100peer ******* as-number 100peer ******* connect-interface LoopBack1#ipv4-family unicastundo synchronizationpeer ******* enable#ipv4-family vpnv4policy vpn-targetpeer ******* enable#ipv4-family vpn-instance vpnaimport-route directpeer ******** as-number 65410#ipv4-family vpn-instance vpnbimport-route directpeer ******** as-number 65420#ospf 1area 0.0.0.0network ******* 0.0.0.0network ********** *********#return       - P 的配置文件#sysname P#mpls lsr-id *******#mpls#mpls ldp#interface GigabitEthernet1/0/0undo shutdownip address ********** *************mplsmpls ldptrust upstream default#interface GigabitEthernet2/0/0undo shutdownip address ********** *************mplsmpls ldptrust upstream default#interface LoopBack1ip address ******* ***************#ospf 1area 0.0.0.0network ******* 0.0.0.0network ********** *********network ********** *********#return       - PE2 的配置文件文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 274HUAWEI NetEngine40E配置指南 1 QoS#sysname PE2#ip vpn-instance vpnaipv4-familyroute-distinguisher 200:1apply-label per-instancevpn-target 111:1 export-extcommunityvpn-target 111:1 import-extcommunitydiffserv-mode pipe af1 green#ip vpn-instance vpnbipv4-familyroute-distinguisher 200:2apply-label per-instancevpn-target 222:2 export-extcommunityvpn-target 222:2 import-extcommunitydiffserv-mode pipe be yellow#mpls lsr-id *******#mpls#mpls ldp#interface GigabitEthernet1/0/0undo shutdownip binding vpn-instance vpnaip address ******** *************#interface GigabitEthernet2/0/0undo shutdownip binding vpn-instance vpnbip address ******** *************#interface GigabitEthernet3/0/0undo shutdownip address ********** *************mplsmpls ldp#interface LoopBack1ip address ******* ***************#bgp 100peer ******* as-number 100peer ******* connect-interface LoopBack1#ipv4-family unicastundo synchronizationpeer ******* enable#ipv4-family vpnv4policy vpn-targetpeer ******* enable#ipv4-family vpn-instance vpnapeer ******** as-number 65430import-route direct#ipv4-family vpn-instance vpnbpeer ******** as-number 65440import-route direct#ospf 1area 0.0.0.0network ******* 0.0.0.0network ********** *********文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 275HUAWEI NetEngine40E配置指南 1 QoS#return       - CE1 的配置文件#sysname CE1#interface GigabitEthernet1/0/0undo shutdownip address ******** *************#bgp 65410peer ******** as-number 100#ipv4-family unicastundo synchronizationimport-route directpeer ******** enable#return       - CE2 的配置文件#sysname CE2#interface GigabitEthernet1/0/0undo shutdownip address ******** *************#bgp 65420peer ******** as-number 100#ipv4-family unicastundo synchronizationimport-route directpeer ******** enable#return       - CE3 的配置文件#sysname CE3#interface GigabitEthernet1/0/0undo shutdownip address ******** *************#bgp 65430peer ******** as-number 100#ipv4-family unicastundo synchronizationimport-route directpeer ******** enable#return       - CE4 的配置文件#sysname CE4#interface GigabitEthernet1/0/0undo shutdownip address ******** *************#bgp 65440peer ******** as-number 100#ipv4-family unicast文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 276HUAWEI NetEngine40E配置指南 1 QoSundo synchronizationimport-route directpeer ******** enable#return### 1.10 用户接入 QoS 配置本章介绍了用户接入相关的 QoS 配置。#### 1.10.1 用户接入 QoS 配置注意事项##### 特性限制表 **1-24** 本特性的使用限制|特性限制|系列|涉及产品||---|---|---||当域下配置了user-max-session时，不支持基于<br>Option82对用户进行GQ限速。若用户上线接口<br>上配置基于option82识别的GQ模板且绑定了有<br>user-max-session配置的域时，用户上线会失<br>败。|NE40E|NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A||同一家庭的用户从不同子接口上线时，如果各子<br>接口配置不同补偿值按本家庭最后一个上线用户<br>所在子接口配置生效。建议各子接口配置相同的<br>最后一公里补偿值|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK||同一个家庭session-limit配置应保持一致，如果<br>配置不一致，不能确定按照哪条配置生效。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK||COA下发qos-profle模板时，如果前后模式不一<br>致（8queue模式和4queue模式），切换过程会<br>存在短暂用户HQOS不生效。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|#### 1.10.2 配置基于 UCL 的复杂流分类配置 UCL 可以实现不同优先级用户的区分，保证用户的承诺带宽和峰值带宽。文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 277HUAWEI NetEngine40E配置指南 1 QoS##### 应用环境UCL 是针对用户级别的 ACL 控制。接入用户在认证的过程中，如果需要对一些用户的流量进行限制，例如允许或限制用户访问某些网站的权利，可以通过配置基于 UCL 的流量策略来实现。在接入设备上，配置 UCL 可以实现不同优先级用户的区分，并对用户的流量进行 QoS 保证，保证用户的承诺带宽和峰值带宽。 UCL 根据规格的配置方式分为用户到网络、用户到用户、网络到网络和网络到用户四类。当在 ACL 定义的规则中指定匹配规则的源用户组时为用户到网络类型，指定匹配规则的目的用户组时为网络到用户类型，既指定源用户组也指定目的用户组时为用户到用户类型，当源和目的都没有指定用户组时为网络到网络类型。说明UCL 规则中支持基于 IPv4 和 IPv6 进行流分类，其中 IPv6 只支持上行。##### 前置任务在配置 UCL 用户的流量策略之前，需要完成以下任务：       - 配置接口的链路层协议参数（和 IP 地址），使接口的链路协议状态为 Up       - 配置路由协议，实现骨干网的 IP 连通性       - 配置接入业务，使用户可以正常接入网络##### ******** 配置用户组创建需要进行流量策略的用户组，作为 UCL 匹配规格的对象。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **user-group** group-name ，创建一个新的用户组。步骤 **3** 执行命令 **commit** ，提交配置。**----**结束##### ******** 配置业务组创建需要进行流量策略的业务组，作为 UCL 匹配规格的对象。##### 背景信息VS 模式下，该配置仅在 Admin VS 支持。##### 操作步骤步骤 **1** 执行命令 **system-view** ，进入系统视图。步骤 **2** 执行命令 **service-group** server-group-name ，创建一个新的业务组。步骤 **3** 执行命令 **commit** ，提交配置。**----**结束文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 278
最终截取: #### 1.10.1 用户接入 QoS 配置注意事项

##### 特性限制


表 **1-24** 本特性的使用限制



|特性限制|系列|涉及产品|
|---|---|---|
|当域下配置了user-max-session时，不支持基于<br>Option82对用户进行GQ限速。若用户上线接口<br>上配置基于option82识别的GQ模板且绑定了有<br>user-max-session配置的域时，用户上线会失<br>败。|NE40E|NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A|
|同一家庭的用户从不同子接口上线时，如果各子<br>接口配置不同补偿值按本家庭最后一个上线用户<br>所在子接口配置生效。建议各子接口配置相同的<br>最后一公里补偿值|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|
|同一个家庭session-limit配置应保持一致，如果<br>配置不一致，不能确定按照哪条配置生效。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|
|COA下发qos-profle模板时，如果前后模式不一<br>致（8queue模式和4queue模式），切换过程会<br>存在短暂用户HQOS不生效。|NE40E|NE40E-X16C/<br>NE40E-X8C/<br>NE40E-X8A/<br>NE40E-X3A/<br>NE40E-X16A/<br>NetEngine 40E-<br>X8AK|

