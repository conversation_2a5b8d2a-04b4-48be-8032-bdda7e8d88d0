

多个用户在一台路由器上对同一业务进行相同配置时系统回应提示信息的例子。

##### 组网需求


如 图 **1-54** 所示， UserA 和 UserB 同时登录路由器。在 UserA 对某业务进行配置后，
UserB 在路由器上进行相同的配置。


图 **1-54** 两阶段生效模式下多用户对同一业务进行相同配置组网图


UserB 提交的配置与 UserA 配置重复，系统会提示用户有冲突的配置。

##### 配置思路


采用如下的思路提交配置：


文档版本 01 (2024-03-31) 版权所有 © 华为技术有限公司 221


HUAWEI NetEngine40E
配置指南 1 基础配置


1. UserA 和 UserB 先后进行配置，且配置的业务重复。


2. UserA 提交配置。


3. UserB 提交配置。

##### 数据准备


接口的 IP 地址

##### 操作步骤


步骤 **1** UserA 和 UserB 同时进行配置，配置的业务重复。


       - UserA 配置路由器接口 1/0/4 的 IP 地址为 ******** 。


<HUAWEI> **system-view**

[ ~ HUAWEI] **interface GigabitEthernet 1/0/4**

[ ~ HUAWEI-GigabitEthernet1/0/4] **ip address ******** 24**


       - UserB 配置路由器接口 1/0/4 的 IP 地址为 ******** 。


<HUAWEI> **system-view**

[ ~ HUAWEI] **interface GigabitEthernet 1/0/4**

[ ~ HUAWEI-GigabitEthernet1/0/4] **ip address ******** 24**


步骤 **2** UserA 提交配置。


[*HUAWEI-GigabitEthernet1/0/4] **commit**


步骤 **3** UserB 提交配置。


可以看到，在 UserB 提交配置时，系统提示用户重复配置。


[*HUAWEI-GigabitEthernet1/0/4] **commit**
ip address ******** 24
Error: The address already exists.


Commit canceled, the configuration conflicted with other user, you can modify
the configuration and commit again.


**----**
结束

##### 配置文件


#

sysname HUAWEI
#
interface GigabitEthernet1/0/4
undo shutdown
ip address ******** *************
#
