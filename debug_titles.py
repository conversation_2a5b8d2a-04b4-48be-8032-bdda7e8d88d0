#!/usr/bin/env python3
"""
调试标题顺序和内容分割问题
"""

import fitz
import pdfplumber
from main2 import PDFToMarkdownConverter

def debug_title_order():
    """调试标题顺序问题"""
    print("=== 调试标题顺序问题 ===")
    
    with PDFToMarkdownConverter("2.pdf") as converter:
        # 获取原始TOC
        toc = converter.fitz_doc.get_toc()
        print(f"原始TOC前20个条目:")
        for i, (level, title, page) in enumerate(toc[:20]):
            print(f"{i+1:3d}. [级别{level}] {title} (页码{page})")
        
        print("\n" + "="*50)
        
        # 获取提取的标题
        titles = converter.extract_titles_from_toc()
        print(f"提取的标题前20个:")
        for i, title in enumerate(titles[:20]):
            print(f"{i+1:3d}. [级别{title.level}] {title.text} (页码{title.page_num})")
            if title.bbox:
                print(f"     位置: top={title.bbox['top']:.1f}, bottom={title.bbox['bottom']:.1f}")
        
        print("\n" + "="*50)
        
        # 检查排序后的标题
        titles.sort(key=lambda t: (t.page_num, t.bbox["top"] if t.bbox else 0))
        print(f"排序后的标题前20个:")
        for i, title in enumerate(titles[:20]):
            print(f"{i+1:3d}. [级别{title.level}] {title.text} (页码{title.page_num})")
            if title.bbox:
                print(f"     位置: top={title.bbox['top']:.1f}, bottom={title.bbox['bottom']:.1f}")

def debug_content_extraction():
    """调试内容提取问题"""
    print("\n=== 调试内容提取问题 ===")
    
    with PDFToMarkdownConverter("2.pdf") as converter:
        titles = converter.extract_titles_from_toc()
        titles.sort(key=lambda t: (t.page_num, t.bbox["top"] if t.bbox else 0))
        
        # 检查前几个标题的内容提取
        for i in range(min(5, len(titles))):
            title = titles[i]
            print(f"\n--- 标题 {i+1}: {title.text} ---")
            print(f"页码: {title.page_num}")
            if title.bbox:
                print(f"位置: top={title.bbox['top']:.1f}, bottom={title.bbox['bottom']:.1f}")
            
            # 确定内容范围
            start_page = title.page_num - 1
            start_y = title.bbox["bottom"] if title.bbox else 0
            
            if i + 1 < len(titles):
                next_title = titles[i + 1]
                end_page = next_title.page_num - 1
                end_y = next_title.bbox["top"] if next_title.bbox else float('inf')
                print(f"下一个标题: {next_title.text} (页码{next_title.page_num})")
                if next_title.bbox:
                    print(f"下一个标题位置: top={next_title.bbox['top']:.1f}")
            else:
                end_page = len(converter.plumber_doc.pages) - 1
                end_y = float('inf')
                print("这是最后一个标题")
            
            print(f"内容范围: 页码{start_page+1}(y>{start_y:.1f}) 到 页码{end_page+1}(y<{end_y:.1f})")
            
            # 提取内容预览
            content_parts = []
            for page_num in range(start_page, min(start_page + 2, end_page + 1)):  # 只看前2页
                if page_num >= len(converter.plumber_doc.pages):
                    break
                    
                page = converter.plumber_doc.pages[page_num]
                page_height = page.height
                
                current_start_y = start_y if page_num == start_page else 0
                current_end_y = end_y if page_num == end_page else page_height
                
                words = page.extract_words()
                page_content = []
                
                for word in words:
                    if word["top"] < current_start_y or word["top"] > current_end_y:
                        continue
                    
                    if converter.is_header_footer(word["text"], page_height, word["top"]):
                        continue
                    
                    if converter.is_table_of_contents(word["text"]):
                        continue
                    
                    page_content.append(word["text"])
                
                if page_content:
                    content_parts.append(" ".join(page_content))
            
            full_content = " ".join(content_parts)
            cleaned_content = converter.clean_text(full_content)
            
            print(f"提取的内容预览 (前200字符):")
            print(f"'{cleaned_content[:200]}...'")

if __name__ == "__main__":
    debug_title_order()
    debug_content_extraction()
